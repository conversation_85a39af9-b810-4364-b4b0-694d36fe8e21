<div>
    <style type="text/css">
        .loader {
            z-index: 9999999999;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .loader-content {
            position: absolute;
            border: 16px solid #f3f3f3;
            border-top: 16px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            top: 40%;
            left: 50%;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
    @if ($currentStep == 1)
    <section class="auth_section">
        <div class="container">
            <div class="stepper_header d-flex justify-content-between">
                <a class="auth_logo" href="/">
                    <img height="39" width="112" src="{{ asset('admin_assets/images/elmos-logo.png') }}"
                        alt="elmos logo">
                </a>

                <div class="steps">
                    <ul class="steps_list">

                        <li class="step_item done">
                            <div class="step_count">1</div>
                            <h2 class="step_title">Verification</h2>
                        </li>

                        <li class="step_item active">
                            <div class="step_count">2</div>
                            <h2 class="step_title">Packages</h2>
                        </li>

                        <li class="step_item">
                            <div class="step_count">3</div>
                            <h2 class="step_title">Successful</h2>
                        </li>

                    </ul>
                </div>

                <a class="log-out" href="{{ route('web.logout') }}"
                            onclick="event.preventDefault(); document.getElementById('logout-form2').submit();">Logout</a>
                <form id="logout-form2" action="{{ route('web.logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>


            <div class="packages_plan_container">
                @error('error') <span class="laravel_error">{{ $message }}</span> @enderror
                @if ($message = Session::get('success'))
                <div class="alert alert-success alert-dismissible mt-4 fade show ">
                    <strong>{{ $message }}</strong>
                    <button type="button" class="btn-close" data-dismiss="alert"></button>
                </div>
                @endif
                @if ($message = Session::get('message'))
                <div class="alert alert-danger alert-dismissible mt-4 fade show">
                    <strong>{{ $message }}</strong>
                    <button type="button" class="btn-close" data-dismiss="alert"></button>
                </div>
                @endif
                <h2 class="plan_title my-5">Select Your Plan</h2>

                <div class="plan_grid">
                    @foreach ($plans as $plan)
                    <div class="auth_wrapper h-100 px-5 d-flex flex-column justify-content-between position-relative">
                        @if($plan->name == config('custom.plans.premium.name'))
                            <p class="pos-abso position-absolute m-0 p-0" style="top: 100px;left:50%;transform:translateX(-50%);font-family: 'Poppins';font-style: normal;font-weight: 600;font-size: 15px;line-height: 16px;color:black;">+ $399 one-time <sub style="bottom: -2px;">set-up fee</sub></p>
                        @endif
                        <div>
                            <h3 class="plan_heading {{ $plan->name == 'Premium' ? 'premium' : 'basic' }} ">
                                {{ $plan->name == 'Premium' ? '$' . $plan->price : '14 Days ' }}<span>{{ $plan->name == 'Premium' ? '/' : 'Free Trial' }}
                                {{ $plan->name == 'Premium' ? $plan->interval : '' }}</span>
                            </h3>
                            <h4 class="plan_type {{ $plan->name == 'Premium' ? 'premium' : 'basic' }} mt-4 mb-5">
                                {{ $plan->name }}</h4>
                            @if ($plan->name == 'Premium')
                            <ul class="plan_list py-4 m-0">
                                <li class="plan_item">Client management</li>
                                <li class="plan_item">Lead management</li>
                                <li class="plan_item">Create Accurate & Profitable Estimates</li>
                                <li class="plan_item">Easily Schedule Jobs</li>
                                <li class="plan_item">Business Insights</li>
                            </ul>
                            @endif
                            @if ($plan->name == config('custom.plans.free.name'))
                            <ul class="plan_list py-4 m-0">
                                <li class="plan_item">Get full access to {{ config('app.name') }} software for 14 days</li>

                            </ul>
                            @endif
                        </div>

                        <button wire:click="goToStepTwo({{ $plan->id }})" type="button" class="btn
                            primaryblue w-100 mt-4">{{ $plan->name == 'Premium' ? 'Buy Now' : 'Start Demo' }}
                        </button>
                    </div>
                    @endforeach
                </div>
            </div>

        </div>
    </section>

    @endif

    @if ($currentStep == 2)
    <section class="auth_section">
        <div class="container">
            <div class="stepper_header d-flex justify-content-between">
                <a class="auth_logo" href="/">
                    <img height="39" width="112" src="{{ asset('admin_assets/images/elmos-logo.png') }}"
                        alt="elmos logo">
                </a>

                <div class="steps">
                    <ul class="steps_list">

                        <li class="step_item done">
                            <div class="step_count">1</div>
                            <h2 class="step_title">Verification</h2>
                        </li>

                        <li class="step_item done">
                            <div class="step_count">2</div>
                            <h2 class="step_title">Packages</h2>
                        </li>

                        <li class="step_item active">
                            <div class="step_count">3</div>
                            <h2 class="step_title">Successful</h2>
                        </li>

                    </ul>
                </div>

                <a class="log-out" href="{{ route('web.logout') }}"
                            onclick="event.preventDefault(); document.getElementById('logout-form2').submit();">Logout</a>
                <form id="logout-form2" action="{{ route('web.logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>

            <div class="packages_plan_container">

                <h2 class="plan_title my-5">Payment Method</h2>
                @error('error') <span class="laravel_error">{{ $message }}</span> @enderror
                <div class="payment_info">
                    <div class="payment_logo">
                        <img width="187px" height="117px" src="{{ asset('admin_assets/images/icons/stripe.png') }}"
                            alt="stripe">
                    </div>



                    <button type="button" wire:click="redirectToStripe"
                        class="btn primaryblue confirm_payment_btn">
                        Confirm Payment
                    </button>

                    <div wire:loading wire:target="redirectToStripe">
                        <section id="loader" class="loader">
                            <div id="loader-content" class="loader-content"></div>
                        </section>
                    </div>

                </div>

            </div>

        </div>

    </section>
    @endif


</div>
