@extends('client.layouts.master')
@section('title', 'Create Estimate Requests')
@section('section')
    <section class="dashboard_main pb-5">

        <!-- Property Detail Modal -->


        <div class="request-wraper">
            <form id="estimate_create_form"
                action="{{ route('client.estimates.requests.store', [request()->route('organization_id')]) }}" method="POST"
                enctype="multipart/form-data">
                @csrf
                <div class="request-details-box item-1">
                    <div class="request-detail-header px-24">
                        <h2 class="request-title">Estimate Request</h2>
                        <p class="estimation-request">Here you can enter details about your estimate</p>
                    </div>

                    {{-- <hr class="border-line-hr"> --}}
                    <div class="title-field-wraper position-relative px-24">
                        <label for="requsetTitle" class="title-label">Estimate Name <span
                                style="color: red;">*</span></label>
                        <input type="text" class="title-field @error('project_name') is-invalid @enderror "
                            id="requsetTitle" name="project_name" placeholder="Enter estimate name"
                            value="{{ old('project_name') }}">
                        @error('project_name')
                            <label id="requsetTitle-error" class="error" for="requsetTitle">{{ $message }}</label>
                        @enderror
                    </div>

                    <label for="propertyDetails" class="title-label d-block px-24 mt-4">Property Details <span
                            style="color: red;">*</span></label>
                    <div class="property-container-main px-24 mt-3">
                        <div class="propert-container">
                            <h2 class="marks-property request-property-name">
                                {{ old('property_name', optional($clientPropertyDetails->propertyAddress)?->property_name) }}
                            </h2>
                            <p class="property-address request-property-address">
                                {{ old('address1', optional($clientPropertyDetails->propertyAddress)?->address1) }}</p>
                            <p class="country-and-address">
                                <span class="propert-country ">City name: <span class="request-property-city">
                                        {{ old('city', optional($clientPropertyDetails->propertyAddress)?->city) }}</span></span>
                                <span class="propert-country ">State: <span class="request-property-state">
                                        {{ old('state', optional($clientPropertyDetails->propertyAddress)?->state) }}</span></span>
                                <span class="propert-country ">Zipcode: <span class="request-property-zipcode">
                                        {{ old('czip', optional($clientPropertyDetails->propertyAddress)?->zip) }}</span></span>
                            </p>
                        </div>
                        @if (
                            $errors->has('property_name') ||
                                $errors->has('address1') ||
                                $errors->has('city') ||
                                $errors->has('state') ||
                                $errors->has('czip'))
                            <label id="requsetTitle-error" class="error">
                                @if ($errors->has('property_name'))
                                    {{ $errors->first('property_name') }}<br>
                                @endif
                                @if ($errors->has('address1'))
                                    {{ $errors->first('address1') }}<br>
                                @endif
                                @if ($errors->has('city'))
                                    {{ $errors->first('city') }}<br>
                                @endif
                                @if ($errors->has('state'))
                                    {{ $errors->first('state') }}<br>
                                @endif
                                @if ($errors->has('czip'))
                                    {{ $errors->first('czip') }}
                                @endif
                            </label>
                        @endif
                    </div>

                    <div class="px-24">
                        <button class="add-new-propert" type="button" data-toggle="modal"
                            data-target="#propertyModal">
                            Add New Property</button>
                    </div>

                    <div class="px-24">
                        <label for="" class="reqest-text-label d-block">Request details</label>
                        <textarea name="description" id="describeProject" class="describe-project mt-3 d-block"
                            placeholder="Please describe your project details here."> {{ old('description') }} </textarea>
                        @error('description')
                            <label id="requsetTitle-error" class="error">{{ $message }}</label>
                        @enderror
                    </div>

                    <div class="px-24 mt-4">
                        <label for="" class="reqest-text-label d-block">How would you like to us to contact
                            you</label>
                        <div class="check-fileds-wraper">
                            <div class="check-box-wraper">
                                <input type="radio" id="email" name="type" value="email"
                                    {{ old('type') == 'email' ? 'checked' : '' }}>
                                <label for="email" class="radio-label"></label>
                                <label for="email" class="checkLabel">Email</label>
                            </div>

                            <div class="check-box-wraper">
                                <input type="radio" id="text" name="type" value="text"
                                    {{ old('type') == 'text' ? 'checked' : '' }}>
                                <label for="text" class="radio-label"></label>
                                <label for="text" class="checkLabel">Text</label>
                            </div>

                            <div class="check-box-wraper">
                                <input type="radio" id="phoneall" name="type" value="phone"
                                    {{ old('type', 'phone') == 'phone' ? 'checked' : '' }}>
                                <label for="phoneall" class="radio-label"></label>
                                <label for="phoneall" class="checkLabel">Phone Call</label>
                            </div>
                        </div>
                        @error('type')
                            <label id="requsetTitle-error" class="error">{{ $message }}</label>
                        @enderror
                    </div>
                    <div class="px-24 mt-5">
                        <div class="dropzone_library_customize needsclick dropzone" id="document-dropzone"></div>
                        <div id="imageSizeError"></div>

                    </div>
                </div>

                <div class="create-estimation-btns">
                    <button type="button" class="estimation-btn cancel-estimation"
                        onclick="window.history.back()">Cancel</button>
                    <button type="submit" class="estimation-btn create-estimation" id="customValidationBtn">Create
                        Estimate Request </button>
                </div>



                <div class="modal fade propert-modal" id="propertyModal" data-backdrop="static" data-keyboard="false"
                    tabindex="-1" aria-labelledby="propertyModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h1 class="modal-title fs-5" id="propertyModalLabel">Add Property</h1>
                                <button type="button" class="btn-close" data-dismiss="modal"
                                    aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="body-wraped" style="gap: 20px;">
                                    <div class="filed-wraper">
                                        <label for="name" class="modal-label">Choose Property</label>
                                        <select class="modal-field clientAddresses">
                                            <option disabled selected>Select Address</option>
                                            @foreach ($addresses as $item)
                                                <option value="{{ encodeID($item->id) }}">{{ $item->address1 }}</option>
                                            @endforeach
                                        </select>

                                    </div>
                                    <div class="filed-wraper">
                                        <label for="name" class="modal-label">Property name <span
                                                style="color:red;">*</span></label>
                                        <input class="modal-field" type="text" id="propertyName" name="property_name"
                                            placeholder="Enter name"
                                            value=" {{ old('property_name', optional($clientPropertyDetails->propertyAddress)?->property_name) }}">
                                    </div>
                                    <div class="filed-wraper">
                                        <label for="address" class="modal-label">Address 1 <span
                                                style="color:red;">*</span></label>
                                        <input class="modal-field" type="text" id="propertyAddress" name="address1"
                                            placeholder="Enter address 1"
                                            value=" {{ old('property_name', optional($clientPropertyDetails->propertyAddress)?->address1) }}">
                                    </div>
                                    <div class="filed-wraper">
                                        <label for="address2" class="modal-label">Address 2</label>
                                        <input class="modal-field" type="text" id="propertyAddress2" name="address2"
                                            placeholder="Enter address 2"
                                            value="   {{ old('property_name', optional($clientPropertyDetails->propertyAddress)?->address2) }}">
                                    </div>
                                    <div class="modal-flex">
                                        <div class="filed-wraper">
                                            <label for="address2" class="modal-label">City<span
                                                    style="color: red;">*</span></label>
                                            <input class="modal-field" type="text" id="clientCIty" name="city"
                                                placeholder="Enter city"
                                                value="{{ old('city', optional($clientPropertyDetails->propertyAddress)?->city) }}">
                                        </div>
                                        <div class="filed-wraper">
                                            <label for="address2" class="modal-label">State<span
                                                    style="color: red;">*</span></label>
                                            <input class="modal-field" type="text" id="lientState" name="state"
                                                placeholder="Enter state"
                                                value="{{ old('state', optional($clientPropertyDetails->propertyAddress)?->state) }}">
                                        </div>
                                    </div>
                                    <div class="filed-wraper">
                                        <label for="zipcode" class="modal-label">Zip code</label>
                                        <input class="modal-field" type="number" id="clientZipcode" name="czip"
                                            placeholder="Enter Zip Code"
                                            value="{{ old('czip', optional($clientPropertyDetails->propertyAddress)?->zip) }}">
                                    </div>
                                    <div class="add-btn cursor-pointer">Update</div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>




            </form>

            <!-- Modal -->
            <div class="operation_modal modal fade" id="filePreviewModal" tabindex="-1" role="dialog"
                aria-labelledby="filePreviewModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered " role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLongTitle">Preview</h5>
                        </div>
                        <div class="modal-body">
                            <div id="fileContent"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn transparent d-block w-100" data-dismiss="modal"
                                aria-label="Close">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            @include('client.partials.profile')
        </div>

    @endsection
    @push('scripts')
        @include('client.requests.dropzoneScript')
        <script>
            $(document).ready(function() {
                $('.clientAddresses').on('change', function(e) {
                    let id = $(this).val();

                    let url =
                        "{{ route('client.estimates.requests.client_address', [request()->route('organization_id'), ':id']) }}";
                    url = url.replace(':id', id);
                    console.log(url);
                    $.ajax({
                        type: "GET",
                        url: url,
                        dataType: "json",
                        success: function(response) {
                            console.log(response);
                            $('#propertyName').val(response.address.property_name);
                            $('#propertyAddress').val(response.address.address1);
                            $('#propertyAddress2').val(response.address.address2);
                            $('#clientCIty').val(response.address.city);
                            $('#lientState').val(response.address.state);
                            $('#clientZipcode').val(response.address.zip);
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        }
                    });

                })
            })
        </script>
    @endpush
