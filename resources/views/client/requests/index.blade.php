@extends('client.layouts.master')
@section('title', 'Requests list')
@section('section')
<style>
    .dropdown-wrapper {
    display: block !important;
}
</style>
    <section class="dashboard_main pb-5 estimates_requests_table_filters">

        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Requests list</h2>

            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                    <option value="" selected>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="open">Open</option>
                    {{-- <option value="close">Close</option> --}}
                    <option value="pending">Pending</option>
                </select>
                <!-- <a href="{{ route('client.estimates.requests.create', [request()->route('organization_id')]) }}"
                    class="btn primaryblue"> New Estimate Request</a> -->
            </div>
        </div>

        <div class="table-responsive">
            <table id="clients_Detail" class="table table-striped custom_datatable display clientTable" style="width:100%">
                <thead>
                    <tr>
                        <th>Request # </th>
                        <th>Estimate Request Detail </th>
                        <th class="width-186">Property Name</th>
                        <th>Created At </th>
                        <th>Communication</th>
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>

    </section>

    <!-- Delete modal -->

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body">
                    {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="delete icon"
                        class="delete-icon">
                    <h2 class="delete-request">Delete Request</h2>
                    <p class="are-sure">Are you sure you want to delete this request?</p>
                    <div class="buttons-wraper">
                        <input type="hidden" class="deleteEstimateId" value="">
                        <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                        <button type="button" class="conform-btn confirmDelete">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modalData"></div>

    </section>

@endsection
@push('scripts')
    @include('client.requests.script')
    @if (!empty(Session::get('showModal')))
        <script>
            $(function() {
                $('#requestModal').modal('show');
            });
        </script>
    @endif
@endpush
