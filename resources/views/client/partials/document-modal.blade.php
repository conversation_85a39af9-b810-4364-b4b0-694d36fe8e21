<!-- Modal -->
<div class="operation_modal modal fade" id="exampleModalCenter" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Documents</h5>

            </div>
            <div class="modal-body">

                <table class="table table-striped custom_datatable overflow-hidden">
                    @forelse($documents as $document)
                        <tr>
                            <td>
                                @if (\File::extension($document->file_name) == 'pdf')
                                    <p style="word-break: break-all;">{{ $document->file_name }}</p>
                                @elseif(in_array(\File::extension($document->file_name),['xls','doc','docx','xlsx']))
                                    <p style="word-break: break-all;">{{ $document->file_name }}</p>
                                @else
                                    <p style="word-break: break-all;">{{ $document->file_name }}</p>
                                @endif
                            </td>
                            <td style="text-align:right;">

                                @if (\File::extension($document->file_name) == 'pdf')
                                    <a href="{{ $document->getUrl() }}" target="_blank"> <i class='fa fa-eye'></i></a>
                                @elseif(in_array(\File::extension($document->file_name),['xls','doc','docx','xlsx']))
                                    <a href="{{ $document->getUrl() }}" target="_blank"> <i
                                            class='fa fa-download'></i></a>
                                @else
                                    <a href="{{ $document->getUrl() }}" target="_blank"> <i class='fa fa-eye'></i></a>
                                @endif
                            </td>
                        </tr>
                    @empty
                    @endforelse
                </table>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn transparent d-block w-100" data-dismiss="modal"
                    aria-label="Close">Close</button>
            </div>
        </div>
    </div>
</div>
