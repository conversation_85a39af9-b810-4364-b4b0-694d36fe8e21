<!DOCTYPE html>
<html lang="en">

<head>

    <!----------------- Meta Tags ------------------>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="Description" CONTENT="{{ config('app.name') }} Dashboard">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.css" rel="stylesheet" />
    <!---------------------- Meta Tages End -------------------->

    @include('client.includes.styles')

    @auth('client')
        @php
            $client = auth('client')->user()
                ? auth('client')
                    ->user()
                    ->load('organization')
                : null;
            $company_name = optional($client->organization)?->company_name;
            $company_image = optional($client->organization)?->profile_photo_path ? asset('storage/user_images/' . $client->organization->profile_photo_path) : null;
        @endphp
    @else
        @php
            $client = null;
            $company_image=null;
        @endphp
    @endauth
    <link rel="icon" type="image/x-icon" href="{{ $company_image ?? '' }}">
    <title>{{ ucfirst(isset($company_name) ? $company_name : config('app.name')) }}</title>
    <style type="text/css">
        #Notes-preview strike {
            text-decoration: line-through;
        }

        #Notes-preview u {
            text-decoration: underline;
        }

        .loader {
            z-index: 9999999999;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .loader-content {
            position: absolute;
            border: 16px solid #f3f3f3;
            border-top: 16px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            top: 40%;
            left: 50%;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>


    <div class="dashboard_wrapper">

        <div class="content_wrapper">
            <header class="dashboard_header">
            <img loading="lazy" class="rounded mt-1 mr-3" height="48px" width="48px"
            src="{{ isset($brandings->profile_photo_path) ? Storage::url('user_images/' . $brandings->profile_photo_path) : asset('admin_assets/images/account.png') }}" alt="total clients">
                <!-- <h2 class="dashboard_header_title">@yield('title', 'Dashboard')</h2> -->

                <div class="header_items">
                    <div class="profile_info_dropdown">
                        <div class="profile_info dropdown-toggle arrow" type="button" id="userDropdown"
                            data-toggle="dropdown" aria-expanded="false">

                            <div class="image">
                                <img class="rounded" height="38px" width="38px"
                                    src="{{ asset('admin_assets/images/dummy_image.webp') }}"
                                    alt="user image">
                            </div>
                            <div class="info">
                                <h2 class="welcome_status">Welcome</h2>
                                <h3 class="profile_name">
                                    {{ $client ? $client->first_name . ' ' . $client->last_name : '' }}
                                </h3>
                            </div>

                        </div>


                        <ul class="dropdown-menu" aria-labelledby="userDropdown">
                            @auth('client')
                                <li><a class="dropdown-item" href="{{ route('client.logout',[request()->route('organization_id')]) }}"
                                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>
                                    <form id="logout-form" action="{{ route('client.logout',[request()->route('organization_id')]) }}" method="POST"
                                        style="display: none;">
                                        @csrf
                                    </form>
                                </li>
                            @endauth
                        </ul>


                    </div>

                    <!-- <i class="aside_toggle fa fa-bars"></i> -->

                </div>
            </header>
            @include('client.includes.nav')
            @include('layouts.partials.flash-messages')
            @yield('section')
            <section id="loader">

                <div id="loader-content"></div>
            </section>
        </div>
    </div>

    @include('client.includes.scripts')
    @stack('scripts')

    <body>

</html>
