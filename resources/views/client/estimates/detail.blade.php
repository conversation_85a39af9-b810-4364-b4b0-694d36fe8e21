@extends('client.layouts.master')
@section('title', 'Estimate')
@section('section')
<style>
    .propert-modal .modal-content {
    border-radius: 8px;
    padding: 0px !important;
}
</style>
    <section class="dashboard_main pb-5">

        <div class="table_filter_header mb-4">
            <div class="header-left-content">
                <h2 class="sub_heading">Estimate Detail</h2>
                @if ($estimate->created_by == 'client')
                    <a href="{{ route('client.estimates.requests.view', [request()->route('organization_id'), encodeID($estimate->id)]) }}"
                        class="view-request-btn">View Estimate Request</a>
                @endif
            </div>

            <div class="header-buttpns-main">
                @if (!optional($estimate->generateEstimate)?->clientAction && $estimate->generateEstimate->status == 'proposed')
                    <button type="button" class="header-btn request-change" data-toggle="modal"
                        data-target="#requestChangeModal">Request Changes</button>
                    <button type="button" class="header-btn reject-btn" data-toggle="modal"
                        data-target="#rejectModal">Reject</button>
                    <button type="button" class="header-btn approve-btn" data-toggle="modal"
                        data-target="#approveModal">Approve</button>
                @endif

                <div class="dropdown">
                    <button type="button" class="header-btn action-btn" data-toggle="dropdown" aria-expanded="false">
                        <svg xmlns="http://www.w3.org/2000/svg" width="5" height="16" viewBox="0 0 5 16"
                            fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M4.5 13.8865C4.5 14.9657 3.6 15.8486 2.5 15.8486C1.4 15.8486 0.5 14.9657 0.5 13.8865C0.5 12.8073 1.4 11.9243 2.5 11.9243C3.6 11.9243 4.5 12.8073 4.5 13.8865ZM4.5 2.11358C4.5 3.19277 3.6 4.07575 2.5 4.07575C1.4 4.07575 0.5 3.19277 0.5 2.11358C0.5 1.0344 1.4 0.151423 2.5 0.151423C3.6 0.151423 4.5 1.0344 4.5 2.11358ZM4.5 8.00007C4.5 9.07926 3.6 9.96223 2.5 9.96223C1.4 9.96223 0.5 9.07926 0.5 8.00007C0.5 6.92088 1.4 6.03791 2.5 6.03791C3.6 6.03791 4.5 6.92088 4.5 8.00007Z"
                                fill="#84818A" />
                        </svg>
                    </button>
                    <ul class="dropdown-menu download-estimates">
                        {{-- <li><a class="dropdown-item" data-toggle="modal" data-target="#deleteModal">Cancel</a></li> --}}
                        <li><a class="dropdown-item"
                                href="{{ route('client.estimates.download', [request()->route('organization_id'), encodeID($estimate->id)]) }}">Download
                                Estimate</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="estimate-detail-box">
            <div class="estimate-top-header">
                <div class="header-left">
                    <span class="status-text">Status</span>
                    @if ($estimate->generateEstimate->status == \App\Models\GenerateEstimate::$PROPOSED)
                        @if (
                            $estimate->generateEstimate->client_status == \App\Models\GenerateEstimate::$CLIENT_PROPOSED_STATUS ||
                                $estimate->generateEstimate->client_status == \App\Models\GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS)
                            <span
                                class="status primary">{{ custom_title_case(optional($estimate->generateEstimate)?->client_status) }}</span>
                        @elseif ($estimate->generateEstimate->client_status == \App\Models\GenerateEstimate::$CLIENT_REJECT_STATUS)
                            <span
                                class="status danger">{{ custom_title_case(optional($estimate->generateEstimate)?->client_status) }}</span>
                        @elseif ($estimate->generateEstimate->client_status == \App\Models\GenerateEstimate::$CLIENT_APPROVE_STATUS)
                            <span
                                class="status success">{{ custom_title_case(optional($estimate->generateEstimate)?->client_status) }}</span>
                        @endif
                    @elseif($estimate->generateEstimate->status == \App\Models\GenerateEstimate::$WON)
                        <span class="status success">Approve</span>
                    @else
                        <span class="status danger">Reject</span>
                    @endif

                </div>
                <div class="header-right">
                    <span class="estimate-id color-oof-white">Estimate ID:</span>
                    <span class="estimate-id">{{ $estimate->job_no }}</span>
                </div>
            </div>
            <div class="table-responsive">
                <table class="detailed-table">
                    <thead>
                        <tr>
                            <th>Estimate Name</th>
                            <th>Property Name</th>
                            <th>Property Address</th>
                            <th>Received At </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <h2 class="td-title">{{ $estimate->opportunity_name }}</h2>
                                {{-- <p class="td-text">{{ Str::limit($estimate->description,100) }}</p> --}}
                            </td>
                            <td>
                                <h2 class="td-title">{{ optional($estimate->propertyInformation)?->name }} </h2>
                                <p class="td-text">{{ optional($estimate->propertyAddress)?->address1 }}</p>
                            </td>
                            <td>
                                <span>{{ optional($estimate->propertyInformation)?->address1 }}</span>
                            </td>
                            <td>
                                <span>{{ optional($estimate->generateEstimate)?->created_at ?: '' }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        @php
            $totalPayable = 0;
        @endphp
        <div class="table-wraper">
            <div class="table-responsive">
                <table class="table table-striped custom_datatable display view-detialed-table" style="width:100%">
                    <thead>
                        <tr>
                            <th>Item / Description</th>
                            <th>UoM</th>
                            <th>Quantity</th>
                            <th>Unit Cost</th>
                            <th>Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($estimations as $item)
                            <tr>
                                <td>{{ $item->item_name }}</td>
                                <td>{{ $item->uom }}</td>
                                <td>{{ $item->quantity }}</td>

                                <td>${{ $item->unit_cost }}
                                </td>
                                <td>${{ $item->total_price }}
                                </td>

                            </tr>
                        @empty
                        @endforelse

                        @forelse($estimateLabor as $item)
                            <tr>
                                <td>{{ $item->labor?->name }}</td>
                                <td>{{ $item->labor?->uom }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td>${{ $item->grand_total / $item->quantity }}
                                </td>
                                <td>${{ $item->grand_total }}
                                </td>

                            </tr>
                        @empty
                        @endforelse

                        @forelse ($estimateOtherCost as $item)
                            <tr>
                                <td>{{ $item->otherCost?->name }}</td>
                                <td>{{ $item->uom }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                                </td>
                                <td>${{ $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0 }}
                                </td>
                            </tr>
                        @empty
                        @endforelse
                        @forelse ($estimateSubContractor as $item)
                            <tr>
                                <td>{{ $item->name }}</td>
                                <td>{{ $item->uom }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td>${{ $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0 }}
                                </td>
                                <td>${{ $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0 }}

                            </tr>
                        @empty
                        @endforelse

                        @forelse ($estimateHardMaterial as $item)
                            <tr>
                                <td>{{ $item->material?->name }}</td>
                                <td>{{ $item->uom }}</td>
                                <td>{{ $item->quantity }}</td>
                                @php
                                    $total_price2 = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
                                @endphp
                                <td>${{ $total_price2 / $item->quantity ?? 0 }}
                                </td>
                                <td>${{ $total_price2 ?? 0 }}
                                </td>
                            </tr>
                        @empty
                        @endforelse
                        @forelse ($estimatePlantMaterial as $item)
                            <tr>
                                <td>{{ $item->material?->name }}</td>
                                <td>{{ $item->uom }}</td>
                                <td>{{ $item->quantity }}</td>
                                @php
                                    $total_price2 = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
                                @endphp
                                <td>${{ $total_price2 / $item->quantity ?? 0 }}
                                </td>
                                <td>${{ $total_price2 ?? 0 }}
                                </td>
                            </tr>
                        @empty
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="total_payable w-100 text-right mt-24 d-flex justify-content-end gap-5 align-items-center">
                <span class="title vertical-middle">Total Payable </span>
                <span class="value vertical-middle"> $
                    {{ $totalFinalPayable = optional($estimate->generateEstimate)?->grand_total && optional($estimate->generateEstimate)?->grand_total > 0 ? optional($estimate->generateEstimate)?->grand_total : optional($estimate->generateEstimate)?->total_price }}</span>
            </div>
        </div>

    </section>

    <!-- Remove modal -->

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body">
                    {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="delete icon"
                        class="delete-icon">
                    <h2 class="delete-request">Want to remove</h2>
                    <p class="are-sure">Are you sure you want to Remove this request?</p>
                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                        <button type="button" class="conform-btn">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Request Modal -->
    <div class="modal fade propert-modal" id="requestChangeModal" data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="requestChangeModalLabel">Change Estimate Request</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="body-wraped">
                        <form
                            action="{{ route('client.estimates.request_change', [request()->route('organization_id'), encodeID(optional($estimate)?->id)]) }}"
                            method="POST" onsubmit="return validateRejectForm(event)">
                            @csrf
                            @method('POST')
                            <div class="filed-wraper">
                                <label for="address" class="modal-label">Enter reason for changes</label>
                                <textarea class="modal-textarea @error('change_reason') is-invalid @enderror" name="change_reason" id="reasonChange"
                                    placeholder="Enter you reason" required>{{ old('change_reason') }}</textarea>
                                @error('change_reason')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                            <button type="submit" class="add-btn mt-3">Submit</button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- Reject Modal -->
    <div class="modal fade propert-modal" id="rejectModal" data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="rejectModalLabel">Reject Estimate</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="body-wraped">
                        <form
                            action="{{ route('client.estimates.reject', [request()->route('organization_id'), encodeID(optional($estimate)?->id)]) }}"
                            method="POST" onsubmit="return validateRejectForm(event)">
                            @csrf
                            @method('POST')
                            <div class="filed-wraper">
                                <label for="name" class="reason">Reason</label>
                                <p class="rejection-detail">Before you reject, Please let us know the reason job was lost.
                                </p>
                            </div>

                            <div class="radio-btns-wraper">
                                <div class="rejection-field-main">
                                    <input class="rejection-field" type="radio" id="pricing" value="pricing"
                                        name="rejection_reason"
                                        {{ old('rejection_reason') === 'pricing' ? 'checked' : '' }}
                                        {{ count(old()) == 0 ? 'checked' : '' }} required>
                                    <label for="pricing" class="radio-btn"></label>
                                    <label for="pricing" class="rejection-label">Price</label>
                                </div>

                                <div class="rejection-field-main">
                                    <input class="rejection-field" type="radio" value="timing" id="timing"
                                        name="rejection_reason"
                                        {{ old('rejection_reason') === 'timing' ? 'checked' : '' }} required>
                                    <label for="timing" class="radio-btn"></label>
                                    <label for="timing" class="rejection-label">Timing</label>
                                </div>

                                <div class="rejection-field-main">
                                    <input class="rejection-field" type="radio" value="budget" id="budget"
                                        name="rejection_reason"
                                        {{ old('rejection_reason') === 'budget' ? 'checked' : '' }} required>
                                    <label for="budget" class="radio-btn"></label>
                                    <label for="budget" class="rejection-label">Budget</label>
                                </div>

                                <div class="rejection-field-main">
                                    <input class="rejection-field" type="radio" value="other" id="other"
                                        name="rejection_reason" {{ old('rejection_reason') === 'other' ? 'checked' : '' }}
                                        required>
                                    <label for="other" class="radio-btn"></label>
                                    <label for="other" class="rejection-label">Other <small
                                            style="color: #90A0B7;">(Please explain below)</small></label>
                                </div>
                            </div>

                            <div class="filed-wraper">
                                <textarea class="modal-textarea @error('reason_for_change') is-invalid @enderror" name="reason_for_change"
                                    id="reasonReject" style="margin-top: 15px;min-height: 127px;"></textarea>
                                @error('reason_for_change')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                                <span class="other-detail-error d-none invalid-feedback">Please Enter Other Detail.</span>
                            </div>

                            <button type="submit" class="add-btn" style="margin-top: 16px;">Reject Estimate </button>
                        </form>
                    </div>


                </div>
            </div>
        </div>
    </div>


    <!-- Approve Modal -->
    <div class="modal fade propert-modal" id="approveModal" data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="approveModalLabel">Approved Estimate</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="body-wraped">
                        <form
                            action="{{ route('client.estimates.approval', [request()->route('organization_id'), encodeID(optional($estimate)?->id)]) }}"
                            method="POST">
                            @csrf
                            @method('POST')
                            <div class="filed-wraper">
                                <label for="name" class="modal-label">Type signature</label>
                                <input class="@error('signature') is-invalid @enderror modal-field"
                                    oninput="showAndValidateInput(event)" type="text" id="name" name="signature"
                                    placeholder="Signature" value="{{ old('signature') }}" required>
                                @error('signature')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="signature-box">
                                <span class="signature">Signature</span>
                            </div>
                            <p class="rejection-detail" style="font-size: 12px;">By writing your signature, you accept
                                this estimate, associated costs, and any terms and conditions that may apply.</p>
                            <button type="submit" class="add-btn" style="margin-top: 23px;">Approved</button>
                        </form>
                    </div>


                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        @if (session()->has('success'))
            toastr.success("{{ session()->pull('success') }}");
        @endif

        @if (session()->has('error'))
            toastr.error("{{ session()->pull('error') }}");
        @endif
    </script>


    @if ($errors->has('change_reason') || $errors->has('change_price'))
        <script>
            $(function() {
                $('#requestChangeModal').modal('show');
            });
        </script>
    @endif


    @if ($errors->has('rejection_reason') || $errors->has('reason_for_change'))
        <script>
            $(function() {
                $('#rejectModal').modal('show');
            });
        </script>
    @endif

    @if ($errors->any() && $errors->has('signature'))
        <script>
            $(function() {
                $(".signature-error").html("{{ $errors->first('signature') }}")
                $(".signature-input").addClass('is-invalid');
                $('#approveModal').modal('show');
            });
        </script>
    @endif
    <script>
        function showAndValidateInput(event) {
            const inputElement = event.target;
            const inputValue = inputElement.value;

            // Remove any non-character characters and update the input value
            const sanitizedValue = inputValue.replace(/[^A-Za-z.\s]+/g, '');
            inputElement.value = sanitizedValue;

            const outputDiv = document.querySelector('.signature');

            // Your existing conditions go here
            // For example, you can add additional logic to display the input value based on some conditions.

            if (sanitizedValue !== '') {
                // Display the sanitized input value in the outputDiv
                outputDiv.textContent = sanitizedValue;
            } else {
                // If the input is empty, clear the outputDiv
                outputDiv.textContent = '';
            }
        }

        function validateRejectForm(event) {
            const otherReasonRadio = document.getElementById('other');
            const reasonTextarea = document.getElementById('reasonReject');
            console.log("hi", reasonTextarea.value);
            if (otherReasonRadio.checked && reasonTextarea.value.trim() === '') {
                $(".other-detail-error").removeClass('d-none')
                $(".modal-textarea").addClass('is-invalid');
                $(".other-detail-error").html('Please provide a reason for rejection.');
                reasonTextarea.focus();
                event.preventDefault(); // Prevent form submission
                return false;
            } else {
                $(".modal-textarea").removeClass('is-invalid');
                $(".other-detail-error").addClass('d-none')
            }

            // Add other validation checks as needed

            // If form validation passes, return true to allow form submission
            return true;
        }
    </script>

@endpush
