@extends('client.layouts.master')
@section('title', 'Estimates')
@section('section')
<style>
    .dropdown-wrapper {
    display: block !important;
}
</style>
    <section class="dashboard_main pb-5 estimates_table_filters">

        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Estimate List</h2>

            <div class="filters estimate_filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search ">
                <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                    <option value="" selected>Filter</option>
                    <option value="clear">Clear</option>
                    <option value="proposed">Proposed</option>
                    <option value="reject">Reject</option>
                    <option value="approve">Approve</option>
                    <option value="request_change">Request Change</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table id="estimates_table" class="table table-striped custom_datatable display estimate-list clientTable"
                style="width:100%">
                <thead>
                    <tr>
                        <th>Estimate #</th>
                        <th>Estimate Detail</th>
                        <th class="width-186">Property Name</th>
                        <th>Received At </th>
                        <th>Total Amount</th>
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>

            </table>
        </div>

    </section>

    <!-- Delete modal -->

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body">
                    {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="delete icon"
                        class="delete-icon">
                    <h2 class="delete-request">Delete Request</h2>
                    <p class="are-sure">Are you sure you want to delete this request?</p>
                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                        <button type="button" class="conform-btn">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    @include('client.estimates.script')
    @if (!empty(Session::get('showModal')))
        <script>
            $(function() {
                $('#requestModal').modal('show');
            });
        </script>
    @endif
@endpush
