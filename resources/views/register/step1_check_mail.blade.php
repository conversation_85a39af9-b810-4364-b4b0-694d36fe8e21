@extends('layouts.authentication.master')
@section('section')
    <section class="auth_section">
        <div class="container">
            <div class="stepper_header d-flex justify-content-between">
                <a class="auth_logo" href="/">
                    <img height="39" width="112" src="{{ asset('admin_assets/images/elmos-logo.png') }}" alt="elmos logo">
                </a>

                <div class="steps">
                    <ul class="steps_list">

                        <li class="step_item active">
                            <div class="step_count">1</div>
                            <h2 class="step_title">Verification</h2>
                        </li>

                        <li class="step_item">
                            <div class="step_count">2</div>
                            <h2 class="step_title">Packages</h2>
                        </li>

                        <li class="step_item">
                            <div class="step_count">3</div>
                            <h2 class="step_title">Successful</h2>
                        </li>

                    </ul>
                </div>
                <a class="log-out" href="{{ route('web.logout') }}"
                            onclick="event.preventDefault(); document.getElementById('logout-form2').submit();">Logout</a>
                <form id="logout-form2" action="{{ route('web.logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>


            <div class="auth_container">

                <div class="auth_wrapper px-5">
                    @if ($message = Session::get('error'))
                        <div class="alert alert-danger alert-dismissible fade show">
                            <strong>{{ $message }}</strong>
                            <button type="button" class="btn-close" data-dismiss="alert"></button>
                        </div>
                    @endif
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/inbox-bg-icon.png') }}"
                            alt="search icon">
                    </div>

                    <h1 class="auth_title text-center">Check your mail</h1>
                    <p class="auth_note text-center mt-4">We’ve sent a verification email to {{ $email ?? '' }}</p>

                    <p class="p text-center mt-5">Click the link in your email to verify your account.</p>

                    <p class="auth_note text-center mt-4">If you have trouble finding your email, check your spam folder for
                        an email from {{ config('app.name') }}</p>

                    <p class="auth_note text-center mt-4 mb-4">Didn’t receive an email? <a
                            href="{{ route('resend-verify-mail') }}" class="ancher_blue">Resend</a></p>


                    <a href="{{ route('resend-verify-mail') }}"
                        class="btn primaryblue w-100 mt-3">Continue</a>
                </div>
            </div>

        </div>
    </section>
    @push('scripts')
        @if (!empty(Session::get('success')))
            <script>
                $(function() {
                    $('#verifiedMailModal').modal('show');
                });
            </script>
        @endif
    @endpush
@endsection
