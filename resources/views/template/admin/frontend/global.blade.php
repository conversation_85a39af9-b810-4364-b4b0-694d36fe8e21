@extends('template.admin.layouts.default')
@section('section')

<section class="dashboard_main">
    <h2 class="sub_heading">Globle Components</h2>

    <div class="panel mt-4">
        <div class="panel_header">
            <h2 class="panel_title">Fields</h2>
        </div>

        <div class="panel_fields mt-4">

            <div class="field">
                <label class="label">Address 1<span class="steric">*</span></label>
                <input type="text" placeholder="Address 1" class="input form-control">
            </div>

            <div class="field">
                <label class="label">Select Box<span class="steric">*</span></label>
                <div class="absolute-error">
                    <select name="" id="" class="input custom_selectBox basic-single-select">
                        <option value="" selected disabled>Salesman Name</option>
                        <option value="">Option1</option>
                        <option value="">Option2</option>
                        <option value="">Option3</option>
                    </select>
                </div>
            </div>

            <div class="field">
                <label class="label">Select With Search<span class="steric">*</span></label>
                <div class="absolute-error">
                    <select name="" id="" class="input custom_selectBox basic-single-select-search">
                        <option value="" selected disabled>Salesman Name</option>
                        <option value="">Option1</option>
                        <option value="">Option2</option>
                        <option value="">Option3</option>
                    </select>
                </div>
            </div>

            <div class="field">
                <label class="label">Mobile Number<span class="steric">*</span></label>
                <div class="absolute-error">
                    <input type="tel" placeholder="Mobile Number" name="off_num" value="" class="input form-control"
                        minlength="9" maxlength="22" oninput="this.value = this.value.replace(/[^+\d]|(?<=\+)\+/g, '');"
                        required>
                </div>
            </div>

            <div class="field">
                <label class="label">Phone Number<span class="steric">*</span></label>
                <div class="custom_number_field">
                    <select name="dial_code" class="input custom_selectBox basic-single-select-search">
                        <option value="">+92</option>
                        <?php
                                    $dial_codes = array('+93', '+355', '+213', '******', '+376', '+244', '******', '+672', '******', '+54', '+374', '+297', '+61', '+43', '+994', '******', '+973', '+880', '******', '+375', '+32', '+501', '+229', '******', '+975', '+591', '+387', '+267', '+55', '+246', '******', '+673', '+359', '+226', '+257', '+855', '+237', '+1', '+238', '+ 345', '+236', '+235', '+56', '+86', '+61', '+57', '+269', '+242', '+682', '+506', '+385', '+53', '+599', '+357', '+420', '+243', '+45', '+253', '******', '******', '******', '******', '+593', '+20', '+503', '+240', '+291', '+372', '+251', '+500', '+298', '+679', '+358', '+33', '+594', '+689', '+241', '+220', '+995', '+49', '+233', '+350', '+30', '+299', '+1 473', '+590', '+1 671', '+502', '+224', '+245', '+592', '+509', '+504', '+852', '+36', '+354', '+91', '+62', '+98', '+964', '+353', '+972', '+39', '+225', '+1 876', '+81', '+962', '+7', '+254', '+686', '+383', '+965', '+996', '+856', '+371', '+961', '+266', '+231', '+218', '+423', '+370', '+352', '+853', '+389', '+261', '+265', '+60', '+960', '+223', '+356', '+692', '+596', '+222', '+230', '+262', '+52', '+691', '+373', '+377', '+976', '+382', '+1 664', '+212', '+258', '+95', '+264', '+674', '+977', '+31', '+599', '+687', '+64', '+505', '+227', '+234', '+683', '+672', '+850', '+1 670', '+47', '+968', '+92', '+680', '+970', '+507', '+675', '+595', '+51', '+63', '+48', '+351', '+1 787', '+1 939', '+974', '+262', '+40', '+7', '+250', '+590', '+290', '+1 869', '+1 758', '+590', '+1 599', '+508', '+1 784', '+685', '+378', '+239', '+966', '+221', '+381', '+248', '+232', '+65', '+1 721', '+421', '+386', '+677', '+252', '+27', '+82', '+211', '+34', '+94', '+249', '+597', '+47', '+268', '+46', '+41', '+963', '+886', '+992', '+255', '+66','+670', '+228', '+690', '+676', '******', '+216', '+90', '+993', '******', '+688', '+256', '+380', '+971', '+44', '+1', '+598', '+998', '+678', '+379', '+58', '+84', '******', '+681', '+967', '+260', '+263');
                                ?>
                        @foreach ($dial_codes as $dial_code)
                        <option value="{{ $dial_code }}">{{ $dial_code }}</option>
                        @endforeach
                    </select>
                    <input type="text" placeholder="" name="phone_no" value="" class="input_number" minlength="9"
                        maxlength="14" oninput="this.value = this.value.replace(/[^+\d]|(?<=\+)\+/g, '');" required>
                </div>
            </div>

        </div>

    </div>

    <div class="panel d-flex align-items-center gap-4 flex-wrap mt-4">
        <button type="button" class="btn primaryblue">PrimaryBlue</button>
        <button type="button" class="btn anchor-btn">Anchor Btn</button>
        <button type="button" class="btn transparent">Transparent Btn</button>
        <form method="" action="" class="file_upload_button_form upload_file_wrapper w-fit"
            enctype="multipart/form-data">
            <label class="btn primaryblue transparent" for="import_file_data">Import Material</label>
            <input class="input_file d-none" type="file" name="" id="import_file_data" accept=".xls, .xlsx">
        </form>

        <form method="" action="" class="image_upload_btn upload_file_wrapper upload_image_btn w-fit"
            enctype="multipart/form-data">
            <label class="btn primaryblue transparent" for="upload_image">Upload Picture</label>
            <input class="input_file d-none" type="file" name="" id="upload_image">
        </form>
        <button type="button" class="trans-danger-btn">Delete</button>

    </div>

    <div class="table_filter_header mt-4 mb-4">
        <h2 class="sub_heading">Sub Heading</h2>
        <h2 class="table_title">Table Title</h2>

        <div class="filters">
            <input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
            <select name="" id="" class="table_filter_select select-small custom_selectBox" style="width:84px;">
                <option value="" selected>Filter</option>
                <option value="Clear">Clear</option>
                <option value="Administrator">Administrator</option>
                <option value="Sales man">Sales man</option>
                <option value="Coordinator">Coordinator</option>
            </select>
            <a href="{{route('frontend2.add_client')}}" class="btn primaryblue">+ Add New Client</a>
        </div>
    </div>

    <div class="panel mt4">
        <h2 class="h2">Simple Table</h2>

        <div class="table_tabs_filter mt-4 mb-4" role="group">
            <input type="radio" class="btn-check" name="companies" id="Premium" autocomplete="off" checked>
            <label class="btn btn-tab" for="Premium">Premium</label>

            <input type="radio" class="btn-check" name="companies" id="Basic" autocomplete="off">
            <label class="btn btn-tab" for="Basic">Basic</label>
        </div>

        <div class="table-responsive">
            <table id="clients_Detail" class="custom_datatable display mt-4" style="width:100%">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Property</th>
                        <th>Added By</th>
                        <th>Project Name</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="table_profile">
                                <img class="image" height="24px" width="24px"
                                    src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                                <h2 class="profile_name">Micheal James</h2>
                            </div>
                        </td>
                        <td>System Architect</td>
                        <td>Edinburgh</td>
                        <td>61</td>
                        <td>
                            <div class="status danger">Expired</div>
                            <!-- <div class="status success">Active</div> -->
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="table_profile">
                                <img class="image" height="24px" width="24px"
                                    src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                                <h2 class="profile_name">Micheal James</h2>
                            </div>
                        </td>
                        <td>Accountant</td>
                        <td>Tokyo</td>
                        <td>63</td>
                        <td>
                            <div class="status danger">Expired</div>
                            <!-- <div class="status success">Active</div> -->
                        </td>
                    </tr>

            </table>
        </div>

        <div class="table_header_footer mt4">
            <div class="page_per">
                <label class="info">Show</label>
                <select name="" id="" class="table_paigination darkarrow">
                    <option value="5">5</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="75">75</option>
                    <option value="All">All</option>
                </select>
                <label class="info">entries of 430 entries</label>
            </div>
        </div>

    </div>

    <div class="panel mt-4">
        <h2 class="h2">Yajra Table</h2>

        <section class="companies_table_filters">
            <div class="table_filter_header mb-4">
                <h2 class="sub_heading">Companies</h2>

                <div class="filters ">
                    <input type="search" placeholder="Search" name="" id="filter_search" class="filter_search">
                    <select name="" id="" class="table_filter_select select-small custom_selectBox" style="width:84px;">
                        <option value="" selected>Filter</option>
                        <option value="Clear">Clear</option>
                        <option value="Administrator">Administrator</option>
                        <option value="Sales man">Sales man</option>
                        <option value="Coordinator">Coordinator</option>
                    </select>
                </div>
            </div>

            <div class="table_tabs_filter mb-4" role="group">
                <input type="radio" class="btn-check" name="companies" id="Premium1" value='Premium' autocomplete="off">
                <label class="btn btn-tab" for="Premium1">Premium</label>

                <input type="radio" class="btn-check" name="companies" id="Basic1" value='Basic' autocomplete="off">
                <label class="btn btn-tab" for="Basic1">Basic</label>
            </div>
        </section>

        <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">

            <thead>
                <tr>
                    <th>Company Name</th>
                    <th>Address</th>
                    <th>Email</th>
                    <th>Package</th>
                    <th>Expiry Date</th>
                    <th>Package Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>sdfsdfsdf</td>
                    <td>sdfsdfsdf</td>
                    <td>sdfsdfsdf</td>
                    <td>sdfsdfsdf</td>
                    <td>sdfsdfsdf</td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img src="http://127.0.0.1:8000/admin_assets/images/icons/vertical-dots.svg"
                                    alt="vertical dots" width="24px" height="24px">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#">Generate Estimate</a></li>
                                <li><a class="dropdown-item" href="http://127.0.0.1:8000/frontend/request-detail">View
                                        Document</a></li>
                                <li><a class="dropdown-item" href="#">Edit Request</a></li>
                                <li><a class="dropdown-item" href="#">Delete Request</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

    </div>

    <div class="panel mt-4">
        <h2 class="text-md text-primary">Change Status</h2>
        <p class="text-placeholder text-sm">You can change organization status</p>
    </div>

    <div class="panel mt-4">
        <h2 class="text-md text-primary mb-4">Modals</h2>

        <div class="d-flex align-items-center gap-4 flex-wrap">
            <button class="btn primaryblue" data-toggle="modal" data-target="#editTableModal">modal1</button>
            <!-- Success modal Button-->
            <button type="button" class="btn primaryblue" data-toggle="modal" data-target="#successModal">
                Success Modal
            </button>
            <!-- Success modal Button-->

            <!-- Delete modal Button-->
            <button type="button" class="btn primaryblue transparent" data-toggle="modal"
                data-target="#DeleteModal">
                Delete Modal
            </button>
            <!-- Delete modal Button-->
        </div>
    </div>

    <div class="panel mt-4">
        <div class="panel_header mb-4">
            <h2 class="panel_title">Upload Document</h2>
        </div>

        <label class="label">Attach Documents</label>
        <div class="drop_zone_wrapper">
            <label for="file_upload" class="upload_documents_wrapper">

                <div class="drop-zone__prompt text-center">
                    <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"
                            fill="#90A0B7" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"
                            fill="#90A0B7" />
                    </svg>
                    <p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p>
                    <p class="placeholder-text font-14">PNG, JPEG, Max size: 2MB</p>
                </div>

                <input type="file" data-max_length="1" class="input_file drop-zone__input" name="" id="file_upload">
            </label>
            <div class="uploaded__img__wrapper mt-5"></div>
        </div>



    </div>


    <!--Success Modal -->
    <div class="modal-small success-modal modal fade" id="successModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Email Verified</h2>
                    <p class="para mt-3 text-center">Your mail account has been successfully verified.</p>
                    <button type="button" class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Continue</button>
                </div>

            </div>
        </div>
    </div>
    <!--Success Modal -->



    <!--Delete Modal -->
    <div class="modal-small Delete-modal modal fade" id="DeleteModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="DeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/delete-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Delete Request</h2>
                    <p class="para mt-3 text-center">Are you sure you want to delete this request.</p>

                    <div class="row">
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue transparent w-100 mt-5 "
                                data-dismiss="modal">Cancel</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue w-100 mt-5 ">Yes</button>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
    <!--Delete Modal -->


    <!-- **************Modals**************** -->
    <!--Edit Modal -->
    <div class="operation_modal modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel"
        aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTableModalLabel">Edit</h5>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h2 class="text-md text-primary mb-3">Change Status</h2>
                    <p class="text-placeholder text-sm">You can change organization status</p>

                    <div class="row">
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Won">
                                <label class="label" for="Won">Won</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Proposed">
                                <label class="label" for="Proposed">Proposed</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Lost" checked>
                                <label class="label" for="Lost">Lost</label>
                            </div>
                        </div>
                    </div>


                    <h2 class="text-md text-primary mb-3 mt-5">Reason</h2>
                    <p class="text-placeholder text-sm">Before you cancel, Please let us know the reason job was lost.
                    </p>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Price" checked>
                        <label class="label" for="Price">Price</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Competition">
                        <label class="label" for="Competition">Competition</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Budget">
                        <label class="label" for="Budget">Budget</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Timing">
                        <label class="label" for="Timing">Timing</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="PoorQualification">
                        <label class="label" for="PoorQualification">Poor Qualification</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Unresponsive">
                        <label class="label" for="Unresponsive">Unresponsive</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="NoDecision">
                        <label class="label" for="NoDecision">No Decision</label>
                    </div>

                    <div class="radio_group mt-5">
                        <input class="" type="radio" name="lost_reason" id="Other">
                        <label class="label" for="Other">Other <span class="placeholder-text font-14">(Please explain
                                below)</span></label>
                    </div>

                    <textarea class="input mt-5" placeholder="Anything you want to share?" name="" id=""
                        cols="30" rows="4"></textarea>


                </div>
                <div class="modal-footer pt-5">
                    <button type="button" class="btn primaryblue w-100">Update</button>
                </div>
            </div>
        </div>
    </div>
    <!-- **************Modals**************** -->
</section>
@endsection
