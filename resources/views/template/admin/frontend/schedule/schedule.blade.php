@extends('template.admin.layouts.default')
<link rel="stylesheet" href="{{ asset('admin_assets/styles/event-calender.css') }}">
<script src="{{ asset('admin_assets/scripts/index.global.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {

    //// the individual way to do it
    var containerEl = document.getElementById('external-events-list');
    var eventEls = Array.prototype.slice.call(
        containerEl.querySelectorAll('.fc-event')
    );
    eventEls.forEach(function(eventEl) {
        new FullCalendar.Draggable(eventEl, {
            eventData: {
                title: eventEl.innerHTML.trim()
            }
        });
    });

    /* initialize the calendar
    -----------------------------------------------------------------*/

    var calendarEl = document.getElementById('calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        editable: true,
        droppable: true,
        validRange: {
            start: new Date() // start date is today
        },
        headerToolbar: {
            left: 'title prev,next',
            // center: 'title',
            right: ''
        },
        drop: function(arg) {
            if (document.getElementById('drop-remove').checked) {
                arg.draggedEl.parentNode.removeChild(arg.draggedEl);
            }
        },
        dayMaxEvents: 2,
        eventContent: function(arg) {
            // create a new element to hold the event content
            var contentEl = document.createElement('div');
            // set the HTML content of the new element
            contentEl.innerHTML = arg.event.title;
            // return the new element
            return {
                domNodes: [contentEl]
            };
        },

        dateClick: function(info) {
            // Show the Bootstrap modal when a cell is double-clicked
            if (info.jsEvent.detail === 2) {
                var modal = new bootstrap.Modal(document.getElementById('selectCalenderItems'));
                modal.show();
            }
        },
    });
    calendar.render();

});
</script>
@section('section')
<section class="dashboard_main">

    <div class="panel event_calender_main">
        <div class='event_calender_wrapper'>

            <div id='calendar-wrap'>
                <div id='calendar'></div>
            </div>

            <div id='external-events'>
                <div class="external-events-filter">
                    <button class="today-btn">Today</button>
                    <div class="select_field">
                        <label for="months">Sort By:</label>
                        <select name="" id="months" class="select form-control arrow">
                            <option value="" selected disabled>Month</option>
                            <option value="Jan">Jan</option>
                            <option value="Feb">Feb</option>
                            <option value="Mar">Mar</option>
                            <option value="Apr">Apr</option>
                            <option value="May">May</option>
                            <option value="Jun">Jun</option>
                            <option value="Jul">Jul</option>
                            <option value="Aug">Aug</option>
                            <option value="Sep">Sep</option>
                            <option value="Oct">Oct</option>
                            <option value="Nov">Nov</option>
                            <option value="Dec">Dec</option>
                        </select>
                    </div>
                </div>

                <div class="external-events-wrapper">
                    <h2 class="title_status mb-3">Unscheduled</h2>
                    <input type="search" name="" id="" class="search_field mb-4" placeholder="Search">
                    <div class="btn-group drop_modify mb-3" role="group"
                        aria-label="Basic checkbox toggle button group">
                        <input type="checkbox" class="btn-check" id="drop-remove" autocomplete="off" checked>
                        <!-- <label class="btn drop_btn" for="drop-remove">Remove After Drop</label> -->
                    </div>
                    <div class="external-events-list" id='external-events-list'>
                        <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
                            <div class="request_id">Request <span>#234543</span></div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
                            <div class="request_id">Request <span>#234522</span></div>
                            <div class='request_name'>Mitcheal Starc</div>
                        </div>

                        <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
                            <div class="request_id">Request <span>#234544</span></div>
                            <div class='request_name'>Hoady Micon</div>
                        </div>

                        <div class='fc-event fc-event-main fc-h-event fc-daygrid-event fc-daygrid-block-event'>
                            <div class="request_id">Request <span>#234577</span></div>
                            <div class='request_name'>Stephen Hawk</div>
                        </div>

                    </div>
                </div>


            </div>

        </div>
    </div>

    <div class="operation_modal modal fade calender_items_modal" id="selectCalenderItems" tabindex="-1"
        aria-labelledby="selectCalenderItemsLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectCalenderItemsLabel">Scheduled Estimate</h5>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">

                    <div class="p-4 pb-0">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="search" name="" id="" class="search_field mb-4" placeholder="Search">
                            </div>
                            <div class="col-md-6">
                                <div class="field">
                                    <select name="" id="" class="input custom_selectBox basic-single-select">
                                        <option value="" selected>All</option>
                                        <option value="Scheduled" selected>Scheduled</option>
                                        <option value="Unscheduled">Unscheduled</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="external-events-list p-4 pt-0">

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="" checked>
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="" checked>
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="" checked>
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="" checked>
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="">
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="">
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="">
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                        <div class='fc-event'>
                            <div class="check_item">
                                <div class="request_id">Request <span>#234543</span></div>
                                <input type="checkbox" class="check_box" name="">
                            </div>
                            <div class='request_name'>Esther Howard</div>
                        </div>

                    </div>


                </div>
                <div class="modal-footer mt-4 p-4">
                    <button class="btn primaryblue transparent px-5" data-dismiss="modal"
                        aria-label="Close">Cancel</button>
                    <button type="button" class="btn primaryblue px-5">Schedule</button>
                </div>
            </div>
        </div>
    </div>

</section>
@endsection