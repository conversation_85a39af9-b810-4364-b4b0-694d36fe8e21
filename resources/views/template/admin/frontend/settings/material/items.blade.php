@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main">

    <div class="settings_tab_grid">

        <x-settings_component.settings_tab />

        <div class="settings_content">
            <div class="d-flex align-items-center gap-4 flex-wrap">
                <a href="{{route('material.items')}}" class="btn primaryblue px-5">Items</a>
                <a href="{{route('material.equipment')}}" class="btn placeholder_btn px-5">Equipment</a>
            </div>

            <div class="table_tabs_filter mt-4 mb-4 nav" role="group">
                <input type="radio" class="btn-check" name="material" id="hard_material" autocomplete="off" checked>
                <label class="btn btn-tab" for="hard_material" data-toggle="tab" data-target="#hard-material"
                    aria-controls="hard-material">Hard Materials</label>

                <input type="radio" class="btn-check" name="material" id="plan_material" autocomplete="off">
                <label class="btn btn-tab" for="plan_material" data-toggle="tab" data-target="#plan-material"
                    aria-controls="plan-material">Plan Materials</label>
            </div>

            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="hard-material" role="tabpanel"
                    aria-labelledby="hard-material-tab">
                    <x-settings_component.materials.items_hard_materials_table />
                </div>
                <div class="tab-pane fade" id="plan-material" role="tabpanel" aria-labelledby="plan-material-tab">
                    <x-settings_component.materials.items_plan_materials_table />
                </div>
            </div>


        </div>
    </div>

   <x-settings_component.help_modal/>


</section>
@endsection