@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main">

    <div class="settings_tab_grid">

        <x-settings_component.settings_tab />

        <div class="settings_content">
            <div class="d-flex align-items-center gap-4 flex-wrap">
                <a href="{{route('material.items')}}" class="btn placeholder_btn px-5">Items</a>
                <a href="{{route('material.equipment')}}" class="btn primaryblue px-5">Equipment</a>
            </div>

            <div class="panel mt-4">

                <div class="table_filter_header mt-4">
                    <h2 class="table_title">Equipment’s</h2>

                    <div class="filters">
                        <img height="24px" width="24px" src="{{asset('admin_assets/images/icons/help-icon.svg')}}"
                            alt="help icon" data-toggle="modal" data-target="#editTableModal">
                        <input type="search" placeholder="Search" name="" id=""
                            class="clients_Detail_Search filter_search">
                        <select name="" id="" class="select-small basic-single-select" style="width:84px;">
                            <option value="" selected>Filter</option>
                            <option value="Clear">Clear</option>
                            <option value="Hours">Hours</option>
                            <option value="Daily">Daily</option>
                            <option value="Weekly">Weekly</option>
                        </select>
                        <a href="#" class="btn primaryblue transparent px-5">Import Material</a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="clients_Detail" class="custom_datatable display mt-4" style="width:100%">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>UoM & unit Cost</th>
                                <th>Gross Margin</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Machines</td>
                                <td>Hours ($9), Daily ($20). Weekly($30)</td>
                                <td>50%</td>
                            </tr>
                            <tr>
                                <td>Machines</td>
                                <td>Hours ($9), Daily ($20). Weekly($30)</td>
                                <td>50%</td>
                            </tr>
                            <tr>
                                <td>Machines</td>
                                <td>Hours ($9), Daily ($20). Weekly($30)</td>
                                <td>50%</td>
                            </tr>
                            <tr>
                                <td>Machines</td>
                                <td>Hours ($9), Daily ($20). Weekly($30)</td>
                                <td>50%</td>
                            </tr>
                            <tr>
                                <td>Machines</td>
                                <td>Hours ($9), Daily ($20). Weekly($30)</td>
                                <td>50%</td>
                            </tr>
                            

                    </table>
                </div>

                <div class="table_header_footer mt4">
                    <div class="page_per">
                        <label class="info">Show</label>
                        <select name="" id="" class="table_paigination darkarrow">
                            <option value="5">5</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="75">75</option>
                            <option value="All">All</option>
                        </select>
                        <label class="info">entries of 430 entries</label>
                    </div>
                </div>

            </div>

        </div>
        
        <x-settings_component.help_modal/>

    </div>
</section>
@endsection