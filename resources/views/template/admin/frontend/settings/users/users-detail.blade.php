@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main">

    <div class="settings_tab_grid">

        <x-settings_component.settings_tab />

        <div class="settings_content">

            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
                <h2 class="sub_heading">USERS</h2>
                <a href="{{route('users.add')}}" class="btn primaryblue">+ Invite new user</a>
            </div>

            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Team Management</h2>
                </div>

                <div class="active_users_indication mt-4">
                    <label class="title">Active User</label>
                    <div class="active_count">5 OF 10</div>
                </div>

                <div class="table_wrapper mt-5">
                    <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">

                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Last Login</th>
                                <th>Status</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="table_profile">
                                        <img class="image" height="24px" width="24px"
                                            src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                                        <h2 class="profile_name">Micheal James</h2>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>Account Owner</td>
                                <td>30 dec 2022</td>
                                <td>
                                    <div class="status danger">Inactive</div>
                                    <!-- <div class="status success">Active</div> -->
                                </td>
                                <td>
                                    <div class="dropdown mx-auto w-fit">
                                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                            <img src="http://127.0.0.1:8000/admin_assets/images/icons/vertical-dots.svg"
                                                alt="vertical dots" width="24px" height="24px">
                                        </div>
                                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#changeStatus" >Change Status</a></li>
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#DeleteModal">Remove</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <td>
                                    <div class="table_profile">
                                        <img class="image" height="24px" width="24px"
                                            src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                                        <h2 class="profile_name">Micheal James</h2>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>Account Owner</td>
                                <td>30 dec 2022</td>
                                <td>
                                    <!-- <div class="status danger">Inactive</div> -->
                                    <div class="status success">Active</div>
                                </td>
                                <td>
                                    <div class="dropdown mx-auto w-fit">
                                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                            <img src="http://127.0.0.1:8000/admin_assets/images/icons/vertical-dots.svg"
                                                alt="vertical dots" width="24px" height="24px">
                                        </div>
                                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#changeStatus">Change Status</a></li>
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#DeleteModal">Remove</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <td>
                                    <div class="table_profile">
                                        <img class="image" height="24px" width="24px"
                                            src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                                        <h2 class="profile_name">Micheal James</h2>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>Account Owner</td>
                                <td>30 dec 2022</td>
                                <td>
                                    <!-- <div class="status danger">Inactive</div> -->
                                    <!-- <div class="status success">Active</div> -->
                                    <div class="status warning">Pending</div>
                                </td>
                                <td>
                                    <div class="dropdown mx-auto w-fit">
                                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                            <img src="http://127.0.0.1:8000/admin_assets/images/icons/vertical-dots.svg"
                                                alt="vertical dots" width="24px" height="24px">
                                        </div>
                                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#changeStatus">Change Status</a></li>
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" data-toggle="modal" data-target="#DeleteModal">Remove</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>

            </div>



        </div>

    </div>

    <div class="operation_modal modal fade" id="changeStatus" tabindex="-1" aria-labelledby="changeStatusLabel"
        aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changeStatusLabel">Edit</h5>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h2 class="text-md text-primary mb-3">Change Status</h2>
                    <p class="text-placeholder text-sm">You can change organization status</p>

                    <div class="row">
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Pending">
                                <label class="label" for="Pending">Pending</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Active" checked>
                                <label class="label" for="Active">Active</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio_group mt-5">
                                <input class="" type="radio" name="project_status" id="Inactive">
                                <label class="label" for="Inactive">Inactive</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer pt-5">
                    <button type="button" class="btn primaryblue w-100">Update</button>
                </div>
            </div>
        </div>
    </div>

    <!--Delete Modal -->
    <div class="modal-small Delete-modal modal fade" id="DeleteModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="DeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/delete-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Remove User</h2>
                    <p class="para mt-3 text-center">This user is permanently removed from the system. Are you sure you want to remove this user?</p>

                    <div class="row">
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue transparent w-100 mt-5 "
                                data-dismiss="modal">Cancel</button>
                        </div>
                        <div class="col-sm-6">
                            <button type="button" class="btn primaryblue w-100 mt-5 ">Yes</button>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
    <!--Delete Modal -->

</section>
@endsection