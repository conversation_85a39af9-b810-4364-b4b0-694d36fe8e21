@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main">
    <h2 class="sub_heading">Overview</h2>

    <div class="dashboard_stat_cards mt-4 pt-2">

        <div class="state_card">
            <div class="info">
                <h2 class="card_title">Total clients</h2>
                <!-- if super admin -->
                <!-- <h2 class="card_title">Total Companies</h2> -->
                <p class="card_count mt-4">100</p>
            </div>
            <div>
                <img loading="lazy" class="rounded" height="48px" width="48px"
                    src="{{ asset('admin_assets/images/icons/total-clients.png') }}" alt="total clients">
            </div>
        </div>

        <div class="state_card">
            <div class="info">
                <h2 class="card_title">Estimate Requests</h2>
                <!-- if super admin -->
                <!-- <h2 class="card_title">Active Companies</h2> -->
                <p class="card_count mt-4">16</p>
            </div>
            <div>
                <img loading="lazy" class="rounded" height="48px" width="48px"
                    src="{{ asset('admin_assets/images/icons/estimate-requests.png') }}" alt="total clients">
            </div>
        </div>

        <div class="state_card">
            <div class="info">
                <h2 class="card_title">Estimates</h2>
                <!-- if super admin -->
                <!-- <h2 class="card_title">Total Earning</h2> -->
                <p class="card_count mt-4">32</p>
            </div>
            <div>
                <img loading="lazy" class="rounded" height="48px" width="48px"
                    src="{{ asset('admin_assets/images/icons/estimates.png') }}" alt="total clients">
            </div>
        </div>

        <div class="state_card">
            <div class="info">
                <h2 class="card_title">Proposals</h2>
                <!-- if super admin -->
                <!-- <h2 class="card_title">Last Month Actives </h2> -->
                <p class="card_count mt-4">20</p>
            </div>
            <div>
                <img loading="lazy" class="rounded" height="48px" width="48px"
                    src="{{ asset('admin_assets/images/icons/proposals.png') }}" alt="total clients">
            </div>
        </div>

    </div>

    <div class="dashboard_sales_graphs">
        <div class="card_wrapper chart_wrapper">
            <div class="chart_header">
                <div class="info">
                    <h2 class="status_title">Won</h2>
                    <p class="value mt-2">$242.5K <span class="indicator"><i
                                class="fa-solid fa-arrow-   trend-up"></i>13.02%</span></p>
                </div>

                <select class="selectBox form-control arrow no-border" name="" id="">
                    <option value="this_month" selected>This Month</option>
                    <option value="jan">Jan</option>
                    <option value="feb">Feb</option>
                </select>
            </div>

            <canvas id="myChart2" style="width:100%;object-fit:contain;max-height:275px;height:100%"></canvas>
        </div>

        <div class="card_wrapper chart_wrapper">
            <div class="chart_header">
                <div class="info">
                    <h2 class="status_title">Statistics</h2>
                    <p class="value mt-2">Sales closed</p>
                </div>

                <select class="selectBox form-control arrow no-border" name="" id="">
                    <option value="this_week" selected>This Week</option>
                    <option value="monday">Mon</option>
                    <option value="tuesday">Tue</option>
                </select>
            </div>

            <canvas id="myChart3" style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

        </div>


        <div class="card_wrapper chart_wrapper">
            <div class="chart_header">
                <div class="info">
                    <h2 class="status_title">Won</h2>
                    <p class="value mt-2">$4.1M <span class="indicator"><i
                                class="fa-solid fa-arrow-   trend-up"></i>123.02k</span></p>
                </div>
                <ul class="legends aligned">
                    <li class="legend-list" onclick="toggleDataBars(0)">
                        <span class="legend-span" id="accepted"></span>
                        <span class="legend-button" id="acceptLine"></span>
                    </li>

                    <li class="legend-list" onclick="toggleDataBars(1)">
                        <span class="legend-span" id="rejected"></span>
                        <span class="legend-button" id="rejectLine"></span>
                    </li>

                </ul>
                <select class="selectBox form-control arrow no-border" name="" id="">
                    <option value="this_week" selected>This FY </option>
                    <option value="monday">Mon</option>
                    <option value="tuesday">Tue</option>
                </select>
            </div>

            <canvas id="myChart4" style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

        </div>



        <div class="card_wrapper chart_wrapper" style="overflow:hidden;padding-bottom: 0px;padding:0">
            <div class="sale-rip-title">
                <h2>Sales Rep</h2>
                <h3><a href="#">View All</a></h3>
            </div>
            <div class="chart_header  wb-kt-scroll"
                style="overflow-y:auto;max-height: 380px;height:100%; padding-left:24px;padding-right:24px;padding-top:15px">
                <ul class="sale-rep">
                    <li>

                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="sale-rip-cont">
                            <div>
                                <h2>Anna Kruger</h2>
                                <p>Sales revenue</p>
                            </div>
                            <div style="display:flex">
                                <h2>$39,188/</h2>
                                <p>$33,000</p>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>

            {{-- <canvas id="myChart3" style="width:100%;object-fit:contain;height:280px"></canvas> --}}

        </div>

    </div>

    <!-- If Super Admin Then Show This -->
    {{-- <div class="table_filter_header mt-4">
        <h2 class="sub_heading">Recently Joined companies</h2>

        <div class="filters">
            <input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
            <select name="" id="" class="select-small basic-single-select" style="width:84px;">
                <option value="" selected>Filter</option>
                <option value="Daily">Daily</option>
                <option value="Lastweek">Last week</option>
            </select>
        </div>
    </div>

    <div class="table-responsive">
        <table class="custom_datatable display mt-4" style="width:100%">
            <thead>
                <tr>
                    <th>Company Name</th>
                    <th>Address</th>
                    <th>Email</th>
                    <th>Active Date</th>
                    <th>Expiry Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Electrolux</td>
                    <td>123 Main St</td>
                    <td><EMAIL></td>
                    <td>02/03/2022</td>
                    <td>08/07/2023</td>
                    <td>
                        <!-- <div class="status warning">InActive</div> -->
                        <div class="status success">Active</div>
                    </td>
                </tr>
                <tr>
                    <td>Electrolux</td>
                    <td>123 Main St</td>
                    <td><EMAIL></td>
                    <td>02/03/2022</td>
                    <td>08/07/2023</td>
                    <td>
                        <!-- <div class="status warning">InActive</div> -->
                        <div class="status success">Active</div>
                    </td>
                </tr>
                <tr>
                    <td>Electrolux</td>
                    <td>123 Main St</td>
                    <td><EMAIL></td>
                    <td>02/03/2022</td>
                    <td>08/07/2023</td>
                    <td>
                        <!-- <div class="status warning">InActive</div> -->
                        <div class="status success">Active</div>
                    </td>
                </tr>
                <tr>
                    <td>Electrolux</td>
                    <td>123 Main St</td>
                    <td><EMAIL></td>
                    <td>02/03/2022</td>
                    <td>08/07/2023</td>
                    <td>
                        <!-- <div class="status warning">InActive</div> -->
                        <div class="status success">Active</div>
                    </td>
                </tr>
                <tr>
                    <td>Electrolux</td>
                    <td>123 Main St</td>
                    <td><EMAIL></td>
                    <td>02/03/2022</td>
                    <td>08/07/2023</td>
                    <td>
                        <!-- <div class="status warning">InActive</div> -->
                        <div class="status success">Active</div>
                    </td>
                </tr>

        </table>
    </div> --}}
    <!-- If Super Admin Then Show This -->
    {{-- <section class="sec">
  <div class="container">
  <div class="accordion" id="accordionExample">
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingOne">
        <button class="accordion-button" type="button" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
          Accordion Item #1
        </button>
      </h2>
      <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-parent="#accordionExample">
        <div class="accordion-body">
          <strong>This is the first item's accordion body.</strong> It is shown by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingTwo">
        <button class="accordion-button collapsed" type="button" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
          Accordion Item #2
        </button>
      </h2>
      <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-parent="#accordionExample">
        <div class="accordion-body">
          <strong>This is the second item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingThree">
        <button class="accordion-button collapsed" type="button" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
          Accordion Item #3
        </button>
      </h2>
      <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-parent="#accordionExample">
        <div class="accordion-body">
          <strong>This is the third item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
        </div>
      </div>
    </div>
  </div>
  <button id="saveBtn" type="submit">Save</button>
  <div id="dialog" title="Saved Sections"></div>

</section> --}}


</section>
{{-- <script>
  // script.js
const saveBtn = document.getElementById('saveBtn');
const newPage = document.getElementById('new-page');

saveBtn.addEventListener('click', () => {
  // Clone the section element
  const sectionClone = document.querySelector('.sec').cloneNode(true);

  // Append the cloned section to the new-page div
  newPage.appendChild(sectionClone);
});

</script> --}}
<script>
var xxValues = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
var yValues = [320, 200, 330, 230, 420, 180, 300];
var barColors = ["#109CF1"];

new Chart("myChart3", {
    type: "line",
    data: {
        labels: xxValues,
        datasets: [{
            // data: yValues,

            borderRadius: 10 // set border radius to 10
        }, {
            type: "bar",
            data: [310, 200, 320, 240, 410, 180, 300],
            borderRadius: 6,
            barThickness: 25,
            borderColor: "#1C7FD7", // change border color to a darker shade of blue
            borderWidth: 0,
            fill: true,
            pointRadius: 0,
            backgroundColor: barColors,

        }]
    },
    options: {
        layout: {
            padding: {
                top: 25 // add a margin of 25 pixels to the top of the chart
            },

        },
        scales: {
            x: {
                grid: {
                    display: false, // remove x-axis line
                    clip: false
                }

            },
            y: {
                ticks: {
                    beginAtZero: false,
                    stepSize: 100,
                    min: 100,
                    max: 300,

                    callback: function(value, index, values) {
                        return '$' + value + 'k';
                    },
                    font: {
                        size: 12,
                        color: '#90A0B7',
                        family: 'Poppins',
                    }
                },
                grid: {
                    display: true,
                },
                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
            }
        },
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: false,
                text: "World Wine Production 2018"
            },
        }
    }
});






var datas = {
    labels: ["2000", "2002", "2004", "2006", "2008", "2010", "2012"],
    datasets: [{
            label: "Lost",
            backgroundColor: "#DF6B32",
            borderWidth: 1,
            borderRadius: 5,
            barThickness: 10,
            barPercentage: 0.8,
            categoryPercentage: 0.5,
            data: [{
                    x: "2000",
                    y: 310
                },
                // {x: "2001", y: 210},
                {
                    x: "2002",
                    y: 530
                },
                {
                    x: "2004",
                    y: 70
                },
                // {x: "2005", y: 330},
                {
                    x: "2006",
                    y: 310
                },
                // {x: "2007", y: 170},
                {
                    x: "2008",
                    y: 190
                },
                // {x: "2009", y: 220},
                {
                    x: "2010",
                    y: 120
                },
                // {x: "2011", y: 580},
                {
                    x: "2012",
                    y: 410
                }
            ],
            barThickness: 'flex',
            categorySpacing: 0.2
        },
        {
            label: "Completed",
            backgroundColor: "#003366",
            borderWidth: 1,
            borderRadius: 5,
            barThickness: 10,
            barPercentage: 0.8,
            categoryPercentage: 0.5,
            data: [{
                    x: "2000",
                    y: 270
                },
                // {x: "2001", y: 180},
                {
                    x: "2002",
                    y: 300
                },
                {
                    x: "2004",
                    y: 140
                },
                // {x: "2005", y: 300},
                {
                    x: "2006",
                    y: 290
                },
                // {x: "2007", y: 140},
                {
                    x: "2008",
                    y: 260
                },
                // {x: "2009", y: 190},
                {
                    x: "2010",
                    y: 190
                },
                // {x: "2011", y: 500},
                {
                    x: "2012",
                    y: 250
                }
            ],
            barThickness: 'flex',
            categorySpacing: 0.2
        },
    ]
};


var doubleChart = new Chart("myChart4", {
    type: "bar",
    data: datas,
    options: {
        responsive: true,
        maintainAspectRatio: true,
        height: 340,
        layout: {
            padding: {
                left: 0,
                right: 10,
                top: 30,
                bottom: 4
            }
        },
        plugins: {
            legend: {
                display: false,
            },
        },
        scales: {
            x: {
                stacked: false,
                grid: {
                    display: false
                },
            },
            y: {
                ticks: {
                    beginAtZero: false,
                    stepSize: 100,
                    min: 100,
                    max: 300,
                    callback: function(value, index, values) {
                        return '$' + value + 'k';
                    },
                    font: {
                        size: 12,
                        color: '#90A0B7',
                        family: 'Poppins',
                    }
                },
                grid: {
                    display: true,
                },
                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
            },

        },
        barThickness: 8,
    }

});
document.getElementById("accepted").style.backgroundColor = doubleChart.data.datasets[0].backgroundColor;
document.getElementById("rejected").style.backgroundColor = doubleChart.data.datasets[1].backgroundColor;


document.getElementById("acceptLine").innerText = doubleChart.data.datasets[0].label;
document.getElementById("rejectLine").innerText = doubleChart.data.datasets[1].label;

function toggleDataBars(value) {
    var barData = doubleChart.isDatasetVisible(value);

    // console.log(lineData);
    if (barData === true) {
        doubleChart.hide(value);
    } else {
        doubleChart.show(value);
    }
}
</script>
@endsection