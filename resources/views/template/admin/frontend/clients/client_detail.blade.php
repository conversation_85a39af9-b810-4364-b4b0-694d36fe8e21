@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main pb-5">
    <h2 class="sub_heading">CLIENT DETAIL</h2>

    <div class="panel mt-4">
        <div class="panel_header">
            <h2 class="panel_title">Client Information</h2>
        </div>

        <div class="row ">

            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Client Name</label>
                    <p class="text">Micheal James</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Title</label>
                    <p class="text">Lorem Ipsm</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Company Name</label>
                    <p class="text">Creator.io</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Email</label>
                    <p class="text"><EMAIL></p>
                    <p class="text"><EMAIL> <span>(Alternate)</span></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Phone Number</label>
                    <p class="text">(*************</p>
                    <p class="text">(************* <span>(Alternate)</span></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                <div class="detail_info">
                    <label for="" class="label">Office Number</label>
                    <p class="text">(*************</p>
                </div>
            </div>

        </div>
    </div>

    <div class="row">
        <div class="col-lg-6">
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Address</h2>
                </div>

                <div class="row">
                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Address 1</label>
                            <p class="text">21st Avenue, St 42</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Address 2</label>
                            <p class="text">21st Avenue, St 42</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">State</label>
                            <p class="text">New York</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">New York</label>
                            <p class="text">Hempstead town</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Zip Code</label>
                            <p class="text">1234</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="col-lg-6">
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Billing Information</h2>
                </div>

                <div class="row">
                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Address 1</label>
                            <p class="text">21st Avenue, St 42</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Address 2</label>
                            <p class="text">21st Avenue, St 42</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">State</label>
                            <p class="text">New York</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">New York</label>
                            <p class="text">Hempstead town</p>
                        </div>
                    </div>

                    <div class="col-md-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Zip Code</label>
                            <p class="text">1234</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="table_filter_header mt-4">
        <h2 class="sub_heading">Estimates History</h2>

        <div class="filters">
            <input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
            <select name="" id="" class="select-small basic-single-select" style="width:84px;">
                <option value="" selected>Filter</option>
                <option value="Completed">Completed</option>
                <option value="In Progress">In Progress</option>
                <option value="Lost">Lost</option>
            </select>
        </div>
    </div>

    <div class="table-responsive  mt-4">
        <table class="table table-striped custom_datatable w-100">
            <thead>
                <tr>
                    <th>Salesman Name</th>
                    <th>Estimator Name</th>
                    <th>Quantity</th>
                    <th>Total Price</th>
                    <th>Total Cost</th>
                    <th>Note</th>
                    <th>Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>15</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Start Estimate</a></li>
                            <li><a class="dropdown-item" href="#">Edit Detail</a></li>
                            <li><a class="dropdown-item" href="#">View Details</a></li>
                        </ul>
                    </div>
                    </td>
                </tr>

                <tr>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>15</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Start Estimate</a></li>
                            <li><a class="dropdown-item" href="#">Edit Detail</a></li>
                            <li><a class="dropdown-item" href="#">View Details</a></li>
                        </ul>
                    </div>
                    </td>
                </tr>

                <tr>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>15</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Start Estimate</a></li>
                            <li><a class="dropdown-item" href="#">Edit Detail</a></li>
                            <li><a class="dropdown-item" href="#">View Details</a></li>
                        </ul>
                    </div>
                    </td>
                </tr>

                <tr>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>15</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines success"></i></td>
                    <td>
                        <!-- <div class="status warning">In Progress</div> -->
                        <div class="status success">Completed</div>
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Start Estimate</a></li>
                            <li><a class="dropdown-item" href="#">Edit Detail</a></li>
                            <li><a class="dropdown-item" href="#">View Details</a></li>
                        </ul>
                    </div>
                    </td>
                </tr>

                <tr>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>15</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines success"></i></td>
                    <td>
                        <!-- <div class="status warning">In Progress</div> -->
                        <div class="status success">Completed</div>
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Start Estimate</a></li>
                            <li><a class="dropdown-item" href="#">Edit Detail</a></li>
                            <li><a class="dropdown-item" href="#">View Details</a></li>
                        </ul>
                    </div>
                    </td>
                </tr>
                

        </table>
    </div>

</section>
@endsection