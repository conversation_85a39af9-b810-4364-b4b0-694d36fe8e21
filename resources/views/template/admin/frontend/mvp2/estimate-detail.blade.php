@extends('template.admin.frontend.mvp2.layouts.default')
@section('section')
<section class="dashboard_main pb-5">

    <div class="table_filter_header mb-4">
        <div class="header-left-content">
            <h2 class="sub_heading">Estimate list</h2>
            <a href="#" class="view-request-btn">View esstimate request</a>
        </div>

        <div class="header-buttpns-main">
            <button type="button" class="header-btn request-change" data-toggle="modal" data-target="#requestChangeModal">Request Changes</button>
            <button type="button" class="header-btn reject-btn" data-toggle="modal" data-target="#rejectModal">Reject</button>
            <button type="button" class="header-btn approve-btn" data-toggle="modal" data-target="#approveModal">Approve</button>
            
            <div class="dropdown">
                <button type="button" class="header-btn action-btn" data-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="5" height="16" viewBox="0 0 5 16" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 13.8865C4.5 14.9657 3.6 15.8486 2.5 15.8486C1.4 15.8486 0.5 14.9657 0.5 13.8865C0.5 12.8073 1.4 11.9243 2.5 11.9243C3.6 11.9243 4.5 12.8073 4.5 13.8865ZM4.5 2.11358C4.5 3.19277 3.6 4.07575 2.5 4.07575C1.4 4.07575 0.5 3.19277 0.5 2.11358C0.5 1.0344 1.4 0.151423 2.5 0.151423C3.6 0.151423 4.5 1.0344 4.5 2.11358ZM4.5 8.00007C4.5 9.07926 3.6 9.96223 2.5 9.96223C1.4 9.96223 0.5 9.07926 0.5 8.00007C0.5 6.92088 1.4 6.03791 2.5 6.03791C3.6 6.03791 4.5 6.92088 4.5 8.00007Z" fill="#84818A"/>
                    </svg>
                </button>
                <ul class="dropdown-menu download-estimates">
                    <li><a class="dropdown-item" data-toggle="modal" data-target="#deleteModal">Cancel</a></li>
                    <li><a class="dropdown-item" href="#">Download Estimate</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="estimate-detail-box">
        <div class="estimate-top-header">
            <div class="header-left">
                <span class="status-text">Status</span>
                <span class="status open">Open</span>
            </div>
            <div class="header-right">
                <span class="estimate-id color-oof-white">Estimate ID:</span>
                <span class="estimate-id">10269502</span>
            </div>
        </div>
        <div class="table-responsive">
            <table class="detailed-table">
                <thead>
                    <tr>
                        <th>Request  Name</th>
                        <th>Property Name</th>
                        <th>Method of communication</th>
                        <th>Received At </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <h2 class="td-title">Door repair property</h2>
                            <p class="td-text">In this line will be have a description<br> of the project. This view..</p>
                        </td>
                        <td>
                            <h2 class="td-title">Markes property </h2>
                            <p class="td-text">21st Avenue, St 42, Hempstead<br> town New York ,1234</p>
                        </td>
                        <td>
                            <span>Phone Call</span>
                        </td>
                        <td>
                            <span>12 May 2020</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="table-wraper">
        <div class="table-responsive">
            <table class="table table-striped custom_datatable display view-detialed-table" style="width:100%">
                <thead>
                    <tr>
                        <th>Item / Description</th>
                        <th>UoM</th>
                        <th>Quantity</th>
                        <th>Unit Cost</th>
                        <th>Total Price</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Hours</td>
                        <td>12</td>
                        <td>$34</td>
                        <td>$234</td>
                    </tr>

                    <tr>
                        <td>Skid Steer Brickman, per hr</td>
                        <td>Week</td>
                        <td>02</td>
                        <td>$20</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Achillea m</td>
                        <td>Hours</td>
                        <td>08</td>
                        <td>$20</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Chainsaw</td>
                        <td>Each</td>
                        <td>02</td>
                        <td>$75</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Chainsaw</td>
                        <td>Each</td>
                        <td>08</td>
                        <td>$50</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Supervision</td>
                        <td>Week</td>
                        <td>02</td>
                        <td>$20</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Technology</td>
                        <td>Pallet</td>
                        <td>08</td>
                        <td>$500</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Technology</td>
                        <td>Pallet</td>
                        <td>02</td>
                        <td>$345</td>
                        <td>$200</td>
                    </tr>

                    <tr>
                        <td>Micheal James</td>
                        <td>Hours</td>
                        <td>08</td>
                        <td>$234</td>
                        <td>$200</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</section>

<!-- Remove modal -->

<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered delete-modal">
      <div class="modal-content">
        <div class="modal-body">
            {{-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> --}}
            <img src="{{asset('admin_assets/images/icons/delete-icon.svg')}}" alt="delete icon" class="delete-icon">
            <h2 class="delete-request">Want to remove</h2>
            <p class="are-sure">Are you sure you want to Remove this request?</p>
            <div class="buttons-wraper">
                <button type="button" class="cancel-btn" data-dismiss="modal">Cancel</button>
                <button type="button" class="conform-btn">Yes</button>
            </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Change Request Modal -->
  <div class="modal fade propert-modal" id="requestChangeModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="requestChangeModalLabel">Change Estimate Request</h1>
          <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

            <div class="body-wraped">
                <div class="filed-wraper">
                    <label for="price" class="modal-label">Your price</label>
                    <input class="modal-field" type="text" id="price" name="change_price" placeholder="Enter you price">
                  </div>
        
                  <div class="filed-wraper">
                    <label for="address" class="modal-label">Enter reason for changes</label>
                    <textarea class="modal-textarea" name="reason_for_change" id="reasonChange" placeholder="Enter you reason"></textarea>
                  </div>
        
                <button type="submit" class="add-btn">Submitted</button>
            </div>
        </div>

      </div>
    </div>
  </div>


  <!-- Reject Modal -->
  <div class="modal fade propert-modal" id="rejectModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="rejectModalLabel">Reject Estimate</h1>
          <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

            <div class="body-wraped">
                <div class="filed-wraper">
                    <label for="name" class="reason">Reason</label>
                    <p class="rejection-detail">Before you reject, Please let us know the reason job was lost.</p>
                  </div>

                  <div class="radio-btns-wraper">
                    <div class="rejection-field-main">
                        <input class="rejection-field" type="radio" id="pricing" name="rejection_reason" checked>
                        <span class="radio-btn"></span>
                        <label for="pricing" class="rejection-label">Price</label>
                    </div>

                    <div class="rejection-field-main">
                        <input class="rejection-field" type="radio" id="timing" name="rejection_reason">
                        <span class="radio-btn"></span>
                        <label for="timing" class="rejection-label">Timing</label>
                    </div>

                    <div class="rejection-field-main">
                        <input class="rejection-field" type="radio" id="budget" name="rejection_reason">
                        <span class="radio-btn"></span>
                        <label for="budget" class="rejection-label">Budget</label>
                    </div>

                    <div class="rejection-field-main">
                        <input class="rejection-field" type="radio" id="other" name="rejection_reason">
                        <span class="radio-btn"></span>
                        <label for="other" class="rejection-label">Other <small style="color: #90A0B7;">(Please explain below)</small></label>
                    </div>
                  </div>

                  <div class="filed-wraper">
                    <textarea class="modal-textarea" name="reason_for_change" id="reasonChange" style="margin-top: 15px;min-height: 127px;"></textarea>
                  </div>
    
                <button type="submit" class="add-btn" style="margin-top: 16px;">Reject Estimate </button>
            </div>


        </div>
      </div>
    </div>
  </div>


  <!-- Approve Modal -->
  <div class="modal fade propert-modal" id="approveModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="approveModalLabel">Approved Estimate</h1>
          <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

            <div class="body-wraped">
                <div class="filed-wraper">
                    <label for="name" class="modal-label">Type signature</label>
                    <input class="modal-field" type="text" id="name" name="property_name" placeholder="ZAPTA /">
                  </div>
                  <div class="signature-box">
                    <span class="signature">ZAPTA</span>
                  </div>
                  <p class="rejection-detail" style="font-size: 12px;">By writing your signature, you accept this estimate, associated costs, and any terms and conditions that may apply.</p>
                <button type="submit" class="add-btn" style="margin-top: 23px;">Approved</button>
            </div>


        </div>
      </div>
    </div>
  </div>
@endsection