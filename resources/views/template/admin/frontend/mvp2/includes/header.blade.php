<!DOCTYPE html>
<html lang="en">

<head>

    <!----------------- Meta Tags ------------------>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="Description" CONTENT="{{ config('app.name') }} Dashboard">

    <!---------------------- Meta Tages End -------------------->

    <!---------------------- Bootstrap Cdn---------------------->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
        integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!------------------------ Bootstrap Cdn Ends-------------------->

    <!----------------------------Select2---------------------------->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!----------------------------Select2---------------------------->

    <!------------------------ Datatables-------------------->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css" />

    <!------------------------ Datatables-------------------->

    <link href="https://cdn.jsdelivr.net/npm/smartwizard@6/dist/css/smart_wizard_all.min.css" rel="stylesheet"
        type="text/css" />

    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/n-style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/globalStyles.css') }}" />

    @yield("css")

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom Css Ends-->

    <title>{{ config('app.name') }} Dashboard</title>

</head>

<body>


    <div class="dashboard_wrapper">
        <div class="sidebar_wrapper">
            <aside class="side_bar">
                <a href="#" class="side_bar_logo">
                    <img width="147" height="53"
                        src="{{ asset('admin_assets/images/icons/construct-logo.svg') }}" alt="elmos-logo-sidebar">
                </a>

                <div class="side_bar_menu">
                    <ul class="menu_links">
                        <li class="menu_item {{ (request()->is('frontend/mvp2/dashboard')) ? 'active' : '' }}">
                            <a href="{{ url('frontend/mvp2/dashboard') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M11.1112 17.8332C11.1112 18.2748 11.2987 18.6991 11.632 19.0115C11.9653 19.324 12.4179 19.4998 12.8889 19.4998H18.2221C18.6932 19.4998 19.1458 19.324 19.479 19.0115C19.8123 18.6991 19.9999 18.2748 19.9999 17.8332V14.4998C19.9999 14.0582 19.8123 13.6339 19.479 13.3215C19.1458 13.009 18.6932 12.8332 18.2221 12.8332H12.8889C12.4179 12.8332 11.9653 13.0091 11.632 13.3215C11.2987 13.6339 11.1112 14.0582 11.1112 14.4998V17.8332ZM7.55556 12.8334H5.77776C5.30652 12.8334 4.8541 13.0092 4.52084 13.3216C4.18738 13.6341 4 14.0584 4 14.5V17.8334C4 18.275 4.18741 18.6993 4.52084 19.0117C4.85412 19.3242 5.30652 19.5 5.77776 19.5H7.55556C8.02662 19.5 8.47904 19.3241 8.81248 19.0117C9.14576 18.6993 9.33332 18.275 9.33332 17.8334V14.5C9.33332 14.0584 9.14574 13.6341 8.81248 13.3216C8.47902 13.0092 8.02662 12.8334 7.55556 12.8334ZM20 6.16664C20 5.72485 19.8124 5.30072 19.4791 4.98828C19.1458 4.67567 18.6933 4.5 18.2222 4.5H5.7778C5.30656 4.5 4.85415 4.67569 4.52089 4.98828C4.18743 5.30073 4.00005 5.72485 4.00005 6.16664V9.5C4.00005 9.94162 4.18745 10.3658 4.52089 10.6784C4.85417 10.9908 5.30656 11.1666 5.7778 11.1666H18.2222C19.2045 11.1666 20 10.4208 20 9.49996L20 6.16664Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Dashboard
                            </a>
                        </li>

                        <li class="menu_item {{ (request()->is('frontend/mvp2/request*')) ? 'active' : '' }} ">
                            <a href="{{ url('frontend/mvp2/requests') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M5.24507 8.3872C5.40847 9.02991 5.83331 9.09527 5.83331 9.09527C6.08386 10.6639 7.3366 11.884 7.75054 12.1672C8.16449 12.4613 8.7963 12.4068 8.7963 12.4068C9.30829 12.4068 9.53705 12.4068 10.3541 11.7315C11.1711 11.0561 11.7811 9.11706 11.7811 9.11706C12.3911 9.17152 12.6417 8.11487 12.6417 7.63556C12.6417 7.16715 12.3149 7.10179 12.3149 7.10179C12.3149 7.10179 12.4674 6.29568 12.3149 5.2717C12.1624 4.24773 11.3018 3.11482 9.47169 2.78802C7.6525 2.46122 6.68299 3.45251 6.68299 3.45251C6.68299 3.45251 6.07297 3.44162 5.49562 4.31309C4.91827 5.18456 5.40847 7.16715 5.40847 7.16715C5.02721 7.35233 5.07078 7.74449 5.24507 8.3872Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M13.6888 9.10611C13.8087 9.58542 14.1355 9.629 14.1355 9.629C14.3206 10.7946 15.2575 11.7096 15.5625 11.9166C15.8675 12.1345 16.3468 12.0909 16.3468 12.0909C16.7281 12.0909 16.9024 12.0909 17.5015 11.5898C18.1115 11.0887 18.5691 9.63989 18.5691 9.63989C19.0266 9.68346 19.2118 8.88825 19.2118 8.53966C19.2118 8.19107 18.9721 8.13661 18.9721 8.13661C18.9721 8.13661 19.0919 7.53747 18.9721 6.77494C18.8523 6.0124 18.2096 5.16272 16.8588 4.92307C15.4971 4.68341 14.7782 5.41327 14.7782 5.41327C14.7782 5.41327 14.3206 5.40238 13.8958 6.05598C13.471 6.70958 13.8196 8.19107 13.8196 8.19107C13.5254 8.34358 13.5581 8.6377 13.6888 9.10611Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M20.6601 12.8861C20.3551 12.5485 19.4074 12.5702 19.037 12.494C18.6775 12.4177 18.0566 11.775 18.0566 11.775C17.8388 12.6029 16.3899 12.9297 16.3899 12.9297C16.3899 12.9297 14.9411 12.6029 14.7233 11.775C14.7233 11.775 14.1023 12.4068 13.7429 12.494C13.4052 12.5702 12.5555 12.5593 12.196 12.8317C11.7167 12.5811 11.1176 11.9711 11.1176 11.9711C10.8234 13.0822 8.88442 13.518 8.88442 13.518C8.88442 13.518 6.94541 13.0822 6.65129 11.9711C6.65129 11.9711 5.82339 12.8208 5.33319 12.9297C4.84299 13.0387 3.56847 13.0169 3.16542 13.4635C2.76236 13.9101 1.80375 16.3829 1.37891 19.0845C1.37891 21.067 16.4008 21.067 16.4008 19.0845C16.3573 18.7903 16.3028 18.4853 16.2374 18.2021C19.0915 18.213 22 17.8426 22 17.1019C21.6841 15.0648 20.9651 13.2238 20.6601 12.8861Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Requests
                            </a>
                        </li>
                    </ul>

                    <a href="#" class="side_bar_logo border-0 text-center" style="position: absolute;bottom: 20px;">
                        <img width="147" height="53"
                            src="{{ asset('admin_assets/images/icons/elemos-white.svg') }}" alt="elmos-logo-sidebar">
                    </a>
                </div>



            </aside>
        </div>
        <div class="content_wrapper">
            <header class="dashboard_header">
                <h2 class="dashboard_header_title">Home</h2>

                <div class="header_items">
                    <a href="#" class="notifications-link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M10 5C10 4.46957 10.2107 3.96086 10.5858 3.58579C10.9609 3.21071 11.4696 3 12 3C12.5304 3 13.0391 3.21071 13.4142 3.58579C13.7893 3.96086 14 4.46957 14 5C15.1484 5.54303 16.1274 6.38833 16.8321 7.4453C17.5367 8.50227 17.9404 9.73107 18 11V14C18.0753 14.6217 18.2954 15.2171 18.6428 15.7381C18.9902 16.2592 19.4551 16.6914 20 17H4C4.54494 16.6914 5.00981 16.2592 5.35719 15.7381C5.70457 15.2171 5.92474 14.6217 6 14V11C6.05956 9.73107 6.4633 8.50227 7.16795 7.4453C7.8726 6.38833 8.85159 5.54303 10 5" stroke="#192A3E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M9 17V18C9 18.7956 9.31607 19.5587 9.87868 20.1213C10.4413 20.6839 11.2044 21 12 21C12.7956 21 13.5587 20.6839 14.1213 20.1213C14.6839 19.5587 15 18.7956 15 18V17" stroke="#192A3E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="notification-status active"></span>
                    </a>

                    <div class="profile_info_dropdown">
                        <div class="profile_info dropdown-toggle arrow" type="button" id="userDropdown"
                            data-toggle="dropdown" aria-expanded="false">

                            <div class="image">
                                <img class="rounded" height="38px" width="38px"
                                    src="{{ asset('admin_assets/images/users/user1.png') }}" alt="user image">
                            </div>
                            <div class="info">
                                <h2 class="welcome_status">Welcome back</h2>
                                <h3 class="profile_name">{{ auth()->user()->name ?? '' }}</h3>
                            </div>

                        </div>


                        <ul class="dropdown-menu" aria-labelledby="userDropdown">
                            @auth
                                <li><a class="dropdown-item" href="{{ route('logout') }}"
                                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST"
                                        style="display: none;">
                                        @csrf
                                    </form>
                                </li>
                            @endauth
                        </ul>


                    </div>

                    <i class="aside_toggle fa fa-bars"></i>

                </div>
            </header>
            @include('layouts.partials.flash-messages')
