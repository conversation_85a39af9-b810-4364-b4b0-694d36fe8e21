<!-- Button trigger modal -->
<!-- <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#successModal">
  Success Modal
</button> -->

<!-- Modal -->
<div class="modal-small success-modal modal fade" id="successModal" data-keyboard="false" tabindex="-1"
    aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-body">
                <div class="text-center mb-4">
                    <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                        alt="check icon">
                </div>

                <h2 class="title text-center">Email Verified</h2>
                <p class="para mt-3 text-center">Your mail account has been successfully verified.</p>
                <button type="button" class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Continue</button>
            </div>

        </div>
    </div>
</div>


<div class="modal-small success-modal modal fade" id="passwordModal" data-keyboard="false" tabindex="-1"
    aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-body">
                <div class="text-center mb-4">
                    <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                        alt="check icon">
                </div>

                <h2 class="title text-center">Password Changed</h2>
                <p class="para mt-3 text-center">Your password has been successfully changed.</p>
                <a href="{{ route('login') }}" class="btn primaryblue w-100 mt-5 ">Go to
                    login</a>
            </div>

        </div>
    </div>
</div>





<!--------------------------------- Jquery --------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
    integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-------------------------------- Jquery Ends------------------------------->

<!--------------------------------- Popper Js ------------------------------->
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
    integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
</script>
<!--------------------------------- Popper Js Ends----------------------------->

<!--------------------------------- Bootstrap 5 cdn ---------------------------->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js" 
    integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+" 
    crossorigin="anonymous"></script>
<!------------------------------------ Bootstrap 5 cdn Ends--------------------->

<!----------------------------Date Picker Cdn Ends---------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<!----------------------------Date Picker Cdn Ends---------------------------->

<!----------------------------Jquery-Validate---------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.2/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/additional-methods.min.js"
    integrity="sha512-6S5LYNn3ZJCIm0f9L6BCerqFlQ4f5MwNKq+EthDXabtaJvg3TuFLhpno9pcm+5Ynm6jdA9xfpQoMz2fcjVMk9g=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!----------------------------Jquery-Validate---------------------------->

<!------------------------------- Custom Js ---------------------------------->
<script type="text/javascript" src="{{ asset('admin_assets/scripts/script.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin_assets/scripts/validations.js') }}"></script>
<!------------------------------ Custom Js Ends --------------------->
@stack('scripts')
</body>

</html>
