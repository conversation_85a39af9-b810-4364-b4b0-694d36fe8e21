@extends('template.admin.frontend.mvp2.layouts.default')
@section('section')
<section class="dashboard_main pb-5">

<div class="request-wraper">
    <div class="request-details-box item-1">
        <div class="request-detail-header px-24">
            <h2 class="request-title">Estimate Request</h2>
            <p class="estimation-request">Here you can enter details about yor estimate</p>
        </div>

        <hr class="border-line-hr">
        <div class="title-field-wraper px-24">
            <label for="requsetTitle" class="title-label">Estimate Name <span style="color: red;">*</span></label>
            <input type="text" class="title-field" id="requsetTitle" name="requset_title" placeholder="Enter estimate name">
        </div>

        <label for="propertyDetails" class="title-label d-block px-24 mt-4">Property Details <span style="color: red;">*</span></label>
        <div class="property-container-main px-24 mt-3">
            <div class="propert-container">
                <h2 class="marks-property">Marks Property door Pepair</h2>
                <p class="property-address">21st Avenue, St 42, Hempstead town New York ,1234</p>
                <p class="country-and-address">
                    <span class="propert-country">City name: <span>Uk</span></span>
                    <span class="propert-country">Country: <span>United kingdom</span></span>
                    <span class="propert-country">Zapcode: <span>SH26378</span></span>
                </p>
            </div>
        </div>

        <div class="px-24">
            <button class="add-new-propert" type="button" data-toggle="modal" data-target="#propertyModal">+ Add new Address</button>
        </div>

        <div class="px-24">
            <label for="" class="reqest-text-label d-block">Request details</label>
            <textarea name="describe_project" id="describeProject" class="describe-project mt-3 d-block" placeholder="Please describe your project details here."></textarea>
        </div>

        <div class="px-24 mt-4">
            <label for="" class="reqest-text-label d-block">How would you like to us to contact you</label>
            <div class="check-fileds-wraper">
                <div class="check-box-wraper">
                    <input type="radio" id="email" name="contact_source">
                    <label for="email" class="radio-label"></label>
                    <label for="email" class="checkLabel">Email</label>
                </div>

                <div class="check-box-wraper">
                    <input type="radio" id="text" name="contact_source">
                    <label for="text" class="radio-label"></label>
                    <label for="text" class="checkLabel">Text</label>
                </div>

                <div class="check-box-wraper">
                    <input type="radio" id="phoneall" name="contact_source" checked>
                    <label for="phoneall" class="radio-label"></label>
                    <label for="phoneall" class="checkLabel">Phone Call</label>
                </div>
            </div>
        </div>
        <div class="px-24 mt-5">
            <div class="dropzone_library_customize needsclick dropzone" id="document-dropzone"></div>
        </div>
    </div>

    <div class="create-estimation-btns">
        <button type="button" class="estimation-btn cancel-estimation">Cancel</button>
        <button type="button" class="estimation-btn create-estimation" id="customValidationBtn">Create Estimate</button>
    </div>
    <div class="request-user-detail item-2">
        <div class="profile-header px-24">
            <h2 lass="header-title">Your Photo</h2>
        </div>
        <div class="upload-image-wraper px-24">
            <div class="image-box">
                <img class="dynamic-image" width="104" height="104" src="{{asset('admin_assets/images/icons/user-profile-icon.svg')}}" alt="user image">
            </div>
            <div class="user-profile-detail">
                <h2 class="edit-photo">Edit your photo</h2>
                <div class="upload-field">
                    <input type="file" id="update" name="profile_image">
                    <label for="update-image" class="update-photo">Update</label>
                </div>
                <p class="image-extensions">PNG, JPG or Gif Max 20Mb</p>
            </div>
        </div>
    </div>

    <div class="request-user-detail item-3">
        <div class="profile-header px-24">
            <h2 lass="header-title">Your Photo</h2>
            <button type="button" class="edit-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M12.4617 7.00281L14.9029 9.45586L8.72395 15.6644L6.28414 13.2123L12.4617 7.00281ZM16.7552 6.41099L15.6665 5.31713C15.5665 5.21662 15.4476 5.13686 15.3167 5.08244C15.1857 5.02802 15.0453 5 14.9035 5C14.7617 5 14.6213 5.02802 14.4904 5.08244C14.3594 5.13686 14.2405 5.21662 14.1405 5.31713L13.0978 6.36505L15.5388 8.8181L16.7552 7.5958C16.912 7.43851 17 7.22548 17 7.00339C17 6.78131 16.912 6.56828 16.7552 6.41099ZM5.00682 16.6603C4.99642 16.7064 4.99794 16.7544 5.01122 16.7998C5.0245 16.8452 5.04911 16.8864 5.08273 16.9196C5.11635 16.9528 5.15788 16.977 5.20339 16.9897C5.24891 17.0024 5.29692 17.0034 5.34291 16.9924L8.06303 16.3296L5.62322 13.8765L5.00682 16.6603Z" fill="#868686"/>
                </svg>
                <span>Edit</span>
            </button>
        </div>
        <div class="profile-detail-body px-24">
            <div class="detailed-data">
                <h2 class="detail-title">Contact person Name</h2>
                <p class="detailed-description">Micheal James</p>
            </div>

            <div class="detailed-data">
                <h2 class="detail-title">Company Name</h2>
                <p class="detailed-description">Creator.io</p>
            </div>

            <div class="detailed-data">
                <h2 class="detail-title">Email</h2>
                <p class="detailed-description"><EMAIL></p>
                <p class="detailed-description"><EMAIL><br><small>(Alternate)</small></p>
            </div>

            <div class="detailed-data">
                <h2 class="detail-title">Phone Number</h2>
                <p class="detailed-description">(*************</p>
                <p class="detailed-description">(*************<br><small>(Alternate)</small></p>
            </div>
        </div>
    </div>
</div>

<div class="my-alert d-flex align-items-center justify-content-between">
    <div class="d-flex align-items-center gap-3">
        <button type="button" class="red-alert">
            <img width="28" height="28" src="{{asset('admin_assets/images/icons/red-alert.svg')}}" alt="close icon">
        </button>
        <p class="alert-text">Password and Username do not match</p>
    </div>
    <button class="alert-close">
        <img width="18" height="18" src="{{asset('admin_assets/images/icons/alert-close.svg')}}" alt="close alert">
    </button>
</div>



<!-- Button trigger modal -->
{{-- <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#propertyModal">
    Launch static backdrop modal
  </button>
   --}}
  <!-- Property Detail Modal -->
  <div class="modal fade propert-modal" id="propertyModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="propertyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="propertyModalLabel">Modal title</h1>
          <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">

            <div class="body-wraped" style="gap: 20px;">
                <div class="filed-wraper">
                    <label for="name" class="modal-label">Property name</label>
                    <input class="modal-field" type="text" id="propertyName" name="property_name" placeholder="Enter name">
                  </div>
        
                  <div class="filed-wraper">
                    <label for="address" class="modal-label">Address <span style="color:red;">*</span></label>
                    <input class="modal-field" type="text" id="propertyAddress" name="property_address" placeholder="Enter contact Name">
                  </div>
        
                  <div class="filed-wraper">
                    <label for="address2" class="modal-label">Address</label>
                    <input class="modal-field" type="text" id="propertyAddress2" name="property_address" placeholder="Enter contact Name">
                  </div>
        
                  <div class="modal-flex">
                    <div class="filed-wraper">
                        <label for="address2" class="modal-label">City<span style="color: red;">*</span></label>
                        <input class="modal-field" type="text" id="clientCIty" name="property_address" placeholder="Enter contact Name">
                    </div>
        
                    <div class="filed-wraper">
                        <label for="address2" class="modal-label">State<span style="color: red;">*</span></label>
                        <input class="modal-field" type="text" id="lientState" name="property_address" placeholder="Enter contact Name">
                      </div>
                   </div>
        
                    <div class="filed-wraper">
                        <label for="zipcode" class="modal-label">Zip code</label>
                        <input class="modal-field" type="number" id="clientZipcode" name="zip_code" placeholder="Enter Zip Code">
                    </div>
        
                    <button type="button" class="add-btn">Add</button>
            </div>


        </div>
      </div>
    </div>
  </div>
  @endsection
@push('js')

<!-- Place this script after including jQuery and Bootstrap -->
<script>
    $(document).ready(function() {
        // Function to check if the input value is empty
        function isEmpty(inputValue) {
            return $.trim(inputValue) === '';
        }
    
        // Function to validate zip code as a 5-digit number
        function isValidZipCode(zipCode) {
            return /^\d{5}$/.test(zipCode);
        }

        // function isValidZipCode(zipCode) {
        //     return /^(?=.*\d)(?=.*[a-zA-Z])[a-zA-Z0-9-_]{5,10}$/.test(zipCode);
        // }



    
        // Function to display error messages
        function showError(inputId, message) {
            $("#" + inputId).next(".error-message").remove(); // Remove any existing error message
            $("#" + inputId).after('<span class="error-message" style="color: red;">' + message + '</span>');
        }
    
        // Function to remove error messages
        function removeError(inputId) {
            $("#" + inputId).next(".error-message").remove();
        }
    
        // Validate the form on button click and set up keyup validation
        $(".add-btn").on("click", function() {
            if (isFormValid()) {
                // Close the modal if the form is valid using Bootstrap modal('hide') method
                $("#propertyModal").modal('hide');
            } else {
                setupKeyupValidation();
            }
        });
    
        // Function to validate the form
        function isFormValid() {
            // Reset previous error messages
            $(".error-message").remove();
    
            var propertyName = $("#propertyName").val();
            var address1 = $("#propertyAddress").val();
            var address2 = $("#propertyAddress2").val();
            var city = $("#clientCIty").val();
            var state = $("#lientState").val();
            var zipCode = $("#clientZipcode").val();
    
            // Validate Property Name
            if (isEmpty(propertyName)) {
                showError("propertyName", "Property name cannot be empty.");
            }
    
            // Validate Address (at least one address should be filled)
            if (isEmpty(address1) && isEmpty(address2)) {
                showError("propertyAddress", "Please enter at least one address.");
                showError("propertyAddress2", ""); // Show error message for the second address field too
            }
    
            // Validate City
            if (isEmpty(city)) {
                showError("clientCIty", "City cannot be empty.");
            }
    
            // Validate State
            if (isEmpty(state)) {
                showError("lientState", "State cannot be empty.");
            }
    
            // Validate Zip Code
            if (!isValidZipCode(zipCode)) {
                showError("clientZipcode", "Please enter a valid 5-digit zip code.");
            }
    
            // Check if there are any error messages
            return $(".error-message").length === 0;
        }
    
        // Function to set up keyup validation for each field
        function setupKeyupValidation() {
            $("#propertyName, #propertyAddress, #propertyAddress2, #clientCIty, #lientState, #clientZipcode").on("keyup", function() {
                var inputId = $(this).attr("id");
                removeError(inputId);
                isFormValid();
            });
        }
    

        function isFormValid2() {
            // Reset previous error messages
            $(".error-message").remove();
    
            var requsetTitle = $("#requsetTitle").val();
            // var address1 = $("#propertyAddress").val();
            // var address2 = $("#propertyAddress2").val();
    
            // Validate Property Name
            if (isEmpty(requsetTitle)) {
                showError("requsetTitle", "Property name cannot be empty.");
            }
    
            // Check if there are any error messages
            return $(".error-message").length === 0;
        }
        // Bind click event to the customValidationBtn
        $("#customValidationBtn").on("click", function() {
            // Check if the form is valid
            if (isFormValid2()) {
                // Close the modal if the form is valid using Bootstrap modal('hide') method
                $("#propertyModal").modal('hide');
            } else {
                // Show errors and open the modal if there are validation errors
                setupKeyupValidation();
                $("#propertyModal").modal('show');
            }
        });
    });
    </script>
    
    
    
    
    
    
@endpush

