@extends('layouts.admin.master')
@section('section')
    <section class="dashboard_main pb-5">

        <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="company-tab-btn" data-toggle="pill" data-target="#company-created"
                    type="button" role="tab" aria-controls="company-created" aria-selected="true">Summary</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="client-created-tab" data-toggle="pill" data-target="#client-created"
                    type="button" role="tab" aria-controls="client-created" aria-selected="false">Cost Summery</button>
            </li>
        </ul>

        <div class="tab-content mt-4" id="jobSummary">
            <div class="tab-pane show active" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn"
                tabindex="0">
                <div class="risk-department mt-24">
                    <div class="bar-chart">
                        <div class="chart-header">
                            <h2 class="heat-map-heading">Actual Vs Target</h2>
                            <ul class="legends">
                                <li class="legend-list" onclick="toggleDataRisk(1)">
                                    <span class="legend-span" id="moderate2"></span>
                                    <span class="legend-button" id="two2"></span>
                                </li>
                                <li class="legend-list" onclick="toggleDataRisk(0)">
                                    <span class="legend-span" id="low2"></span>
                                    <span class="legend-button" id="one1"></span>
                                </li>



                                {{-- <li class="legend-list" onclick="toggleDataRisk(3)">
                                <span class="legend-span" id="high3"></span>
                                <span class="legend-button" id="three3"></span>
                            </li>
        
                            <li class="legend-list" onclick="toggleDataRisk(4)">
                                <span class="legend-span" id="extreme44"></span>
                                <span class="legend-button" id="four4"></span>
                            </li> --}}

                            </ul>
                            <div class="field">
                                <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                    <select name="due_date" id="chartyearSelector" class="input basic-single-select">
                                        <option value="disabled" selected disabled>Year</option>
                                        <option value="">option1</option>
                                        <option value="">option2</option>
                                        <option value="">option3</option>
                                    </select>
                                    <!-- Hidden input field for the datepicker to attach to -->
                                    <input type="text" id="customDateInput" value="" style="display: none;">
                                </div>
                            </div>
                        </div>
                        <div id="riskChart"></div>
                    </div>
                    <div class="dughnut-chart position-relative">
                        <div class="chart-header mb-3 d-flex justify-content-between align-items-center gap-2">
                            <h2 class="heat-map-heading">Target Status</h2>
                            <div class="field">
                                <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                    <select name="due_date" id="yearSelector" class="input basic-single-select">
                                        <option value="disabled" selected disabled>Year</option>
                                        <option value="">option1</option>
                                        <option value="">option2</option>
                                        <option value="">option3</option>
                                    </select>
                                    <!-- Hidden input field for the datepicker to attach to -->
                                    <input type="text" id="customDateInput" value="" style="display: none;">
                                </div>
                            </div>
                        </div>
                        <canvas id="dughnutChart" style="width: 100%;object-fit: contain;"></canvas>
                        <span class="dughnu-value" id="dughtnValue"></span>
                        <ul class="dughnut-legends d-flex flex-column">


                            <li class="dughnut-list" onclick="toggleDoughnut(0)">
                                <div class="dughnut-value">
                                    <span class="box" id="boxbg"></span>
                                    <span class="text" id="underView"></span>
                                </div>
                                <span class="percentage">35,5k</span>
                            </li>

                            <li class="dughnut-list" onclick="toggleDoughnut(1)">
                                <div class="dughnut-value">
                                    <span class="box" id="boxbg2"></span>
                                    <span class="text" id="pendings"></span>
                                </div>
                                <span class="percentage">10,5k </span>
                            </li>

                        </ul>
                    </div>
                </div>

                <div class="risk-department mt-5 w-100">
                    <div class="bar-chart px-0">
                        <div class="chart-header" style="padding: 0px 24px;">
                            <h2 class="heat-map-heading">Target Table</h2>

                            <div class="d-flex align-items-center">
                                <p class="backlog m-0" style="padding-right: 4px">Back log</p>
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                    viewBox="0 0 13 13" fill="none">
                                    <path
                                        d="M6.5 0C10.0841 0 13 2.91594 13 6.50004C13 10.0841 10.0841 13 6.5 13C2.9159 13 1.33365e-06 10.0841 1.33365e-06 6.50004C1.33365e-06 2.91594 2.9159 0 6.5 0ZM6.5 11.8182C9.43249 11.8182 11.8182 9.43248 11.8182 6.50004C11.8182 3.56759 9.43249 1.18182 6.5 1.18182C3.56752 1.18182 1.18182 3.56759 1.18182 6.50004C1.18182 9.43248 3.56756 11.8182 6.5 11.8182Z"
                                        fill="#94959B" />
                                    <path
                                        d="M6.50036 2.75781C6.93472 2.75781 7.28809 3.11141 7.28809 3.54605C7.28809 3.98029 6.93472 4.33357 6.50036 4.33357C6.06601 4.33357 5.71264 3.98029 5.71264 3.54605C5.71264 3.11141 6.06601 2.75781 6.50036 2.75781ZM6.50029 5.51539C6.82663 5.51539 7.09119 5.77996 7.09119 6.1063V9.65175C7.09119 9.97809 6.82663 10.2427 6.50029 10.2427C6.17395 10.2427 5.90938 9.97809 5.90938 9.65175V6.1063C5.90938 5.77996 6.17395 5.51539 6.50029 5.51539Z"
                                        fill="#94959B" />
                                </svg>
                                <p class="price m-0" style="padding-left: 9px;padding-right:25px">$53674</p>
                                <div class="field">
                                    <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                        <select name="due_date" id="yearSelect" class="input basic-single-select">
                                            <option value="disabled" selected disabled>Year</option>
                                            <option value="">option1</option>
                                            <option value="">option2</option>
                                            <option value="">option3</option>
                                        </select>
                                        <!-- Hidden input field for the datepicker to attach to -->
                                        <input type="text" id="customDateInput" value=""
                                            style="display: none;">
                                    </div>
                                </div>
                            </div>
                            <div id="riskChart"></div>
                        </div>
                        <div class="dughnut-chart position-relative">
                            <div class="chart-header mb-3 d-flex justify-content-between align-items-center gap-2">
                                <h2 class="heat-map-heading">Target Status</h2>
                                <div class="date-selector">
                                    <select name="date_selector" id="dateSelector">
                                        <option value="Year" selected>Year 2023</option>
                                        <option value="">Option2</option>
                                        <option value="">Option3</option>
                                        <option value="">Option4</option>
                                    </select>
                                </div>
                            </div>
                            <canvas id="dughnutChart" style="width: 100%;object-fit: contain;"></canvas>
                            <span class="dughnu-value" id="dughtnValue"></span>
                            <ul class="dughnut-legends d-flex flex-column">


                                <li class="dughnut-list" onclick="toggleDoughnut(0)">
                                    <div class="dughnut-value">
                                        <span class="box" id="boxbg"></span>
                                        <span class="text" id="underView"></span>
                                    </div>
                                    <span class="percentage">35,5k</span>
                                </li>

                                <li class="dughnut-list" onclick="toggleDoughnut(1)">
                                    <div class="dughnut-value">
                                        <span class="box" id="boxbg2"></span>
                                        <span class="text" id="pendings"></span>
                                    </div>
                                    <span class="percentage">10,5k </span>
                                </li>

                            </ul>
                        </div>
                    </div>

                    <div class="risk-department mt-5 w-100">
                        <div class="bar-chart px-0">
                            <div class="chart-header" style="padding: 0px 24px;">
                                <h2 class="heat-map-heading">Target Table</h2>

                                <div class="d-flex align-items-center">
                                    <p class="backlog m-0" style="padding-right: 4px">Back log</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                        viewBox="0 0 13 13" fill="none">
                                        <path
                                            d="M6.5 0C10.0841 0 13 2.91594 13 6.50004C13 10.0841 10.0841 13 6.5 13C2.9159 13 1.33365e-06 10.0841 1.33365e-06 6.50004C1.33365e-06 2.91594 2.9159 0 6.5 0ZM6.5 11.8182C9.43249 11.8182 11.8182 9.43248 11.8182 6.50004C11.8182 3.56759 9.43249 1.18182 6.5 1.18182C3.56752 1.18182 1.18182 3.56759 1.18182 6.50004C1.18182 9.43248 3.56756 11.8182 6.5 11.8182Z"
                                            fill="#94959B" />
                                        <path
                                            d="M6.50036 2.75781C6.93472 2.75781 7.28809 3.11141 7.28809 3.54605C7.28809 3.98029 6.93472 4.33357 6.50036 4.33357C6.06601 4.33357 5.71264 3.98029 5.71264 3.54605C5.71264 3.11141 6.06601 2.75781 6.50036 2.75781ZM6.50029 5.51539C6.82663 5.51539 7.09119 5.77996 7.09119 6.1063V9.65175C7.09119 9.97809 6.82663 10.2427 6.50029 10.2427C6.17395 10.2427 5.90938 9.97809 5.90938 9.65175V6.1063C5.90938 5.77996 6.17395 5.51539 6.50029 5.51539Z"
                                            fill="#94959B" />
                                    </svg>
                                    <p class="price m-0" style="padding-left: 9px;padding-right:25px">$53674</p>
                                    <div class="date-selector">
                                        <select name="date_selector" id="dateSelector">
                                            <option value="Year" selected>Year 2023</option>
                                            <option value="">Option2</option>
                                            <option value="">Option3</option>
                                            <option value="">Option4</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="table-responsive p-0 mt-3">
                                <table id="clients_Detail"
                                    class="table table-striped custom_datatable display job-summary text-center"
                                    style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Month</th>
                                            <th>Revenue</th>
                                            <th>Target</th>
                                            <th>Target %</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        <tr>
                                            <td>February</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>

                                        <tr>
                                            <td>March</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>January</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>April</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>May</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>June</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>July</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>August</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>September</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>October</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>


                                        <tr>
                                            <td>November</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>

                                        <tr>
                                            <td>December</td>
                                            <td>$4000</td>
                                            <td>$4000</td>
                                            <td>50%</td>
                                        </tr>

                                        <tr>
                                            <td><strong>Total</strong></td>
                                            <td><strong>$24566</strong></td>
                                            <td><strong>$6000</strong></td>
                                            <td><strong>25%</strong></td>
                                        </tr>



                                </table>
                            </div>
                        </div>

                        <div class="backog-container">
                            <div class="d-flex align-items-center gap-3">
                                <h2 class="backlog-heading">Back log</h2>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                    viewBox="0 0 18 18" fill="none">
                                    <path
                                        d="M9 0C13.9626 0 18 4.03745 18 9.00005C18 13.9627 13.9626 18 9 18C4.0374 18 2.41399e-06 13.9627 2.41399e-06 9.00005C2.41399e-06 4.03745 4.0374 0 9 0ZM9 16.3636C13.0604 16.3636 16.3636 13.0604 16.3636 9.00005C16.3636 4.93975 13.0604 1.63636 9 1.63636C4.93964 1.63636 1.63637 4.93975 1.63637 9.00005C1.63637 13.0604 4.93969 16.3636 9 16.3636Z"
                                        fill="#003366" />
                                    <path
                                        d="M9.00013 3.81836C9.60155 3.81836 10.0908 4.30796 10.0908 4.90976C10.0908 5.51101 9.60155 6.00018 9.00013 6.00018C8.39871 6.00018 7.90944 5.51101 7.90944 4.90976C7.90944 4.30796 8.39871 3.81836 9.00013 3.81836ZM9.00002 7.63654C9.45187 7.63654 9.8182 8.00287 9.8182 8.45472V13.3638C9.8182 13.8157 9.45187 14.182 9.00002 14.182C8.54817 14.182 8.18184 13.8157 8.18184 13.3638V8.45472C8.18184 8.00287 8.54817 7.63654 9.00002 7.63654Z"
                                        fill="#003366" />
                                </svg>
                            </div>
                            <p class="backlog-description mt-1">Work not yet scheduled.
                            </p>
                            <div class="d-flex align-items-center justify-content-between" style="margin-top: 22px">
                                <p class="backlog-description">unscheduled Job</p>
                                <p class="backlog-description p-0" style="min-width:20%;">Amount</p>
                            </div>

                            <div class="d-flex align-items-center justify-content-between mt-2" style="margin-top: 8px;">
                                <p class="backlog-project">4 Project</p>
                                <p class="backlog-project" style="min-width:20%;">$2674987</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="client-created" role="tabpanel" aria-labelledby="client-created-tab"
                    tabindex="0">
                    <div class="table-responsive horizontalScroll p-0 mt-5">
                        <table id="clients_Detail" class="table table-striped custom_datatable display field-table"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>Operation #</th>
                                    <th>Project Name</th>
                                    <th>Month Revenue Forecasted</th>
                                    <th>Scheduled Date</th>
                                    <th>Completed Date</th>
                                    <th>Contract Amount</th>
                                    <th>Labor Hours</th>
                                    <th>Labor Burden Dollars</th>
                                    <th>Equipment Cost</th>
                                    <th>Labor</th>
                                    <th>Hard Material</th>
                                    <th>Plant Material</th>
                                    <th>Other job cost</th>
                                    <th>Sub Contractor</th>
                                    <th class="bg-lightgreen">Hours</th>
                                    <th class="bg-lightgreen">Equipmet Cost2</th>
                                    <th class="bg-lightgreen">Labor3</th>
                                    <th class="bg-lightgreen">Hard Material4</th>
                                    <th class="bg-lightgreen">Plant Material</th>
                                    <th class="bg-lightgreen">Other Job Cost</th>
                                    <th class="bg-lightgreen">Sub Contractor</th>
                                    <th class="bg-lightgreen">Accurals Needed</th>
                                    <th>Forcasted GP$</th>
                                    <th>Forcasted GP%</th>
                                    <th>Sales Person</th>
                                    <th>Estimator</th>
                                    <th>Ops Manager</th>
                                    <th>Client Name</th>
                                    <th class="bg-lightgreen">Po #</th>
                                    <th class="bg-lightgreen">Work Type Type</th>
                                    <th class="bg-lightgreen">Service Line</th>
                                    <th>Note</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>

                                <tr>
                                    <td>233</td>
                                    <td>Door repair</td>
                                    <td>February</td>
                                    <td>02/02/2023</td>
                                    <td>24/02/2023</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td>$2300</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td>30%</td>
                                    <td>40%</td>
                                    <td>Person name</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td>name here</td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td><input type="text" name="table_field" placeholder="-------"
                                            class="table-field">
                                    </td>
                                    <td class="svg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z"
                                                fill="#E7E7E7" />
                                        </svg>
                                    </td>
                                </tr>



                        </table>
                    </div>
                </div>
            </div>




    </section>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://code.highcharts.com/highcharts.js"></script>
        <script src="https://code.highcharts.com/highcharts-more.js"></script>
        <script>
            //   $(document).ready(function () {
            // Risk By Department chart
            var chartRisk = Highcharts.chart('riskChart', {


                credits: {
                    enabled: false
                },
                legend: {
                    enabled: false
                },
                chart: {
                    type: 'column',
                    style: {
                        borderRadius: '6px',
                        background: '#FFFFFF',
                        boxShadow: '0px 4px 20px rgba(13, 10, 44, 0.08)',
                        padidng: '16px'
                    },
                    height: 344,
                    events: {
                        load: function() {
                            if (this.chartHeight < 300) {
                                this.setSize(undefined, 300);
                            }
                        }
                    }

                },
                title: {
                    enabled: false,
                    text: '',
                },
                xAxis: {
                    categories: ['Jan', 'Fab', 'Mar', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov',
                        'Dec'
                    ],

                    labels: {
                        style: {
                            fontFamily: 'Poppins',
                            fontStyle: 'normal',
                            fontSize: '14px',
                            lineHeight: '16px',
                            fontWeight: '400',
                            color: 'rgba(27, 35, 55, 0.8)',
                        }
                    },
                    min: 0,
                    max: 9,
                    title: {
                        text: '',

                    },

                    // crosshair: true
                },
                yAxis: {
                    categories: [70000, 80000, 100000, 120000, 200000, 250000],
                    tickPositions: [50000, 100000, 150000, 200000, 250000, 300000],
                    labels: {
                        formatter: function() {
                            return (this.value / 1000) + 'k';
                        },
                        style: {
                            fontFamily: 'Poppins',
                            fontStyle: 'normal',
                            fontSize: '14px',
                            lineHeight: '18px',
                            fontWeight: '400',
                            color: '#615E83',
                        }
                    },
                    // beginAtZero: true,
                    // startOnTick: true,
                    // reversed: true,


                    // tickInterval: 20,
                    title: {
                        text: '',

                    },
                    tickPositioner: function() {
                        var positions = [],
                            tick = Math.floor(this.dataMin),
                            increment = Math.ceil((this.dataMax - this.dataMin) / 5);
                        for (tick; tick - increment <= this.dataMax; tick += increment) {
                            positions.push(tick);
                        }
                        return positions;
                    }
                },
                tooltip: {
                    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y:.1f} mm</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {

                    column: {
                        groupPadding: 0.2,
                        borderWidth: 1,
                        borderRadius: 5,
                        borderColor: 'white',
                    }
                },
                series: [{
                        name: 'Total Sales',
                        data: [70000, 80000, 100000, 150000, 170000, 190000, 220000, 160000, 100000, 110000, 240000,
                            160000
                        ],
                        pointWidth: 12,
                        color: '#036',

                    },
                    {
                        name: 'Total Revenue',
                        data: [150000, 200000, 250000, 240000, 250000, 280000, 240000, 100000, 120000, 190000,
                            140000, 290000
                        ],
                        pointWidth: 12,
                        color: '#FE9B6A',

                    },


                ]
            });


            document.getElementById("low2").style.backgroundColor = chartRisk.series[0].color;
            document.getElementById("moderate2").style.backgroundColor = chartRisk.series[1].color;
            // document.getElementById("high3").style.backgroundColor = chartRisk.series[2].color;
            // document.getElementById("extreme44").style.backgroundColor = chartRisk.series[3].color;

            document.getElementById("one1").innerText = chartRisk.series[0].name;
            document.getElementById("two2").innerText = chartRisk.series[1].name;
            // document.getElementById("three3").innerText = chartRisk.series[2].name;
            // document.getElementById("four4").innerText = chartRisk.series[3].name;

            function toggleDataRisk(value) {
                var seriesed2 = chartRisk.series[value]; // Get the series object by index
                // console.log(seriesed);
                if (seriesed2) { // Check that the seriesed object is not undefined
                    if (seriesed2.visible) {
                        seriesed2.hide();
                    } else {
                        seriesed2.show();
                    }
                } else {
                    console.log('Invalid series index:', value);
                }
            }

            // console.log(chartRisk.series[1]);


            //   });

            // $(document).ready(function () {

            var dughnutData = [25, 75];

            var doughnutChart = new Chart("dughnutChart", {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Remaining'],
                    datasets: [{
                        label: 'Target Status',
                        data: dughnutData,
                        backgroundColor: [
                            '#3459AB',
                            '#FE9B6A',
                        ],
                        hoverOffset: 4,
                        borderRadius: 0,
                        borderWidth: 0,
                        spacing: 0,
                    }]
                },
                options: {
                    aspectRatio: 1.1,
                    plugins: {
                        legend: {
                            display: false,
                        },
                    },
                    cutout: '80%',
                },

            });



            document.getElementById("boxbg").style.backgroundColor = doughnutChart.data.datasets[0].backgroundColor[0];
            document.getElementById("boxbg2").style.backgroundColor = doughnutChart.data.datasets[0].backgroundColor[1];
            // document.getElementById("boxbg3").style.backgroundColor = doughnutChart.data.datasets[0].backgroundColor[2];
            // document.getElementById("boxbg4").style.backgroundColor = doughnutChart.data.datasets[0].backgroundColor[3];


            document.getElementById("underView").innerText = doughnutChart.data.labels[0];
            document.getElementById("pendings").innerText = doughnutChart.data.labels[1];
            // document.getElementById("rejections").innerText = doughnutChart.data.labels[2];
            // document.getElementById("approval").innerText = doughnutChart.data.labels[3];
            // dughtnValue

            document.getElementById("dughtnValue").innerText = doughnutChart.data.datasets[0].data[0] + "%";
            document.getElementById("dughtnValue").style.color = doughnutChart.data.datasets[0].backgroundColor[0];

            // console.log(doughnutChart.data.datasets.data[value])

            function toggleDoughnut(value) {
                // Toggle data values
                if (dughnutData[value] !== null) {
                    dughnutData[value] = null;
                } else { // Set the value back to its original value or any desired value
                    if (value === 0) {
                        dughnutData[value] = 25;
                    } else {
                        dughnutData[value] = 75;
                    }
                }

                // Update the chart to reflect the changes
                doughnutChart.data.datasets[0].data = dughnutData;
                doughnutChart.update();
            }

            //  });
        </script>
    @endpush
@endsection
