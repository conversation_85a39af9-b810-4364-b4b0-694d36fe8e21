@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main pb-5">

    <div class="table_filter_header mb-4">
        <h2 class="sub_heading">Operation Detail</h2>

        <div class="filters">
            <input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
            <select name="" id="" class="select-small basic-single-select">
                <option value="" selected disabled>Filter</option>
                <option value="Clear">Clear</option>
                <option value="In Progress">In Progress</option>
                <option value="Completed">Completed</option>
                <option value="Closed">Closed</option>
            </select>
        </div>
    </div>

    <div class="table-responsive">
        <table id="clients_Detail" class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>Request #</th>
                    <th>Project Name</th>
                    <th>Client Name</th>
                    <th>Operational Manager</th>
                    <th>Schedule Date</th>
                    <th>Division</th>
                    <th>Total Cost</th>
                    <th>Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>10269502</td>
                    <td>Marketing Cleanup</td>
                    <td>
                        <div class="table_profile">
                            <img class="image" height="24px" width="24px"
                                src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                            <h2 class="profile_name">Micheal James</h2>
                        </div>
                    </td>
                    <td>---</td>
                    <td>02/03/2022</td>
                    <td>LAN</td>
                    <td>$690.00</td>
                    <td>
                        <!-- <div class="status warning">Pending</div> -->
                        <div class="status success">Completed</div>
                        <!-- <div class="status primary">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#changeStatus">Change Status</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#assignOm">Assign OM</a></li>
                                <li><a class="dropdown-item" href="{{route('frontend2.operations_detail')}}">View Details</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>10269502</td>
                    <td>Marketing Cleanup</td>
                    <td>
                        <div class="table_profile">
                            <img class="image" height="24px" width="24px"
                                src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                            <h2 class="profile_name">Jon Doe</h2>
                        </div>
                    </td>
                    <td>---</td>
                    <td>02/03/2022</td>
                    <td>LAN</td>
                    <td>$690.00</td>
                    <td>
                        <div class="status primary">In Progress</div>
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#changeStatus">Change Status</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#assignOm">Assign OM</a></li>
                                <li><a class="dropdown-item" href="{{route('frontend2.operations_detail')}}">View Details</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>10269502</td>
                    <td>Marketing Cleanup</td>
                    <td>
                        <div class="table_profile">
                            <img class="image" height="24px" width="24px"
                                src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                            <h2 class="profile_name">Jon Doe</h2>
                        </div>
                    </td>
                    <td>---</td>
                    <td>02/03/2022</td>
                    <td>LAN</td>
                    <td>$690.00</td>
                    <td>
                        <div class="status warning">Pending</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#changeStatus">Change Status</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#assignOm">Assign OM</a></li>
                                <li><a class="dropdown-item" href="{{route('frontend2.operations_detail')}}">View Details</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>10269502</td>
                    <td>Marketing Cleanup</td>
                    <td>
                        <div class="table_profile">
                            <img class="image" height="24px" width="24px"
                                src="{{asset('admin_assets/images/users/user1.png')}}" alt="profile image">
                            <h2 class="profile_name">Jon Doe</h2>
                        </div>
                    </td>
                    <td>---</td>
                    <td>02/03/2022</td>
                    <td>LAN</td>
                    <td>$690.00</td>
                    <td>
                        <div class="status warning">Pending</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#changeStatus">Change Status</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#assignOm">Assign OM</a></li>
                                <li><a class="dropdown-item" href="{{route('frontend2.operations_detail')}}">View Details</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

        </table>
    </div>


</section>

<!--Change Status -->
<div class="operation_modal modal fade" id="changeStatus" tabindex="-1" aria-labelledby="changeStatusLabel"
    aria-hidden="true">
    <div class="modal-dialog m400">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusLabel">Edit</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h2 class="text-md text-primary mb-3">Change Status</h2>
                <p class="text-placeholder text-sm">You can change organization status</p>

                <div class="row">
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Won">
                            <label class="label" for="Won">Won</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Proposed">
                            <label class="label" for="Proposed">Proposed</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Lost" checked>
                            <label class="label" for="Lost">Lost</label>
                        </div>
                    </div>
                </div>


            </div>
            <div class="modal-footer pt-5">
                <button type="button" class="btn primaryblue w-100">Update</button>
            </div>
        </div>
    </div>
</div>

<!-- Assign OM -->
<div class="operation_modal modal fade" id="assignOm" tabindex="-1" aria-labelledby="assignOmLabel" aria-hidden="true">
    <div class="modal-dialog m400">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignOmLabel">Assign OM</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <input type="search" placeholder="Search" name="" id=""
                    class="clients_Detail_Search filter_search w-100">

                <div class="table-responsive assign_om_table mt-4">
                    <table class="table table-striped custom_datatable shadow-none display m-0 p-0" style="width:100%">
                        <tbody>
                            <tr>
                                <td class="select_om">
                                    <input type="radio" name="selectOM" id="result1">
                                    <label for="result1">Robert Fox</label>
                                </td>
                            </tr>
                            <tr>
                                <td class="select_om">
                                    <input type="radio" name="selectOM" id="result2">
                                    <label for="result2">Danny</label>
                                </td>
                            </tr>
                            <tr>
                                <td class="select_om">
                                    <input type="radio" name="selectOM" id="result3">
                                    <label for="result3">Micheal</label>
                                </td>
                            </tr>
                            <tr>
                                <td class="select_om">
                                    <input type="radio" name="selectOM" id="result4">
                                    <label for="result4">Stephen</label>
                                </td>
                            </tr>

                    </table>
                </div>

            </div>
            <div class="modal-footer pt-5">
                <button type="button" class="btn primaryblue w-100">Select</button>
            </div>
        </div>
    </div>
</div>

@endsection