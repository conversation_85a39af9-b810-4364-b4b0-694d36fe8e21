@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main pb-5">

    <div class="table_filter_header mb-4">
        <h2 class="sub_heading">Estimate</h2>

        <div class="filters">
            <input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
            <select name="" id="" class="select-small basic-single-select">
                <option value="" selected disabled>Filter</option>
                <option value="Open">Won</option>
                <option value="Close">Lost</option>
                <option value="Close">Proposed</option>
            </select>
            <a href="{{route('frontend2.generate_estimate')}}" class="btn primaryblue">Generate Estimate</a>
        </div>
    </div>

    <div class="table-responsive">
        <table id="clients_Detail" class="table table-striped custom_datatable display" style="width:100%">
            <thead>
                <tr>
                    <th>SR #</th>
                    <th>Project Name</th>
                    <th>Client Name</th>
                    <th>Salesman Name</th>
                    <th>Estimator Name</th>
                    <th>Total Price</th>
                    <th>Total Cost</th>
                    <th>Note</th>
                    <th>Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>10269502</td>
                    <td>Project name here </td>
                    <td>Jon Doe</td>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>
                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#jobLostReason">Remove</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>10269502</td>
                    <td>Project name here </td>
                    <td>Jon Doe</td>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>
                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#jobLostReason">Remove</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>10269502</td>
                    <td>Project name here </td>
                    <td>Jon Doe</td>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>
                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#jobLostReason">Remove</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>10269502</td>
                    <td>Project name here </td>
                    <td>Jon Doe</td>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines success"></i></td>
                    <td>
                        <div class="status success">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>
                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#jobLostReason">Remove</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>10269502</td>
                    <td>Project name here </td>
                    <td>Jon Doe</td>
                    <td>Annette Black</td>
                    <td>Floyd Miles</td>
                    <td>$690.00</td>
                    <td>$690.00</td>
                    <td><i class="file-icon fa-solid fa-file-lines warning"></i></td>
                    <td>
                        <div class="status warning">In Progress</div>
                        <!-- <div class="status success">Completed</div> -->
                    </td>
                    <td>
                        <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="{{asset('admin_assets/images/icons/vertical-dots.svg')}}" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                                <li><a class="dropdown-item" href="#" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>
                                <li><a class="dropdown-item" href="#">View Details</a></li>
                                <li><a class="dropdown-item" href="#">Remove</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>

        </table>
    </div>


</section>

<!--Edit Modal -->
<div class="operation_modal modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel"
    aria-hidden="true">
    <div class="modal-dialog m400">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTableModalLabel">Edit</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h2 class="text-md text-primary mb-3">Change Status</h2>
                <p class="text-placeholder text-sm">You can change organization status</p>

                <div class="row">
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Won">
                            <label class="label" for="Won">Won</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Proposed">
                            <label class="label" for="Proposed">Proposed</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="radio_group mt-5">
                            <input class="" type="radio" name="project_status" id="Lost" checked>
                            <label class="label" for="Lost">Lost</label>
                        </div>
                    </div>
                </div>


                <h2 class="text-md text-primary mb-3 mt-5">Reason</h2>
                <p class="text-placeholder text-sm">Before you cancel, Please let us know the reason job was lost.</p>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Price" checked>
                    <label class="label" for="Price">Price</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Competition">
                    <label class="label" for="Competition">Competition</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Budget">
                    <label class="label" for="Budget">Budget</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Timing">
                    <label class="label" for="Timing">Timing</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="PoorQualification">
                    <label class="label" for="PoorQualification">Poor Qualification</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Unresponsive">
                    <label class="label" for="Unresponsive">Unresponsive</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="NoDecision">
                    <label class="label" for="NoDecision">No Decision</label>
                </div>

                <div class="radio_group mt-5">
                    <input class="" type="radio" name="lost_reason" id="Other">
                    <label class="label" for="Other">Other <span class="placeholder-text font-14">(Please explain
                            below)</span></label>
                </div>

                <textarea class="input mt-5" placeholder="Anything you want to share?" name="" id=""
                    cols="30" rows="4"></textarea>


            </div>
            <div class="modal-footer pt-5">
                <button type="button" class="btn primaryblue w-100">Update</button>
            </div>
        </div>
    </div>
</div>
@endsection