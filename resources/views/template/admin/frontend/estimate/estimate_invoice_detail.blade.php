@extends('template.admin.layouts.default')
@section('section')
<section class="dashboard_main pb-5">
    <div class="panel mt-4">
        <div class="panel_header">
            <h2 class="panel_title">Request Details</h2>
            <p class="text"><span class="placeholder-text">Request #: </span>10269502</p>
        </div>

        <!-- Request Detail Info -->
        <div class="row mt-5">
            <div class="col-lg-4 col-md-6">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Client Name</label>
                            <p class="text">Micheal <PERSON></p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Company Name</label>
                            <p class="text">Creator.io </p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Phone Number</label>
                            <p class="text">(*************</p>
                            <p class="text">(*************</p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Email</label>
                            <p class="text"><EMAIL></p>
                            <p class="text"><EMAIL></p>
                            <p class="placeholder-text font-14">(Alternate)</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Office Number</label>
                            <p class="text">(*************</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Project Name</label>
                            <p class="text">Micheal James</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Lead Source</label>
                            <p class="text">Lorem Ipsm</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Salesperson Name</label>
                            <p class="text">John Doe</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Probability</label>
                            <p class="text">Cleaning City</p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="detail_info mt-4">
                            <label for="" class="label">Estimator Name</label>
                            <p class="text">John Doe</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="detail_info mt-4">
                    <label for="" class="label">Property</label>
                    <p class="text">Micheal James Property</p>
                </div>

                <div class="detail_info mt-4">
                    <label for="" class="label">Address 1</label>
                    <p class="text">21st Avenue, St 42, Hempstead town New York ,1234</p>
                </div>
            </div>
        </div>
        <!-- Request Detail Info -->
    </div>

    <div class="panel mt-4">
        <div class="scop-work-title pb-2">
            <h2>Scope of Work</h2>
            <div class="scop-title-button">
                <button class="scop-view-note-btn" type="button" data-toggle="modal"
                    data-target="#viewNoteModal">View Note</button>
                <button class="scop-add-note-btn" type="button" data-toggle="modal"
                    data-target="#addNoteModal">Add Note</button>
            </div>
        </div>

        <div class="general_accordian accordion mt-4">
            <!-- Equipment Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="EquipmentData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#EquipmentData-collapseOne" aria-expanded="true"
                        aria-controls="EquipmentData-collapseOne">
                        Equipment
                    </button>
                </h2>
                <div id="EquipmentData-collapseOne" class="accordion-collapse collapse show"
                    aria-labelledby="EquipmentData">
                    <div class="accordion-body">

                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <!-- Equipment Starts-->

            <!-- Labor Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="LaborData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#LaborData-collapseTwo" aria-expanded="true"
                        aria-controls="LaborData-collapseTwo">
                        Labor
                    </button>
                </h2>
                <div id="LaborData-collapseTwo" class="accordion-collapse collapse show" aria-labelledby="LaborData">
                    <div class="accordion-body">
                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Labor Starts-->

            <!-- Plant Materials Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="PlantMaterialsData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#PlantMaterialsData-collapseThree" aria-expanded="true"
                        aria-controls="PlantMaterialsData-collapseThree">
                        Plant Materials
                    </button>
                </h2>
                <div id="PlantMaterialsData-collapseThree" class="accordion-collapse  collapse show"
                    aria-labelledby="PlantMaterialsData">
                    <div class="accordion-body">
                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Plant Materials Starts-->

            <!-- Hard Materials Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="HardMaterialsData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#HardMaterialsData-collapseThree" aria-expanded="true"
                        aria-controls="HardMaterialsData-collapseThree">
                        Hard Materials
                    </button>
                </h2>
                <div id="HardMaterialsData-collapseThree" class="accordion-collapse  collapse show"
                    aria-labelledby="HardMaterialsData">
                    <div class="accordion-body">
                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Hard Materials Starts-->

            <!-- Other job Cost Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="OtherjobCostData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#OtherjobCostData-collapseThree" aria-expanded="true"
                        aria-controls="OtherjobCostData-collapseThree">
                        Other job Cost
                    </button>
                </h2>
                <div id="OtherjobCostData-collapseThree" class="accordion-collapse  collapse show"
                    aria-labelledby="OtherjobCostData">
                    <div class="accordion-body">
                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Other job Cost Starts-->

            <!-- Sub-Contractor Starts-->
            <div class="accordion-item mt-4">
                <h2 class="accordion-header" id="SubContractorData">
                    <button class="accordion-button" type="button" data-toggle="collapse"
                        data-target="#SubContractorData-collapseThree" aria-expanded="true"
                        aria-controls="SubContractorData-collapseThree">
                        Sub-Contractor
                    </button>
                </h2>
                <div id="SubContractorData-collapseThree" class="accordion-collapse collapse show"
                    aria-labelledby="SubContractorData">
                    <div class="accordion-body">
                        <div class="table-responsive">
                            <table class="table table-striped shadow-none custom_datatable display" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Items Name</th>
                                        <th>UoM</th>
                                        <th>Quantity</th>
                                        <th>Unit Cost</th>
                                        <th>Total Cost</th>
                                        <th>Gross Margin</th>
                                        <th>Total price</th>
                                        <th>Unit price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Micheal James</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$690.00</td>
                                        <td>$690.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>2</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                                    <tr>
                                        <td>General</td>
                                        <td>Each</td>
                                        <td>1</td>
                                        <td>$500</td>
                                        <td>$500</td>
                                        <td>50%</td>
                                        <td>$150.00</td>
                                        <td>$150.00</td>
                                    </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Sub-Contractor Starts-->

        </div>

        <div class="total_payable d-flex justify-content-end align-items-center gap-4 flex-wrap mt-4 mb-4">
            <p class="text">Total Payable</p>
            <h2 class="value">$ 25000</h2>
        </div>
    </div>

    <div class="d-flex align-items-center justify-content-between gap-4 flex-wrap mt-5">
        <button class="btn primaryblue transparent min-w-174" type="button">Cancel</button>
        <div class="d-flex align-items-center justify-content-between gap-4 flex-wrap">
            <button class="btn primaryblue transparent min-w-174" type="button">Save</button>
            <a href="{{route('estimate.pdf')}}" class="btn primaryblue min-w-174" type="button">Save & Download</a>
        </div>
    </div>
</section>

<!-- Modal -->
<div class="operation_modal modal fade" id="addNoteModal" data-keyboard="false" tabindex="-1"
    aria-labelledby="addNoteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addNoteModalLabel">Add Note</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pb-0">
                <div class="wycwyg_editor addNoteSummer" id="addNoteSummer" style="height:200px"></div>
            </div>
            <div class="modal-footer gap-4 mb-3">
                <button type="button" class=" invoice-pg-2-cls" data-dismiss="modal">Cancel</button>
                <button type="button" class=" invoice-pg-2-sv">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="operation_modal modal fade" id="viewNoteModal" data-keyboard="false" tabindex="-1"
    aria-labelledby="viewNoteModalLabel-1" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel-1">View Note</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="invoice-1-content">
                    <h3>Enter description</h3>
                    <p>Lorem ipsum dolor sit amet consectetur. A odio nascetur et lobortis nibh venenatis eget
                        tempus mi. Ut dui laoreet vitae fringilla sit gravida. Tincidunt amet aliquet mi sed
                        suspendisse. Id odio erat ultricies diam in. Pellentesque lectus nibh egestas ultrices quis
                        neque faucibus sed eu. Fringilla eget velit dolor massa scelerisque cursus ipsum. Accumsan
                        nibh commodo sagittis faucibus tincidunt. Nisl a imperdiet lacus tellus arcu ut. Nulla
                        dapibus dui praesent vitae est.
                        Tristique in dui sed velit egestas commodo. In vestibulum amet justo aliquet nunc eu at vel
                        enim. Tellus nec ullamcorper risus imperdiet. Nulla viverra risus fermentum quis convallis
                        tincidunt vitae. Varius nunc adipiscing odio urna consequat nunc accumsan.</p>

                    <h3>Enter description</h3>
                    <p>Lorem ipsum dolor sit amet consectetur. A odio nascetur et lobortis nibh venenatis eget
                        tempus mi. Ut dui laoreet vitae fringilla sit gravida. Tincidunt amet aliquet mi sed
                        suspendisse. Id odio erat ultricies diam in.</p>
                    <p>Pellentesque lectus nibh egestas ultrices quis neque faucibus sed eu. Fringilla eget velit
                        dolor massa scelerisque cursus ipsum. Accumsan nibh commodo sagittis faucibus tincidunt.
                        Nisl a imperdiet lacus tellus arcu ut. Nulla dapibus dui praesent vitae est.
                        Tristique in dui sed velit egestas commodo. In vestibulum amet justo aliquet nunc eu at vel
                        enim. Tellus nec ullamcorper risus imperdiet. Nulla viverra risus fermentum quis convallis
                        tincidunt vitae. Varius nunc adipiscing odio urna consequat nunc accumsan.</p>
                </div>
            </div>
            <div class="modal-footer gap-4">
                <button type="button" class="trans-danger-btn">Delete</button>
                <button type="button" class=" invoice-pg-2-cls" data-dismiss="modal">Close</button>
                <button type="button" class=" invoice-pg-2-sv">Edit</button>
            </div>
        </div>
    </div>
</div>
@endsection