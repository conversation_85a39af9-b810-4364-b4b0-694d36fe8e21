<!DOCTYPE html>
<html lang="en">

<head>

    <!----------------- Meta Tags ------------------>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="Description" CONTENT="{{ config('app.name') }} Dashboard">

    <!---------------------- Meta Tages End -------------------->

    <!---------------------- Bootstrap Cdn---------------------->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
        integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!------------------------ Bootstrap Cdn Ends-------------------->

    <!----------------------------Select2---------------------------->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!----------------------------Select2---------------------------->

    <!------------------------ Datatables-------------------->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css" />

    <!------------------------ Datatables-------------------->

    <link href="https://cdn.jsdelivr.net/npm/smartwizard@6/dist/css/smart_wizard_all.min.css" rel="stylesheet"
        type="text/css" />

    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/n-style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/globalStyles.css') }}" />

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom Css Ends-->

    <title>{{ config('app.name') }} Dashboard</title>

</head>

<body>


    <div class="dashboard_wrapper">
        <div class="sidebar_wrapper">
            <aside class="side_bar">
                <a href="#" class="side_bar_logo">
                    <img width="81.16px" height="45.33px"
                        src="{{ asset('admin_assets/images/elmos-logo-sidebar.png') }}" alt="elmos-logo-sidebar">
                </a>

                <div class="side_bar_menu">
                    <ul class="menu_links">
                        <li class="menu_item @if (Route::currentRouteName() == 'frontend.dashboard') active @endif">
                            <a href="{{ route('frontend.dashboard') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M11.1112 17.8332C11.1112 18.2748 11.2987 18.6991 11.632 19.0115C11.9653 19.324 12.4179 19.4998 12.8889 19.4998H18.2221C18.6932 19.4998 19.1458 19.324 19.479 19.0115C19.8123 18.6991 19.9999 18.2748 19.9999 17.8332V14.4998C19.9999 14.0582 19.8123 13.6339 19.479 13.3215C19.1458 13.009 18.6932 12.8332 18.2221 12.8332H12.8889C12.4179 12.8332 11.9653 13.0091 11.632 13.3215C11.2987 13.6339 11.1112 14.0582 11.1112 14.4998V17.8332ZM7.55556 12.8334H5.77776C5.30652 12.8334 4.8541 13.0092 4.52084 13.3216C4.18738 13.6341 4 14.0584 4 14.5V17.8334C4 18.275 4.18741 18.6993 4.52084 19.0117C4.85412 19.3242 5.30652 19.5 5.77776 19.5H7.55556C8.02662 19.5 8.47904 19.3241 8.81248 19.0117C9.14576 18.6993 9.33332 18.275 9.33332 17.8334V14.5C9.33332 14.0584 9.14574 13.6341 8.81248 13.3216C8.47902 13.0092 8.02662 12.8334 7.55556 12.8334ZM20 6.16664C20 5.72485 19.8124 5.30072 19.4791 4.98828C19.1458 4.67567 18.6933 4.5 18.2222 4.5H5.7778C5.30656 4.5 4.85415 4.67569 4.52089 4.98828C4.18743 5.30073 4.00005 5.72485 4.00005 6.16664V9.5C4.00005 9.94162 4.18745 10.3658 4.52089 10.6784C4.85417 10.9908 5.30656 11.1666 5.7778 11.1666H18.2222C19.2045 11.1666 20 10.4208 20 9.49996L20 6.16664Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Dashboard
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == getRouteAlias() . '.client.index' ||
                                Route::currentRouteName() == 'frontend2.add_client' ||
                                Route::currentRouteName() == 'frontend2.client_detail') active @endif">
                            <a href="{{ route(getRouteAlias() . '.client.index') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M5.24507 8.3872C5.40847 9.02991 5.83331 9.09527 5.83331 9.09527C6.08386 10.6639 7.3366 11.884 7.75054 12.1672C8.16449 12.4613 8.7963 12.4068 8.7963 12.4068C9.30829 12.4068 9.53705 12.4068 10.3541 11.7315C11.1711 11.0561 11.7811 9.11706 11.7811 9.11706C12.3911 9.17152 12.6417 8.11487 12.6417 7.63556C12.6417 7.16715 12.3149 7.10179 12.3149 7.10179C12.3149 7.10179 12.4674 6.29568 12.3149 5.2717C12.1624 4.24773 11.3018 3.11482 9.47169 2.78802C7.6525 2.46122 6.68299 3.45251 6.68299 3.45251C6.68299 3.45251 6.07297 3.44162 5.49562 4.31309C4.91827 5.18456 5.40847 7.16715 5.40847 7.16715C5.02721 7.35233 5.07078 7.74449 5.24507 8.3872Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M13.6888 9.10611C13.8087 9.58542 14.1355 9.629 14.1355 9.629C14.3206 10.7946 15.2575 11.7096 15.5625 11.9166C15.8675 12.1345 16.3468 12.0909 16.3468 12.0909C16.7281 12.0909 16.9024 12.0909 17.5015 11.5898C18.1115 11.0887 18.5691 9.63989 18.5691 9.63989C19.0266 9.68346 19.2118 8.88825 19.2118 8.53966C19.2118 8.19107 18.9721 8.13661 18.9721 8.13661C18.9721 8.13661 19.0919 7.53747 18.9721 6.77494C18.8523 6.0124 18.2096 5.16272 16.8588 4.92307C15.4971 4.68341 14.7782 5.41327 14.7782 5.41327C14.7782 5.41327 14.3206 5.40238 13.8958 6.05598C13.471 6.70958 13.8196 8.19107 13.8196 8.19107C13.5254 8.34358 13.5581 8.6377 13.6888 9.10611Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M20.6601 12.8861C20.3551 12.5485 19.4074 12.5702 19.037 12.494C18.6775 12.4177 18.0566 11.775 18.0566 11.775C17.8388 12.6029 16.3899 12.9297 16.3899 12.9297C16.3899 12.9297 14.9411 12.6029 14.7233 11.775C14.7233 11.775 14.1023 12.4068 13.7429 12.494C13.4052 12.5702 12.5555 12.5593 12.196 12.8317C11.7167 12.5811 11.1176 11.9711 11.1176 11.9711C10.8234 13.0822 8.88442 13.518 8.88442 13.518C8.88442 13.518 6.94541 13.0822 6.65129 11.9711C6.65129 11.9711 5.82339 12.8208 5.33319 12.9297C4.84299 13.0387 3.56847 13.0169 3.16542 13.4635C2.76236 13.9101 1.80375 16.3829 1.37891 19.0845C1.37891 21.067 16.4008 21.067 16.4008 19.0845C16.3573 18.7903 16.3028 18.4853 16.2374 18.2021C19.0915 18.213 22 17.8426 22 17.1019C21.6841 15.0648 20.9651 13.2238 20.6601 12.8861Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Clients
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.requests' || Route::currentRouteName() == 'frontend.request_detail') active @endif">
                            <a href="{{ route('frontend2.requests') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M15.0741 2.1167C15.7048 2.1167 16.2335 2.56688 16.3565 3.16346H17.4139C17.283 1.98832 16.2833 1.0697 15.0742 1.0697C13.8624 1.0697 12.8628 1.98832 12.7346 3.16346H13.792C13.9121 2.56666 14.4406 2.1167 15.0741 2.1167H15.0741Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M8.0078 7.35097V4.95105L5.08179 7.87435H7.48423C7.77212 7.87435 8.0078 7.63885 8.0078 7.35097Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M18.4764 4.2103H17.4296V6.04225C17.4296 7.3404 16.3722 8.39758 15.0743 8.39758C13.7735 8.39758 12.7189 7.34021 12.7189 6.04225C12.7189 5.7518 12.9519 5.51887 13.2423 5.51887C13.5302 5.51887 13.7657 5.75181 13.7657 6.04225C13.7657 6.76463 14.3519 7.35082 15.0743 7.35082C15.7939 7.35082 16.3828 6.76463 16.3828 6.04225V4.2103H9.05483V7.35082C9.05483 8.21705 8.34816 8.9212 7.48446 8.9212H4.34393V23.0538C4.34393 23.3417 4.57688 23.5771 4.86731 23.5771H18.4764C18.7643 23.5771 18.9998 23.3416 18.9998 23.0538L19 4.73377C19 4.44588 18.7645 4.21021 18.4766 4.21021L18.4764 4.2103ZM8.2695 15.464C8.2695 14.2942 8.86098 13.2629 9.76137 12.6506C9.48133 12.2632 9.31648 11.7895 9.31648 11.2767C9.31648 9.97857 10.3711 8.92139 11.6718 8.92139C12.97 8.92139 14.0271 9.97876 14.0271 11.2767C14.0271 11.7897 13.8596 12.2634 13.5797 12.6506C14.4801 13.2631 15.0741 14.2942 15.0741 15.464V16.2492C15.0741 16.5371 14.8386 16.7726 14.5507 16.7726H8.79304C8.50259 16.7726 8.26966 16.5371 8.26966 16.2492L8.2695 15.464ZM16.9061 22.0069H6.43751C6.14706 22.0069 5.91412 21.7714 5.91412 21.4835C5.91412 21.193 6.14707 20.9601 6.43751 20.9601H16.9061C17.194 20.9601 17.4295 21.193 17.4295 21.4835C17.4295 21.7714 17.194 22.0069 16.9061 22.0069ZM16.9061 19.3897H6.43751C6.14706 19.3897 5.91412 19.1542 5.91412 18.8663C5.91412 18.5759 6.14707 18.343 6.43751 18.343H16.9061C17.194 18.343 17.4295 18.5759 17.4295 18.8663C17.4295 19.1542 17.194 19.3897 16.9061 19.3897Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M14.0272 15.4641C14.0272 14.5508 13.5039 13.7603 12.7448 13.3704C12.423 13.5352 12.0591 13.6322 11.6717 13.6322C11.2843 13.6322 10.9207 13.5354 10.5961 13.3704C9.8372 13.7603 9.31635 14.5508 9.31635 15.4641V15.7259H14.0272L14.0272 15.4641Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M12.9804 11.2766C12.9804 11.9992 12.3945 12.5852 11.6718 12.5852C10.949 12.5852 10.3632 11.9992 10.3632 11.2766C10.3632 10.5538 10.949 9.96802 11.6718 9.96802C12.3945 9.96802 12.9804 10.5538 12.9804 11.2766Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Requests
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.estimate' || Route::currentRouteName() == 'frontend.generate_estimate') active @endif">
                            <a href="{{ route('frontend2.estimate') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M13.2316 5.88378H16.2094L12.9006 2.57495V5.5528C12.818 5.71846 12.9835 5.88377 13.2316 5.88377L13.2316 5.88378Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M7.2763 10.3507C7.35904 10.268 7.44179 10.1025 7.44179 9.93718C7.44179 9.77169 7.35904 9.68895 7.2763 9.60638C7.19356 9.52364 7.02807 9.4409 6.78003 9.35815V10.5989C6.94552 10.5162 7.11082 10.4335 7.2763 10.3507Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M5.86983 6.46265C5.62177 6.54539 5.45629 6.62813 5.37355 6.71087C5.20807 6.87636 5.20807 6.9591 5.20807 7.12441C5.20807 7.2899 5.29081 7.37264 5.37355 7.45521C5.4563 7.53812 5.62178 7.70343 5.86983 7.78618V6.46265Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M13.0663 9.44088H16.4579L16.4577 6.71105H13.2316C12.5699 6.71105 11.9909 6.13205 11.9909 5.47027V2.3269H2.56086C1.89907 2.3269 1.32007 2.90591 1.32007 3.56769V21.104C1.32007 21.7658 1.89907 22.3448 2.56086 22.3448H11.4117C11.1634 22.014 10.9981 21.6003 10.9981 21.104L10.998 11.5083C10.998 10.3502 11.9079 9.44043 13.066 9.44043L13.0663 9.44088ZM4.79427 8.77909C4.46348 8.61361 4.21527 8.44812 4.04976 8.20009C3.8017 7.95186 3.71896 7.62108 3.71896 7.20737C3.71896 6.7111 3.88445 6.29738 4.29797 5.88385C4.62876 5.55305 5.20777 5.38758 5.86956 5.30484V4.3123H6.77954V5.30484C7.52406 5.38758 8.10307 5.55307 8.59934 5.88385L8.10307 6.95917C7.68936 6.71111 7.19308 6.54563 6.77954 6.4629V8.03449C7.19308 8.11723 7.52406 8.28272 7.85486 8.36528C8.18566 8.44803 8.35113 8.69608 8.59938 8.94429C8.76487 9.19252 8.93018 9.52329 8.93018 9.93683C8.93018 10.4331 8.76469 10.8468 8.35117 11.1776C8.02038 11.5084 7.44118 11.7566 6.77958 11.8394V12.8319L5.86959 12.8323V11.8397C5.45606 11.8397 4.95961 11.757 4.6288 11.5915C4.29801 11.426 3.88429 11.2607 3.63626 11.0952L4.13253 10.0199C4.38076 10.1854 4.6288 10.3507 4.95978 10.4335C5.29058 10.5162 5.62157 10.599 5.95233 10.6817V9.19266C5.45606 9.02717 5.04252 8.86186 4.79427 8.77912V8.77909ZM9.75743 20.4425H4.54612C4.29789 20.4425 4.13258 20.277 4.13258 20.029C4.13258 19.7807 4.29806 19.6154 4.54612 19.6154H9.83985C10.0881 19.6154 10.2534 19.7809 10.2534 20.029C10.1708 20.1943 10.0055 20.4425 9.7573 20.4425H9.75743ZM9.75743 18.3745H4.54612C4.29789 18.3745 4.13258 18.209 4.13258 17.9609C4.13258 17.7127 4.29806 17.5474 4.54612 17.5474H9.83985C10.0881 17.5474 10.2534 17.7129 10.2534 17.9609C10.1708 18.2092 10.0055 18.3745 9.7573 18.3745H9.75743ZM9.75743 16.3892H4.54612C4.29789 16.3892 4.13258 16.2237 4.13258 15.9756C4.13258 15.7274 4.29806 15.5621 4.54612 15.5621H9.83985C10.0881 15.5621 10.2534 15.7276 10.2534 15.9756C10.2536 16.2239 10.0055 16.3892 9.7573 16.3892H9.75743Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M20.7592 10.2678H13.0663C12.4045 10.2678 11.8255 10.8468 11.8255 11.5086V21.1043C11.8255 21.7661 12.4045 22.3451 13.0663 22.3451H20.7592C21.421 22.3451 22 21.7661 22 21.1043V11.5086C22 10.8468 21.421 10.2678 20.7592 10.2678ZM14.7205 21.2696H13.8933C13.645 21.2696 13.4797 21.1041 13.4797 20.8561C13.4797 20.6079 13.6452 20.4426 13.8933 20.4426H14.7205C14.9688 20.4426 15.1341 20.608 15.1341 20.8561C15.1341 21.1041 14.886 21.2696 14.7205 21.2696ZM14.7205 19.2016H13.8933C13.645 19.2016 13.4797 19.0361 13.4797 18.7881C13.4797 18.5398 13.6452 18.3745 13.8933 18.3745H14.7205C14.9688 18.3745 15.1341 18.54 15.1341 18.7881C15.1341 19.0361 14.886 19.2016 14.7205 19.2016ZM14.7205 17.2163H13.8933C13.645 17.2163 13.4797 17.0508 13.4797 16.8027C13.4797 16.5545 13.6452 16.3892 13.8933 16.3892H14.7205C14.9688 16.3892 15.1341 16.5547 15.1341 16.8027C15.1341 17.0508 14.886 17.2163 14.7205 17.2163ZM17.2848 21.2696H16.4576C16.2094 21.2696 16.044 21.1041 16.044 20.8561C16.044 20.6079 16.2095 20.4426 16.4576 20.4426H17.2848C17.5331 20.4426 17.6984 20.608 17.6984 20.8561C17.6984 21.1041 17.5331 21.2696 17.2848 21.2696ZM17.2848 19.2016H16.4576C16.2094 19.2016 16.044 19.0361 16.044 18.7881C16.044 18.5398 16.2095 18.3745 16.4576 18.3745H17.2848C17.5331 18.3745 17.6984 18.54 17.6984 18.7881C17.6984 19.0361 17.5331 19.2016 17.2848 19.2016ZM17.2848 17.2163H16.4576C16.2094 17.2163 16.044 17.0508 16.044 16.8027C16.044 16.5545 16.2095 16.3892 16.4576 16.3892H17.2848C17.5331 16.3892 17.6984 16.5547 17.6984 16.8027C17.6984 17.0508 17.5331 17.2163 17.2848 17.2163ZM19.9319 21.2696H19.1046C18.8564 21.2696 18.6911 21.1041 18.6911 20.8561C18.6911 20.6079 18.8566 20.4426 19.1046 20.4426H19.9319C20.1801 20.4426 20.3454 20.608 20.3454 20.8561C20.3454 21.1041 20.1799 21.2696 19.9319 21.2696ZM19.9319 19.2016H19.1046C18.8564 19.2016 18.6911 19.0361 18.6911 18.7881C18.6911 18.5398 18.8566 18.3745 19.1046 18.3745H19.9319C20.1801 18.3745 20.3454 18.54 20.3454 18.7881C20.3454 19.0361 20.1799 19.2016 19.9319 19.2016ZM19.9319 17.2163H19.1046C18.8564 17.2163 18.6911 17.0508 18.6911 16.8027C18.6911 16.5545 18.8566 16.3892 19.1046 16.3892H19.9319C20.1801 16.3892 20.3454 16.5547 20.3454 16.8027C20.3454 17.0508 20.1799 17.2163 19.9319 17.2163ZM20.3454 13.9902C20.3454 14.4865 19.9319 14.8174 19.5182 14.8174H14.3069C13.8106 14.8174 13.4796 14.4866 13.4796 13.9902V12.8321C13.4796 12.3359 13.8104 12.0049 14.3069 12.0049H19.5182C20.0144 12.0049 20.3454 12.4184 20.3454 12.8321V13.9902Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Estimate
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.schedule') active @endif">
                            <a href="{{ route('frontend2.schedule') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M3.00023 7.64412V6.60656C3.00023 5.9386 3.26037 5.29789 3.72362 4.82566C4.18672 4.35325 4.81485 4.08799 5.47006 4.08799H7.67302V5.69987C7.67732 5.97622 7.89595 6.19918 8.16712 6.20354C8.29805 6.20354 8.42364 6.15049 8.51633 6.05597C8.60902 5.96163 8.66105 5.83339 8.66105 5.69986V4.08798H13.9562V5.69986C13.9562 5.97797 14.1774 6.20353 14.4501 6.20353C14.723 6.20353 14.9442 5.97796 14.9442 5.69986V4.08798H17.1473H17.1472C17.8022 4.08798 18.4305 4.35326 18.8936 4.82565C19.3569 5.29788 19.617 5.93859 19.617 6.60655V7.64411L3.00023 7.64412ZM8.66099 3.50368C8.66099 3.36998 8.60896 3.24191 8.51628 3.14739C8.42359 3.05305 8.298 3 8.16706 3C7.89417 3 7.67296 3.2254 7.67296 3.50367V4.08798H8.66081L8.66099 3.50368ZM14.9441 3.50368C14.9441 3.2254 14.7229 3.00001 14.45 3.00001C14.1772 3.00001 13.956 3.22541 13.956 3.50368V4.08799H14.9439L14.9441 3.50368ZM21 19.1993H20.9998C21.0002 20.0679 20.6053 20.8873 19.9307 21.4177C19.2562 21.9479 18.3784 22.1288 17.5549 21.9077C16.7312 21.6863 16.055 21.0878 15.7244 20.2874C15.5804 19.9434 15.5065 19.5732 15.507 19.1994C15.507 18.4566 15.7964 17.7442 16.3114 17.2189C16.8265 16.6938 17.5251 16.3987 18.2535 16.3987C18.4837 16.3978 18.713 16.4282 18.935 16.4893L19.0734 16.5195C19.0734 16.5296 19.0832 16.5296 19.0932 16.5296V16.5297C19.6458 16.7139 20.1274 17.071 20.4697 17.5503C20.812 18.0295 20.9976 18.6064 20.9998 19.1994L21 19.1993ZM18.7476 17.6881H18.7474C18.7431 17.4118 18.5245 17.1888 18.2535 17.1844C17.9806 17.1844 17.7596 17.41 17.7596 17.6881V18.8567L17.1668 19.0884C16.9395 19.1724 16.8053 19.4117 16.8494 19.6539C16.8935 19.8962 17.103 20.0703 17.3446 20.0656C17.4051 20.0654 17.4652 20.0552 17.5224 20.0354L18.4313 19.6727C18.621 19.5947 18.7459 19.4078 18.7475 19.1993L18.7476 17.6881ZM14.618 20.0354H14.6178C14.6848 20.3909 14.8118 20.7317 14.9932 21.0429H5.46983C4.81466 21.0429 4.18649 20.7774 3.7234 20.3052C3.26013 19.8328 3 19.1923 3 18.5243V8.65151H19.6171V15.663C19.3027 15.5274 18.9693 15.4424 18.6293 15.4111C18.5009 15.4011 18.3822 15.3909 18.2539 15.3909V15.3911C17.2636 15.3918 16.3143 15.7932 15.6141 16.5073C14.9138 17.2213 14.5201 18.1893 14.5195 19.1992C14.5164 19.4811 14.5496 19.7622 14.6182 20.0352L14.618 20.0354ZM9.10499 11.3413C9.1093 11.6179 9.32792 11.8408 9.59891 11.8452H15.7439C15.875 11.8452 16.0006 11.7921 16.0933 11.6976C16.1858 11.6031 16.238 11.475 16.238 11.3413C16.238 11.2078 16.1858 11.0797 16.0933 10.9852C16.0006 10.8907 15.875 10.8377 15.7439 10.8377H9.59891C9.32619 10.8377 9.10499 11.0632 9.10499 11.3413ZM9.10499 14.3436C9.10499 14.4771 9.15702 14.6053 9.2497 14.6997C9.34239 14.7942 9.46798 14.8472 9.59892 14.8472H15.7439C16.0168 14.8472 16.238 14.6217 16.238 14.3436C16.238 14.0653 16.0168 13.8399 15.7439 13.8399H9.59892C9.3262 13.8399 9.10499 14.0653 9.10499 14.3436ZM9.10499 17.3456C9.10499 17.4793 9.15702 17.6074 9.2497 17.7019C9.34239 17.7964 9.46798 17.8495 9.59892 17.8495H13.3729C13.6456 17.8495 13.8668 17.6239 13.8668 17.3456C13.8668 17.0675 13.6456 16.842 13.3729 16.842H9.59892C9.3262 16.842 9.10499 17.0675 9.10499 17.3456ZM6.3783 11.3413C6.3826 11.6178 6.60123 11.8407 6.87222 11.8451H7.34651C7.47745 11.8451 7.60321 11.792 7.69573 11.6975C7.78842 11.603 7.84044 11.4749 7.84044 11.3412C7.84044 11.2077 7.78842 11.0797 7.69573 10.9851C7.60321 10.8906 7.47745 10.8376 7.34651 10.8376H6.87222C6.5995 10.8376 6.3783 11.0632 6.3783 11.3413ZM6.3783 14.3435C6.3783 14.477 6.43033 14.6052 6.52301 14.6996C6.6157 14.7941 6.74129 14.8472 6.87223 14.8472H7.34652C7.61925 14.8472 7.84044 14.6216 7.84044 14.3435C7.84044 14.0652 7.61924 13.8398 7.34652 13.8398H6.87223C6.5995 13.8398 6.3783 14.0652 6.3783 14.3435ZM6.3783 17.3455C6.3783 17.4792 6.43033 17.6073 6.52301 17.7018C6.6157 17.7963 6.74129 17.8494 6.87223 17.8494H7.34652C7.47746 17.8494 7.60322 17.7963 7.69574 17.7018C7.78843 17.6073 7.84045 17.4792 7.84045 17.3455C7.84045 17.212 7.78842 17.0839 7.69574 16.9894C7.60322 16.8949 7.47746 16.8419 7.34652 16.8419H6.87223C6.60123 16.8462 6.38257 17.0692 6.3783 17.3455Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Schedule
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.operations' || Route::currentRouteName() == 'frontend2.operations_detail') active @endif">
                            <a href="{{ route('frontend2.operations') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M20.5012 14.3145L19.3369 14.6056C19.1784 15.1404 18.9644 15.6564 18.6973 16.1474L19.3152 17.1771C19.4728 17.4394 19.4314 17.7755 19.2149 17.9916L17.3293 19.8771C17.1131 20.0929 16.7778 20.1343 16.5148 19.9774L15.4851 19.3595C14.9941 19.6268 14.4782 19.8407 13.9433 19.9992L13.6522 21.1634C13.5781 21.4599 13.3114 21.6682 13.0054 21.6682H10.3385C10.0326 21.6682 9.76588 21.4599 9.69163 21.1633L9.40068 19.9992C8.86582 19.8407 8.34985 19.6267 7.85887 19.3595L6.82919 19.9773C6.56711 20.1342 6.23078 20.0929 6.01468 19.877L4.12912 17.9914C3.91258 17.7752 3.87121 17.4393 4.02881 17.1769L4.64673 16.1472C4.37945 15.6563 4.16559 15.1403 4.0071 14.6054L2.84285 14.3143C2.54624 14.2402 2.33789 13.9735 2.33789 13.6675V11.0006C2.33789 10.6947 2.54624 10.428 2.84285 10.3537L4.0071 10.0626C4.16559 9.52777 4.3796 9.01181 4.64673 8.52083L4.02881 7.49115C3.87121 7.22878 3.91258 6.89274 4.12912 6.67664L6.01468 4.79108C6.23092 4.57484 6.56711 4.53257 6.82919 4.69077L7.85887 5.30869C8.34985 5.04141 8.86582 4.82754 9.40068 4.66905L9.69177 3.50481C9.76589 3.20835 10.0326 3 10.3386 3H13.0055C13.3114 3 13.5781 3.20835 13.6524 3.50496L13.9435 4.66921C14.4783 4.8277 14.9943 5.04171 15.4853 5.30884L16.515 4.69092C16.7781 4.53273 17.1134 4.57499 17.3295 4.79123L19.215 6.6768C19.4316 6.89303 19.4729 7.22892 19.3153 7.4913L18.6974 8.52099C18.9647 9.01196 19.1786 9.52793 19.337 10.0628L20.5013 10.3539C20.7978 10.428 21.0061 10.6947 21.0061 11.0007V13.6676C21.0061 13.9735 20.7977 14.2402 20.5011 14.3145H20.5012ZM17.6725 7.33371C17.6725 7.14947 17.5234 7.00035 17.3391 7.00035C17.1549 7.00035 17.0058 7.14947 17.0058 7.33371V8.35223C15.7494 6.66592 13.8007 5.66691 11.672 5.66691C7.99565 5.66691 5.00478 8.65778 5.00478 12.3341C5.00478 12.5184 5.1539 12.6675 5.33814 12.6675C5.52238 12.6675 5.6715 12.5184 5.6715 12.3341C5.6715 9.02551 8.36338 6.33363 11.672 6.33363C13.6909 6.33363 15.5317 7.33074 16.6503 9.00052H15.3389C15.1547 9.00052 15.0055 9.14964 15.0055 9.33388C15.0055 9.51812 15.1547 9.66724 15.3389 9.66724H17.3391C17.5233 9.66724 17.6724 9.51812 17.6724 9.33388L17.6725 7.33371ZM9.00511 12.3341C9.00511 13.807 10.1991 15.001 11.672 15.001C13.1449 15.001 14.3389 13.807 14.3389 12.3341C14.3389 10.8612 13.1449 9.66724 11.672 9.66724C10.1991 9.66724 9.00511 10.8612 9.00511 12.3341ZM18.0059 12.0008C17.8216 12.0008 17.6725 12.1499 17.6725 12.3341C17.6725 15.6427 14.9806 18.3346 11.672 18.3346C9.65309 18.3346 7.81225 17.3375 6.69368 15.6677H8.0051C8.18934 15.6677 8.33846 15.5186 8.33846 15.3344C8.33846 15.1501 8.18934 15.001 8.0051 15.001H6.00493C5.82069 15.001 5.67157 15.1501 5.67157 15.3344V17.3345C5.67157 17.5188 5.82069 17.6679 6.00493 17.6679C6.18917 17.6679 6.3383 17.5188 6.3383 17.3345V16.316C7.59467 18.0023 9.54332 19.0013 11.6721 19.0013C15.3484 19.0013 18.3393 16.0105 18.3393 12.3341C18.3393 12.1499 18.1902 12.0008 18.0059 12.0008H18.0059Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M13.672 12.3341C13.672 11.2294 12.7765 10.3339 11.6718 10.3339C10.5671 10.3339 9.67163 11.2294 9.67163 12.3341C9.67163 13.4388 10.5671 14.3342 11.6718 14.3342C12.7765 14.3342 13.672 13.4388 13.672 12.3341Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Operations
                            </a>
                        </li>

                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.reports') active @endif">
                            <a href="{{ route('frontend2.reports') }}" class="menu_link">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M18.906 3.20312H16.6703V4.93548C16.6703 5.49051 16.2421 5.98189 15.6831 5.98992H9.32874C8.76592 5.98603 8.30998 5.52621 8.31391 4.9632L8.32192 3.20312H6.09404C5.49165 3.20312 5 3.69475 5 4.29717V20.7284C5 21.3269 5.49163 21.8224 6.09404 21.8224H18.906C19.5086 21.8224 20 21.3269 20 20.7284V4.29717C20 3.69478 19.5084 3.20312 18.906 3.20312H18.906ZM18.3866 17.4424H6.61359C6.39546 17.4424 6.21717 17.2641 6.21717 17.0459C6.21717 16.8278 6.39547 16.6495 6.61359 16.6495H7.69181V11.0127C7.69181 10.7946 7.86622 10.6163 8.08823 10.6163H9.80869C10.0307 10.6163 10.2051 10.7946 10.2051 11.0127V16.6495H11.2438V13.2603C11.2438 13.0383 11.4221 12.8639 11.6402 12.8639H13.3646C13.5827 12.8639 13.761 13.0383 13.761 13.2603V16.6495H14.7997L14.7994 9.47073C14.7994 9.24872 14.9777 9.07431 15.1959 9.07431H16.9163C17.1344 9.07431 17.3127 9.24872 17.3127 9.47073V16.6495H18.3871C18.6052 16.6495 18.7835 16.8278 18.7835 17.0459C18.7833 17.2641 18.6048 17.4424 18.3869 17.4424H18.3866Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M9.32885 5.19514H15.6715C15.8102 5.19514 15.937 5.12786 16.0084 5.00884C16.0837 4.89394 16.0876 4.74723 16.0284 4.62432L14.8471 2.22201C14.7798 2.0872 14.6409 2 14.4903 2L10.5103 2.00023C10.3597 2.00023 10.221 2.08743 10.1535 2.22224L8.97227 4.62455C8.91277 4.74746 8.91689 4.89417 8.99219 5.00906C9.06336 5.12785 9.19017 5.19514 9.32909 5.19514H9.32885Z"
                                        fill="#E7E7E7" />
                                </svg>
                                Reports
                            </a>
                        </li>

                        <!-- Super Admin Routes -->
                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.companies') active @endif">
                            <a href="{{ route('frontend2.companies') }}" class="menu_link">
                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M21.5892 22.9508V5.70394H19.3557V22.9508H17.9491V0.357422L6.04755 3.69436V8.47461H11.1316V22.9508H9.72494V9.88126H3.81736V22.9508H0.933594V24.3574H23.0664V22.9508H21.5892ZM13.8062 14.932V12.1027H15.2129V14.932H13.8062ZM15.2129 17.2699V20.0992H13.8062V17.2699H15.2129ZM13.8062 9.76483V6.93555H15.2129V9.76483H13.8062ZM6.0678 12.1027H7.47445V14.932H6.0678V12.1027ZM6.0678 17.2699H7.47445V20.0992H6.0678V17.2699Z"
                                        fill="#E7E7E7" />
                                </svg>

                                Companies
                            </a>
                        </li>
                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.packages') active @endif">
                            <a href="{{ route('frontend2.packages') }}" class="menu_link">
                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_3201_2718)">
                                        <path
                                            d="M4.19005 24.3574H19.8095C20.3135 24.3574 20.7222 23.9495 20.7222 23.4463V1.26847C20.7222 0.765267 20.3135 0.357361 19.8095 0.357361H8.39942V4.77392C8.39942 5.16116 8.08498 5.47503 7.69709 5.47503H3.27734V23.4463C3.27734 23.9495 3.68595 24.3574 4.19005 24.3574Z"
                                            fill="#E7E7E7" />
                                        <path d="M4.27051 4.07283H6.9947V1.35333L4.27051 4.07283Z" fill="#E7E7E7" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_3201_2718">
                                            <rect width="24" height="24" fill="white"
                                                transform="translate(0 0.357361)" />
                                        </clipPath>
                                    </defs>
                                </svg>
                                Packages
                            </a>
                        </li>
                        <li class="menu_item @if (Route::currentRouteName() == 'frontend2.transactions') active @endif">
                            <a href="{{ route('frontend2.transactions') }}" class="menu_link">
                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M22.5 4.85736V22.4824C22.5 22.7824 22.05 22.9324 21.75 22.7074L20.625 21.6574C20.4 21.5074 20.025 21.5074 19.8 21.6574L18.675 22.5574C18.3 22.8574 17.7 22.8574 17.325 22.5574L16.2 21.6574C15.975 21.4324 15.6 21.4324 15.375 21.6574L14.25 22.5574C13.875 22.8574 13.275 22.8574 12.825 22.5574L11.7 21.6574C11.475 21.4324 11.1 21.4324 10.875 21.6574L9.75 22.5574C9.375 22.8574 8.775 22.8574 8.325 22.5574L7.2 21.6574C6.975 21.5074 6.6 21.5074 6.375 21.6574L5.25 22.7074C4.95 23.0074 4.5 22.7824 4.5 22.4824V1.85736H19.275C21 1.85736 22.5 3.20736 22.5 4.85736Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M4.5 1.85736V7.03236H2.25C1.875 7.03236 1.5 6.65736 1.5 6.28236V4.85736C1.5 3.28236 2.85 1.93236 4.5 1.85736Z"
                                        fill="#E7E7E7" />
                                    <path
                                        d="M19.2736 6.20736H7.64863C7.27363 6.20736 6.97363 5.90736 6.97363 5.53236C7.04863 5.15736 7.34863 4.85736 7.64863 4.85736H19.2736C19.6486 4.85736 19.9486 5.15736 19.9486 5.53236C19.9486 5.90736 19.6486 6.20736 19.2736 6.20736Z"
                                        fill="#003366" />
                                    <path
                                        d="M19.2736 10.1823H7.64863C7.27363 10.1823 6.97363 9.88234 6.97363 9.50734C6.97363 9.13234 7.27363 8.83234 7.64863 8.83234H19.2736C19.6486 8.83234 19.9486 9.13234 19.9486 9.50734C19.9486 9.88234 19.6486 10.1823 19.2736 10.1823Z"
                                        fill="#003366" />
                                    <path
                                        d="M19.2736 14.1574H7.64863C7.27363 14.1574 6.97363 13.8574 6.97363 13.4824C6.97363 13.1074 7.27363 12.8074 7.64863 12.8074H19.2736C19.6486 12.8074 19.9486 13.1074 19.9486 13.4824C19.9486 13.8574 19.6486 14.1574 19.2736 14.1574Z"
                                        fill="#003366" />
                                    <path
                                        d="M19.2736 18.1323H7.64863C7.27363 18.1323 6.97363 17.8323 6.97363 17.4573C6.97363 17.0823 7.27363 16.7823 7.64863 16.7823H19.2736C19.6486 16.7823 19.9486 17.0823 19.9486 17.4573C19.9486 17.8323 19.6486 18.1323 19.2736 18.1323Z"
                                        fill="#003366" />
                                </svg>
                                Transactions
                            </a>
                        </li>
                        <!-- Super Admin Routes -->

                    </ul>
                </div>
            </aside>
        </div>
        <div class="content_wrapper">
            <header class="dashboard_header">
                <h2 class="dashboard_header_title">Home</h2>

                <div class="header_items">
                    <a href="#">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12.0117 14.8453C11.3109 14.8453 10.6547 14.5734 10.1578 14.0765C9.66329 13.5796 9.38907 12.9234 9.38907 12.2226C9.38907 11.5218 9.66329 10.8656 10.1578 10.3687C10.6547 9.87417 11.3109 9.59995 12.0117 9.59995C12.7125 9.59995 13.3688 9.87417 13.8656 10.3687C14.3602 10.8656 14.6344 11.5218 14.6344 12.2226C14.6344 12.9234 14.3602 13.5796 13.8656 14.0765C13.6229 14.3211 13.3341 14.515 13.0158 14.6469C12.6976 14.7789 12.3563 14.8463 12.0117 14.8453ZM21.675 9.32573L20.1422 10.6359C20.2149 11.0812 20.2524 11.5359 20.2524 11.9882C20.2524 12.4406 20.2149 12.8976 20.1422 13.3406L21.675 14.6507C21.7908 14.7499 21.8737 14.8819 21.9126 15.0293C21.9515 15.1766 21.9447 15.3323 21.893 15.4757L21.8719 15.5367C21.45 16.7163 20.818 17.8097 20.0063 18.764L19.9641 18.8132C19.8655 18.9291 19.7341 19.0124 19.5873 19.0522C19.4404 19.0919 19.285 19.0862 19.1414 19.0359L17.2383 18.3585C16.5352 18.9351 15.7524 19.3898 14.9039 19.7062L14.5359 21.696C14.5082 21.8459 14.4355 21.9839 14.3275 22.0914C14.2195 22.199 14.0813 22.2712 13.9313 22.2984L13.868 22.3101C12.6492 22.5304 11.3649 22.5304 10.1461 22.3101L10.0828 22.2984C9.93282 22.2712 9.79463 22.199 9.68662 22.0914C9.57861 21.9839 9.5059 21.8459 9.47814 21.696L9.10782 19.6968C8.26742 19.3779 7.48453 18.9243 6.78986 18.3539L4.87267 19.0359C4.72915 19.0866 4.57358 19.0925 4.42664 19.0528C4.2797 19.013 4.14834 18.9294 4.05001 18.8132L4.00782 18.764C3.19757 17.8087 2.56565 16.7156 2.1422 15.5367L2.12111 15.4757C2.01564 15.1828 2.10236 14.8546 2.33907 14.6507L3.89064 13.3265C3.81798 12.8859 3.78282 12.4359 3.78282 11.9906C3.78282 11.5406 3.81798 11.0906 3.89064 10.6546L2.34376 9.33042C2.22797 9.23129 2.1451 9.09927 2.10616 8.9519C2.06723 8.80453 2.07407 8.6488 2.12579 8.50542L2.14689 8.44448C2.57111 7.26558 3.19689 6.17573 4.01251 5.21714L4.0547 5.16792C4.15326 5.05203 4.28463 4.96872 4.43148 4.92897C4.57834 4.88922 4.73379 4.89491 4.87735 4.94526L6.79454 5.62729C7.49298 5.05308 8.2711 4.59839 9.11251 4.28433L9.48282 2.28511C9.51059 2.13521 9.5833 1.9973 9.69131 1.88971C9.79932 1.78212 9.9375 1.70994 10.0875 1.68276L10.1508 1.67105C11.3815 1.44955 12.6419 1.44955 13.8727 1.67105L13.9359 1.68276C14.086 1.70994 14.2241 1.78212 14.3322 1.88971C14.4402 1.9973 14.5129 2.13521 14.5406 2.28511L14.9086 4.27495C15.757 4.5937 16.5399 5.04605 17.243 5.62261L19.1461 4.94526C19.2896 4.8945 19.4452 4.88862 19.5921 4.92839C19.7391 4.96816 19.8704 5.0517 19.9688 5.16792L20.0109 5.21714C20.8266 6.18042 21.4524 7.26558 21.8766 8.44448L21.8977 8.50542C21.9984 8.79605 21.9117 9.12183 21.675 9.32573ZM12.0117 8.1023C9.73595 8.1023 7.89142 9.94683 7.89142 12.2226C7.89142 14.4984 9.73595 16.3429 12.0117 16.3429C14.2875 16.3429 16.132 14.4984 16.132 12.2226C16.132 9.94683 14.2875 8.1023 12.0117 8.1023Z"
                                fill="#90A0B7" />
                        </svg>
                    </a>

                    <div class="profile_info_dropdown">
                        <div class="profile_info dropdown-toggle arrow" type="button" id="userDropdown"
                            data-toggle="dropdown" aria-expanded="false">

                            <div class="image">
                                <img class="rounded" height="38px" width="38px"
                                    src="{{ asset('admin_assets/images/users/user1.png') }}" alt="user image">
                            </div>
                            <div class="info">
                                <h2 class="welcome_status">Welcome back,</h2>
                                <h3 class="profile_name">{{ auth()->user()->name ?? '' }}</h3>
                            </div>

                        </div>


                        <ul class="dropdown-menu" aria-labelledby="userDropdown">
                            @auth
                                <li><a class="dropdown-item" href="{{ route('logout') }}"
                                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST"
                                        style="display: none;">
                                        @csrf
                                    </form>
                                </li>
                            @endauth
                        </ul>


                    </div>

                    <i class="aside_toggle fa fa-bars"></i>

                </div>
            </header>
            @include('layouts.partials.flash-messages')
