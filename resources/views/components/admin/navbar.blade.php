<style>
    .navbar {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        padding: 0px !important;
    }
    .nav-item.active {
        border-bottom: none !important;
    }
    .nav-item.active .nav-link{
        border-bottom: 4px solid rgba(47, 204, 64, 1) !important;
    }
    #navbarSupportedContent .custom-navbar .dropdown .dropdown-toggle::after {
        display: inline-block !important;
        width: 0 !important;
        height: 0 !important;
        margin-left: .255em !important;
        vertical-align: .255em !important;
        content: "" !important;
        border-top: .3em solid !important;
        border-right: .3em solid transparent !important;
        border-bottom: 0 !important;
        border-left: .3em solid transparent !important;
        transform: rotate(0deg);
    }
    #navbarSupportedContent .custom-navbar .show .dropdown-toggle::after {

        transform: rotate(180deg) !important; /* Rotate arrow */
    }
    @media (min-width: 992px) {
        .navbar-expand-lg .navbar-nav .nav-link {
            padding-right: .1rem;
            padding-left: .1rem;
        }
    }

</style>
<nav class="navbar navbar-expand-lg navbar-light" style="padding: 0px 5px !important;">
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav mr-auto custom-navbar ml-3" style="gap: 8px">
            <x-navbar-item
                href="{{ route('frontend.dashboard') }}"
                label="Dashboard"
                :active="Route::currentRouteName() == 'frontend.dashboard'"
            />
            @organization
            <!-- Navbar items -->
            <x-navbar-item
                href="{{ route('organization.accounts')}}"
                label="Accounts"
                :active="in_array(Route::currentRouteName(), ['organization.accounts', 'organization.account.add', 'organization.accounts.edit'])"/>
            <x-navbar-item href="{{ route('organization.properties')}}" label="Properties"
                           :active="in_array(Route::currentRouteName(), ['organization.properties', 'organization.property.detail'])" />


            <x-navbar-item href="{{ route('organization.contactList')}}" label="Contacts" :active="in_array(Route::currentRouteName(), ['organization.contactList'])" />

            <x-navbar-item
                href="{{ route('organization.opportunity.index') }}"
                label="Opportunity"
                :active="in_array(Route::currentRouteName(), ['organization.opportunity.index', 'organization.create.opportunity', 'organization.estimate-generate.index', 'organization.archive-estimate-generate.archive', 'organization.opportunity.detail', 'organization.edit.opportunity'])"
                :dropdownItems="[[
                    'href' => route('organization.create.opportunity'), 'label' => 'Create opportunity'],
                    ['href' => route('organization.opportunity.index'), 'label' => 'Opportunity Listing'],

                ]"
            />
            <x-navbar-item href="{{ route('organization.schedule.index')}}" label="Schedule" :active="Route::currentRouteName() == 'organization.schedule.index'" />

            <!-- Repeat for other items -->
            <x-navbar-item href="{{route('organization.operation.index')}}" label="Operation" :active="Route::currentRouteName() == 'organization.operation.index'" />

            <x-navbar-item
                label="Invoices"
                :active="in_array(Route::currentRouteName(), ['organization.invoices.index', 'organization.transactions'])"
                :dropdownItems="[[
                    'href' => route('organization.invoices.index'), 'label' => 'Invoices'],
                    ['href' => route('organization.transactions'), 'label' => 'Transactions'],
                ]"
            />

            <x-navbar-item href="{{ route('organization.financialTool')}}" label="Forecast" :active="Route::currentRouteName() == 'organization.financialTool'" />

            <x-navbar-item href="{{ route ('organization.customer-issues.index')}}" label="Issues" :active="Route::currentRouteName() == 'organization.customer-issues.index' || Route::currentRouteName() == 'organization.create.customer-issue'" />
            <x-navbar-item href="{{ route ('organization.reports.index')}}" label="Reports" :active="Route::currentRouteName() == 'organization.reports.index'"/>
            <x-navbar-item href="{{ route ('organization.employee.index')}}" label="Employee" :active="Route::currentRouteName() == 'organization.employee.index'"/>
            @endorganization
            @admin
            <x-navbar-item href="{{ route('admin.company.index')}}" label="Companies" :active="request()->routeIs('admin.company.index')" />
            @endadmin
        </ul>
    </div>
</nav>
