{{-- <div class="panel">

    <div class="table_filter_header mt-4">
        <h2 class="table_title">Plant Materials</h2>

        <div class="filters">
            <img height="24px" width="24px" src="{{ asset('admin_assets/images/icons/help-icon.svg') }}" alt="help
icon"
data-toggle="modal" data-target="#editTableModal">
<input type="search" placeholder="Search" name="" id="" class="clients_Detail_Search filter_search">
<select name="" id="" class="table_filter_select select-small custom_selectBox">
    <option value="" selected>Filter</option>
    <option value="Clear">Clear</option>
    <option value="LF">1 Gal</option>
    <option value="Each">Quart</option>
</select>
<a href="#" class="btn primaryblue transparent px-5">Import Material</a>
</div>
</div>

<div class="table-responsive">
    <table id="clients_Detail" class="custom_datatable display mt-4" style="width:100%">
        <thead>
            <tr>
                <th>Material Name</th>
                <th>Type</th>
                <th>Size</th>
                <th>Cost</th>
                <th>Install</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>
            <tr>
                <td>Drainage, ADS 4'' perf pipe w/sock</td>
                <td>Perennial</td>
                <td>1 Gal</td>
                <td>$3.85</td>
                <td>0.100</td>
            </tr>

    </table>
</div>

<div class="table_header_footer mt4">
    <div class="page_per">
        <label class="info">Show</label>
        <select name="" id="" class="table_paigination darkarrow">
            <option value="5">5</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="75">75</option>
            <option value="All">All</option>
        </select>
        <label class="info">entries of 430 entries</label>
    </div>
</div>

</div> --}}


<div class="panel pt-4">
    <div class="showSuccessMessage">

    </div>
    <div class="table_filter_header pb-3">

        <h2 class="table_title">Plant Materials</h2>

        <div class="filters plant_materials_table_filters">
            {{-- <img height="24px" width="24px" src="{{ asset('admin_assets/images/icons/help-icon.svg') }}" alt="help icon"
                data-toggle="modal" data-target="#editTableModal"> --}}
            <input type="search" placeholder="Search" name="" id="filter_search"
                class="clients_Detail_Search filter_search">
            <select name="" id="select__plant_filter" class="table_filter_select select-small custom_selectBox">
                <option value="" selected>Filter</option>
                <option value="Clear2">Clear</option>
                {{-- <option value="1 Gal">1 Gal</option>
                <option value="Quart">Quart</option> --}}
            </select>
            <form method="" action="" id="plant-material-import" class="upload_button_form file_wrapper w-fit"
                enctype="multipart/form-data">
                @csrf
                <input class="plant_input_file d-none" type="file" name="file" id="import_plant_file_data" accept=".xls, .xlsx">
                <label class="btn primaryblue primaryblue22 transparent px-5" for="import_plant_file_data">Import Material</label>
            </form>

        </div>
    </div>

    <table id="clients_Detail" class="table table-striped custom_datatable display mx-0 my-4 yajra-datatable2"
        style="width:100%">
        <thead>
            <tr>
                <th>Material Name</th>
                <th>Type</th>
                <th>Size</th>
                <th>Unit Cost</th>
                <th>Labour / Unit</th>
            </tr>
        </thead>
        <tbody>
    </table>

</div>
