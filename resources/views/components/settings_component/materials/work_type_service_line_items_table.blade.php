<div class="panel pt-4">
    <div class="showSuccessMessage">

    </div>
    <div class="table_filter_header pb-3">

        <h2 class="table_title">Work Type</h2>
        <select name="" id="select__service_line_filter" class="table_filter_select select-small custom_selectBox">
            <option value="" selected>Filter</option>
        </select>
        <div class="filters work_type_materials_table_filters">
            {{-- <img height="24px" width="24px" src="{{ asset('admin_assets/images/icons/help-icon.svg') }}" alt="help icon"
                data-toggle="modal" data-target="#editTableModal"> --}}
            <input type="search" placeholder="Search" name="" id="filter_search"
                class="clients_Detail_Search filter_search">

            <form method="" action="" id="work-type-material-import" class="upload_button_form file_wrapper w-fit"
                enctype="multipart/form-data">
                @csrf
                <input class="plant_input_file d-none" type="file" name="file" id="import_plant_file_data" accept=".xls, .xlsx">
                <label class="btn primaryblue transparent px-5" for="import_plant_file_data">Import Work Type</label>
            </form>

        </div>
    </div>

    <table id="clients_Detail" class="table table-striped custom_datatable display mx-0 my-4 yajra-datatable2"
        style="width:100%">
        <thead>
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Service Line</th>
            </tr>
        </thead>
        <tbody>
    </table>

</div>
