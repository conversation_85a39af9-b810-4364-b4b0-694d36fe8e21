<div class="panel pt-4">
    <div class="showMessage">

    </div>
    <div class="table_filter_header pb-3">
        <h2 class="table_title">Hard Materials</h2>

        <div class="filters materials_table_filters">
            {{-- <img height="24px" width="24px" src="{{ asset('admin_assets/images/icons/help-icon.svg') }}" alt="help icon"
                data-toggle="modal" data-target="#editTableModal"> --}}
            <input type="search" placeholder="Search" name="" id="filter_search"
                class="clients_Detail_Search filter_search">
            <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox"
                style="width:84px;">
                <option value="" selected>Filter</option>
                <option value="Clear">Clear</option>
                {{-- <option value="LF">LF</option>
                <option value="Each">Each</option> --}}
            </select>
            <form method="" action="" id="hard-material-import"
                class="file_upload_button_form upload_file_wrapper w-fit" enctype="multipart/form-data">
                @csrf
                <label class="btn primaryblue primaryblue22 transparent px-5" for="import_file_data">Import Material</label>
                <input class="input_file d-none" type="file" name="file" id="import_file_data" accept=".xls, .xlsx">
            </form>
        </div>
    </div>

    <table class="table table-striped custom_datatable display mx-0 my-4 yajra-datatable" style="width:100%">

        <thead>
            <tr>
                <th>Material Name</th>
                <th>UoM</th>
                <th>Unit Cost</th>
                <th>Labor/Unit</th>
                <th>Material Image</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>




</div>
