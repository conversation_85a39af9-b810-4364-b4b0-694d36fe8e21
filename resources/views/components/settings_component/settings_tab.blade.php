<style>
    .settings-sidebar {
        width: 200px;
        background: #ffffff;
        border-right: 1px solid #e5e7eb;
        padding: 16px 0;
        font-family: 'Inter', sans-serif;
    }

    /* Section Headers */
    .sidebar-section-header {
        color: #6b7280;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 12px 16px 8px;
        margin: 0;
    }

    /* Menu List */
    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    /* Menu Items */
    .tab_link {
        padding: 0 8px;
        position: relative;
    }

    /* Menu Links */
    .sidebar-menu-link {
        display: block;
        padding: 8px 16px;
        color: #4b5563;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    /* Hover State */
    .sidebar-menu-link:hover {
        background-color: #f3f4f6;
        color: #111827;
    }

    /* Active State */
    .tab_link.active>.sidebar-menu-link {
        background-color: #E5F7FF;
        color: #4779eb;
        font-weight: 600;
        padding-left: 13px;
        border-radius: 8px;
    }

    /* Submenu Styles */
    .sidebar-submenu {
        list-style: none;
        padding-left: 16px;
        margin: 4px 0;
        display: none;
    }

    .sidebar-submenu-item {
        position: relative;
    }

    .sidebar-submenu-link {
        display: block;
        padding: 6px 16px;
        color: #4b5563;
        font-size: 13px;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .sidebar-submenu-link:hover {
        background-color: #f3f4f6;
        color: #111827;
    }

    /* Active Submenu Item */
    .sidebar-submenu-item.active>.sidebar-submenu-link {
        color: #1BB6D5;
        font-weight: 500;
        background-color: #f3f4f6;
    }

    /* Dropdown Indicator */
    .sidebar-menu-link.has-submenu {
        position: relative;
        padding-right: 32px;
    }

    .sidebar-menu-link.has-submenu::after {
        content: "›";
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%) rotate(90deg);
        transition: transform 0.2s;
        font-size: 16px;
    }

    .sidebar-menu-link.has-submenu.expanded::after {
        transform: translateY(-50%) rotate(0deg);
    }

    /* Nested Submenu Styles */
    .sidebar-subsubmenu {
        list-style: none;
        padding-left: 16px;
        margin: 4px 0;
        display: none;
    }

    .sidebar-subsubmenu-item {
        position: relative;
    }

    .sidebar-subsubmenu-link {
        display: block;
        padding: 6px 16px 6px 24px;
        color: #4b5563;
        font-size: 13px;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .sidebar-subsubmenu-link:hover {
        background-color: #f3f4f6;
        color: #111827;
    }

    /* Active Sub-submenu Item */
    .sidebar-subsubmenu-item.active>.sidebar-subsubmenu-link {
        color: #4779eb;
        font-weight: 500;
        background-color: #E5F7FF;
    }

    /* Show active submenus by default */
    .tab_link.active .sidebar-submenu,
    .sidebar-submenu-item.active .sidebar-subsubmenu {
        display: block;
    }

    /* Add this to your existing CSS */
    .settings-sidebar a,
    .settings-sidebar .sidebar-menu-link,
    .settings-sidebar .sidebar-submenu-link,
    .settings-sidebar .sidebar-subsubmenu-link {
        text-decoration: none !important;
    }

    /* Hover states should also not have underlines */
    .settings-sidebar a:hover,
    .settings-sidebar .sidebar-menu-link:hover,
    .settings-sidebar .sidebar-submenu-link:hover,
    .settings-sidebar .sidebar-subsubmenu-link:hover {
        text-decoration: none !important;
    }
</style>

<div class="settings-sidebar">
    <!-- Business Management Section -->
    <h6 class="sidebar-section-header" style="font-weight: bold; color: #111827;">Business Management</h6>
    <ul class="sidebar-menu">
        @can('general_setting')
            <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.general.settings') active @endif">
                <a href="{{ route(getRouteAlias() . '.general.settings') }}"
                   class="sidebar-menu-link">Company Settings</a>
            </li>
        @endcan
        @can('estimate_branding')
            <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.branding') active @endif">
                <a href="{{ route(getRouteAlias() . '.branding') }}" class="sidebar-menu-link">Estimate
                    Branding</a>
            </li>
        @endcan

        <li class="tab_link @if (request()->is('*accounting*')) active @endif">
            <div
                class="sidebar-menu-link has-submenu @if (request()->is('*accounting*')) expanded @endif">
                Accounting Settings</div>
            <ul class="sidebar-submenu"
                @if (request()->is('*accounting*')) style="display: block;" @endif>
                <li class="sidebar-submenu-item @if (request()->is('*invoice*')) active @endif">
                    <a class="sidebar-submenu-link">Invoice</a>
                </li>
                <li class="sidebar-submenu-item @if (request()->is('*product*') || request()->is('*service*')) active @endif">
                    <a class="sidebar-submenu-link">Product & Services</a>
                </li>
            </ul>
        </li>

        @can('default_settings')
            <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.company.default.settings') active @endif">
                <a class="sidebar-menu-link" href="{{ route(getRouteAlias() . '.company.default.settings') }}">Proposal Settings</a>
            </li>
        @endcan

        <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.division.settings') active @endif">
            <a href="{{ route(getRouteAlias() . '.division.settings') }}" class="sidebar-menu-link">Divisions Setting</a>
        </li>
        @can('users_setting')
            <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.users.list' ||
                    Route::currentRouteName() == getRouteAlias() . '.users.add' ||
                    Route::currentRouteName() == getRouteAlias() . '.users.edit' ||
                    Route::currentRouteName() == getRouteAlias() . '.users.invite') active @endif">
                <a href="{{ route(getRouteAlias() . '.users.list') }}" class="sidebar-menu-link">Team Management</a>
            </li>
        @endcan
        <li @class(['tab_link' => true, 'active' => request()->routeIs('organization.set.target')])>
            <a href="{{ route('organization.set.target') }}" class="sidebar-menu-link">Monthly Sales Target</a>
        </li>

    </ul>

    <!-- Catalog Section -->
    <h6 class="sidebar-section-header" style="font-weight: bold; color: #111827;">Catalog</h6>
    <ul class="sidebar-menu">
        <li class="tab_link @if (request()->is('*landscape*') ||
                request()->is('*enhancement*') ||
                request()->is('*maintenance*')) active @endif">
            <div class="sidebar-menu-link has-submenu @if (request()->is('*landscape*') ||
                      request()->is('*enhancement*') || request()->is('*maintenance*')) expanded @endif">Landscape</div>
            <ul class="sidebar-submenu"
                @if (request()->is('*landscape*') || request()->is('*enhancement*') || request()->is('*maintenance*')) style="display: block;" @endif>
                <!-- Enhancement Dropdown -->
                <li class="sidebar-submenu-item @if (request()->is('*enhancement*') || request()->is('*margin*') || request()->is('*material*')) active @endif">
                    <div class="sidebar-menu-link has-submenu @if (request()->is('*enhancement*') || request()->is('*margin*') || request()->is('*material*')) expanded @endif">
                        Enhancement</div>
                    <ul class="sidebar-subsubmenu"
                        @if (request()->is('*enhancement*') || request()->is('*margin*') || request()->is('*material*')) style="display: block;" @endif>
                        <li class="sidebar-subsubmenu-item @if (Route::currentRouteName() == getRouteAlias() . '.margin_setup') active @endif">
                            <a href="{{ route(getRouteAlias() . '.margin_setup') }}" class="sidebar-subsubmenu-link">Margin Settings</a>
                        </li>
                        <li class="sidebar-subsubmenu-item @if (Route::currentRouteName() == getRouteAlias() . '.material.settings') active @endif">
                            <a href="{{ route(getRouteAlias() . '.material.settings') }}"
                               class="sidebar-subsubmenu-link">Enhancement Items</a>
                        </li>
                    </ul>
                </li>

                <!-- Maintenance Setup -->
                <li class="sidebar-submenu-item @if (request()->routeIs('*maintenance*')) active @endif">
                    <div class="sidebar-menu-link has-submenu @if (request()->routeIs('*maintenance*')) expanded @endif">
                        Maintenance Setup</div>
                    <ul class="sidebar-subsubmenu"
                        @if (request()->routeIs('*maintenance*')) style="display: block;" @endif>
                        <li class="sidebar-subsubmenu-item">
                            <a class="sidebar-subsubmenu-link">GP Scale</a>
                        </li>
                        <li class="sidebar-subsubmenu-item">
                            <a class="sidebar-subsubmenu-link">Maintenance Items</a>
                        </li>
                        <li class="sidebar-subsubmenu-item">
                            <a class="sidebar-subsubmenu-link">Base Maintenance</a>
                        </li>
                        <li @class(['submenu-active' => request()->routeIs('organization.settings.maintenance.book-of-business'), 'sidebar-subsubmenu-item' => true])>
                            <a href="{{ route('organization.settings.maintenance.book-of-business') }}"
                                @class(['sidebar-subsubmenu-link' => true])>Book of Business</a>
                        </li>
                        <li class="sidebar-subsubmenu-item">
                            <a class="sidebar-subsubmenu-link">Renewal Settings</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </li>

        <!-- Snow Setup -->
        <li class="tab_link @if (Route::currentRouteName() == getRouteAlias() . '.snow.index') active @endif">
            <a href="{{ route(getRouteAlias() . '.snow.index') }}" class="sidebar-menu-link">Snow
                Setup</a>
        </li>
    </ul>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle submenus when clicking on parent items
        document.querySelectorAll('.sidebar-menu-link.has-submenu').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const submenu = this.nextElementSibling;
                const isExpanded = this.classList.toggle('expanded');

                if (isExpanded) {
                    submenu.style.display = 'block';
                } else {
                    submenu.style.display = 'none';
                }

                // Close other open menus at the same level
                const parentLi = this.closest('li');
                if (parentLi && parentLi.parentElement) {
                    parentLi.parentElement.querySelectorAll('li').forEach(
                        sibling => {
                            if (sibling !== parentLi) {
                                const siblingLink = sibling
                                    .querySelector(
                                        '.sidebar-menu-link.has-submenu'
                                    );
                                const siblingSubmenu = sibling
                                    .querySelector('ul');
                                if (siblingLink && siblingSubmenu) {
                                    siblingLink.classList.remove(
                                        'expanded');
                                    siblingSubmenu.style.display =
                                        'none';
                                }
                            }
                        });
                }
            });
        });

        // Automatically expand parent menus for active items
        document.querySelectorAll(
            '.sidebar-submenu-item.active, .sidebar-subsubmenu-item.active').forEach(
            item => {
                let parentMenu = item.closest('ul');
                while (parentMenu) {
                    parentMenu.style.display = 'block';
                    const parentLink = parentMenu.previousElementSibling;
                    if (parentLink && parentLink.classList.contains('has-submenu')) {
                        parentLink.classList.add('expanded');
                    }
                    parentMenu = parentMenu.parentElement.closest('ul');
                }
            });
    });
</script>
