<script>
    var isLogTrue = '{{ config('app.javascriptLog') }}';

    if (isLogTrue != true) {
        console.log = () => {}
        console.error = () => {}
        console.debug = () => {}
    }

    @if(auth()->check() && !auth()->user()->isAdmin())
        var orgDateFormat =  "{{ getOrgDateFormat() }}";
    @else
        var orgDateFormat =  "Y-m-d";
    @endif

    function customNumberFormat(value, decimals = 2, decimalSeparator = '.', thousandsSeparator = ',') {
        if (typeof value === 'number') {
            const roundedValue = Number(value.toFixed(decimals)).toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals,
                useGrouping: true,
                minimumIntegerDigits: 1,
            });
            return roundedValue.replace(/,/g, thousandsSeparator);
        }
        return value;
    }
</script>

<!--------------------------------- Jquery --------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"
    integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-------------------------------- Jquery Ends------------------------------->

<!--------------------------------- Popper Js ------------------------------->
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
    integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
</script>
<!--------------------------------- Popper Js Ends----------------------------->

<!--------------------------------- Bootstrap 5 cdn ---------------------------->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js" 
    integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+" 
    crossorigin="anonymous"></script>
<!------------------------------------ Bootstrap 5 cdn Ends--------------------->

<!----------------------------Date Picker Cdn Ends---------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<!----------------------------Date Picker Cdn Ends---------------------------->

<!----------------------------Jquery-Validate---------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.2/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/additional-methods.min.js"
    integrity="sha512-6S5LYNn3ZJCIm0f9L6BCerqFlQ4f5MwNKq+EthDXabtaJvg3TuFLhpno9pcm+5Ynm6jdA9xfpQoMz2fcjVMk9g=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!----------------------------Jquery-Validate---------------------------->

<!------------------------------- Custom Js ---------------------------------->
<script type="text/javascript" src="{{ asset('admin_assets/scripts/script.js') }}"></script>
<script type="text/javascript" src="{{ asset('admin_assets/scripts/validations.js') }}"></script>
<!------------------------------ Custom Js Ends --------------------->

