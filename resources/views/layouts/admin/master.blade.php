<!DOCTYPE html>
<html lang="en">

<head>

    <!----------------- Meta Tags ------------------>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="Description" CONTENT="{{ config('app.name') }} Dashboard">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!---------------------- Meta Tages End -------------------->

    <!---------------------- Bootstrap Cdn---------------------->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
          integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!------------------------ Bootstrap Cdn Ends-------------------->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.css" rel="stylesheet" />
    <!----------------------------Select2---------------------------->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!----------------------------Select2---------------------------->
    <link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.10.21/css/dataTables.bootstrap4.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">

    <!-- Calendar Css -->
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/event-calender.css') }}">
    <!-- Toaster Css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/n-style.css') }}" />
    <link rel="stylesheet" href="{{ asset('admin_assets/styles/globalStyles.css') }}" />
    <!-- Custom Css Ends-->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <link rel="icon" type="image/png" href="{{ asset('admin_assets/images/icons/favicon.svg') }}">
    <title>{{ config('app.name') }} Dashboard</title>

    <style type="text/css">
        #Notes-preview strike {
            text-decoration: line-through;
        }

        #Notes-preview u {
            text-decoration: underline;
        }

        .loader {
            z-index: 9999999999;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .loader-content {
            position: absolute;
            border: 16px solid #f3f3f3;
            border-top: 16px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            top: 40%;
            left: 50%;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .dollar {
            position: absolute;
            top: 8px;
            left: 3px;
            font-size: 14px;
        }
    </style>
    {{--@livewireStyles--}}
    @yield('styles')
    @yield('partial-styles')
</head>


<body>
<div class="dashboard_wrapper">
    <div class="content_wrapper">
        @include('layouts.admin.partials.header')
        <x-admin.navbar />
        @include('layouts.partials.flash-messages')

        @yield('section')
        <section id="loader">

            <div id="loader-content"></div>
        </section>

    </div>

</div>

<x-success-modal
    title="Task Created Successful!"
    message="Your action was successful."
    showButton="false"
    buttonText="Cancel" />


@include('layouts.admin.partials.scripts')
{{--@livewireScripts--}}
@stack('scripts')
@yield('extra-scripts')
@if (!empty(Session::get('showModal')))
    <script>
        $(function() {
            $('#requestModal').modal('show');
        });
    </script>
@endif
</body>

</html>
