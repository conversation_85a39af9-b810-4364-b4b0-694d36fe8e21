<script>
    var isLogTrue = '{{ config('app.javascriptLog') }}';

    if (isLogTrue != true) {
        console.log = () => {}
        console.error = () => {}
        console.debug = () => {}
    }

    @if(auth()->check() && !auth()->user()->isAdmin())
        var orgDateFormat =  "{{ getOrgDateFormat() }}";
    @else
        var orgDateFormat =  "Y-m-d";
    @endif

    function customNumberFormat(value, decimals = 2, decimalSeparator = '.', thousandsSeparator = ',') {
        if (typeof value === 'number') {
            const roundedValue = Number(value.toFixed(decimals)).toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals,
                useGrouping: true,
                minimumIntegerDigits: 1,
            });
            return roundedValue.replace(/,/g, thousandsSeparator);
        }
        return value;
    }
</script>


<!-- Removed duplicate jQuery slim -->
<!-- Removed old Popper.js -->
<!-- Removed old Bootstrap 4.0.0 -->

<!--------------------------------- Jquery --------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js"
    integrity="sha512-pumBsjNRGGqkPzKHndZMaAG+bir374sORyzM3uulLV14lN5LyykqNk8eEeUlUkB3U0M4FApyaHraT65ihJhDpQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-------------------------------- Jquery Ends------------------------------->

<!-- Popper.js (required for Bootstrap 4.0.0 dropdowns and tooltips) -->
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.12.9/dist/umd/popper.min.js" 
    integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" 
    crossorigin="anonymous"></script>

<!-- Bootstrap 4.6.2 JS (Updated for consistency) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js" 
    integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+" 
    crossorigin="anonymous"></script>

<!-- Bootstrap Datepicker -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/js/bootstrap-datepicker.min.js"></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!----------------------------Jquery-Validate---------------------------->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/additional-methods.min.js"
    integrity="sha512-6S5LYNn3ZJCIm0f9L6BCerqFlQ4f5MwNKq+EthDXabtaJvg3TuFLhpno9pcm+5Ynm6jdA9xfpQoMz2fcjVMk9g=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<!----------------------------Jquery-Validate---------------------------->

<!-- DataTables -->
<script src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/dataTables.bootstrap4.min.js"></script>

<!-- SmartWizard (only load if needed) -->
@stack('smartwizard-scripts')

<!-- Dropzone -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
<!-- <script>
    $(document).on('click', '.pagination a', function(event) {
        event.preventDefault()
        const page = $(this).attr('href').split('page=')[1]

        manipulateTable(page)
    })
</script> -->
<!-- Summernote -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs4.min.js"></script>

<!------------------------------- Calendar js ---------------------------------->
<script src="{{ asset('admin_assets/scripts/index.global.js') }}"></script>

<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>


<!------------------------------- Custom Js ---------------------------------->
<script type="text/javascript" src="{{ asset('admin_assets/scripts/script.js') }}"></script>
{{-- <script type="text/javascript" src="{{ asset('admin_assets/scripts/n-script.js') }}"></script> --}}
<script type="text/javascript" src="{{ asset('admin_assets/scripts/validations.js') }}"></script>

<!-- TinyMCE (only load if needed) -->
@stack('tinymce-scripts')

<!------------------------------ Custom Js Ends --------------------->
<script type="text/javascript">
    $(document).ready(function() {
        toastr.options = {
            "positionClass": "toast-top-right",
            "timeOut": "5000",
            "closeButton": true
        }
    });

    /*------------------------------------------
    --------------------------------------------
    Add Loading When fire Ajax Request
    --------------------------------------------
    --------------------------------------------*/
    $(document).ajaxStart(function() {
        $('#loader').addClass('loader');
        $('#loader-content').addClass('loader-content');
    });

    /*------------------------------------------
    --------------------------------------------
    Remove loader When fire Ajax Request
    --------------------------------------------
    --------------------------------------------*/
    $(document).ajaxStop(function() {
        $('#loader').removeClass('loader');
        $('#loader-content').removeClass('loader-content');
    });
</script>
