<style>
    @media (max-width: 547px) {
        .dashboard_header_title2 {
            width: 40%;
        }
        .dashboard_header_title2 img {
            width: 72px !important;
        }
        .aside_toggle2:focus{
            box-shadow: none;
            outline: none
        }

    }
</style>
<header class="dashboard_header d-flex">

    <div class="dashboard_header_title d-flex dashboard_header_title2" style=" ">

        <img loading="lazy" class="rounded" style="margin-top: 5px !important;" height="30%" width="25%"
             src="{{ asset('admin_assets/images/updated-logo.png') }}" alt="total clients"></div>

    <div class="header_items">
        @canany(['general_setting', 'account_setting', 'estimate_branding', 'margin_setting', 'material_setting',
            'users_setting', 'target_setting'])
            @php
                $routeLink = route(getRouteAlias() . '.general.settings');
                if (
                    auth('web')
                        ->user()
                        ->can('general_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.general.settings');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('account_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.account.settings');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('estimate_branding')
                ) {
                    $routeLink = route(getRouteAlias() . '.branding');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('margin_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.margin_setup');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('material_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.material.settings');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('users_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.users.list');
                } elseif (
                    auth('web')
                        ->user()
                        ->can('target_setting')
                ) {
                    $routeLink = route(getRouteAlias() . '.branding');
                }
            @endphp

            <a href="{{ $routeLink }}">
                <img src="{{ asset('admin_assets/images/settings-02.png')}}" height="20" width="20"  alt="Setting Icon">
            </a>
            <img src="{{ asset('admin_assets/images/bell-icon.png')}}" height="20" width="20" alt="Bell Icon">

        @endcanany
        <img src="{{ asset('admin_assets/images/Divider.png')}}" height="30" width="3"   alt="Setting Icon">

        <!-- Ensure you include Bootstrap CSS and JS in your HTML -->

        <div class="dropdown">
            <!-- Dropdown trigger -->
            <div class=" d-flex align-items-center" type="button" id="userDropdown" data-toggle="dropdown" aria-expanded="false">
                @if (auth('web')->check())
                    <div class="image">
                        @if (auth()->user()->isEmployee())
                            <img class="rounded-circle" height="30" width="30"
                                 src="{{ auth()->user()->image ? asset('storage/user_images/' . auth()->user()->image) : asset('admin_assets/images/dummy_image.webp') }}"
                                 alt="user image">
                        @else
                            <img class="rounded-circle" height="30" width="30"
                                 src="{{ auth()->user()->profile_photo_path ? asset('storage/user_images/' . auth()->user()->profile_photo_path) : asset('admin_assets/images/dummy_image.webp') }}"
                                 alt="user image">
                        @endif
                    </div>
                @endif
            </div>

            <!-- Dropdown menu -->
            <ul class="dropdown-menu" aria-labelledby="userDropdown">
                @auth
                    <li><a class="dropdown-item" href="{{ route(getRouteAlias() . '.changePasswordScreen') }}">Change Password</a></li>
                    <li><a class="dropdown-item" href="{{ route('web.logout') }}"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>
                        <form id="logout-form" action="{{ route('web.logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    </li>
                @endauth
            </ul>
        </div>

        {{-- <div class="profile_info_dropdown">
            <div class="profile_info dropdown-toggle arrow" type="button" id="userDropdown" data-toggle="dropdown"
                aria-expanded="false">

                @if (auth('web')->check())
                    <div class="image">
                        @if (auth()->user()->isEmployee())
                            <img class="rounded" height="38px" width="38px"
                                src="{{ auth()->user()->image ? asset('storage/user_images/' . auth()->user()->image) : asset('admin_assets/images/dummy_image.webp') }}"
                                alt="user image">
                        @else
                            <img class="rounded" height="38px" width="38px"
                                src="{{ auth()->user()->profile_photo_path ? asset('storage/user_images/' . auth()->user()->profile_photo_path) : asset('admin_assets/images/dummy_image.webp') }}"
                                alt="user image">
                        @endif
                    </div>
                    <div class="info">
                        <h2 class="welcome_status">Welcome</h2>
                        <h3 class="profile_name">
                            {{ auth()->user()->isOrganization()? auth()->user()->company_name: auth()->user()->name }}
                        </h3>
                    </div>
                @endif

            </div>


            <ul class="dropdown-menu" aria-labelledby="userDropdown">
                @auth
                    <li><a class="dropdown-item" href="{{ route(getRouteAlias() . '.changePasswordScreen') }}">Change
                            Password</a>
                    </li>
                    <li><a class="dropdown-item" href="{{ route('web.logout') }}"
                            onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>

                        <form id="logout-form" action="{{ route('web.logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    </li>
                @endauth
            </ul>


        </div> --}}

        <!-- <i class="aside_toggle fa fa-bars"></i> -->
        <button class="navbar-toggler aside_toggle2 d-inline d-lg-none" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <i class="aside_toggle fa fa-bars" style="color: white !important;"></i>
        </button>
    </div>
</header>


