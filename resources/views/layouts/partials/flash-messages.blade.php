@if ($message = Session::get('success'))
    <div class="alert alert-success alert-dismissible fade show" style="margin-top: 20px !important;">
        {{ $message }}
        <!-- <button type="button" class="btn-close" data-dismiss="alert"></button> -->
    </div>
@endif


@if ($message = Session::get('error'))
    <div class="alert alert-danger alert-dismissible fade show" style="margin-top: 20px !important;">
        {{ $message }}
        <!-- <button type="button" class="close" data-dismiss="alert"></button> -->
    </div>
@endif


@if ($message = Session::get('warning'))
    <div class="alert alert-warning alert-dismissible fade show" style="margin-top: 20px !important;">
        {{ $message }}
        <!-- <button type="button" class="close" data-dismiss="alert">×</button> -->
    </div>
@endif


@if ($message = Session::get('info'))
    <div class="alert alert-info alert-dismissible fade show" style="margin-top: 20px !important;">
        <!-- <button type="button" class="close" data-dismiss="alert">×</button> -->
        {{ $message }}
    </div>
@endif
<script>
    // Automatically hide alert after 5 seconds (5000 milliseconds)
    setTimeout(function() {
        var alert = document.querySelectorAll('.alert');
        alert.forEach(function(element) {
            var bootstrapAlert = new bootstrap.Alert(element); // Use Bootstrap 5 alert
            bootstrapAlert.close(); // Hide the alert
        });
    }, 5000); // 5000 milliseconds = 5 seconds
</script>
