@extends('layouts.admin.master')
@section('title', 'Companies')
@section('section')
    <section class="dashboard_main">

        <section class="companies_table_filters">
            <div class="table_filter_header mb-4">
                <h2 class="sub_heading">Companies</h2>

                <div class="filters ">
                    <input type="search" placeholder="Search" name="" id="filter_search" class="filter_search">
                    <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox"
                        style="width:84px;">
                        <option value="" selected>Filter</option>
                        <option value="Clear">Clear</option>
                        <option value="Active">Active</option>
                        <option value="Expired">Expired</option>
                    </select>
                </div>
            </div>

            <div class="table_tabs_filter mb-4" role="group">
                <input type="radio" class="btn-check all" name="companies" id="All" value=''
                    autocomplete="off" checked>
                <label class="btn btn-tab" for="All">All</label>

                <input type="radio" class="btn-check" name="companies" id="Premium" value='Premium' autocomplete="off">
                <label class="btn btn-tab" for="Premium">Premium</label>

                <input type="radio" class="btn-check" name="companies" id="Basic" value='Basic' autocomplete="off">
                <label class="btn btn-tab" for="Basic">Basic</label>
            </div>
        </section>

        <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">

            <thead>
                <tr>
                    <th>Company Name</th>
                    <th>Address</th>
                    <th>Email</th>
                    <th>Package</th>
                    <th>Expiry Date</th>
                    <th>Package Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </section>


    <!-- Assign OM -->
    <div class="operation_modal modal fade" id="extendTrial" tabindex="-1" aria-labelledby="extendTrialLabel"
        aria-hidden="true">
        <div class="modal-dialog m400">
            <div class="modal-content">
                <form id="extendTrialForm" action="" method="POST">
                    @method('POST')
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="extendTrialLabel">Extend Trial</h5>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <input type="date"  name="date" id="dateManager"
                            class="input form-control  w-100" required>

                    </div>
                    <div class="modal-footer pt-5">
                        <button type="submit" class="btn primaryblue w-100 selectManager">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @push('scripts')
        @include('admin.company.script')
    @endpush
@endsection
