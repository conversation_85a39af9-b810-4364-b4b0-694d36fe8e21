@extends('layouts.admin.master')
@section('title', 'Home')
@section('section')
    <section class="dashboard_main companies_table_filters">
        <h2 class="sub_heading">Overview</h2>

        <div class="dashboard_stat_cards mt-4 pt-2">

            <div class="state_card">
                <div class="info">
                    <h2 class="card_title">Total Companies</h2>
                    <!-- if super admin -->
                    <!-- <h2 class="card_title">Total Companies</h2> -->
                    <p class="card_count mt-4">{{ $totalCompanies ?? 0 }}</p>
                </div>
                <div>
                    <img loading="lazy" class="rounded" height="48px" width="48px"
                         src="{{ asset('admin_assets/images/icons/total-clients.png') }}" alt="total clients">
                </div>
            </div>

            <div class="state_card">
                <div class="info">
                    <h2 class="card_title">Active Companies</h2>
                    <!-- if super admin -->
                    <!-- <h2 class="card_title">Active Companies</h2> -->
                    <p class="card_count mt-4">{{ $activeCompanies ?? 0 }}</p>
                </div>
                <div>
                    <img loading="lazy" class="rounded" height="48px" width="48px"
                         src="{{ asset('admin_assets/images/icons/estimate-requests.png') }}" alt="total clients">
                </div>
            </div>

            <div class="state_card">
                <div class="info">
                    <h2 class="card_title">Total Earnings</h2>
                    <!-- if super admin -->
                    <!-- <h2 class="card_title">Total Earning</h2> -->
                    <p class="card_count mt-4">${{ $earnings ?? 0 }}</p>
                </div>
                <div>
                    <img loading="lazy" class="rounded" height="48px" width="48px"
                         src="{{ asset('admin_assets/images/icons/estimates.png') }}" alt="total clients">
                </div>
            </div>

            <div class="state_card">
                <div class="info">
                    <h2 class="card_title">Last Month Actives</h2>
                    <!-- if super admin -->
                    <!-- <h2 class="card_title">Last Month Actives </h2> -->
                    <p class="card_count mt-4">{{ $lastMonthActives ?? 0 }}</p>
                </div>
                <div>
                    <img loading="lazy" class="rounded" height="48px" width="48px"
                         src="{{ asset('admin_assets/images/icons/proposals.png') }}" alt="total clients">
                </div>
            </div>

        </div>

        <div class="dashboard_sales_graphs">
            <div class="card_wrapper chart_wrapper">
                <div class="chart_header mb-5">
                    <div class="info">
                        <p class="value">New Active Companies</p>
                    </div>

                    <select class="selectBox form-control arrow no-border" name="" id="" onchange="getSelectedValue(this);">
                        <option value="week" selected>Week</option>
                        <option value="month">Month</option>
                        <option value="year">Year</option>
                    </select>
                </div>

                <canvas id="waveform-chart" style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>
            </div>

            <div class="card_wrapper chart_wrapper">
                <div class="chart_header mb-5">
                    <div class="info">
                        <p class="value">Earning</p>
                    </div>

                    <select class="selectBox form-control arrow no-border" onchange="getval(this);" name="" id="">
                        <option value="week" selected> Week</option>
                        <option value="month">Month</option>
                        <option value="year">Year</option>
                    </select>
                </div>

                <canvas id="myChart3" style="width:100%;object-fit:contain;max-height:300px;height:100%"></canvas>

            </div>

        </div>

        <!-- If Super Admin Then Show This -->
        <div class="table_filter_header mt-4 mb-4">
            <h2 class="sub_heading">Recently Joined companies</h2>

            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                       class="clients_Detail_Search filter_search filter_search">
                <select name="" id="select_filter" class="select-small basic-single-select" style="width:84px;">
                    <option value="" selected>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="today">Today</option>
                    <option value="last_week">Last Week</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="custom_datatable display mt-4 yajra-datatable" style="width:100%">
                <thead>
                <tr>
                    <th>Company Name</th>
                    <th>Address</th>
                    <th>Email</th>
                    <th>Active Date</th>
                    <th>Expiry Date</th>
                    <th>Status</th>
                </tr>
                </thead>
                <tbody>
                </tbody>

            </table>
        </div>
        <!-- If Super Admin Then Show This -->
    </section>

    @push('scripts')
        <script type="text/javascript">
            var salesLostFilter = 'week';
            var barColors = ["#109CF1"];
            var waveformChart = null;
            var chart3 = null;
            var planName = '';
            var status = '';
            $(document).ready(function() {
                
                // Fallback to hide loader after page is fully loaded
                setTimeout(function() {
                    $('#loader').removeClass('loader');
                    $('#loader-content').removeClass('loader-content');
                }, 5000);

                var companies_table = $('.yajra-datatable').DataTable({
                    processing: true,
                    responsive: true,
                    // scrollX: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route(getRouteAlias() . '.company.list') }}",
                        data: function(d) {
                            d.planName = planName,
                                d.status = status
                        }
                    },
                    columns: [{
                        data: 'company_name',
                        name: 'company_name',
                        orderable: false
                    }, {
                        data: 'address',
                        name: 'address',
                        orderable: false
                    }, {
                        data: 'email',
                        name: 'email',
                        orderable: false
                    }, {
                        data: 'start_at',
                        name: 'start_at',
                        orderable: false
                    },
                        {
                            data: 'ends_at',
                            name: 'ends_at',
                            orderable: false
                        }, {
                            data: 'status',
                            name: 'status',
                            orderable: false
                        }


                    ],
                    language: {
                        zeroRecords: "Sorry we could not find any results",
                        paginate: {
                            "previous": "<i class='fa fa-angle-left' aria-hidden='true'></i>",
                            "next": "<i class='fa fa-angle-right' aria-hidden='true'></i>"
                        },


                    },
                    dom: '<"top">rt<"bottom"lip><"clear">',
                });

                // Custome Search Filter
                $('.companies_table_filters #filter_search').on('keyup', function() {
                    companies_table.search(this.value).draw();
                });

                // Custom Select Filter
                $('.companies_table_filters #select_filter').on('change', function() {
                    var selectedValue = $(this).val();
                    if (selectedValue == "Clear") {
                        resetFilters();
                    } else {
                        status = selectedValue;
                        companies_table.columns().search('').draw();
                    }
                });

                // Custom Radio Buttons Filter
                $('.companies_table_filters .table_tabs_filter input[name="companies"]').on('click', function() {
                    planName = $(this).val();
                    companies_table.search('').columns().search('').draw();
                });

                // Reset all filters
                function resetFilters() {
                    $('.companies_table_filters #filter_search').val('');
                    $('.companies_table_filters #select_filter').val('');
                    $('.companies_table_filters .table_tabs_filter input.all').prop('checked', true)
                    planName = '';
                    status = ''
                    companies_table.search('').columns().search('').draw();
                }


            });

            function ajaxCAll(url, filter) {

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        filter: salesLostFilter,
                        "_token": "{{ csrf_token() }}",
                    },

                    success: function(chartData) {
                        if (chartData.labels && chartData.data) {

                            if (url === '{{ route(getRouteAlias() . '.waveChartData') }}') {
                                waveChart(chartData.labels, chartData.data);
                            }
                            if (url === '{{ route(getRouteAlias() . '.chartData') }}') {
                                renderChart(chartData.labels, chartData.data);
                            }

                        }
                        
                        // Track completion
                        checkAllLoaded();
                    },
                    error: function(response) {
                        console.error('AJAX Error:', response);
                        // Handle specific error cases
                        if (response.responseJSON && response.responseJSON.errors) {
                            $.each(response.responseJSON.errors, function(field_name, error) {
                                console.error(field_name + ':', error);
                            });
                        }
                    }
                })

            }
            // Execute chart data calls with proper completion tracking
            var chartCallsCompleted = 0;
            var totalChartCalls = 2;
            
            function checkAllLoaded() {
                chartCallsCompleted++;
                if (chartCallsCompleted >= totalChartCalls) {
                    // All chart calls completed, ensure loader is hidden
                    setTimeout(function() {
                        $('#loader').removeClass('loader');
                        $('#loader-content').removeClass('loader-content');
                    }, 500);
                }
            }
            
            ajaxCAll('{{ route(getRouteAlias() . '.waveChartData') }}', salesLostFilter);
            ajaxCAll('{{ route(getRouteAlias() . '.chartData') }}', salesLostFilter);


            function waveChart(labels, data) {
                if (waveformChart) {
                    waveformChart.destroy(); // Destroy the existing waveform chart instance if it exists
                }
                waveformChart = new Chart('waveform-chart', {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            borderColor: "#109CF1",
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: "white",
                            pointBorderColor: "#FE9B6A",
                            pointBorderWidth: 2,
                            pointRadius: [0, 3, 3, 3, 3, 0, 0],
                            spanGaps: true, // add gap for last data point
                        }]
                    },
                    options: {
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                ticks: {
                                    beginAtZero: false,
                                    stepSize: 100,
                                    // min: 100,
                                    // max: 300,
                                    callback: function(value, index, values) {
                                        return value;
                                    },
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                },
                                grid: {
                                    display: true,
                                },
                                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
                            },
                            x: {
                                grid: {
                                    display: false,
                                    //   borderDash: [10, 5],
                                    //   borderWidth: 1.02,
                                    borderColor: '#E0E0E0'
                                },
                            }
                        }
                    }
                });
            }

            function renderChart(labels, data) {
                if (chart3) {
                    chart3.destroy(); // Destroy the existing bar chart instance if it exists
                }
                chart3 = new Chart("myChart3", {
                    type: "line",
                    data: {
                        labels: labels,
                        datasets: [{
                            // data: yValues,

                            borderRadius: 10 // set border radius to 10
                        }, {
                            type: "bar",
                            data: data,
                            borderRadius: 6,
                            barThickness: 25,
                            borderColor: "#1C7FD7", // change border color to a darker shade of blue
                            borderWidth: 0,
                            fill: true,
                            pointRadius: 0,
                            backgroundColor: barColors,

                        }]
                    },
                    options: {
                        layout: {
                            padding: {
                                top: 0,
                                left: 0,
                                bottom: 0,
                                right: 0
                            },

                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false, // remove x-axis line
                                    clip: false
                                }

                            },
                            y: {
                                ticks: {
                                    beginAtZero: false,
                                    stepSize: 100,
                                    // min: 100,
                                    // max: 300,

                                    callback: function(value, index, values) {
                                        return '$' + value;
                                    },
                                    font: {
                                        size: 12,
                                        color: '#90A0B7',
                                        family: 'Poppins',
                                    }
                                },
                                grid: {
                                    display: true,
                                },
                                borderColor: 'rgba(0, 0, 0, 0)', // Remove y-axis lines
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: false,
                                text: "World Wine Production 2018"
                            },
                        }
                    }
                });
            }

            function getSelectedValue(object) {
                salesLostFilter = object.value;
                ajaxCAll('{{ route(getRouteAlias() . '.waveChartData') }}', salesLostFilter);
            }

            function getval(object) {
                salesLostFilter = object.value;
                ajaxCAll('{{ route(getRouteAlias() . '.chartData') }}', salesLostFilter);
            }
        </script>
    @endpush
@endsection
