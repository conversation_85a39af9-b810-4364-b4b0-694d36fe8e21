@extends('layouts.admin.master')
@section('title', 'Invoice')
@section('styles')
    <style>
        textarea[readonly] {
            background-color: #f6f6f6 !important;
            border: 1px solid #e7e7e7 !important;
            opacity: 1 !important;
        }
    </style>
    <style>
    .select2-container--default{
        margin-top: 21px !important;
        margin-right: 7px !important;
    }
    .table_filter_dropdown{
        margin-top: -45px !important;
    }
    .dropdown-toggle::after {
    display: none !important;

}
.propert-modal .modal-content {
    border-radius: 8px;
    padding: 0px !important;
}

.hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {

        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
a{
    text-decoration: none !important;
}
.invoice-warning{
    background-color: #E6F1FB !important;
    color: #0074D9 !important;
}
.invoice-approved{
    background-color: #D3F6ED !important;
    color: #04C295 !important;
}
.hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {

        .hidemodelbtn {
            font-size: 15px !important;
            color: #7e8a9d !important;
        }
    }
</style>
@endsection
@section('section')
    <section class="dashboard_main pb-5 invoiceTable">

        <div class="table_filter_header mb-4 invoiceListing">
            <h2 class="sub_heading">Invoice</h2>

            <div class="d-flex align-items-center gap-3">
                <div class="filters">
                    <input type="search" placeholder="Search" name="" id=""
                        class="invoice_search filter_search">
                    <select name="" id="invoice_filter" class="select-small basic-single-select">
                        <option value="" selected>Clear</option>
                        <option value="due">Due</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>
                @can('create_invoice')
                   @if (optional($Organization->stripeAccount)?->acc_connected == 1 &&
                            optional($Organization->stripeAccount)?->acc_connected)
                        <a type="button" style="color: white" data-toggle="modal" data-target="#selectClientModal"
                            class="btn primaryblue text-decoration-none">+ Create New
                            Invoice</a>
                     @else
                    <a type="button" style="color: white" data-toggle="modal" data-target="#selectClientModal"
                            class="btn primaryblue text-decoration-none">+ Create New
                            Invoice</a>
{{--                     <a style="color: white" href="{{ route(getRouteAlias() . '.account.settings', 'acc_connect=null') }}"--}}
{{--                            class="btn primaryblue text-decoration-none">+ Create New--}}
{{--                            Invoice</a>--}}
                    @endif
                @endcan
            </div>
        </div>
        <div class="table-responsive">
            <table id="Invoice_Detail" class="table table-striped custom_datatable display yajra-datatable"
                style="width:100%">
                <thead>
                    <tr>
                        <th>Invoice</th>
                        <th>Client</th>
                        <th>Project</th>
                        <th>Date</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="invoices-table-body">

                </tbody>

            </table>
        </div>


    </section>

    <!-- Collect Payment Modal -->
    <div class="modal fade propert-modal" id="collectPaymentModal" data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="collectPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="collectPaymentModalLabel"><b>Collect Payment</b></h1>
                    <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D; margin-top: 11px;" aria-hidden="true"></i>
                </button>
                    <!-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> -->
                </div>
                <div class="modal-body">
                    <form id="collectPaymentForm">
                        <div class="body-wraped">
                            <label for="price" class="modal-label">Amount</label>
                            <div class="filed-wraper position-relative">
                                <input class="input form-control Payableprice" type="text" id="price" name="amount"
                                    placeholder="Enter amount" readonly value="">
                                <span class="dollar">$</span>
                            </div>

                            <div class="filed-wraper">
                                <label for="price" class="modal-label">Payment Method</label>
                                <select name="payment_method" id=""
                                    class="input basic-single-select modal-field payment_method">
                                    <option value="" disabled>Select method</option>
                                    <option value="Cash" selected>Cash</option>
                                    <option value="Check">Check</option>
                                    <option value="Credit card">Credit card</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="Money order">Money order</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>

                            <div class="filed-wraper custom-appending date1">
                                <label for="price" class="modal-label">Transaction Date</label>
                                <input id="customDateAppend" type="text" name="transaction_date" value=""
                                    placeholder="Select date" class="input form-control datepicker-icon">
                            </div>

                            <div class="filed-wraper d-none additional_information">
                                <label for="price" class="modal-label additional_information_label">Check number</label>
                                <input class="input form-control additional_information_value " type="text"
                                    placeholder="Enter check number" name="additional_information">
                            </div>

                            <h2 for="name" class="modal-sub-heading" style="margin-top: 26px;">Additional details</h2>
                            {{-- <p class="rejection-detail">Before you reject, Please let us know the reason job was lost.</p> --}}
                            <div class="filed-wraper">
                                <textarea class="modal-textarea form-control" name="notes" id="reasonChange"
                                    placeholder="Anything you want to share? (Optional)" style="min-height: 207px;"></textarea>
                            </div>

                            <div class="balance d-flex align-items-center" style="gap: 42px;margin-top:12px;">
                                <label class="balance-title">Invoice balance</label>
                                <p class="invoice-balance"></p>
                            </div>

                            <div class="balance d-flex align-items-center account_balance" style="gap: 34px;">
                                <label class="balance-title">Account Balance</label>
                                <p class="total-account-balance"></p>
                            </div>
                            <button type="submit" class="add-btn " style="margin-top:10px;">Collect</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>


    <!-- =Select CLient Modal -->
    <div class="modal fade propert-modal select-client" id="selectClientModal" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="selectClientModalLabel">Contacts</h1>
                    <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D; margin-top: 13px" aria-hidden="true"></i>
                </button>
                </div>
                <div class="modal-body clients_list_for_invoices">

                    <div class="table_filter_header mb-4 d-flex align-items-center flex-row" style="flex-wrap: unset;">
                        <h2 class="generate-invoice">Generate invoice </h2>
                        <div class="filters d-flex align-items-center" style="flex-wrap: unset;">
                            <input type="search" placeholder="Search" name="" id=""
                                class="client_search filter_search">
                            @can('add_client')
                                <!-- <a href="{{ route(getRouteAlias() . '.create.client') }}" class="btn primaryblue"
                                    style="white-space: nowrap;">+ Add New Client</a> -->
                            @endcan
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="clients_Detail" class="table table-striped custom_datatable display client-table  "
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone Number</th>
                                    <th class="text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($clients as $client)
                                    <tr>
                                        <td class="d-flex align-items-center gap-2">
                                            <!-- <img width="24" height="24"
                                                src=" {{ $client->image ? asset('storage/client_images/' . $client->image) : asset('admin_assets/images/icons/profile-avatar.svg') }}"
                                                alt="profile picture"> -->
                                            <span class="profile-name">{{ $client?->first_name }} {{ $client?->last_name }}</span>
                                        </td>
                                        <td class="profile-email">{{ $client?->email }}</td>
                                        <td>{{ $client?->phone_number }}</td>

                                        <td>
                                            <a href="{{ route(getRouteAlias() . '.invoices.create', encodeId($client->id)) }}"
                                                class="text-decoration-none">
                                                <span class="client-status select">Select</span>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach


                            </tbody>
                        </table>
                        <div class="no-record-found text-center" style="display: none;font-size:14px">Sorry we could not
                            find any results
                        </div>

                    </div>


                </div>

            </div>
        </div>
    </div>

    <!--Delete Modal -->
    <div class="modal-small Delete-modal modal fade" id="DeleteInvoiceModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="DeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="padding: 0px;border-radius:0px">
                <div class="modal-header" style="background: #f7f1f1;padding: 18px;">
                    <h5 class="modal-title fw-bold">Delete Invoice?</h5>
                    <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
                    <!-- <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button> -->
                </div>
                <div class="modal-body" style="padding: 18px;">
                    <div class="mb-4">
                        <p style="color:#231e1e">By deleting this invoice:</p>
                        <ul class="mt-4">
                            <li style="list-style: disc;font-size:14px">Its total will be removed from your client's
                                balance</li>
                            <li style="list-style: disc;font-size:14px">It will be removed from reports, which could skew
                                your accounting
                            </li>
                        </ul>
                    </div>

                    <div class="modal-footer border-0 p-0">
                        <button type="button" class="btn primaryblue transparent  mt-5 "
                            data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn primaryblue  mt-5 confirmDeleteBtn">Delete Invoice</button>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!--Delete Modal -->
    @push('scripts')
        @include('organization.invoices.script')
        <script>
            var datepickerInstance;
            $(document).ready(function() {
                datepickerInstance = $("#customDateAppend").datepicker({
                    container: ".custom-appending",
                    // format: "{{ getOrgDateFormat() }}", // Correct format tokens
                    // Other options for the datepicker
                }).data('datepicker');

                // Attach an event listener to the changeDate event
                $("#customDateAppend").on("changeDate", function() {
                    datepickerInstance.hide(); // Hide the datepicker
                });
            });
        </script>
    @endpush






@endsection
