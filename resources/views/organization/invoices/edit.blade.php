@extends('layouts.admin.master')
@section('title', 'Invoice')
@section('section')
    <style>
        @media(max-width:390px) {
            .subtotal-main .d-flex {
                flex-wrap: wrap;
                gap: 8px
            }
        }

        .product-selector {
            position: absolute;
            right: 0px;
            left: 0;
            top: 94%;
            /* transform:translateY(-50%); */
            border: 1px solid var(--bordercolor);
            z-index: 1;
            background: #FFF;
            appearance: none;
            padding: 0;
            display: none;
            border-radius: 0 0 4px 4px;
        }

        .li-option {
            padding: 5px 15px;
            cursor: pointer;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #192a3e;
        }

        .li-option:hover {
            background-color: var(--primaryblue);
            color: var(--lightgray);
        }

        .dollar {
            position: absolute;
            top: 8px;
            left: 3px;
            font-size: 14px;
        }
        .dz-preview .dz-image-edit img{
    width: 65px !important;
}
.dz-preview .dz-image-edit img{
    width: 65px !important;
}
/* .dz-preview{
    display: none !important;
}
.dz-started .dz-preview
    {
        display: inline-block !important;
    } */

    .preview-file-choose{

gap: 0px;
border-radius: 8px;
border: 1px solid #E4E4E4;
opacity: 0px;
text-align: center;
display: none;
}
.dz-started .preview-file-choose{
    display: inline !important;

gap: 0px;
border-radius: 8px !important;
border: 1px solid #E4E4E4  !important;

text-align: center !important;
}
.dz-preview{
    /* display: none !important; */
    min-height: 110px;
    width: 81px;
    /* margin: 0px 0px 0px 12px !important; */

}
.dz-started .dz-preview
    {
        display: inline-block !important;
    }
    .dropzone_library_customize{
        /* display: flex; */
    }
    .dz-filename span{
        /* display: none; */
    }
    </style>
    <section class="dashboard_main pb-5">
        {{-- <h2 class="sub_heading">Estimate Requests</h2> --}}

        <div class="panel mt-4">
            <div class="panel_header justify-content-start gap-3">
                <h2 class="panel_title">Contact Information</h2>
                @canany(['client_listing', 'add_client', 'edit_client', 'client_detail'])
                    <!-- <a href="{{ route(getRouteAlias() . '.client.detail', encodeId($client->id)) }}" class="view-request-btn">View
                        client profile</a> -->
                @endcanany
            </div>

            <div class="row ">

                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">
                        <label for="" class="label">Contact Name</label>
                        <p class="text">{{ $client?->first_name }} {{ $client?->last_name }} </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">
                        <label for="" class="label">Title</label>
                        <p class="text">{{ $client?->title }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">
                        <label for="" class="label">Website</label>
                        <p class="text">{{ $client?->accountget?->website ?? '-----' }}</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">
                        <label for="" class="label">Company Name</label>
                        <p class="text">{{ $client?->accountget?->company_name ?? '-----' }}</p>
                        <!-- @if ($client?->alternate_no)
                            <p class="text">{{ $client?->alternate_no }} <span>(Alternate)</span></p>
                        @endif -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">
                        <label for="" class="label">Email</label>
                        <p class="text">{{ $client?->email }}</p>
                        <!-- @if ($client?->alternate_email)
                            <p class="text">{{ $client?->alternate_email }} <span>(Alternate)</span></p>
                        @endif -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                    <div class="detail_info">

                        <label for="" class="label">Phone Number </label>
                        <p class="text">{{ $client?->phone_number }} </p>

                    </div>
                </div>

                @if ($invoice->operation_id)
                    <div class="col-lg-3 col-md-4 col-sm-6 mt-5">
                        <div class="detail_info">
                            <label for="" class="label">Po Number </label>
                            <p class="text">{{ $costSummary?->po_number ?? '---' }} </p>
                        </div>
                    </div>
                @endif

            </div>
        </div>

        <form id="edit-Client-Invoice" enctype="multipart/form-data">
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Invoice information</h2>
                </div>
                <div class="alert creation-error alert-danger alert-dismissible fade">
                </div>
                <div class="panel_fields mt-4">

                    <div class="field">
                        <label class="label">Project Name</label>
                        <div class="absolute-error position-relative">
                            <input type="text" id="infoSubject" placeholder="Enter name here" name="subject"
                                class="input form-control" value="{{ $invoice?->subject }}">
                        </div>
                    </div>

                    <div class="field position-relative">
                        <label class="label">Issued Date</label>
                        <input type="text" id="issueDate" name="issue_date"
                            value="{{ $invoice?->issue_date }}"
                            placeholder="Select date" class="input form-control datepicker-icon" readonly
                            style="border: 1px solid #90a0b7;background-color:white;">
                    </div>

                    <div class="field">
                        <label class="label">Due Date</label>
                        <div class="absolute-error position-relative" style="height: 100%;">
                            <select id="dateSelect" class="input basic-single-select">
                                <option value="disabled" selected disabled>Select date</option>
                                <option value="net15">Net 15</option>
                                <option value="net30">Net 30</option>
                                <option value="net45">Net 45</option>
                                <option value="custom" data-provide="datepicker">Custom Date</option>
                            </select>
                            <!-- Hidden input field for the datepicker to attach to -->
                            <input type="text" name="due_date" id="customDateInput" style="display: none;"
                                value="{{ $invoice->due_date }}">
                        </div>
                    </div>


                </div>

            </div>

            <div class="panel mt-4" style="padding-bottom: 50px;">
                @if (!$invoice->operation_id)
                    <div class="panel_header">
                        <h2 class="panel_title">Update Invoice</h2>
                    </div>

                    <div class="panel_fields panel_invoice_fields mt-5">
                        <div class="field">
                            <label class="label">Product/Service name</label>
                            <div class="absolute-error position-relative blue-element">
                                <input id="productService-name" type="text" placeholder="Enter name here"
                                    name="product_name" class="input form-control product_name" required>
                                <ul class="product-selector" id="productSelector">
                                    @foreach ($invoiceItems as $item)
                                        <li class="li-option" data-id="{{ encodeID($item->id) }}">{{ $item->name }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>



                        <div class="field">
                            <label class="label">Quantity</label>
                            <div class="absolute-error position-relative">
                                <input type="number" class="input form-control quantity" name="quantity"
                                    id="quantitySelect" placeholder="Enter quantity" min="1">
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Unit Price</label>
                            <div class="absolute-error position-relative">
                                <input id="unitPrice" type="number" placeholder="Enter Unit Price" name="unit_price"
                                    min="0" max="100" class="input form-control unit_price" required>
                                <span class="dollar">$</span>
                                <span id="unitPrice_error" class="custom-error" style="color: red;"></span>
                            </div>
                        </div>

                        <div class="field">
                            <label class="label">Total price</label>
                            <div class="absolute-error position-relative">
                                <input type="text" class="input form-control total_price" value="0" readonly>
                                <span class="dollar">$</span>
                            </div>
                        </div>
                    </div>

                    <div class="field mt-4">
                        <label class="label">Product Description</label>
                        <div class="absolute-error">
                            <textarea placeholder="Enter description" name="description" class="description input form-control"></textarea>
                        </div>
                    </div>
                    <input type="hidden" class="invoice_item_id" name="invoice_item_id" value="">

                    <div class="btn primaryblue ms-auto  add-button" style="margin-top: 26px">Add</div>
                @endif


                <div class="table-responsive" style="margin-top: 50px;">
                    <label class="itemsHeading pb-2">Items</label>
                    <table id="Product-Items-Detail" class="table table-striped custom_datatable display"
                        style="width:100%">
                        <thead>
                            <tr>
                                <th>Product / service</th>
                                <th>Description</th>
                                <th class="width-186">Quantity</th>
                                <th>Unit price</th>
                                <th>Total price</th>
                                @if (!$invoice->operation_id)
                                    <th class="text-center">Action</th>
                                @endif
                            </tr>
                        </thead>
                        <tbody class="invoice-products-body">
                            @include('organization.invoices.partials.products_table')

                        </tbody>
                    </table>
                </div>




                <div class="panel_header mb-4 mt-5">
                    <h2 class="panel_title">Internal Notes & Attachments</h2>
                </div>
                <div class="">
                    <textarea class="p-4" placeholder="Enter Personal Notes"
                        style="width: 100%;border: 1px dashed #90a0b7; border-width: 2px;" name="notes">{{ $invoice?->notes }}</textarea>
                </div>
                <!-- <div class="px-24 mt-2">
                    <div class="dropzone_library_customize needsclick dropzone" id="documen-dropzone"></div>
                    <div id="imageSizeError"></div>
                </div> -->
                <div class="mt-2">
                    <div class="dropzone_library_customize dropzone">
                    <div class="dz-preview dz-file-preview dz-processing dz-success dz-complete needsclick" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;" id="my-dropzone-edit-invoice">
                        <svg width="26" height="26" style="margin-top: 28px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>





                <div class="subtotal-main" style="max-width:350px">

                    @if (!$invoice->operation_id)
                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="total-title">Sub total</h2>
                            <p>$<span class="sub-total-price"></span></p>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="total-title">Discount</h2>
                            <div class="field position-relative">
                                {{-- <label class="label">Discount</label> --}}
                                <div class="absolute-error">
                                    <input id="discount" type="number" placeholder="Enter Discount" name="discount"
                                        class="input form-control discount_pkr total-discount" style="width: 230px"
                                        value="{{ $invoice?->discount_price }}">
                                    <select name="discount_method" id="discount_method" class="position-absolute"
                                        style="right: 10px;top: 9px;border-radius: 25px;background: #eee;border: none;padding: 3px 4px;">
                                        <option @selected($invoice?->discount_method == 'per') value="per">Per%</option>
                                        <option @selected($invoice?->discount_method == 'amount') value="amount">$</option>
                                    </select>
                                    <span id="discount_error" class="custom-error"
                                        style="color: red;font-size: 10px;bottom: -22px">
                                    </span>
                                </div>
                            </div>
                            {{-- <p class="total-discount">0%</p> --}}
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <h2 class="total-title">Tax</h2>
                            <div class="field" style="max-width:230px; width:100%">
                                {{-- <label class="label">Tax (%)</label> --}}
                                <div class="absolute-error position-relative">
                                    <input id="tax" type="number" placeholder="Enter tax" name="tax"
                                        class="input form-control item-tax total-tax" max="100" min="1"
                                        value="{{ $invoice?->tax }}">
                                    <span class="custom-error" id="tax_error"
                                        style="color: red;font-size: 11px;bottom: -22px;">
                                    </span>
                                </div>
                            </div>
                            {{-- <p class="total-tax">0%</p> --}}
                        </div>
                    @endif
                    <hr style="border-top: 1px solid #BBB">

                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="total-title">Total</h2>
                        <p class="total-price" style="font-weight: 700;">${{ $invoice->total }}</p>
                    </div>

                </div>

            </div>

            <div class="d-flex justify-content-end gap-3 flex-wrap ms-auto mt-5" style="width: 100%; gap: 15px !important">
                <a href="{{ route(getRouteAlias() . '.invoices.index') }}" class="btn transparent bg-transparent px-5"
                    style="width: 176px;">Back</a>
                <button type="button" class="btn transparent bg-transparent validate-fields"
                    style="width: 176px;">Update</button>
                <button type="button" class="btn primaryblue validate-fields send-email" style="width: 176px;">Send as
                    Email</button>
            </div>

        </form>
        <!-- Modal -->
        <div class="operation_modal modal fade" id="filePreviewModal" tabindex="-1" role="dialog"
            aria-labelledby="filePreviewModalTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLongTitle">Preview</h5>
                    </div>
                    <div class="modal-body">
                        <div id="fileContent"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn transparent d-block w-100" data-dismiss="modal"
                            aria-label="Close">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @include('organization.partials.send-email', [
        'to_email' => $client->email,
        'sentInvoiceAttachment' => true,
        'isExternalSubmitHandler' => 'sendEmailHandler',
        'isOperational' => !empty($invoice->operation_id) ? true : false,
        'sales_order_number' => optional($invoice->operation)?->sales_order_number,
    ])
    @push('scripts')
    <script>
         $(document).ready(function() {
                $('.dropzone_library_customizess .dz-message').html(
                    '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )
            })
            // var imageClick = document.querySelectorAll('.dz-filename');
            // for (let index = 0; index < imageClick.length; index++) {
            //     imageClick[index].addEventListener("click", function() {
            //         console.log("hello test click")
            //     });

            // }
            $(document).on('click', '#my-dropzone-edit-invoice', function() {
    $('#imageSizeError').html('');
});



    Dropzone.autoDiscover = false; // Disable auto-discovery



    var uploadedDocumentMap = {};
    const myDropzonesss = new Dropzone("#my-dropzone-edit-invoice", {
    url: "{{ route(getRouteAlias() . '.invoices.file-store', [(isset($routeParam)) ? $routeParam : request()->route('id')]) }}", // Replace with your server upload URL
    addRemoveLinks: true,
    dictRemoveFile: "Remove", // Proper text for remove button
    previewTemplate: `
        <div class="dz-preview dz-file-preview dz-edit-preview">
       <div class="dz-image-edit"></div>
         <img class="dz-details-imagess" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>


        </div>
    `,
    previewsContainer: ".dropzone_library_customize",
    acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
    },
    init: function () {
        var iconSrc = "";

        @foreach ($docs as $index => $doc)
    <?php
    $fileExtension = pathinfo($doc->file_name, PATHINFO_EXTENSION);
    ?>
    var exts = "{{$fileExtension}}";
    let mockFiless{{ $index+1 }} = {
        id: {{ $doc->id }},
        name: "{{ $doc->file_name }}", // File name
        file: '{{ Storage::url($doc->disk) }}' // Path to the file on the server
    };

    // Add the file to Dropzone
    this.emit("addedfile", mockFiless{{ $index+1 }});
    this.emit("complete", mockFiless{{ $index+1 }});

    // Optionally, add a hidden input field for the file path
    // $('form').append('<input type="hidden" name="document[]" value="{{ $doc->name }}">');

    var iconSrc = "";
    switch (exts) {
        case "pdf":
            iconSrc = "data:image/svg+xml;base64,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";
            break;
        case "xls":
        case "xlsx":
            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo="; // Add the SVG for XLS icon
            break;
        case "doc":
        case "docx":
            iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4="; // Add the SVG for DOC icon
            break;
        default:
            iconSrc = "{{ Storage::url('uploads/' . $doc->file_name) }}"; // Default icon for unsupported formats
            break;
    }

    // Find the specific Dropzone container for this file
    var dropzoneItems = document.querySelectorAll('.dropzone .dz-edit-preview'); // Get all Dropzone items
    var currentDropzoneItem = dropzoneItems[{{ $index }}]; // Select the current file's container

    if (currentDropzoneItem) {
        // Create the icon image element
        var iconElement = document.createElement("img");
        iconElement.src = iconSrc;
        iconElement.alt = 'File icon';
        iconElement.style.height = "60px";
        iconElement.style.width = "100%";
        $('.dz-remove').css('margin-top', '-19px');
        $('.dropzone_library_customize').addClass('dz-started');
        // $('.dropzone_library_customize').addClass('dz-started');

        // Append the icon to the current Dropzone container
        var iconContainer = currentDropzoneItem.querySelector('.dz-image-edit');
        if (iconContainer) {
            iconContainer.appendChild(iconElement);
        }
    }
@endforeach


        this.on("success", function (file, response) {
            // Assuming your API response contains a key 'filePath'
            $('#edit-Client-Invoice').append('<input type="hidden" name="document[]" value="' + response.flname + '">');
            uploadedDocumentMap[file.name] = response.flname;
        });
        this.on("addedfile", function (file) {
            $('.dropzone_library_customize').addClass('dz-started');
            // Extract file extension
            let ext = file.name.split('.').pop().toLowerCase();

            // Default icon (if no match found)
            let iconSrc = "default-icon.png";

            // Icons for specific file types
            if (ext === "pdf") {
                iconSrc = "data:image/svg+xml;base64,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";
            } else if (ext === "xls" || ext === "xlsx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
            } else if (ext === "doc" || ext === "docx") {
                iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
            }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "gif") {
                const reader = new FileReader();
                reader.onload = function (e) {
                    $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
                    // Find the preview image element and set the source
                    $(file.previewElement).find(".dz-details-imagess").attr("src", e.target.result);
                };
                reader.readAsDataURL(file);
            }
            $(file.previewElement).find(".dz-details-imagess").css("width", '100%');

            // Update the preview element with the corresponding icon
            if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
            $(file.previewElement).find(".dz-details-imagess").attr("src", iconSrc);
            }
        });

        this.on("removedfile", function (file) {

            console.log("File removed: ", file.name); // Log file removal
                // You can add additional logic here to update UI or handle response data
                // You can add additional logic here to update UI or handle response data


            file.previewElement.remove();
        var name = '';
        if (typeof file.file_name !== 'undefined') {
            name = file.file_name;
        } else {
            name = uploadedDocumentMap[file.name];
        }

        console.info(file.id);

            // Send an AJAX request to delete the file from the server
            fetch("{{ route(getRouteAlias() . '.invoice.file.delete.server') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ name: uploadedDocumentMap[file.name], file_id: file.id })
            })
            .then(response => response.json())
            .then(data => {
                console.info('File deleted from server:', data);

                // var tys = document.querySelectorAll('.dz-image-edit img');
                // console.info(file.id);
                // if (tys.length == 0 && file.id != undefined) {
                //     $('.dropzone_library_customize').removeClass('dz-started');
                //     $('.dz-preview').css('display', 'none');
                // }else{
                //     $('.dropzone_library_customize').addClass('dz-started');
                //     $('.dz-preview').css('display', 'inline-block');
                // }

                // You can add additional logic here to update UI or handle response data
            })
            .catch(error => {
                console.error('Error deleting file from server:', error);
            });
            $('#edit-Client-Invoice').find('input[name="document[]"][value="' + name + '"]').remove();
        });

        // Add error handling if needed
        this.on("error", function (file, errorMessage) {
            console.error("Dropzone error:", errorMessage);
        });
    }
});

     </script>
        <script>
            $(document).ready(function(e) {
                let dueDate = "{{ $invoice->due_date }}"
                $('#select2-dateSelect-container').text((dueDate));
                console.log(dueDate);
            })
        </script>
        <script>
            $(document).ready(function(e) {

                $("#issueDate").datepicker({
                    startDate: "today",
                    autoclose: true, // Set the minimum date to today
                    // format: "{{ getOrgDateFormat() }}",
                });

                // $("input[data-provide='datepicker']").change(function () {
                //     $(this).datepicker("hide");
                // });

                if (!@json($invoice->operation_id)) {
                    calculateSubTotalPriceSum();
                    finalPrice();
                }

                $(document).on("sendEmailHandler", sendEmailHandler);

                function sendEmailHandler(e) {

                    var formData = {};
                    if (e.customData instanceof FormData) {
                        formData = e.customData;
                    }

                    // if (!(formData.has("send-message") && formData.get("send-message").includes(
                    //         '@{{ INVOICE_NUMBER }}'))) {
                    //     let errorHtml =
                    //         '<label id="summernote-error" class="error error-message" for="summernote">Please user @{{ INVOICE_NUMBER }} message</label>';
                    //     $('#emailModal .note-editor.note-frame').after(errorHtml);
                    //     return;
                    // }


                    $("#edit-Client-Invoice")
                        .serializeArray()
                        .forEach(function(field) {
                            formData.append(field.name, field.value);
                        });

                    formData.append('sentEmail', true);
                    editInvoiceAjaxSubmit(formData)
                }

                function editInvoiceAjaxSubmit(formData) {

                    formData.append('subTotalPrice', $('.sub-total-price').text());
                    formData.append('tax', $('.total-tax').text().replace('%', ''));
                    formData.append('totalPrice', $('.total-price').text().replace('$', ''));

                    let id = "{{ encodeID($invoice->id) }}";
                    let url = '{{ route(getRouteAlias() . '.invoices.update', ':id') }}';
                    url = url.replace(':id', id);
                    $('.creation-error').removeClass('show');

                    $.ajax({
                        url: url,
                        method: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            window.location.href =
                                '{{ route(getRouteAlias() . '.invoices.index') }}';
                            toastr.success(response.message);

                        },
                        error: function(request, xhr, status, error) {
                            let errorResponse = JSON.parse(request.responseText);
                            if (errorResponse.hasOwnProperty('creation_error') && request.status ==
                                400) {
                                $('.creation-error').addClass('show').html(errorResponse
                                    .creation_error);
                            }
                            $.each(errorResponse.errors, function(field_name, error) {

                                if (field_name == 'due_date') {
                                    $("#" + 'dateSelect').after(
                                        '<span class="error-message" style="color: red;">' +
                                        error + '</span>');
                                } else {
                                    $(document)
                                        .find("#" + 'Subject cannot be empty.' + " [name=" +
                                            field_name +
                                            "]")
                                        .after(
                                            '<span class="error-message" style="color: red;">' +
                                            error + '</span>');
                                }

                            });
                        }
                    });
                }

                // Generating Invoice
                $('#edit-Client-Invoice').on('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    editInvoiceAjaxSubmit(formData);
                });

                $(document).on('custom-click', '.add-invoice-product-btn', function() {
                    $('.panel_invoice_fields .laravel-error ').remove();

                    var data = {
                        product_name: $('.product_name').val(),
                        quantity: $('.quantity').val(),
                        unit_price: $('.unit_price').val(),
                        description: $('.description').val(),
                        invoice_item_id: $('.invoice_item_id').val()
                    };

                    let id = "{{ encodeID($invoice->id) }}";
                    let url = '{{ route(getRouteAlias() . '.invoices.add.product', ':id') }}';
                    url = url.replace(':id', id);

                    $.ajax({
                        url: url,
                        method: 'POST',
                        data: data,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            $('.product_name,.unit_price,.total_price,.description')
                                .removeClass('valid').val(
                                    '');
                            $('.quantity').val('').trigger('change');
                            $('.invoice-products-body').html(response.html);
                            calculateSubTotalPriceSum();
                            finalPrice();
                            $('.add-button').text('Add').removeClass('add-invoice-product-btn');
                            $('.invoice_item_id').val('');
                            console.log(response);
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                            let errorResponse = JSON.parse(xhr.responseText);
                            $.each(errorResponse.errors, function(field_name, error) {
                                $(document)
                                    .find("." + 'panel_invoice_fields' + " [name=" +
                                        field_name +
                                        "]")
                                    .after(
                                        '<span class="error-message laravel-error" style="color: red;">' +
                                        error + '</span>');
                            });
                        }
                    });
                })


                $(document).on('click', '.edit-invoice-product-btn', function(e) {
                    let id = $(this).attr('data-id');
                    let url = '{{ route(getRouteAlias() . '.invoices.retrieve.product', ':id') }}';
                    url = url.replace(':id', id);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            calculateSubTotalPriceSum();
                            $('.invoice_item_id').val(id);
                            $('.product_name').val(response.product.name),
                                $('#select2--container').text(response.product.quantity),
                                $('.quantity').val(response.product.quantity);
                            $('.unit_price').val(response.product.unit_price),
                                $('.total_price').val(response.product.total_price),
                                $('.description').val(response.product.description),
                                finalPrice();
                            $('.add-button').text('Update');
                            console.log(response.product);
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                        }
                    });
                })


                $(document).on('click', '.delete-invoice-product-btn', function(e) {
                    let id = $(this).attr('data-id');
                    let url = '{{ route(getRouteAlias() . '.invoices.remove.product', ':id') }}';
                    url = url.replace(':id', id);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            $('.invoice-products-body').html(response.html);
                            calculateSubTotalPriceSum();
                            finalPrice();
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                        }
                    });
                })


                // Calculating subtotal price of invoice
                function calculateSubTotalPriceSum() {
                    let sum = 0;
                    $('#Product-Items-Detail .item-total-price').each(function() {
                        let value = parseFloat($(this).data('value'));
                        if (!isNaN(value)) {
                            sum += value;
                        }
                    });
                    $('.sub-total-price').text(customNumberFormat(sum));
                    $('.total-price').text(parseFloat(sum.toFixed(2))).prepend('$');
                }




                $('.item-tax, .discount_pkr').on('input', function() {
                    finalPrice();
                });



                $('#discount_method').on('change', function() {
                    finalPrice();
                })



                $('.quantity ,.unit_price').on('input', function() {
                    $('.total_price').val(($('.quantity').val() * $('.unit_price').val()).toFixed(2));
                })


                function finalPrice() {
                    $('.total-tax').text($('.item-tax').val()).append('%');

                    let discountValue = parseFloat($('.discount_pkr').val());
                    let discountMethod = $('#discount_method').val();
                    let subTotalPrice = parseFloat($('.sub-total-price').text().replace('$', '').replace(',', ''));

                    let totaltax = $('.total-tax').text();
                    totaltax = parseFloat(totaltax.replace('%', ''));
                    if (isNaN(discountValue)) {
                        let taxPercentage = $('.item-tax').val();
                        if (taxPercentage == '') {
                            $('.total-tax').text('0%');
                        } else {
                            $('.total-tax').text(taxPercentage + '%');
                        }
                        if (discountMethod == 'amount') {
                            $('.total-discount').text('0').prepend('$');
                        } else {
                            $('.total-discount').text('0').append('%');
                        }

                        let taxDecimal = taxPercentage / 100;
                        let taxAmount = subTotalPrice * taxDecimal;
                        let totalFinalPrice = subTotalPrice + taxAmount;
                        $('.total-price').text(parseFloat(totalFinalPrice.toFixed(2))).prepend('$');

                    } else {

                        if (discountMethod == 'amount') {

                            if (isNaN(totaltax)) {
                                totaltax = 0;
                            }
                            $('.total-discount').text(discountValue).prepend('$');
                            let totalDiscountedPrice = (subTotalPrice - discountValue);
                            let taxAmount = totalDiscountedPrice * (totaltax / 100);
                            let totalPriceWithTax = totalDiscountedPrice + taxAmount;
                            $('.total-price').text(parseFloat(totalPriceWithTax.toFixed(2))).prepend('$');
                            $('.total-tax').text(totaltax).append('%');

                            return totalPriceWithTax;
                        } else {
                            if (isNaN(totaltax)) {
                                totaltax = 0;
                            }
                            $('.total-discount').text(discountValue).append('%');
                            let discountDecimal = discountValue / 100;
                            let discoubtAmount = subTotalPrice * discountDecimal;
                            let totalDiscountedPrice = subTotalPrice - discoubtAmount;

                            let taxAmount = totalDiscountedPrice * (totaltax / 100);
                            let totalPriceWithTax = totalDiscountedPrice + taxAmount;
                            $('.total-price').text(parseFloat(totalPriceWithTax.toFixed(2))).prepend('$');
                            $('.total-tax').text(totaltax).append('%');

                            return totalPriceWithTax;
                        }
                    }
                }




            });
        </script>

        <script>
            $("#tax").on('input change', function() {
                var value = parseInt(this.value, 10); // Parse the input value as an integer
                // var errorSpan = $("#error");
                $("#tax_error").html('');

                if (value > 100 || value < 0) {
                    $("#tax_error").html('Value must be between 0 and 100');
                    $('.validate-fields').css({
                        cursor: 'not-allowed',
                        pointerEvents: 'none'
                    });
                    // $('.validate-fields').css('cursor','not-allowed');
                    // $('.validate-fields').css('pointerEvents','none');
                    this.value = ''; // Clear the input value

                } else {
                    $("#tax_error").html(''); // Hide the error message if the value is within the limit
                    $('.validate-fields').css({
                        cursor: 'pointer',
                        pointerEvents: 'auto'
                    });

                }
            });

            $("#discount").on('input', function() {
                let subTotal = $('.sub-total-price').html();
                $("#discount_error").html('');
                var value = parseInt(this.value, 10); // Parse the input value as an integer
                if (value > subTotal && $('#discount_method').val() === 'amount') {
                    this.value = ''; // Clear the input value
                    $("#discount_error").text('Discount cannot be more than the subtotal');
                } else if ((value > 100 || value < 0) && $('#discount_method').val() === 'per') {
                    $("#discount_error").html('Value must be between 0 and 100');
                    $('.validate-fields').css({
                        cursor: 'not-allowed',
                        pointerEvents: 'none'
                    });

                    this.value = ''; // Clear the input value
                } else {
                    $("#discount_error").html(''); // Hide the error message if the value is within the limit
                    $('.validate-fields').css({
                        cursor: 'pointer',
                        pointerEvents: 'auto'
                    });

                }
            });

            // $("#quantitySelect").on('input change', function() {
            //     var value = parseInt(this.value, 10); // Parse the input value as an integer
            //     // $('.error-message').remove();
            //     $(this).next('.error-message').html('');

            // if (value < 1) {

            //     $(this).next('.error-message').html('Value must be greater than 0.');
            //     this.value = ''; // Clear the input value
            //     // $('.error-message').remove();
            // }
            // });

            // unitPrice

            // $("#unitPrice").on('input change', function() {
            //     var value = parseInt(this.value, 10); // Parse the input value as an integer
            //     $("#unitPrice_error").html('');

            //     if (value < 0) {

            //         $("#unitPrice_error").html('Value must be greater than 1.');
            //         this.value = ''; // Clear the input value
            //     } else {
            //         $("#unitPrice_error").html(''); // Hide the error message if the value is within the limit

            //     }
            // });
            $(document).ready(function() {
                // Function to check if the input value is empty
                function isEmpty(inputValue) {
                    return $.trim(inputValue) === '';
                }

                function isAlphanumeric(value) {
                    return /^[a-zA-Z0-9]*$/.test(value);
                }

                function isLengthValid(value, minLength, maxLength) {
                    return value.length >= minLength && value.length <= maxLength;
                }
                // Function to display error messages
                function showError(inputId, message) {
                    $("#" + inputId).next(".error-message").remove(); // Remove any existing error message
                    $("#" + inputId).after('<span class="error-message" style="color: red;">' + message + '</span>');

                    // alert("sdfds");
                }

                // Function to remove error messages
                function removeError(inputId) {
                    $("#" + inputId).next(".error-message").remove();
                }

                // Validate the form on button click and set up keyup validation
                $(".validate-fields").on("click", function(e) {
                    if (isFormValid()) {
                        // Close the modal if the form is valid using Bootstrap modal('hide') method
                        // $("#propertyModal").modal('hide');

                        $('.creation-error').addClass('fade').html('');
                        // check If button has send-email then validate if invoice has any items and show modal for email sending
                        if ($(e.target).hasClass('send-email')) {
                            if (!$("#Product-Items-Detail > tbody > tr").length > 0) {
                                $('.creation-error').removeClass('fade').html(
                                    'Please add atleast one item to create the invoice');
                            } else {
                                // Set Email Modal Header
                                $('#emailModal .modal-title').html("Email Invoice to " +
                                    "{{ $client->full_name }}")

                                // Set Email Defualt Subject

                                $('#emailModal #subject').val(
                                    'Invoice From {{ optional($client->organization)?->company_name }} - {{ customDateFormat(now()) }}'
                                )

                                var website = @json(optional($organization->companyAddress)->website_url);
                                var sumhtml = '<span class="im">' +
                                    '<h1 style="font-style: normal; font-weight: 700; font-size: 24px; line-height: 33.6px; color: rgb(25, 42, 62);">Hi {{ $client->full_name }}, </h1>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">Thank you for the opportunity to provide you with an invoice.</p>' +
                                    '<p style="font-family: Arial, Helvetica, sans-serif; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 24px;">The invoice total is <strong style="color: rgb(25, 42, 62);">' +
                                    $('.total-price').text() +
                                    '</strong> as on {{ customDateFormat(now()) }}</p>' +
                                    '<p><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span><span class="im" style="color: rgb(80, 0, 80); font-family: Arial, Helvetica, sans-serif; font-size: small;"></span></p>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;">If you have any questions, please do not hesitate to contact us at<span>&nbsp;</span><a target="_blank" rel="noopener noreferrer" href="mailto:{{ optional($client->organization)->email }}" style="color: rgb(17, 85, 204);"> {{ optional($client->organization)->email }}</a>.<br>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131); padding-top: 10px;"><br>Regards, </p>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->company_name }} </strong> </p>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ $organization->email }} </strong> </p>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->phone_no }} </strong> </p>' +
                                    '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> {{ optional($organization->companyAddress)->address1 }} </strong> </p>';

                                if (website) {
                                    sumhtml +=
                                        '<p style="font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px; color: rgb(112, 118, 131);"><strong> ' +
                                        website + ' </strong> </p>';
                                }


                                $('#emailModal .summernote').summernote('code', sumhtml);
                                // Set Email Defualt Message
                                $('#emailModal').modal('show')
                            }
                        } else {
                            if (!$("#Product-Items-Detail > tbody > tr").length > 0) {
                                $('.creation-error').removeClass('fade').html(
                                    'Please add atleast one item to create the invoice');
                            } else {
                                $('#edit-Client-Invoice').submit();
                            }
                        }
                    } else {
                        setupKeyupValidation();
                        setTimeout(function() {
                            let errorMessages = document.getElementsByClassName("error-message");

                            if (errorMessages) {
                                let firstErrorMessage = errorMessages[0];
                                let yOffset = window.scrollY;
                                let targetOffset = firstErrorMessage.offsetTop;

                                window.scrollTo({
                                    top: targetOffset,
                                    behavior: "smooth"
                                });
                            }
                        }, 200);
                    }
                });

                // Function to validate the form
                function isFormValid() {
                    // Reset previous error messages
                    $(".error-message").remove();

                    var propertyName = $("#infoSubject").val();
                    var address1 = $("#issueDate").val();
                    // var dateSelect = $("#dateSelect").val();
                    var dateSelect = $("#customDateInput").val();
                    var discount = $("#discount").val();
                    var discountMethod = $("#discount_method").val();
                    var tax = $("#tax").val();


                    // Validate Property Name
                    if (isEmpty(propertyName)) {
                        showError("infoSubject", "Subject cannot be empty.");
                    } else if (!isAlphanumeric(propertyName)) {
                        // showError("infoSubject", "Subject must be alphanumeric.");
                    } else if (!isLengthValid(propertyName, 3, 100)) {
                        showError("infoSubject", "Subject length must be between 3 and 100 characters.");
                    }
                    // Validate Address
                    if (isEmpty(address1)) {
                        showError("issueDate", "Issue date cannot be empty.");
                    }

                    // Validate Date Select
                    if (isEmpty(dateSelect) || dateSelect === "custom" || dateSelect === 'disabled') {
                        showError("dateSelect", "Please select a valid due date.");
                    }

                    // if (isEmpty(discount)) {
                    //     showError("discount", "Discount cannot be empty.");
                    // } else if (isNaN(discount) || Number(discount) <= 0) {
                    //     showError("discount", "Discount must be a positive number.");
                    // }

                    // Validate Discount Method
                    // if (isEmpty(discountMethod) || (discountMethod !== "per" && discountMethod !== "amount")) {
                    //     showError("discount_method", "Please select a valid discount method.");
                    // }

                    // if (isEmpty(tax)) {
                    //     showError("tax", "Tax cannot be empty.");
                    // } else if (isNaN(tax) || Number(tax) < 0 || Number(tax) > 100) {
                    //     showError("tax", "Tax must be a number between 0 and 100.");
                    // }

                    // Check if there are any error messages
                    return $(".error-message").length === 0;
                }

                // Function to set up keyup validation for each field
                function setupKeyupValidation() {
                    $("#infoSubject, #issueDate, #dateSelect, #customDateInput, #discount_method, #tax").on(
                        "keyup change",
                        function() {
                            var inputId = $(this).attr("id");
                            removeError(inputId);
                            isFormValid();
                            // isFormValid2();
                        });

                    $("#issueDate").on("change", function() {
                        var inputId = $(this).attr("id");
                        removeError(inputId);
                        isFormValid();
                    });

                    $("#dateSelect").on("change", function() {
                        var inputId = $(this).attr("id");
                        removeError(inputId);
                        isFormValid();
                    });
                }
                // });

                // $(document).ready(function() {
                // Function to check if the input value is empty
                function isEmpty(inputValue) {
                    return $.trim(inputValue) === '';
                }

                // Function to display error messages
                function showError(inputId, message) {
                    $("#" + inputId).next(".error-message").remove(); // Remove any existing error message
                    $("#" + inputId).after('<span class="error-message" style="color: red;">' + message + '</span>');
                }

                // Function to remove error messages
                function removeError(inputId) {
                    $("#" + inputId).next(".error-message").remove();
                }

                // Validate the form on button click and set up keyup validation
                $(".add-button").on("click", function(e) {
                    // console.log('here ')
                    if (isFormValid2()) {
                        // Close the modal if the form is valid using Bootstrap modal('hide') method
                        $(e.target).addClass('add-invoice-product-btn');
                        // Trigger a custom event instead of directly clicking the button
                        $('.add-invoice-product-btn').trigger('custom-click');
                    } else {
                        $(e.target).removeClass('add-invoice-product-btn');
                        setupKeyupValidation2();
                    }
                });

                // Function to validate the form
                function isFormValid2() {
                    // Reset previous error messages
                    $(".error-message").remove();

                    var propertyName = $("#productService-name").val();
                    var address1 = $("#unitPrice").val();
                    var dateSelect = $("#quantitySelect").val();

                    // Validate Property Name
                    if (isEmpty(propertyName)) {
                        showError("productService-name", "Service name cannot be empty.");
                    }

                    // Validate Address
                    if (isEmpty(address1)) {
                        showError("unitPrice", "Unit price cannot be empty.");
                    } else if (address1 < 1) {

                        showError("unitPrice", "Value must be greater than 0.");

                        // $('.error-message').remove();
                    }

                    // Validate Date Select
                    if (isEmpty(dateSelect)) {
                        showError("quantitySelect", "Please enter quantity.");
                    } else if (dateSelect < 1) {

                        showError("quantitySelect", "Value must be greater than 0.");

                        // $('.error-message').remove();
                    }

                    // if (value < 0) {
                    //     showError("quantitySelect", "Enter value greater than 1.");
                    //     this.val()='';
                    // }

                    // Check if there are any error messages
                    return $(".error-message").length === 0;
                }

                // Function to set up keyup validation for each field
                function setupKeyupValidation2() {

                    $("#productService-name, #quantitySelect, #unitPrice").on("keyup", function() {
                        var inputId = $(this).attr("id");
                        removeError(inputId);
                        // isFormValid();
                        isFormValid2();
                    });

                    $("#quantitySelect").on("change", function() {
                        var inputId = $(this).attr("id");
                        removeError(inputId);
                        // isFormValid();
                        isFormValid2();
                    });


                }
            });


            $(document).ready(function() {

                const inputElement = document.getElementById('productService-name');
                const selectElement = document.getElementById('productSelector');
                const liOption = document.querySelector('.li-option');

                if (inputElement && selectElement) {
                    inputElement.addEventListener('focus', function() {
                        selectElement.style.display = 'block';
                    });
                }

                $(".li-option").on("click", function() {
                    $("#productService-name").val($(this).text());
                    $("#productService-name").next(".error-message").remove();
                    selectElement.style.display = 'none';
                });

                document.addEventListener('click', function(event) {
                    var targetElement = event.target;
                    if (selectElement) {
                        if (!selectElement.contains(targetElement) && targetElement.id !==
                            'productService-name') {
                            selectElement.style.display = 'none';
                        }
                    }
                });

            });
        </script>
        <script>
            $(document).ready(function(e) {
                $('#productSelector .li-option').click(function(e) {
                    let id = $(this).data('id');
                    let url = '{{ route(getRouteAlias() . '.invoices.ItemsDetails', ':id') }}';
                    url = url.replace(':id', id);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            $('#quantitySelect').val(response.quantity);
                            $('#unitPrice').val(response.unit_price);
                            $('.total_price').val(response.total_price);
                            $('.description').val(response.description);
                            console.log(response);
                        },
                        error: function(xhr, status, error) {
                            console.error(error);
                        }
                    });
                })
            })
        </script>

        @include('organization.invoices.partials.dropzone_script')
    @endpush
@endsection
