@extends('layouts.admin.master')
@section('title', 'Requests')
@section('styles')
    <style>
        .no-wrap-white-space {
            white-space: nowrap;
        }
        .dropdown-toggle::after {
    display: none !important;

}
    </style>
@endsection
@section('section')
    <section class="dashboard_main pb-5 estimates_table_filters">

        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Request list</h2>


            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                    <option value="" selected>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="open">Open</option>
                    {{-- <option value="close">Close</option> --}}
                    <option value="pending">Pending</option>
                </select>
            </div>
        </div>

        {{-- <ul class="nav company-client_tabs" id="company-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="company-tab-btn" data-toggle="pill" data-target="#company-created"
                    type="button" role="tab" aria-controls="company-created" aria-selected="true">Company
                    Created</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="client-created-tab" data-toggle="pill"
                    data-target="#client-created" type="button" role="tab" aria-controls="client-created"
                    aria-selected="false">Client Request</button>
            </li>
        </ul> --}}
        <div class="tab-content mt-4" id="companyClientTabs">
            <div class="tab-pane fade" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn"
                tabindex="0">
                <p>Data is not available yet.</p>
            </div>
            <div class="tab-pane fade show active" id="client-created" role="tabpanel" aria-labelledby="client-created-tab"
                tabindex="0">
                <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">

                    <thead>
                        <tr>
                            <th>ER #</th>
                            <th>Property Name</th>
                            <th>Project Name</th>
                            <th>Client Name</th>
                            <th>Phone Number</th>
                            <th>Salesman Name</th>
                            <th>Estimator Name</th>
                            <th>Date & Time</th>
                            <th>Status</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>



    </section>
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal')
    <div class="modal-small success-modal modal fade" id="requestModal" data-keyboard="false" tabindex="-1"
        aria-labelledby="requestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img height="70px" width="70px" src="{{ asset('admin_assets/images/icons/check-icon.png') }}"
                            alt="check icon">
                    </div>

                    <h2 class="title text-center">Request Created</h2>
                    <p class="para mt-3 text-center">Your request has been successfully created.</p>
                    <a href="{{ route(getRouteAlias() . '.estimate.index') }}" type="button"
                        class="btn primaryblue w-100 mt-5 " data-dismiss="modal">Go to list</a>
                </div>

            </div>

        </div>
    </div>

    <div id="modalData"></div>

    </section>

    @push('scripts')
        @include('organization.estimate.script')
        @if (!empty(Session::get('showModal')))
            <script>
                $(function() {
                    $('#requestModal').modal('show');
                });
            </script>
        @endif
    @endpush
@endsection
