<x-jet.listing-table>
    <x-slot name="header">
        <th>Request #</th>
        <th>Project Name</th>
        <th>Client Name</th>
        <th>Phone Number</th>
        <th>Salesman Name</th>
        <th>Estimator Name</th>
        <th>Status</th>
        <th class="text-center">Action</th>

    </x-slot>
    <x-slot name="body">
        @forelse($estimates as $estimate)
            <tr>
                <td>{{ $estimate->job_no }}</td>
                <td>{{ $estimate->project_name }}</td>
                <td>{{ $estimate->client->first_name }}</td>
                <td>{{ $estimate->client->mobile_no }}</td>
                <td>{{ $estimate->saleMan->name }}</td>
                <td>{{ $estimate->estimator->name }}</td>
                <td>
                    <div class="status warning">{{ $estimate->is_active == 1 ? 'Open' : 'Close' }}</div>
                    <!-- <div class="status success">Completed</div> -->
                </td>
                <td>
                    <div class="dropdown mx-auto w-fit">
                        <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{ asset('admin_assets/images/icons/vertical-dots.svg') }}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li><a class="dropdown-item" href="#">Generate Estimate</a></li>
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.estimate.edit', encodeId($estimate->id)) }}">Edit
                                    Request</a></li>
                            <li>
                                <a class="dropdown-item" data-toggle="modal" data-target="#DeleteModal"
                                    onclick="showModal('{{ encodeId($estimate->id) }}')">
                                    Delete Request
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        @empty
            @include('layouts.partials.no-data')
        @endforelse
    </x-slot>
    <x-slot name="pagination">
        @if (isset($estimates))
            {{ $estimates->links() }}
        @endif
    </x-slot>
</x-jet.listing-table>
