@extends('layouts.admin.master') @section('title', 'Accounts')
@section('styles')
<style>
    textarea[readonly] {
        background-color: #f6f6f6 !important;
        border: 1px solid #e7e7e7 !important;
        opacity: 1 !important;
    }
    .dropdown-toggle::after {
    display: none !important;

}
</style>
<style>
    .select2-container--default {
        margin-top: 21px !important;
        margin-right: 7px !important;
    }
    .table_filter_dropdown {
        margin-top: -45px !important;
    }
    .select2-dropdown--below {
        margin-top: -45px !important;
        background: white !important;
    }
    .required_field {
        color: red;
    }
    .dropdown-toggle::after {
        display: inline-block;
        width: 0;
        height: 0;
        margin-left: 0.255em;
        vertical-align: 0.255em;
        display: none !important;
        content: "";
        border-top: 0.3em solid;
        border-right: 0.3em solid transparent;
        border-bottom: 0;
        border-left: 0.3em solid transparent;
    }
    .even:hover {
        background-color: #e1f4ff !important;
    }
    table.dataTable.display tbody tr:hover > .sorting_1,
    table.dataTable.order-column.hover tbody tr:hover > .sorting_1 {
        background-color: #e1f4ff !important;
    }
    table.dataTable.display tbody tr:hover,
    table.dataTable.order-column.hover tbody tr:hover {
        background-color: #e1f4ff !important;
    }

    .hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .hidemodelbtn {
            font-size: 15px !important;
        }
    }
    span {
        font-size: 14px;
        color: #192a3e;
    }
    input::placeholder {
        font-size: 14px;
    }
    input {
        font-size: 14px !important;
    }
    button {
        font-size: 14px !important;
    }
    select {
        font-size: 14px !important;
    }
    .showMessage {
    display: none;
    padding: 0px 10px 7px 10px;
    background: #c3e6cb;
    color: #155724;
    text-align: left;
}
.showMessage p{
    color: #155724;
    padding: 8px 0px 0px 0px;
}
</style>
@endsection @section('section')
<section class="dashboard_main pb-5 propertyTable">
    <div class="showMessage"></div>
    <div class="table_filter_header mb-4 propertyListing mx-2">
        <h1 class="sub_heading">Properties</h1>

        <div class="d-flex align-items-center gap-3">
            <div class="filters">
                <input
                    type="search"
                    placeholder="Search"
                    name=""
                    id=""
                    class="property_search filter_search"
                />
                <!-- <a
                    type="button"
                    data-toggle="modal"
                    data-target="#selectClientModal"
                    class="text-decoration-none"
                    style="
                        padding: 9px 16px 5px 16px;
                        gap: 8px;
                        border-radius: 6px;
                        border: 1px 0px 0px 0px;
                        opacity: 0px;
                        background: #ffffff;
                        display: flex !important;
                    "
                    ><svg
                        style="margin-top: 3px"
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                            stroke="#51566C"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                    <label class="">Import</label>
                </a> -->
                <form
                    method=""
                    action=""
                    id="importproperty"
                    style="margin-top: 3px"
                    class="file_upload_button_form upload_file_wrapper w-fit"
                    enctype="multipart/form-data"
                >
                    @csrf

                    <label
                        class="btn primaryblue primaryblue22 transparent px-5"
                        for="import_file_data"
                        style="
                            border: 1px solid var(--lightgray);
                            background-color: white !important;
                        "
                        ><svg
                            style="margin-top: 3px"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        &nbsp;
                        <span style="font-size: 12px; color: #514f6e !important"
                            >Import</span
                        ></label
                    >
                    <input
                        class="input_file d-none"
                        type="file"
                        name="file"
                        id="import_file_data"
                        accept=".xls, .xlsx"
                    />
                </form>
                <!-- <a
                    type="button"
                    data-toggle="modal"
                    data-target="#selectClientModal"
                    class="text-decoration-none"
                    style="
                        padding: 9px 16px 5px 16px;
                        gap: 8px;
                        border-radius: 6px;
                        border: 1px 0px 0px 0px;
                        opacity: 0px;
                        background: #ffffff;
                        display: flex !important;
                    "
                    ><svg
                        width="16"
                        height="16"
                        style="margin-top: 3px"
                        viewBox="0 0 18 18"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                            stroke="#51566C"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                    <label class="">Export</label>
                </a> -->
                <form
                    method="POST"
                    id="export-opportunity"

                    action="{{ route(getRouteAlias() . '.export.properties') }}"
                >
                    @csrf
                    <button
                        type="submit"
                        {{ $property == 0 ? 'disabled' : '' }}
                        class="btn primaryblue transparent px-5"
                        style="border: 1px solid var(--lightgray); color: black !important"
                    >
                        <svg
                            width="16"
                            height="16"
                            style="margin-top: 3px"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <span
                            class=""
                            style="font-size: 12px; color: #514f6e !important"
                            >&nbsp;Export</span
                        >
                    </button>
                </form>
                <!-- <select
                    name=""
                    id="property_filter"
                    class="select-small basic-single-select"
                >
                    <option value="" selected>Clear</option>
                    <option value="due">Due</option>
                    <option value="paid">Paid</option>
                    <option value="overdue">Overdue</option>
                </select> -->
            </div>

            <a
                type="button"
                data-toggle="modal"
                data-target="#selectPropertyModal"
                class="btn primaryblue text-decoration-none text-white ml-3" style="border: 1px solid var(--lightgray);"
                >+ New Property</a
            >
        </div>
    </div>
    <div class="table-responsive">
        <table
            id="contact_list"
            class="table table-striped custom_datatable display yajra-datatable"
            style="width: 100%"
        >
            <thead>
                <tr>
                    <th>Property Name</th>
                    <th>Address</th>
                    <th>City</th>
                    <th>State</th>
                    <th>Zip</th>
                    <th>Account</th>
                    <th>Account Owner</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody id=""></tbody>
        </table>
    </div>
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal')
</section>

<!-- add property Modal -->
<div
    class="modal fade select-client"
    id="selectPropertyModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4 class="modal-title px-3" id="addItemModalLabel">
                    <b style="color: #0074d9 !important"
                        >Property Information</b
                    >
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body" style="padding: 13px 20px !important">
                <form
                    id=""
                    action="{{URL::route(getRouteAlias() . '.property.add')}}"
                    method="POST"
                >
                    @csrf
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 98%">
                            <span for="item_name"
                                >Property Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id=""
                                    required
                                    class="form-control"
                                    name="property_name"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                            </div>
                        </div>

                        <!-- </div>
                    <div class="col"> -->

                    </div>
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >

                    <div class="form-group" style="width: 47%">
                            <span for="">
                                Address 1
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="address1"
                                required
                                maxlength="60"
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Address 1"
                            />
                            <!-- </div> -->
                        </div>
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Address 2
                                <label class="" for="">(Optional)</label></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id=""
                                    name="address2"
                                    maxlength="60"
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Address 2"
                                />
                            </div>
                        </div>

                        <!-- </div>
                <div class="col"> -->

                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >City
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id=""
                                    name="city"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="City"
                                />
                            </div>
                        </div>

                        <!-- </div>
                <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id=""
                                name="state"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="State"
                            />
                            <!-- </div> -->
                        </div>
                    </div>
                    <!-- <div class="row"> -->
                  <div class="row px-2" style="justify-content: space-around; display: flex">
                    <div class="form-group" style="width: 47%">
                        <span for="">
                            Zip Code
                            <label class="required_field" for="">*</label></span
                        >
                        <input
                            type="text"
                            id=""
                            name="zip_code"
                            required
                            style="height: 30px !important; text-align: left"
                            class="form-control"
                            placeholder="Zip Code"
                        />
                        <!-- </div> -->
                    </div>
                    <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="account"
                                style="height: 30px !important"
                                class="form-control"
                                required
                                id="category_list"
                            >
                            <option value="" selected>Select Account</option>
                                @foreach($accounts as $item)
                                <option value="{{$item->id}}">
                                    {{$item->company_name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                  </div>
                    <!-- </div> -->

                    <!-- <div class="form-group">
            <b for="">Gross Margin</b>
          <input type="number" id="item_gross_margin" style="height: 30px !important;" class="form-control " placeholder="Item Gross Margin">
        </div> -->
                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn primaryblue px-5 py-2"
                        >
                            Add
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- =edit property Modal -->
<div
    class="modal fade select-client"
    id="editPropertyModal"
    data-backdrop="static"
    data-keyboard="false"
    tabindex="-1"
    aria-labelledby="selectClientModalLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #e1f4ff">
                <h4 class="modal-title px-3" id="addItemModalLabel">
                    <b style="color: #0074d9 !important"
                        >Property Information</b
                    >
                </h4>
                <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body" style="padding: 13px 20px !important">
                <form
                    id=""
                    action="{{URL::route(getRouteAlias() . '.property.update')}}"
                    method="POST"
                >
                    @csrf
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 98%">
                            <span for="item_name"
                                >Property Name
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="hidden"
                                    id="property_id"
                                    required
                                    class="form-control"
                                    name="property_id"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                                <input
                                    type="text"
                                    id="property_name"
                                    required
                                    class="form-control"
                                    name="property_name"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Enter company name"
                                />
                            </div>
                        </div>

                        <!-- </div>
                    <div class="col"> -->

                    </div>
                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                    <div class="form-group" style="width: 47%">
                            <span for="">
                                Address 1
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="address1"
                                name="address1"
                                maxlength="60"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="Address 1"
                            />
                            <!-- </div> -->
                        </div>
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >Address 2
                                <label class="" for="">(Optional)</label></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="address2"
                                    maxlength="60"
                                    name="address2"
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="Address 2"
                                />
                            </div>
                        </div>

                        <!-- </div>
                <div class="col"> -->

                    </div>

                    <div
                        class="row px-2"
                        style="justify-content: space-around; display: flex"
                    >
                        <!-- <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for=""
                                >City
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="city"
                                    name="city"
                                    required
                                    class="form-control"
                                    style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    placeholder="City"
                                />
                            </div>
                        </div>

                        <!-- </div>
                <div class="col"> -->
                        <div class="form-group" style="width: 47%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >
                            <input
                                type="text"
                                id="state"
                                name="state"
                                required
                                style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                class="form-control"
                                placeholder="State"
                            />
                            <!-- </div> -->
                        </div>
                    </div>
                    <div class="row px-2" style="justify-content: space-around; display: flex">
                    <div class="form-group" style="width: 47%">
                        <span for="">
                            Zip Code
                            <label class="required_field" for="">*</label></span
                        >
                        <input
                            type="text"
                            id="zip_code"
                            name="zip_code"
                            required
                            style="height: 30px !important; text-align: left"
                            class="form-control"
                            placeholder="Zip Code"
                        />
                        </div>
                        <div class="form-group" style="width: 47%">
                            <span for="item_name"
                                >Account
                                <label class="required_field" for=""
                                    >*</label
                                ></span
                            >

                            <select
                                name="account"
                                style="height: 30px !important"
                                class="form-control"
                                required
                                id="account"
                            >
                            <option value="" selected>Select Account</option>
                                @foreach($accounts as $item)
                                <option value="{{$item->id}}">
                                    {{$item->company_name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- </div> -->

                    <!-- <div class="form-group">
            <b for="">Gross Margin</b>
          <input type="number" id="item_gross_margin" style="height: 30px !important;" class="form-control " placeholder="Item Gross Margin">
        </div> -->
                    <div class="d-flex" style="gap: 17px; justify-content: end">
                        <button
                            type="button"
                            data-dismiss="modal"
                            aria-label="Close"
                            class="btn px-5 py-2"
                            style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            onclick="addNewItemModel()"
                            class="btn primaryblue px-5 py-2"
                        >
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!--Delete Modal -->

<!--Delete Modal -->
@push('scripts') @include('organization.property.script')
<script>
    var datepickerInstance;
    $(document).ready(function () {
        datepickerInstance = $("#customDateAppend")
            .datepicker({
                container: ".custom-appending",
                format: "{{ getOrgDateFormat() }}", // Correct format tokens
                // Other options for the datepicker
            })
            .data("datepicker");

        // Attach an event listener to the changeDate event
        $("#customDateAppend").on("changeDate", function () {
            datepickerInstance.hide(); // Hide the datepicker
        });
    });
</script>
@endpush @endsection
