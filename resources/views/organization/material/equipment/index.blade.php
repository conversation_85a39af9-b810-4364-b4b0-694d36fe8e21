@extends('layouts.admin.master')
@section('title', 'Settings')

@section('section')
<style>
    .select2-container--default{
        margin-top: 21px !important;
    }
    .table_filter_dropdown{
        margin-top: -45px !important;
    }
    .dropdown-toggle::after {
    display: none !important;

}
</style>
    <section class="dashboard_main">

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content">
            <!-- <select name="" id="select-division" style="padding: 9px;
    margin: 12px 0px;
    background: transparent;
    border: 1.5px solid #E3E1E1FF;
    border-radius: 5px; padding-right: 30px;">
            <option selected disabled>Select Division</option>
            @foreach($div as $item)
            <option value="{{$item->id}}">{{$item->name}}</option>
            @endforeach
        </select> -->
                <div class="d-flex align-items-center gap-4 flex-wrap mb-4">
                    <a href="{{ route(getRouteAlias() . '.materials.index') }}" style="margin-left: 0px !important;" class="btn placeholder_btn px-5">Materials</a>
                    <a href="{{ route(getRouteAlias() . '.material.equipment.index') }}"
                        class="btn primaryblue px-5">Equipment</a>
                    <a href="{{ route(getRouteAlias() . '.material.jobCost.index') }}" class="btn placeholder_btn px-5">Other
                        Job Cost</a>
                    <!-- <a href="{{ route(getRouteAlias() . '.material.service-line.index') }}" class="btn placeholder_btn px-5">Service Line</a> -->
                    <!-- <a href="{{ route(getRouteAlias() . '.material.labor.index') }}" class="btn placeholder_btn px-5">Labor</a> -->
                    <a href="{{ route(getRouteAlias() . '.material.contractor.index') }}" class="btn  placeholder_btn px-5">Sub Contractor</a>
                </div>

                <div class="panel pt-4">
                    <div class="showMessage">

                    </div>
                    <div class="table_filter_header pb-3">
                        <h2 class="table_title">Equipment’s</h2>

                        <div class="filters equipment_table_filters">
                            {{-- <img height="24px" width="24px" src="{{ asset('admin_assets/images/icons/help-icon.svg') }}"
                                alt="help icon" data-toggle="modal" data-target="#editTableModal"> --}}
                            <input type="search" placeholder="Search" name="" id="filter_search"
                                class="clients_Detail_Search filter_search">
                            <select name="" id="select_filter"
                                class="table_filter_select select-small custom_selectBox" style="width:84px;">
                                <option value="Clear">Clear</option>
                                <option value="Hours">Hours</option>
                                <option value="Daily">Daily</option>
                                <option value="Weekly">Weekly</option>
                            </select>
                            <form method="" action="" id="equipment-material"
                                class="file_upload_button_form upload_file_wrapper w-fit" enctype="multipart/form-data">
                                @csrf
                                <label class="btn primaryblue primaryblue22 transparent" for="import_file_data">Import Equipment</label>
                                <input class="input_file d-none" type="file" name="file" id="import_file_data" accept=".xls, .xlsx">
                            </form>
                            {{-- <a href="#" class="btn primaryblue transparent px-5">Import Material</a> --}}
                        </div>
                    </div>

                    <table class="table table-striped custom_datatable display mx-0 my-4 yajra-datatable"
                        style="width:100%">

                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>UoM </th>
                                <th>Unit Cost</th>
                                {{-- <th>Gross Margin</th> --}}
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>

                    </table>


                </div>

            </div>

            <x-settings_component.help_modal />

        </div>
    </section>
    @push('scripts')
        @include('organization.material.equipment.script')
    @endpush
@endsection
