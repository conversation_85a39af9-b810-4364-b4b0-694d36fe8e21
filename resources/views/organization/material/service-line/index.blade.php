@extends('layouts.admin.master')
@section('title', 'Settings')

@section('section')
<style>
    .dropdown-toggle::after {
    display: none !important;

}
</style>
    <section class="dashboard_main">

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content">
                <div class="d-flex align-items-center gap-4 flex-wrap">
                    <a href="{{ route(getRouteAlias() . '.materials.index') }}" style="margin-left: 0px !important;" class="btn placeholder_btn px-5">Materials</a>
                    <a href="{{ route(getRouteAlias() . '.material.equipment.index') }}"
                        class="btn placeholder_btn px-5">Equipment</a>
                    <a href="{{ route(getRouteAlias() . '.material.jobCost.index') }}" class="btn placeholder_btn px-5">Other
                        Job Cost</a>
                    <!-- <a href="{{ route(getRouteAlias() . '.material.service-line.index') }}" class="btn primaryblue px-5">Service Line</a> -->
                     <!-- <a href="{{ route(getRouteAlias() . '.material.labor.index') }}" class="btn placeholder_btn px-5">Labor</a> -->
                     <a href="{{ route(getRouteAlias() . '.material.contractor.index') }}" class="btn  placeholder_btn px-5">Sub Contractor</a>
                </div>

                <div class="table_tabs_filter mt-4 mb-4 nav" role="group">
                    <input type="radio" class="btn-check" name="material" id="hard_material" autocomplete="off" checked>
                    <label class="btn btn-tab  yajra_hard_material" style="margin-top: -5px;
    text-align: left;
    margin-left: -21px;" for="hard_material" data-toggle="tab" data-target="#hard-material"
                        aria-controls="hard-material">Service Line</label>

                    <input type="radio" class="btn-check" name="material" id="plan_material" autocomplete="off">
                    <label class="btn btn-tab yajra_plant_material" style="margin-top: -5px;
    text-align: left;
    margin-left: -21px;" for="plan_material" data-toggle="tab"
                        data-target="#plan-material" aria-controls="plan-material">Work Type</label>
                </div>

                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="hard-material" role="tabpanel"
                        aria-labelledby="hard-material-tab">
                        <x-settings_component.materials.service_line_materials_table />
                    </div>
                    <div class="tab-pane fade" id="plan-material" role="tabpanel" aria-labelledby="plan-material-tab">
                        <x-settings_component.materials.work_type_service_line_items_table />
                    </div>
                </div>


            </div>
        </div>

        <x-settings_component.help_modal />


    </section>
    @push('scripts')
        @include('organization.material.service-line.script')
    @endpush
@endsection
