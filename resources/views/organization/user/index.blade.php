@extends('layouts.admin.master')
@section('title', 'Settings')

@section('section')
<style>
    .dropdown-toggle::after {
    display: none !important;

}
.hidemodelbtn {
        font-size: 18px !important;
        color: #7e8a9d !important;
    }
    .hidemodelbtn:focus {
        outline: none !important;
        box-shadow: none !important;
    }
    .hidemodelbtn:hover {
        cursor: pointer !important;
    }
    @media screen and (max-width: 580px) {
        .hidemodelbtn {
            font-size: 15px !important;
        }
    }
</style>
    <section class="dashboard_main">

        <div class="settings_tab_grid">

            <x-settings_component.settings_tab />

            <div class="settings_content">

                <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
                    <h2 class="sub_heading">Users</h2>
                    <a href="{{ route(getRouteAlias() . '.users.invite') }}" class="btn primaryblue">+ Invite New User</a>
                </div>

                <div class="panel mt-4">
                    <div class="panel_header">
                        <h2 class="panel_title">Team Management</h2>
                    </div>

                    <div class="active_users_indication mt-4">
                        <label class="title">Active User</label>
                        <div class="active_count">{{ $onlineUsers }} OF {{ $totalUser }}</div>
                    </div>

                    <div class="table_wrapper mt-5">
                        <table class="table table-striped custom_datatable yajra-datatable" style="width:100%">

                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Last Login</th>
                                    <th>Status</th>
                                    <th class="text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody>


                            </tbody>
                        </table>
                    </div>

                </div>



            </div>

        </div>

        <div class="operation_modal modal fade" id="changeStatus" tabindex="-1" aria-labelledby="changeStatusLabel"
            aria-hidden="true">
            <div class="modal-dialog m400">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="changeStatusLabel">Edit</h5>
                        <button
                    type="button"
                    class="btn-close hidemodelbtn px-3"
                    data-dismiss="modal"
                    aria-label="Close"
                    style="border: none; background-color: transparent"
                >
                <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                </button>
                    </div>
                    <div class="modal-body">
                        <h2 class="text-md text-primary mb-3">Change Status</h2>
                        <p class="text-placeholder text-sm">You can change team status</p>

                        <div class="row">
                            {{-- <div class="col-6">
                                <div class="radio_group mt-5">
                                    <input class="" type="radio" name="project_status" id="Pending">
                                    <label class="label" for="Pending">Pending</label>
                                </div>
                            </div> --}}
                            <div class="col-6">
                                <div class="radio_group mt-5">
                                    <input class="" type="radio" name="project_status" id="Active">
                                    <label class="label" for="Active">Active</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="radio_group mt-5">
                                    <input class="" type="radio" name="project_status" id="Inactive">
                                    <label class="label" for="Inactive">Inactive</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer pt-5">
                        <button type="button" class="btn primaryblue w-100 updateStatus">Update</button>
                    </div>
                </div>
            </div>
        </div>

        @include('layouts.admin.confirmation-modal')
        @include('layouts.partials.success-modal')


    </section>
    @push('scripts')
        @include('organization.user.script')
    @endpush
@endsection
