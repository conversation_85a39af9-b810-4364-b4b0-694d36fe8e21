<div class="modal fade select-client" id="addVendorBillModal" data-backdrop="static"
    data-keyboard="false" tabindex="-1" aria-labelledby="addVendorBillLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg">
        <div class="modal-content border-0 rounded-3 shadow">
            <div class="modal-header" style="background: #e1f4ff;">
                <h5 class="modal-title fw-semibold text-primary" id="addVendorBillLabel"
                    style="font-size: 19px;font-weight: 600;">Add Vendor Bill</h5>
                <button type="button" class="btn-close px-3" data-dismiss="modal"
                    aria-label="Close"
                    style="background: transparent; border: none;font-size: 27px;">
                    <i class="fa fa-times text-muted fs-5" aria-hidden="true"></i>
                </button>
            </div>
            <form id="addVendorBillForm" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="item_id" id="item_id">
                <input type="hidden" name="bill_id" id="bill_id">
                <div class="modal-body py-4 px-4">
                                        <!-- Category Radios - Generated from Enum -->
                    <div class="d-flex flex-wrap gap-4 mb-4">
                        @foreach(\App\Enums\BillCategoryType::cases() as $category)
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="category"
                                    id="{{ strtolower(str_replace(' ', '_', $category->radioValue())) }}"
                                    value="{{ $category->radioValue() }}">
                                <label class="form-check-label" for="{{ strtolower(str_replace(' ', '_', $category->radioValue())) }}">
                                    &nbsp; {{ $category->radioValue() }}
                                </label>
                            </div>
                            &nbsp; &nbsp;
                        @endforeach
                    </div>

                    <!-- Vendor Name -->
                    <div class="row mb-3">
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-semibold">Vendor Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" name="vendor_name" class="form-control bill"
                                placeholder="Enter Account name">
                            <small class="text-danger" id="vendor_name"></small>
                        </div>
                        <!-- Invoice Number -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Invoice Number <span
                                    class="text-danger">*</span></label>
                            <input type="number" name="invoice_number" id="invoice_number"
                                class="form-control bill" placeholder="Invoice Number">
                            <small class="text-danger" id="invoice_number"></small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Invoice Amount <span
                                    class="text-danger">*</span></label>
                            <input type="number" name="invoice_amount" id="invoice_amount"
                                class="form-control bill" placeholder="Enter Invoice Amount">
                            <small class="text-danger" id="invoice_amount"></small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Due Date <span
                                    class="text-danger">*</span></label>
                            <div class="month-input-container" style="position: relative;">
                                <input type="date" name="due_date" id="due_date"
                                    class="form-control start-date bill">
                                <div class="calendar-icon"
                                    style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);">
                                    <img src="{{ asset('vector.png') }}" alt="">
                                </div>
                            </div>
                            <small class="text-danger" id="due_date"></small>
                        </div>

                        <!-- Invoice Date -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">QTY <span
                                    class="text-danger">*</span></label>
                            <div class="month-input-container" style="position: relative;">
                                <input type="input" name="actual_qty" id="actual_qty"
                                    class="form-control bill">
                            </div>
                            <small class="text-danger" id="actual_qty"></small>
                        </div>

                    </div>

                    <!-- Dropzone -->
                    <div class="mb-4">
                        <div id="imageDropzone" class="border rounded p-4 text-center"
                            style="border-style: dashed; background: #f8f9fa;">
                            <input type="file" name="invoice_image" id="invoice_image"
                                class="d-none" accept="image/*">
                            <div id="dropzoneContent">
                                <img src="{{ asset('operationIcon/uploadIcon.png') }}"
                                    alt="" style="cursor: pointer;">
                            </div>
                            <div id="imagePreview" class="d-none">
                                <img id="previewImage" src="#" alt="Preview"
                                    class="img-thumbnail mt-2" style="max-height: 150px;">
                                <button type="button" class="btn btn-sm btn-danger mt-2"
                                    onclick="removeImage()">
                                    <i class="fas fa-trash-alt"></i> Remove
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-3">
                        <button type="button" class="btn btn-outline-primary"
                            data-dismiss="modal"
                            style="width: 126px; height: 40px; font-size: 15px; border-radius: 8px;">Cancel</button>
                        &nbsp;&nbsp;
                        <button type="submit" class="btn btn-primary"
                            style="width: 126px; height: 40px; font-size: 15px; border-radius: 8px;">Add</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade select-client" id="editVendorBillModal" data-backdrop="static"
    data-keyboard="false" tabindex="-1" aria-labelledby="editVendorBillLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg">
        <div class="modal-content border-0 rounded-3 shadow">
            <div class="modal-header" style="background: #e1f4ff;">
                <h5 class="modal-title fw-semibold text-primary" id="editVendorBillLabel"
                    style="font-size: 19px;font-weight: 600;">Edit Vendor Bill</h5>
                <button type="button" class="btn-close px-3" data-dismiss="modal"
                    aria-label="Close"
                    style="background: transparent; border: none;font-size: 27px;">
                    <i class="fa fa-times text-muted fs-5" aria-hidden="true"></i>
                </button>
            </div>
            <form id="editVendorBillForm" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <input type="hidden" name="item_id" id="edit_item_id">
                <input type="hidden" name="bill_id" id="edit_bill_id">
                <div class="modal-body py-4 px-4">
                                        <!-- Category Radios - Generated from Enum -->
                    <div class="d-flex flex-wrap gap-4 mb-4">
                        @foreach(\App\Enums\BillCategoryType::cases() as $category)
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="category"
                                    id="edit_{{ strtolower(str_replace(' ', '_', $category->radioValue())) }}"
                                    value="{{ $category->radioValue() }}">
                                <label class="form-check-label" for="edit_{{ strtolower(str_replace(' ', '_', $category->radioValue())) }}">
                                    &nbsp; {{ $category->radioValue() }}
                                </label>
                            </div>
                            &nbsp; &nbsp;
                        @endforeach
                    </div>

                    <!-- Vendor Name -->
                    <div class="row mb-3">
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-semibold">Vendor Name <span
                                    class="text-danger">*</span></label>
                            <input type="text" name="vendor_name" id="edit_vendor_name"
                                class="form-control bill" placeholder="Enter Account name">
                            <small class="text-danger" id="edit_vendor_name_error"></small>
                        </div>
                        <!-- Invoice Number -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Invoice Number <span
                                    class="text-danger">*</span></label>
                            <input type="number" name="invoice_number" id="edit_invoice_number"
                                class="form-control bill" placeholder="Invoice Number">
                            <small class="text-danger" id="edit_invoice_number_error"></small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Invoice Amount <span
                                    class="text-danger">*</span></label>
                            <input type="number" name="invoice_amount" id="edit_invoice_amount"
                                class="form-control bill" placeholder="Enter Invoice Amount">
                            <small class="text-danger" id="edit_invoice_amount_error"></small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">Due Date <span
                                    class="text-danger">*</span></label>
                            <div class="month-input-container" style="position: relative;">
                                <input type="date" name="due_date" id="edit_due_date"
                                    class="form-control start-date bill">
                                <div class="calendar-icon"
                                    style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);">
                                    <img src="{{ asset('vector.png') }}" alt="">
                                </div>
                            </div>
                            <small class="text-danger" id="edit_due_date_error"></small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-semibold">QTY <span
                                    class="text-danger">*</span></label>
                            <div class="month-input-container" style="position: relative;">
                                <input type="input" name="actual_qty" id="edit_actual_qty"
                                    class="form-control bill">
                            </div>
                            <small class="text-danger" id="edit_actual_qty_error"></small>
                        </div>
                    </div>

                    <!-- Dropzone -->
                    <div class="mb-4">
                        <div id="editImageDropzone" class="border rounded p-4 text-center"
                            style="border-style: dashed; background: #f8f9fa;">
                            <input type="file" name="invoice_image" id="edit_invoice_image"
                                class="d-none" accept="image/*">
                            <div id="editDropzoneContent">
                                <img src="{{ asset('operationIcon/uploadIcon.png') }}"
                                    alt="" style="cursor: pointer;">
                            </div>
                            <div id="editImagePreview" class="d-none">
                                <img id="editPreviewImage" src="#" alt="Preview"
                                    class="img-thumbnail mt-2" style="max-height: 150px;">
                                <button type="button" class="btn btn-sm btn-danger mt-2"
                                    onclick="removeEditImage()">
                                    <i class="fas fa-trash-alt"></i> Remove
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="current_image" id="current_image">
                    </div>
                    {{--
                    <!-- Buttons -->
                    <div class="d-flex justify-content-end gap-3">
                        <button type="button" class="btn btn-outline-primary"
                            data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div> --}}

                    <div class="d-flex justify-content-end gap-3">
                        <button type="button" class="btn btn-outline-primary"
                            data-dismiss="modal"
                            style="width: 126px; height: 40px; font-size: 15px; border-radius: 8px;">Cancel</button>
                        &nbsp;&nbsp;
                        <button type="submit" class="btn btn-primary"
                            style="width: 126px; height: 40px; font-size: 15px; border-radius: 8px;">Update</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js"></script>


<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel"
    aria-hidden="true">
    <div class="modal-dialog delete-modal">
        <div class="modal-content">
            <div class="modal-body text-center">
                <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}"
                    alt="delete icon" class="delete-icon mb-3">
                <h2 class="delete-request">Delete</h2>
                <p class="are-sure">Are you sure you want to remove this request?</p>

                <input type="hidden" id="delete_item_id">

                <div class="buttons-wraper">
                    <button type="button" class="cancel-btn" data-dismiss="modal"
                        style="height: 41px; color: #0074d9; border: 2px solid black;">Cancel</button>
                    <button type="button" class="conform-btn" id="confirmDeleteBtn"
                        style="background-color: #0074d9;height: 41px;">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const dropzone = document.getElementById('imageDropzone');
    const fileInput = document.getElementById('invoice_image');
    const dropzoneContent = document.getElementById('dropzoneContent');
    const imagePreview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');

    dropzone.addEventListener('click', () => fileInput.click());

    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                dropzoneContent.classList.add('d-none');
                imagePreview.classList.remove('d-none');
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(event => {
        dropzone.addEventListener(event, e => {
            e.preventDefault();
            e.stopPropagation();
        }, false);
    });

    dropzone.addEventListener('drop', function(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });

    function removeImage() {
        fileInput.value = '';
        dropzoneContent.classList.remove('d-none');
        imagePreview.classList.add('d-none');
    }
</script>

<script>
    // ------------------ Add Modal Radio Button Handling -------------------
    $(document).on('click', '.open-bill-modal', function() {
        let itemId = $(this).data('item-id');
        $('#item_id').val(itemId);
        $('.text-danger').text('');
        removeImage();

        // Get the category map from the view
        let itemCategoryMap = @json($itemCategoryMap);
        let category = itemCategoryMap[itemId];

        // Debug logs
        console.log('Item ID:', itemId);
        console.log('Category from map:', category);

        // Uncheck all radio buttons first
        $('input[name="category"]').prop('checked', false);

        // Select the appropriate radio button
        if (category) {
            // Find the radio button with matching value
            let $radio = $(`input[name="category"][value="${category}"]`);
            if ($radio.length) {
                $radio.prop('checked', true);
                console.log('Selected radio:', $radio.attr('id'));
            } else {
                console.warn('No radio found for category:', category);
                $('#{{ strtolower(str_replace(' ', '_', \App\Enums\BillCategoryType::CONTRACTORS->radioValue())) }}').prop('checked', true);
            }
        } else {
            console.log('No category found, defaulting to contractors');
            $('#{{ strtolower(str_replace(' ', '_', \App\Enums\BillCategoryType::CONTRACTORS->radioValue())) }}').prop('checked', true);
        }
    });

    // ------------------ Edit Image Dropzone Handling -------------------
    const editDropzone = document.getElementById('editImageDropzone');
    const editFileInput = document.getElementById('edit_invoice_image');
    const editDropzoneContent = document.getElementById('editDropzoneContent');
    const editImagePreview = document.getElementById('editImagePreview');
    const editPreviewImage = document.getElementById('editPreviewImage');

    editDropzone.addEventListener('click', () => editFileInput.click());

    editFileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                editPreviewImage.src = e.target.result;
                editDropzoneContent.classList.add('d-none');
                editImagePreview.classList.remove('d-none');
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(event => {
        editDropzone.addEventListener(event, e => {
            e.preventDefault();
            e.stopPropagation();
        }, false);
    });

    editDropzone.addEventListener('drop', function(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length > 0) {
            editFileInput.files = files;
            editFileInput.dispatchEvent(new Event('change'));
        }
    });

    function removeEditImage() {
        editFileInput.value = '';
        editDropzoneContent.classList.remove('d-none');
        editImagePreview.classList.add('d-none');
        $('#current_image').val('');
    }


    $(document).on('click', '.edit-bill', function() {
        let billId = $(this).data('bill-id');
        $('.text-danger').text('');
        removeEditImage();

        $.ajax({
            url: `/organization/item-bill/${billId}`,
            method: "GET",
            success: function(response) {
                let bill = response.bill;

                // Set form values
                $('#edit_item_id').val(bill.item_id);
                $('#edit_bill_id').val(bill.id);
                $('#edit_vendor_name').val(bill.vendor_name);
                $('#edit_invoice_number').val(bill.invoice_number);
                $('#edit_invoice_amount').val(bill.invoice_amount);
                $('#edit_due_date').val(bill.due_date?.split(' ')[0] || '');
                $('#edit_actual_qty').val(bill.actual_qty);

                // Fetch category_type from EstimateItem and map it
                $.ajax({
                    url: `/organization/estimate-items/${bill.item_id}/category`,
                    method: "GET",
                    success: function(categoryResponse) {
                        let categoryType = categoryResponse
                            .category_type.trim().toLowerCase();

                        // Map DB values to form radio values using enum
                        const categoryMap = @json(collect(\App\Enums\BillCategoryType::cases())->mapWithKeys(function($case) {
                            return [$case->value => $case->radioValue()];
                        }));

                        // Clear all category radios first
                        $('input[name="category"]').prop('checked',
                            false);

                        // Select the correct radio if mapping exists
                        if (categoryMap.hasOwnProperty(
                                categoryType)) {
                            let radioValue = categoryMap[
                                categoryType];
                            $(`input[name="category"][value="${radioValue}"]`)
                                .prop('checked', true);
                        }

                        console.log('Mapped Category:', categoryMap[
                            categoryType]);
                    },
                    error: function() {
                        console.error('Failed to fetch category');
                    }
                });

                // Handle image
                if (bill.invoice_image) {
                    $('#current_image').val(bill.invoice_image);
                    $('#editPreviewImage').attr('src', bill.invoice_image);
                    $('#editDropzoneContent').addClass('d-none');
                    $('#editImagePreview').removeClass('d-none');
                }

                // Show modal
                $('#editVendorBillModal').modal('show');
            },
            error: function() {
                toastr.error('Failed to fetch bill data');
            }
        });
    });
    // Submit Edit Form (unchanged from your working version)
    $(document).on('submit', '#editVendorBillForm', function(e) {
    e.preventDefault();

    let form = $(this)[0];
    let formData = new FormData(form);
    let billId = $('#edit_bill_id').val();

    $.ajax({
        url: `/organization/item-bill/${billId}`,
        method: "POST",
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-HTTP-Method-Override': 'PUT'
        },
        success: function(response) {
            toastr.success(response.message);
            $('#editVendorBillModal').modal('hide');
            $('#editVendorBillForm')[0].reset();
            removeEditImage();

            let bill = response.bill;
            let selector = `.open-bill-modal[data-item-id="${bill.item_id}"]`;
            let $subTable = $(selector).closest('tr').next('.sub-details').find('tbody');
            let $oldRow = $subTable.find(`.edit-bill[data-bill-id="${bill.id}"]`).closest('tr');

            let updatedRow = `
            <tr>
                <td>${bill.vendor_name}</td>
                <td>${bill.invoice_number}</td>
                <td>${bill.actual_qty}</td>
                <td>${bill.due_date_formatted}</td>
                <td>$${parseFloat(bill.invoice_amount).toFixed(2)}</td>
                <td class="text-center">
                    ${bill.invoice_image ? `<img src="{{ asset('operationIcon/opImage.png') }}" alt="View" class="img-fluid view-image" style="cursor:pointer; width: 24px;" data-url="${bill.invoice_image}">` : ''}
                </td>
                <td class="text-end">
                    <img src="{{ asset('operationIcon/opEdit.png') }}" class="edit-bill" data-bill-id="${bill.id}" style="cursor:pointer;" />
                    <img src="{{ asset('operationIcon/opDelete.png') }}" class="delete-bill" data-id="${bill.id}" style="cursor:pointer;" />
                </td>
            </tr>`;

            $oldRow.replaceWith(updatedRow);

            // Recalculate totals
            const $parentRow = $(selector).closest('tr');
            const $qtyCell = $parentRow.find('td').eq(2);
            const $costCell = $parentRow.find('td').eq(4);
            const $varCell = $parentRow.find('td').eq(5);
            const $estCostCell = $parentRow.find('td').eq(3);

            let totalQty = 0, totalCost = 0;
            $parentRow.next('.sub-details').find('tbody tr').each(function() {
                totalQty += parseFloat($(this).find('td').eq(2).text()) || 0;
                totalCost += parseFloat($(this).find('td').eq(4).text().replace(/[^\d.-]/g, '')) || 0;
            });

            let estCost = parseFloat($estCostCell.text().replace(/[^\d.-]/g, '')) || 0;
            let newVar = estCost - totalCost;

            $qtyCell.text(totalQty);
            $costCell.text(`$${totalCost.toFixed(2)}`);
            $varCell.text(`${newVar >= 0 ? '+' : '-'}$${Math.abs(newVar).toFixed(2)}`);

            // Redirect to refresh page after edit
            if (typeof refreshOperationData === 'function') {
                refreshOperationData();
            }
        },
        error: function(xhr) {
            if (xhr.status === 422) {
                let errors = xhr.responseJSON.errors;
                $.each(errors, function(key, value) {
                    $(`#edit_${key}_error`).text(value[0]);
                });
            } else {
                toastr.error('Something went wrong!');
            }
        }
    });
});

    $(document).ready(function() {
        $('#addVendorBillForm').on('submit', function(e) {
            e.preventDefault();

            let form = $(this)[0];
            let formData = new FormData(form);
            let itemId = $('#addVendorBillForm input[name="item_id"]').val();

            $.ajax({
                url: "{{ route('organization.item-bill.store') }}",
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    try {
                        toastr.success(response.message);

                        // Properly hide modal
                        $('#addVendorBillModal').modal('hide').hide();
                        $('.modal-backdrop').remove();
                        $('body').removeClass('modal-open');

                        $('#addVendorBillForm')[0].reset();
                        removeImage();

                        let bill = response.bill;
                        let selector =
                            `.open-bill-modal[data-item-id="${bill.item_id}"]`;

                        // Remove "no bills" row if exists
                        $(selector).closest('tr')
                            .next('.sub-details')
                            .find('tbody .text-muted')
                            .closest('tr')
                            .remove();

                        let newRow = `
                            <tr>
                                <td>${bill.vendor_name}</td>
                                <td>${bill.invoice_number}</td>
                                <td>${bill.actual_qty}</td>
                                <td>${bill.due_date_formatted}</td>
                                <td>$${parseFloat(bill.invoice_amount).toFixed(2)}</td>
                                <td class="text-center">
                                    ${bill.invoice_image ? `<img src="{{ asset('operationIcon/opImage.png') }}"
                                        alt="View"
                                        class="img-fluid view-image"
                                        style="cursor:pointer; width: 24px;"
                                        data-url="${bill.invoice_image}">` : ''}
                                </td>
                                <td class="text-end">
                                    <img src="{{ asset('operationIcon/opEdit.png') }}"
                                        class="edit-bill"
                                        data-bill-id="${bill.id}"
                                        style="cursor:pointer;" />
                                    <img src="{{ asset('operationIcon/opDelete.png') }}"
                                        class="delete-bill"
                                        data-id="${bill.id}"
                                        style="cursor:pointer;" />
                                </td>
                            </tr>`;

                        $(selector).closest('tr')
                            .next('.sub-details')
                            .find('tbody')
                            .prepend(newRow);

                        // Update calculations
                        updateCalculations(selector, bill);

                        // Redirect to refresh page with latest data
                        if (typeof refreshOperationData === 'function') {
                            refreshOperationData();
                        } else {
                            location.reload();
                        }

                    } catch (e) {
                        console.error('Error in success handler:', e);
                    }
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        $.each(errors, function(key, value) {
                            $('[name="' + key + '"]').next(
                                'small.text-danger').text(
                                value[0]);
                        });
                    } else {
                        toastr.error('Something went wrong!');
                    }
                },
                complete: function() {
                    // Any cleanup if needed
                }
            });
        });
    });

    function updateCalculations(selector, bill) {
        try {
            // Update row values
            const $itemRow = $(selector).closest('tr');
            const $qtyCell = $itemRow.find('td').eq(2);
            const $costCell = $itemRow.find('td').eq(4);
            const $varCell = $itemRow.find('td').eq(5);
            const $estCostCell = $itemRow.find('td').eq(3);

            let oldQty = parseFloat($qtyCell.text()) || 0;
            let oldCost = parseFloat($costCell.text().replace(/[^\d.-]/g, '')) || 0;
            let estCost = parseFloat($estCostCell.text().replace(/[^\d.-]/g, '')) || 0;

            let newQty = oldQty + parseFloat(bill.actual_qty);
            let newCost = oldCost + parseFloat(bill.invoice_amount);
            let newVar = estCost - newCost;

            $qtyCell.text(newQty);
            $costCell.text(`$${newCost.toFixed(2)}`);
            $varCell.text(`${newVar >= 0 ? '+' : '-'}$${Math.abs(newVar).toFixed(2)}`);

            // Category totals
            const $categoryRow = $itemRow.prevAll('tr[class*="-main-row"]').first();
            if ($categoryRow.length) {
                const $catQtyCell = $categoryRow.find('td').eq(2);
                const $catCostCell = $categoryRow.find('td').eq(4);
                const $catVarCell = $categoryRow.find('td').eq(5);
                const $catEstCostCell = $categoryRow.find('td').eq(3);

                let catOldQty = parseFloat($catQtyCell.text()) || 0;
                let catOldCost = parseFloat($catCostCell.text().replace(/[^\d.-]/g, '')) || 0;
                let catEstCost = parseFloat($catEstCostCell.text().replace(/[^\d.-]/g, '')) || 0;

                let newCatQty = catOldQty + parseFloat(bill.actual_qty);
                let newCatCost = catOldCost + parseFloat(bill.invoice_amount);
                let newCatVar = catEstCost - newCatCost;

                $catQtyCell.text(newCatQty);
                $catCostCell.text(`$${newCatCost.toFixed(2)}`);
                $catVarCell.text(`${newCatVar >= 0 ? '+' : '-'}$${Math.abs(newCatVar).toFixed(2)}`);
            }
        } catch (e) {
            console.error('Error in calculations:', e);
        }
    }

    let deleteBillId = null;

    // When delete icon is clicked
    $(document).on('click', '.delete-bill', function() {
        deleteBillId = $(this).data('id');
        $('#delete_item_id').val(deleteBillId);
        $('#deleteModal').modal('show');
    });

    // When "Yes" button is clicked in modal
    $('#confirmDeleteBtn').on('click', function() {
        const billId = $('#delete_item_id').val();

        $.ajax({
            url: `/organization/item-bill/${billId}`,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success('Vendor Bill deleted successfully');
                $(`img.delete-bill[data-id="${billId}"]`).closest('tr')
                    .remove();
                $('#deleteModal').modal('hide');

                // Redirect to refresh page after delete
                if (typeof refreshOperationData === 'function') {
                    refreshOperationData();
                }
            },
            error: function() {
                toastr.error('Failed to delete bill');
            }
        });
    });


    $(document).on('blur', '.labor-qty-input, .labor-cost-input', function() {
        let itemId = $(this).data('item-id');
        let qty = $(`.labor-qty-input[data-item-id="${itemId}"]`).val();
        let cost = $(`.labor-cost-input[data-item-id="${itemId}"]`).val();

        $.ajax({
            url: '/organization/labor-bill',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                item_id: itemId,
                actual_qty: qty,
                actual_cost: cost
            },
            success: function(res) {
                location.reload();
                toastr.success('Labor data saved');

                // Build row HTML
                let bill = res.bill;
                let selector =
                    `.open-bill-modal[data-item-id="${bill.item_id}"]`;

                // Remove "no bills" row if exists
                $(selector).closest('tr')
                    .next('.sub-details')
                    .find('tbody .text-muted')
                    .closest('tr')
                    .remove();

                // Check if row already exists (based on vendor name 'Labor Entry')
                let existingRow = $(selector).closest('tr')
                    .next('.sub-details')
                    .find('tbody tr')
                    .filter(function() {
                        return $(this).find('td:first').text().trim() ===
                            'Labor Entry';
                    });

                let newRow = `
                <tr>
                    <td>Labor Entry</td>
                    <td>N/A</td>
                    <td>${bill.actual_qty}</td>
                    <td>${bill.due_date_formatted}</td>
                    <td>$${parseFloat(bill.invoice_amount).toFixed(2)}</td>
                    <td class="text-center">
                        ${bill.invoice_image ? `<a href="${bill.invoice_image}" target="_blank"><img
                            src="{{ asset('operationIcon/opImage.png') }}" alt="View"
                            style="cursor:pointer;"></a>` : ''}
                    </td>
                    <td class="text-center">
                        <img src="{{ asset('operationIcon/opEdit.png') }}"
                            class="edit-bill" data-bill-id="${bill.id}"
                            style="cursor:pointer;" />
                    </td>
                    <td class="text-center">
                        <img src="{{ asset('operationIcon/opDelete.png') }}"
                            class="delete-bill" data-id="${bill.id}"
                            style="cursor:pointer;" />
                    </td>
                </tr>
            `;

                if (existingRow.length) {
                    existingRow.replaceWith(newRow);
                } else {
                    $(selector).closest('tr')
                        .next('.sub-details')
                        .find('tbody')
                        .prepend(newRow);
                }

                // Optional: recalculate variance
                updateCalculations(selector, bill);

                // Redirect to refresh page after labor update
                if (typeof refreshOperationData === 'function') {
                    refreshOperationData();
                }
            },
            error: function() {
                toastr.error('Error saving labor bill');
            }
        });
    });
</script>






<style>
    #imageDropzone:hover {
        border-color: #adb5bd !important;
    }
</style>









<div class="modal fade" id="imagePreviewModal" data-backdrop="static"
    data-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 800px;">
        <div class="modal-content border-0">
            <div class="modal-header" style="background: #e1f4ff;">
                <h5 class="modal-title fw-semibold text-primary" style="font-size: 19px;">Bill
                    Image</h5>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-sm btn-link text-primary px-2 mb-1"
                        id="downloadImageBtn"
                        style="background: transparent; border: none; font-size: 20px;"
                        title="Download Image">
                        <img src="{{ asset('operationIcon/billDownload.png') }}" alt="">
                    </button>
                    <button type="button" class="btn-close px-3" data-dismiss="modal"
                        aria-label="Close"
                        style="background: transparent; border: none;font-size: 27px;">
                        <i class="fa fa-times text-muted fs-5" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body text-center d-flex justify-content-center align-items-center"
                style="min-height: 300px;">
                <img id="fullSizePreview" src="" class="img-fluid rounded"
                    style="max-height: 60vh; width: 501px;">
            </div>
        </div>
    </div>
</div>



<script>
    let currentImageUrl = '';

    function showImagePreview(imageUrl) {
        const previewModal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
        const fullSizeImg = document.getElementById('fullSizePreview');

        currentImageUrl = imageUrl;
        fullSizeImg.src = imageUrl;

        previewModal.show();
    }

    $(document).on('click', '.view-image', function() {
        const imageUrl = $(this).data('url');
        showImagePreview(imageUrl);
    });

    document.getElementById('downloadImageBtn').addEventListener('click', function() {
        if (!currentImageUrl) return;
        const link = document.createElement('a');
        link.href = currentImageUrl;
        link.download = currentImageUrl.split('/').pop() || 'bill_image';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
</script>



<style>
    #imagePreviewModal .modal-dialog {
        max-width: 800px;
        /* Adjust as needed */
    }

    #imagePreviewModal .modal-content {
        background: white;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    }

    #imagePreviewModal .modal-body {
        background-color: white;
        padding: 20px;
    }

    #imageCaption {
        background: #f8f9fa;
        padding: 5px 10px;
        border-radius: 5px;
        display: inline-block;
        margin-top: 10px;
    }

    #downloadImageBtn:hover {
        color: #0d6efd !important;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }
</style>
