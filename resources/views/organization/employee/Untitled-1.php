<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\Division;
use App\Models\Employee;
use App\Traits\PermissionMiddlewareTrait;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class EmployeeController extends Controller
{
    use PermissionMiddlewareTrait;

    // public function __construct()
    // {
    //     $permissionsMap = [
    //         'index' => ['view_employee', 'create_employee', 'edit_employee', 'employee_listing'],
    //         'listing' => ['view_employee', 'create_employee', 'edit_employee', 'employee_listing'],
    //         'store' => ['create_employee'],
    //         'edit' => ['edit_employee'],
    //         'update' => ['edit_employee'],
    //         'destroy' => ['delete_employee'],
    //         'checkEmployeeNumber' => ['create_employee', 'edit_employee'],
    //     ];
    //     $this->applyPermissionMiddleware($permissionsMap);
    // }

    public function index()
    {
        $divisions = Division::all();

        return view('organization.employee.index', compact('divisions'));
    }

    public function listing(Request $request)
    {
        // Get the organization ID of the logged-in user
        $organizationId = getOrganizationId(); // Fetch organization ID based on logged-in user

        // Fetch employees based on status, search query, and organization_id
        $employees = Employee::latest()
            ->where('organization_id', $organizationId)  // Filter by organization_id
            ->when(request('employee_status'), function ($query) {
                if (request('employee_status') !== 'Clear') {
                    $query->where('status', request('employee_status'));
                }
            })
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                        ->orWhere('last_name', 'like', "%{$request->search}%")
                        ->orWhere('phone_number', 'like', "%{$request->search}%")
                        ->orWhere('email_address', 'like', "%{$request->search}%");
                });
            })
            ->with('division')
            ->select('id', 'employee_number', 'first_name', 'last_name', 'phone_number', 'email_address', 'title', 'division_id', 'status', 'pay_rate', 'overtime_rate', 'drivers_license_number', 'state_issued', 'date_issued', 'expiration_date', 'dot_medical_card_issued_date', 'dot_medical_card_expiration_date');

        // Return DataTables response
        return DataTables::eloquent($employees)
            ->addColumn('division', function (Employee $employee) {
                return $employee->division ? $employee->division->name : 'N/A';
            })
            ->addColumn('status', function (Employee $employee) {
                return '<div class="status '.($employee->status == 'Active' ? 'success' : 'warning').'">'.ucfirst($employee->status).'</div>';
            })
            ->addColumn('action', function (Employee $employee) {
                $user = auth('web')->user();
                $html = '<div class="dropdown border-none bg-none mx-auto w-fit">
                <button style="background: transparent !important; border: none !important; box-shadow: none !important;" 
                    class="btn btn-secondary dropdown-toggle" 
                    type="button" 
                    id="dropdown'.$employee->id.'" 
                    data-toggle="dropdown" 
                    aria-expanded="false">
                    <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdown'.$employee->id.'">';

                $html .= '<li><a class="dropdown-item view-employee" href="javascript:void(0)" data-id="'.encodeId($employee->id).'">View</a></li>';
                $html .= '<li><a class="dropdown-item edit-employee" href="javascript:void(0)" data-id="'.encodeId($employee->id).'">Edit</a></li>';
                $html .= '<li><a class="dropdown-item delete-employee" href="javascript:void(0)" data-id="'.encodeId($employee->id).'">Delete</a></li>';
                $html .= '</ul></div>';

                return $html;
            })
            ->rawColumns(['status', 'action'])
            ->only(['employee_number', 'first_name', 'last_name', 'phone_number', 'email_address', 'division', 'status', 'action'])
            ->toJson();
    }

    public function store(Request $request)
    {
        // Validate the input data
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'email_address' => 'required|email',
            'title' => 'nullable|string|max:255',
            'division_id' => 'required|exists:divisions,id',
            'status' => 'required|string',
            'pay_rate' => 'required|numeric',
            'overtime_rate' => 'required|numeric',
            'employee_number' => 'required', // employee_number is now required from the frontend
            'drivers_license_number' => 'nullable|string',
            'state_issued' => 'nullable|string',
            'date_issued' => 'nullable|date',
            'expiration_date' => 'nullable|date',
            'dot_medical_card_issued_date' => 'nullable|date',
            'dot_medical_card_expiration_date' => 'nullable|date',
        ]);

        // Get the organization ID based on the logged-in user
        $organizationId = getOrganizationId();  // Fetch organization ID based on logged-in user

        // Check if the email address already exists in the same organization
        $existingEmployee = Employee::where('email_address', $request->email_address)
            ->where('organization_id', $organizationId)  // Check within the same organization
            ->first();

        // If email exists in the same organization, return an error response
        if ($existingEmployee) {
            return response()->json([
                'status' => 'error',
                'message' => 'This email is already registered for this organization.',
            ], 400);
        }

        // Check if the employee number already exists in the same organization
        $existingEmployeeNumber = Employee::where('employee_number', $request->employee_number)
            ->where('organization_id', $organizationId)  // Check within the same organization
            ->first();

        // If employee number exists in the same organization, return an error response
        if ($existingEmployeeNumber) {
            return response()->json([
                'status' => 'error',
                'message' => 'This employee number is already registered for this organization.',
            ], 400);
        }

        // Create employee and assign organization ID
        $employee = Employee::create(array_merge($validated, [
            'organization_id' => $organizationId, // Add the organization ID to the employee record
        ]));

        // Return success response with the created employee number
        return response()->json([
            'status' => 'success',
            'message' => 'Employee created successfully.',
            'employee_number' => $employee->employee_number,  // Return the employee number that was created
        ]);
    }

    public function edit($id)
    {
        $employee = Employee::where('id', decodeId($id))
            ->with('division')
            ->select([
                'id',
                'employee_number',
                'first_name',
                'last_name',
                'phone_number',
                'email_address',
                'title',
                'division_id',
                'status',
                'pay_rate',
                'overtime_rate',
                'drivers_license_number',
                'state_issued',
                'date_issued',
                'expiration_date',
                'dot_medical_card_issued_date',
                'dot_medical_card_expiration_date',
            ])
            ->firstOrFail();
        $employee->division_name = $employee->division ? $employee->division->name : 'N/A';

        return response()->json($employee);
    }

    public function update(Request $request, $id)
    {
        // Fetch the employee based on ID
        $employee = Employee::where('id', decodeId($id))->firstOrFail();

        // Validate the request data
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'email_address' => 'required|email', // Removed 'unique' validation here for email
            'title' => 'nullable|string|max:255',
            'division_id' => 'required|exists:divisions,id',
            'status' => 'required|string',
            'pay_rate' => 'required|numeric',
            'overtime_rate' => 'required|numeric',
            'employee_number' => 'required|unique:employees,employee_number,'.$employee->id,
            'drivers_license_number' => 'nullable|string',
            'state_issued' => 'nullable|string',
            'date_issued' => 'nullable|date',
            'expiration_date' => 'nullable|date',
            'dot_medical_card_issued_date' => 'nullable|date',
            'dot_medical_card_expiration_date' => 'nullable|date',
        ]);

        // Get the organization ID based on the logged-in user
        $organizationId = getOrganizationId(); // Fetch organization ID based on logged-in user

        // Check if the email address already exists in the same organization, excluding the current employee
        $existingEmployee = Employee::where('email_address', $request->email_address)
            ->where('organization_id', $organizationId) // Check within the same organization
            ->where('id', '!=', $employee->id)  // Exclude the current employee from the check
            ->first();

        // If email exists in the same organization, return an error response
        if ($existingEmployee) {
            return response()->json([
                'status' => 'error',
                'message' => 'This email is already registered for this organization.',
            ], 400);
        }

        // Update the employee with the validated data
        $employee->update($validated);

        // Return success response
        return response()->json(['message' => 'Employee updated successfully.']);
    }

    public function destroy($id)
    {
        $employee = Employee::where('id', decodeId($id))->firstOrFail();
        $employee->delete();

        return response()->json(['message' => 'Employee deleted successfully.']);
    }

    // public function checkEmployeeNumber(Request $request)
    // {
    //     $exists = Employee::where('employee_number', $request->employee_number)->exists();
    //     return response()->json(['exists' => $exists]);
    // }

    public function generateEmployeeNumber(Request $request)
    {
        // Get the organization_id from the authenticated user
        $organizationId = auth()->user()->organization_id;

        // Generate a random employee number (5 digits)
        $employeeNumber = mt_rand(10000, 99999);

        return response()->json(['employeeNumber' => $employeeNumber]);
    }

    public function checkEmployeeNumber(Request $request)
    {
        $organizationId = auth()->user()->organization_id;  // Get organization_id from authenticated user

        $exists = Employee::where('employee_number', $request->employee_number)
            ->where('organization_id', $organizationId)
            ->exists();

        return response()->json(['exists' => $exists]);
    }
}
