@extends('layouts.admin.master')
@section('title', 'Employees')

@section('section')
    <style>
        input[type="date"]::-webkit-calendar-picker-indicator {
            display: none;
        }

        /* Date input wrapper styling */
        .date-input-wrapper {
            position: relative;
            cursor: pointer;
        }

        /* Calendar icon styling */
        .calendar-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            padding: 5px;
            z-index: 2;
        }

        .calendar-icon:hover {
            opacity: 0.8;
        }

        /* Form styling */
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }

        .col-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0 15px;
            margin-bottom: 15px;
        }

        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 15px;
            margin-bottom: 15px;
        }

        .select2-container--default {
            margin-top: 21px !important;
        }

        .table_filter_dropdown {
            margin-top: -45px !important;
        }

        .employee-dropdown-toggle::after {
            display: none !important;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            /* optional if you want more control */
        }

        .showMessage {
            display: none;
            padding: 0px 10px 7px 10px;
            background: #f8a0a0;
            color: #fa0a0a;
            text-align: left;
        }

        .showMessage p {
            color: #155724;
        }

        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
            border: none;
            background: none;
        }

        .employee-dropdown-toggle {
            background: none;
            border: none;
        }

        .custom-modal-width {
            max-width: 700px !important;
            width: 100%;
        }

        .employee-dropdown-toggle:hover,
        .employee-dropdown-toggle:focus,
        .employee-dropdown-toggle:active {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
            outline: none;
            color: #000;
        }

        .operation_modal .row {
            margin-bottom: 15px !important;
        }

        .operation_modal .row:last-of-type {
            margin-bottom: 10px !important;
        }

        .operation_modal select.form-control {
            padding-left: 12px !important;
            padding-right: 12px !important;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
            background-repeat: no-repeat !important;
            background-position: right 12px center !important;
            background-size: 16px !important;
        }

        .operation_modal .modal-body {
            padding: 0.6rem 2.4rem !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {
            .hidemodelbtn {
                font-size: 15px !important;
            }
        }

        .status.success {
            background-color: #d3ffd4 !important;
            color: #4dd51b !important;
            border-top-left-radius: 22px !important;
            border-bottom-left-radius: 22px !important;
            border-top-right-radius: 22px !important;
            border-bottom-right-radius: 22px !important;
            width: 101px !important;
            height: 36px !important;
            font-size: 14px !important;
        }

        .status.warning {
            background-color: #c3ccd7 !important;
            color: #000000 !important;
            border-top-left-radius: 22px !important;
            border-bottom-left-radius: 22px !important;
            border-top-right-radius: 22px !important;
            border-bottom-right-radius: 22px !important;
            width: 101px !important;
            height: 36px !important;
            font-size: 14px !important;
        }

        .dropdown img {
            vertical-align: middle;
        }

        .dropdown-menu {
            min-width: 100px;
        }

        .text-center {
            text-align: center !important;
        }

        .operation_modal label {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 400 !important;
            font-size: 14px !important;
            line-height: 22px !important;
            letter-spacing: 0% !important;
            width: 100% !important;
            margin-bottom: 5px !important;
        }

        .operation_modal .required-asterisk {
            color: #E91010 !important;
            font-family: 'Poppins', sans-serif !important;
            font-weight: 400 !important;
            font-size: 14px !important;
            line-height: 22px !important;
            letter-spacing: 0% !important;
            margin-left: 2px !important;
        }

        .operation_modal .input-group-text {
            height: 38px !important;
            border-radius: 6px 0 0 6px !important;
            border: 1px solid #ced4da !important;
            background-color: #f8f9fa !important;
            color: #495057 !important;
            font-size: 15.5px !important;
            padding: 8px 12px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .operation_modal .input-group .form-control:not(:first-child) {
            border-radius: 0 6px 6px 0 !important;
            border-left: 0 !important;
        }

        .operation_modal .form-control {
            width: 100%;
            max-width: 362.5px;
            height: 38px !important;
            border-radius: 6px;
            border: 1px solid #ced4da;
            padding: 8px 12px;
            font-size: 15.5px;
            box-sizing: border-box;
        }

        .operation_modal .form-control:disabled {
            background-color: #f8f9fa !important;
        }

        .custom-modal-heighty {
            line-height: 40px !important;
        }

        .operation_modal .input-group {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .operation_modal .input-group .form-control {
            flex: 1;
            max-width: 362.5px;
        }

        .operation_modal .input-group .btn {
            height: 38px;
            border-radius: 6px;
        }
    </style>

    <section class="dashboard_main pb-5 operations_table_filters">
        <div class="showMessage"></div>
        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Employees</h2>
            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                <select name="" id="select_filter" class="select-small basic-single-select">
                    <option value="" selected disabled>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                </select>
                {{-- <button class="btn primaryblue" data-toggle="modal"
                    data-target="#addEmployeeModal">Add Employee</button> --}}

                <button type="submit" data-toggle="modal" data-target="#addEmployeeModal"
                    class="btn btn-primary"
                    style="width: 146px; height: 38px; font-size: 15px;border-radius: 4px;">+ Add
                    Employee
                </button>
            </div>
        </div>


        <div class="table-responsive">
            <table id="clients_Detail"
                class="table table-striped yajra-datatable custom_datatable display" style="width:100%">
                <thead>
                    <tr>
                        <th>Employee #</th>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Phone Number</th>
                        <th>Email address</th>
                        <th>Division</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </section>

    <!-- Add Employee Modal -->
    <div class="operation_modal modal fade" id="addEmployeeModal" tabindex="-1"
        aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered m400 custom-modal-width">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEmployeeModalLabel"
                        style="color: #0074d9; font-weight: bold;">Add Employee</h5>
                    <button type="button" class="btn-close hidemodelbtn px-3 cancel-trigger"data-title="Cancel Changes"
                    data-type="add">
                        <i class="fa fa-times" style="color: #7E8A9D; font-size: 29px;margin-top: -3px;"
                            aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body custom-modal-height">
                    <form action="{{ route(getRouteAlias() . '.employee.store') }}" method="POST"
                        id="addEmployeeForm">
                        @csrf
                        <div class="row" style="margin-top: 24px;">
                            <div class="col-6">
                                <label>First Name<span class="required-asterisk">*</span></label>
                                <input type="text" name="first_name" class="form-control" required>
                            </div>
                            <div class="col-6">
                                <label>Last Name<span class="required-asterisk">*</span></label>
                                <input type="text" name="last_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="row" style="margin-top: -14px;">
                            <div class="col-6">
                                <label>Phone Number<span class="required-asterisk">*</span></label>
                                <input type="text" name="phone_number" class="form-control"oninput="maskPhoneNumber(event)" minlength="12" maxlength="12" required>
                            </div>
                            <div class="col-6">
                                <label>Email Address<span class="required-asterisk">*</span></label>
                                <input type="email" name="email_address" class="form-control"
                                    required>
                            </div>
                        </div>


                        <div class="row" style="margin-top: -14px;">
                            <div class="col-4">
                                <label>Title</label>
                                <input type="text" name="title" class="form-control">
                            </div>
                            <div class="col-4">
                                <label>Division<span class="required-asterisk">*</span></label>
                                <select name="division_id" class="form-control" required>
                                    @foreach ($divisions as $division)
                                        <option value="{{ $division->id }}">{{ $division->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-4">
                                <label>Status<span class="required-asterisk">*</span></label>
                                <select name="status" class="form-control" required>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-4">
                                <label>Pay Rate<span class="required-asterisk">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" name="pay_rate"
                                        class="form-control" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <label>Overtime Rate<span class="required-asterisk">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" name="overtime_rate"
                                        class="form-control" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <label>Employee Number<span class="required-asterisk">*</span></label>
                                <div class="input-group" style="position: relative;">
                                    <input type="text" name="employee_number"
                                        id="add_employee_number" class="form-control" required>
                                    <a href="javascript:void(0)" class="generate-employee-number"
                                        style="position: absolute; right: 10px; bottom: 5px; font-size: 14px; color: #007bff; text-decoration: underline;">
                                        Generate
                                    </a>
                                </div>
                            </div>


                        </div>
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-3">
                                <label>Drivers License #</label>
                                <input type="text" name="drivers_license_number"
                                    class="form-control">
                            </div>
                            <div class="col-3">
                                <label>State Issued</label>
                                <input type="text" name="state_issued" class="form-control">
                            </div>
                            <div class="col-3">
                                <label>Date Issued</label>
                                <div class="input-group" style="position: relative;">
                                    <input type="date" name="date_issued" class="form-control">
                                    <a href="javascript:void(0)" class="calendar-icon"
                                        style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 14px; color: #007bff; text-decoration: underline;">
                                        <img src="{{ asset('icon.png') }}" alt="Generate"
                                            style="width: 20px; height: 20px;">
                                    </a>
                                </div>
                            </div>

                            <div class="col-3">
                                <label>Expiration Date</label>
                                <div class="input-group" style="position: relative;">
                                    <input type="date" name="expiration_date"
                                        class="form-control">
                                    <a href="javascript:void(0)" class="calendar-icon"
                                        style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 14px; color: #007bff; text-decoration: underline;">
                                        <img src="{{ asset('icon.png') }}" alt="Generate"
                                            style="width: 20px; height: 20px;">
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: -8px;">
                            <div class="col-6">
                                <label>DOT Medical Card Issued Date</label>
                                <div class="input-group" style="position: relative;">
                                    <input type="date" name="dot_medical_card_issued_date"
                                        class="form-control">
                                    <a href="javascript:void(0)" class="calendar-icon"
                                        style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 14px; color: #007bff; text-decoration: underline;">
                                        <img src="{{ asset('icon.png') }}" alt="Generate"
                                            style="width: 20px; height: 20px;">
                                    </a>
                                </div>
                            </div>

                            <div class="col-6">
                                <label>DOT Medical Card Expiration Date</label>
                                <div class="input-group" style="position: relative;">
                                    <input type="date" name="dot_medical_card_expiration_date"
                                        class="form-control">
                                    <a href="javascript:void(0)" class="calendar-icon"
                                        style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-size: 14px; color: #007bff; text-decoration: underline;">
                                        <img src="{{ asset('icon.png') }}" alt="Generate"
                                            style="width: 20px; height: 20px;">
                                    </a>
                                </div>
                            </div>
                        </div>

                        <style>
                            input[type="date"]::-webkit-calendar-picker-indicator {
                                display: none;
                            }
                        </style>


                        <div class="modal-footer px-0" style="margin-top: -24px;">
                             <button type="button" class="btn btn-outline-primary me-3 cancel-trigger"
                            style="width: 126px; height: 43px; font-size: 15px; border-radius: 8px;"
                            data-type="add">
                            Cancel
                        </button>
                            <button type="submit" class="btn btn-primary"
                                style="width: 126px; height: 44px; font-size: 15px;border-radius: 8px;">
                                Add
                            </button>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Employee Modal -->
    <div class="operation_modal modal fade" id="editEmployeeModal" tabindex="-1"
        aria-labelledby="editEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered m400 custom-modal-width">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEmployeeModalLabel"
                        style="color: #0074d9; font-weight: bold;">Edit Employee</h5>
                    <button type="button" class="btn-close hidemodelbtn px-3 cancel-trigger"
                        aria-label="Close"
                       data-type="edit">
                        <i class="fa fa-times"
                            style="color: #7E8A9D; font-size: 29px;margin-top: -3px;"
                            aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body custom-modal-height">
                    <form action="" method="POST" id="editEmployeeForm">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="employee_id" id="edit_employee_id">
                        <div class="row" style="margin-top: 24px;">
                            <div class="col-6">
                                <label>First Name<span class="required-asterisk">*</span></label>
                                <input type="text" name="first_name" id="edit_first_name"
                                    class="form-control" required>
                            </div>
                            <div class="col-6">
                                <label>Last Name<span class="required-asterisk">*</span></label>
                                <input type="text" name="last_name" id="edit_last_name"
                                    class="form-control" required>
                            </div>
                        </div>
                        <div class="row" style="margin-top: -14px;">
                            <div class="col-6">
                                <label>Phone Number<span class="required-asterisk">*</span></label>
                                <input type="text" name="phone_number" id="edit_phone_number"
                                    class="form-control" oninput="maskPhoneNumber(event)" minlength="12" maxlength="12" required>
                            </div>
                            <div class="col-6">
                                <label>Email Address<span class="required-asterisk">*</span></label>
                                <input type="email" name="email_address" id="edit_email_address"
                                    class="form-control" required>
                            </div>
                        </div>
                        <div class="row" style="margin-top: -14px;">
                            <div class="col-4">
                                <label>Title</label>
                                <input type="text" name="title" id="edit_title"
                                    class="form-control">
                            </div>
                            <div class="col-4">
                                <label>Division<span class="required-asterisk">*</span></label>
                                <select name="division_id" id="edit_division_id" class="form-control"
                                    required>
                                    @foreach ($divisions as $division)
                                        <option value="{{ $division->id }}">{{ $division->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-4">
                                <label>Status<span class="required-asterisk">*</span></label>
                                <select name="status" id="edit_status" class="form-control"
                                    required>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 16px;">
                            <div class="col-4">
                                <label>Pay Rate<span class="required-asterisk">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" name="pay_rate"
                                        id="edit_pay_rate" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <label>Overtime Rate<span class="required-asterisk">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" name="overtime_rate"
                                        id="edit_overtime_rate" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <label>Employee Number<span class="required-asterisk">*</span></label>
                                <div class="input-group" style="position: relative;">
                                    <input type="text" name="employee_number"
                                        id="edit_employee_number" class="form-control" required>
                                    <a href="javascript:void(0)" class="generate-employee-number"
                                        style="position: absolute; right: 10px; bottom: 5px; font-size: 14px; color: #007bff; text-decoration: underline;">
                                        Generate
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="row" style="margin-top: 16px;">
                            <div class="col-3">
                                <label>Drivers License #</label>
                                <input type="text" name="drivers_license_number"
                                    id="edit_drivers_license_number" class="form-control">
                            </div>
                            <div class="col-3">
                                <label>State Issued</label>
                                <input type="text" name="state_issued" id="edit_state_issued"
                                    class="form-control">
                            </div>
                            <div class="col-3">
                                <label>Date Issued</label>
                                <div class="input-group date-input-wrapper">
                                    <input type="date" name="date_issued" id="edit_date_issued"
                                        class="form-control">
                                    <span class="calendar-icon">
                                        <img src="{{ asset('icon.png') }}" alt="Calendar"
                                            style="width: 20px; height: 20px;">
                                    </span>
                                </div>
                            </div>

                            <div class="col-3">
                                <label>Expiration Date</label>
                                <div class="input-group date-input-wrapper">
                                    <input type="date" name="expiration_date"
                                        id="edit_expiration_date" class="form-control">
                                    <span class="calendar-icon">
                                        <img src="{{ asset('icon.png') }}" alt="Calendar"
                                            style="width: 20px; height: 20px;">
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: -11px">
                            <div class="col-6">
                                <label>DOT Medical Card Issued Date</label>
                                <div class="input-group date-input-wrapper">
                                    <input type="date" name="dot_medical_card_issued_date"
                                        id="edit_dot_medical_card_issued_date" class="form-control">
                                    <span class="calendar-icon">
                                        <img src="{{ asset('icon.png') }}" alt="Calendar"
                                            style="width: 20px; height: 20px;">
                                    </span>
                                </div>
                            </div>

                            <div class="col-6">
                                <label>DOT Medical Card Expiration Date</label>
                                <div class="input-group date-input-wrapper">
                                    <input type="date" name="dot_medical_card_expiration_date"
                                        id="edit_dot_medical_card_expiration_date"
                                        class="form-control">
                                    <span class="calendar-icon">
                                        <img src="{{ asset('icon.png') }}" alt="Calendar"
                                            style="width: 20px; height: 20px;">
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer px-0" style="margin-top: -24px;">
                            <button type="button" class="btn btn-outline-primary me-3 cancel-trigger"
                                style="width: 126px; height: 43px; font-size: 15px; border-radius: 8px; "      
                            data-type="edit">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary"
                                style="width: 126px; height: 44px; font-size: 15px;border-radius: 8px;">
                                Update
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
     <!-- Confirmation Discard -->

<div class="modal fade" id="cancelConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered delete-modal">
        <div class="modal-content">
            <div class="modal-body text-center">
                <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}" alt="warning icon"
                    class="delete-icon">
                <h2 class="delete-request modal-title-text">Confirm</h2>
                <p class="are-sure modal-message-text">Are you sure?</p>
                <div class="buttons-wraper">
                    <button type="button" class="cancel-btn" data-dismiss="modal">No</button>
                    <button type="button" class="conform-btn confirmYesBtn">Yes</button>
                
            </div>
        </div>
    </div>
</div>

    <!-- View Employee Modal -->
    <div class="operation_modal modal fade" id="viewEmployeeModal" tabindex="-1"
        aria-labelledby="viewEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog  modal-dialog-centered m400 custom-modal-width">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEmployeeModalLabel"
                        style="color: #0074d9; font-weight: bold;">View Employee</h5>
                    <button type="button" class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal" aria-label="Close">
                        <i class="fa fa-times"
                            style="color: #7E8A9D; font-size: 29px;margin-top: -3px;"
                            aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body custom-modal-height">
                    <div class="row">
                        <div class="col-6">
                            <label>First Name</label>
                            <input type="text" id="view_first_name" class="form-control" disabled>
                        </div>
                        <div class="col-6">
                            <label>Last Name</label>
                            <input type="text" id="view_last_name" class="form-control" disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label>Phone Number</label>
                            <input type="text" id="view_phone_number" class="form-control" oninput="maskPhoneNumber(event)"
                                disabled>
                        </div>
                        <div class="col-6">
                            <label>Email Address</label>
                            <input type="email" id="view_email_address" class="form-control"
                                disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-4">
                            <label>Title</label>
                            <input type="text" id="view_title" class="form-control" disabled>
                        </div>
                        <div class="col-4">
                            <label>Division</label>
                            <input type="text" id="view_division" class="form-control" disabled>
                        </div>
                        <div class="col-4">
                            <label>Status</label>
                            <input type="text" id="view_status" class="form-control" disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-4">
                            <label>Pay Rate</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" step="0.01" id="view_pay_rate"
                                    class="form-control" disabled>
                            </div>
                        </div>
                        <div class="col-4">
                            <label>Overtime Rate</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" step="0.01" id="view_overtime_rate"
                                    class="form-control" disabled>
                            </div>
                        </div>
                        <div class="col-4">
                            <label>Employee Number</label>
                            <input type="text" id="view_employee_number" class="form-control"
                                disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-3">
                            <label>Drivers License #</label>
                            <input type="text" id="view_drivers_license_number"
                                class="form-control" disabled>
                        </div>
                        <div class="col-3">
                            <label>State Issued</label>
                            <input type="text" id="view_state_issued" class="form-control"
                                disabled>
                        </div>
                        <div class="col-3">
                            <label>Date Issued</label>
                            <input type="date" id="view_date_issued" class="form-control"
                                disabled>
                        </div>
                        <div class="col-3">
                            <label>Expiration Date</label>
                            <input type="date" id="view_expiration_date" class="form-control"
                                disabled>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label>DOT Medical Card Issued Date</label>
                            <input type="date" id="view_dot_medical_card_issued_date"
                                class="form-control" disabled>
                        </div>
                        <div class="col-6">
                            <label>DOT Medical Card Expiration Date</label>
                            <input type="date" id="view_dot_medical_card_expiration_date"
                                class="form-control" disabled>
                        </div>
                    </div>
                    <div class="modal-footer px-0">
                        <button type="button" class="btn btn-outline-primary"
                            style="width: 126px;height: 37px;font-size: 15px;"
                            data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered delete-modal">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}"
                        alt="delete icon" class="delete-icon mb-3">
                    <h2 class="delete-request">Delete</h2>
                    <p class="are-sure">Are you sure you want to remove this request?</p>

                    <!-- Hidden input to store ID -->
                    <input type="hidden" id="delete_item_id">

                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal"
                            style="height: 41px; color: #0074d9; border: 2px solid black;">Cancel</button>
                        <button type="button" class="conform-btn" id="confirmDeleteBtn"
                            style="background-color: #0074d9;height: 41px; ">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Get all the date input fields and their associated icons
                const dateInputs = document.querySelectorAll('input[type="date"]');

                dateInputs.forEach(input => {
                const wrapper = input.closest('.input-group');
                const icon = wrapper ? wrapper.querySelector('.calendar-icon') : null;

            // Open date picker when input is clicked
            input.addEventListener("click", function () {
                 if (this.showPicker) {
                     this.showPicker();
                }

            });

        // Close calendar on double-click
        input.addEventListener("dblclick", function () {
            this.blur();
        });

        // Open date picker when icon is clicked
                    if (icon) {
                        icon.addEventListener('click', function(e) {
                e.preventDefault();
                if (input.showPicker) {
                    input.showPicker();
                } else {
                    input.focus();
                }
                        });
                    }
                });

            });

        



            document.addEventListener('DOMContentLoaded', function() {
                // Get all date input wrappers
                const dateWrappers = document.querySelectorAll('.date-input-wrapper');

                dateWrappers.forEach(wrapper => {
                    const input = wrapper.querySelector('input[type="date"]');
                    const icon = wrapper.querySelector('.calendar-icon');

                    // Open date picker when clicking anywhere in wrapper
                    wrapper.addEventListener('click', function(e) {
                        // Prevent opening if clicking directly on the input
                        if (e.target !== input) {
                            input.showPicker();
                        }
                    });

                    // Also make icon clickable
                    if (icon) {
                        icon.addEventListener('click', function(e) {
                            e.stopPropagation();
                            input.showPicker();
                        });
                    }
                });
            });

            $(document).ready(function() {
                // Initialize DataTable
                let table = $('#clients_Detail').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false, // Disable default search as we're implementing our own search
                    paging: false,
                    ajax: {
                        url: '{{ route(getRouteAlias() . '.employee.listing') }}',
                        data: function(d) {
                            // Send search and filter data with the request
                            d.employee_status = $('#select_filter').val();
                            d.search = $('#filter_search')
                                .val(); // Send the search query
                        }
                    },
                    columns: [{
                            data: 'employee_number',
                            name: 'employee_number'
                        },
                        {
                            data: 'first_name',
                            name: 'first_name'
                        },
                        {
                            data: 'last_name',
                            name: 'last_name'
                        },
                        {
                            data: 'phone_number',
                            name: 'phone_number'
                        },
                        {
                            data: 'email_address',
                            name: 'email_address'
                        },
                        {
                            data: 'division',
                            name: 'division'
                        },
                        {
                            data: 'status',
                            name: 'status'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false,
                            render: function(data, type, row) {
                                return data;
                            }
                        }
                    ],
                    columnDefs: [{
                        targets: -1,
                        createdCell: function(td, cellData, rowData, row, col) {
                            $(td).addClass('text-center');
                        }
                    }],
                    drawCallback: function() {
                        $('.employee-dropdown-toggle').dropdown();
                    }
                });

                // Trigger table reload on search input change
                $('#filter_search').on('keyup', function() {
                    table.ajax.reload();
                });

                // Trigger table reload on filter selection change
                $('#select_filter').on('change', function() {
                    table.ajax.reload();
                });
                // Function to generate unique employee number


                // function generateUniqueEmployeeNumber(callback) {
                //     let employeeNumber = Math.floor(10000 + Math.random() * 90000);
                //     $.ajax({
                //         url: '{{ route(getRouteAlias() . '.employee.checkEmployeeNumber') }}',
                //         method: 'POST',
                //         data: {
                //             _token: '{{ csrf_token() }}',
                //             employee_number: employeeNumber
                //         },
                //         success: function(response) {
                //             if (response.exists) {
                //                 generateUniqueEmployeeNumber(callback);
                //             } else {
                //                 callback(employeeNumber);
                //             }
                //         },
                //         error: function() {
                //             callback(employeeNumber);
                //         }
                //     });
                // }

                // // Auto-generate employee number when Add Employee Modal is shown
                // $('#addEmployeeModal').on('shown.bs.modal', function() {
                //     generateUniqueEmployeeNumber(function(employeeNumber) {
                //         $('#add_employee_number').val(employeeNumber);
                //     });
                // });

                // // Generate Employee Number on button click
                // $('.generate-employee-number').click(function() {
                //     let inputField = $(this).siblings('input[name="employee_number"]');
                //     generateUniqueEmployeeNumber(function(employeeNumber) {
                //         inputField.val(employeeNumber);
                //     });
                // }); 

                //              function generateUniqueEmployeeNumber(callback, isRandom = false) {
                //     if (isRandom) {
                //         // Generate a random number directly
                //         let employeeNumber = Math.floor(10000 + Math.random() * 90000);
                //         callback(employeeNumber);
                //         return;
                //     }

                //     // Fetch the next available employee number for the current organization
                //     $.ajax({
                //         url: '{{ route(getRouteAlias() . '.employee.checkEmployeeNumber') }}',
                //         method: 'POST',
                //         data: {
                //             _token: '{{ csrf_token() }}',
                //         },
                //         success: function(response) {
                //             let employeeNumber = response.nextEmployeeNumber; // Use the next available number
                //             callback(employeeNumber);
                //         },
                //         error: function() {
                //             // In case of error, generate a random number as a fallback
                //             let employeeNumber = Math.floor(10000 + Math.random() * 90000);
                //             callback(employeeNumber);
                //         }
                //     });
                // }

                // // Auto-generate employee number when Add Employee Modal is shown
                // $('#addEmployeeModal').on('shown.bs.modal', function() {
                //     generateUniqueEmployeeNumber(function(employeeNumber) {
                //         $('#add_employee_number').val(employeeNumber);
                //     });
                // });

                // // Generate Employee Number on button click with random number generation
                // $('.generate-employee-number').click(function() {
                //     let inputField = $(this).siblings('input[name="employee_number"]');

                //     // Check if we should generate a random number
                //     generateUniqueEmployeeNumber(function(employeeNumber) {
                //         inputField.val(employeeNumber);
                //     }, true);  // Passing `true` to force random number generation
                // });

                let nextEmployeeNumber = 0; // Variable to store the next available employee number

                function generateUniqueEmployeeNumber(callback, isRandom = false) {
                    if (isRandom) {
                        // Generate a random number directly
                        let employeeNumber = Math.floor(10000 + Math.random() * 90000);
                        callback(employeeNumber);
                        return;
                    }

                    // Fetch the next available employee number for the current organization
                    $.ajax({
                        url: '{{ route(getRouteAlias() . '.employee.checkEmployeeNumber') }}',
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                        },
                        success: function(response) {
                            // Get the next available employee number (based on the highest one)
                            nextEmployeeNumber = response
                            .nextEmployeeNumber; // Store it in a variable
                            callback(nextEmployeeNumber);
                        },
                        error: function() {
                            // In case of error, generate a random number as a fallback
                            let employeeNumber = Math.floor(10000 + Math.random() *
                                90000);
                            callback(employeeNumber);
                        }
                    });
                }

                // Auto-generate employee number when Add Employee Modal is shown
                // $('#addEmployeeModal').on('shown.bs.modal', function() {
                //     generateUniqueEmployeeNumber(function(employeeNumber) {
                //         $('#add_employee_number').val(
                //         employeeNumber); // Set the initial employee number
                //     });
                // });

                // Generate Employee Number on button click with incremental value
                $('.generate-employee-number').click(function() {
                    let inputField = $(this).siblings('input[name="employee_number"]');
                    let formattedNumber = String(nextEmployeeNumber).padStart(3, '0');

                    // Set the value in the input field

                    nextEmployeeNumber++;
                    inputField.val("Emp" + formattedNumber);
                });







                // Populate View Modal
                $(document).on('click', '.view-employee', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route(getRouteAlias() . '.employee.edit', ':id') }}'
                            .replace(':id', id),
                        method: 'GET',
                        success: function(data) {
                            console.log('View Modal Data:', data);
                            $('#view_first_name').val(data.first_name);
                            $('#view_last_name').val(data.last_name);
                            $('#view_phone_number').val(data.phone_number);
                            $('#view_email_address').val(data.email_address);
                            $('#view_title').val(data.title);
                            $('#view_division').val(data.division_name ||
                                'N/A');
                            $('#view_status').val(data.status);
                            $('#view_pay_rate').val(data.pay_rate);
                            $('#view_overtime_rate').val(data.overtime_rate);
                            $('#view_employee_number').val(data
                                .employee_number);
                            $('#view_drivers_license_number').val(data
                                .drivers_license_number);
                            $('#view_state_issued').val(data.state_issued);
                            $('#view_date_issued').val(data.date_issued);
                            $('#view_expiration_date').val(data
                                .expiration_date);
                            $('#view_dot_medical_card_issued_date').val(data
                                .dot_medical_card_issued_date);
                            $('#view_dot_medical_card_expiration_date').val(data
                                .dot_medical_card_expiration_date);
                            $('#viewEmployeeModal').modal('show');
                        }
                    });
                });

                // Populate Edit Modal
                $(document).on('click', '.edit-employee', function() {
                    let id = $(this).data('id');
                    $.ajax({
                        url: '{{ route(getRouteAlias() . '.employee.edit', ':id') }}'
                            .replace(':id', id),
                        method: 'GET',
                        success: function(data) {
                            console.log('Edit Modal Data:', data);
                            $('#edit_employee_id').val(id);
                            $('#edit_first_name').val(data.first_name);
                            $('#edit_last_name').val(data.last_name);
                            $('#edit_phone_number').val(data.phone_number);
                            $('#edit_email_address').val(data.email_address);
                            $('#edit_title').val(data.title);
                            $('#edit_division_id').val(data.division_id);
                            $('#edit_status').val(data.status);
                            $('#edit_pay_rate').val(data.pay_rate);
                            $('#edit_overtime_rate').val(data.overtime_rate);
                            $('#edit_employee_number').val(data
                                .employee_number);
                            $('#edit_drivers_license_number').val(data
                                .drivers_license_number);
                            $('#edit_state_issued').val(data.state_issued);
                            $('#edit_date_issued').val(data.date_issued);
                            $('#edit_expiration_date').val(data
                                .expiration_date);
                            $('#edit_dot_medical_card_issued_date').val(data
                                .dot_medical_card_issued_date);
                            $('#edit_dot_medical_card_expiration_date').val(data
                                .dot_medical_card_expiration_date);
                            $('#editEmployeeForm').attr('action',
                                '{{ route(getRouteAlias() . '.employee.update', ':id') }}'
                                .replace(':id', id));
                            $('#editEmployeeModal').modal('show');
                        }
                    });
                });

                // Delete Employee with SweetAlert2


                // Form submission with AJAX
                $('#addEmployeeForm, #editEmployeeForm').on('submit', function(e) {
                    e.preventDefault();
                    let form = $(this);
                    $.ajax({
                        url: form.attr('action'),
                        method: form.attr('method'),
                        data: form.serialize(),
                        success: function(response) {
                            form.closest('.modal').modal('hide');
                            table.ajax.reload();
                            // $('.showMessage').html('<p style="margin-top: 10px;">' + response.message +
                            //     '</p>').show().delay(3000).fadeOut();
                            form[0]
                                .reset(); // ✅ Reset form on success (optional)
                        },

                        error: function(xhr) {
                            console.log(xhr
                                .responseJSON
                            ); // Log the full response to the console

                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                let errorMessage = xhr.responseJSON.message;

                                $('.showMessage').html(
                                    '<p style="color: red;height: 22px;">' +
                                    errorMessage + '</p>'
                                ).show().delay(3000).fadeOut();
                            }

                            form[0].reset();
                        }

                    });
                });

            });
        </script>

        <script>
            $(document).on('click', '.delete-employee', function() {
                let id = $(this).data('id'); // Get employee ID

                // Set the ID to the hidden input field in the modal
                $('#delete_item_id').val(id);

                // Show the delete confirmation modal
                $('#deleteModal').modal('show');
            });

            $('#confirmDeleteBtn').on('click', function() {
                // Get the ID from the hidden input
                let id = $('#delete_item_id').val();

                // Send AJAX request to delete the employee
                $.ajax({
                    url: '{{ route(getRouteAlias() . '.employee.destroy', ':id') }}'
                        .replace(':id', id),
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        $('#deleteModal').modal('hide')
                        location.reload();
                        // $('.showMessage').html('<p style="margin-top: 10px;">' + response.message +
                        //     '</p>').show().delay(3000).fadeOut();
                        form[0]
                            .reset();
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON.errors || {
                            message: ['Failed to delete employee.']
                        };
                        let errorMessage = 'Error: ';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        Swal.fire({
                            title: 'Error!',
                            html: errorMessage,
                            icon: 'error',
                            timer: 3000,
                            showConfirmButton: false
                        });
                    }
                });
            });
        </script>
        <script>
$(document).ready(function () {

    let targetModal = "";

    $(".cancel-trigger").on("click", function (e) {
        e.preventDefault();

        // Detect which modal this came from
        let type = $(this).data("type");

        if (type === "add") {
            $("#cancelConfirmModal .modal-title-text").text("Cancel Changes");
            $("#cancelConfirmModal .modal-message-text").text("Are you sure you want to cancel adding this employee?");
            $("#cancelConfirmModal .confirmYesBtn").text("Yes, Cancel");
            targetModal = "#addEmployeeModal";
        }

        if (type === "edit") {
            $("#cancelConfirmModal .modal-title-text").text("Discard Changes");
            $("#cancelConfirmModal .modal-message-text").text("Are you sure you want to discard changes to this employee?");
            $("#cancelConfirmModal .confirmYesBtn").text("Yes, Discard");
            targetModal = "#editEmployeeModal";
        }

        $("#cancelConfirmModal").modal("show");

        // Bind Yes button action
        $(".confirmYesBtn").off("click").on("click", function () {
            $("#cancelConfirmModal").modal("hide"); 
            $(targetModal).modal("hide");
        });
    });
});

</script>
    @endpush
@endsection
