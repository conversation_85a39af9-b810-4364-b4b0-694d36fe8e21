@extends('layouts.admin.master')

@push('tinymce-scripts')
<!-- TinyMCE -->
<script src="{{ asset('assets/tinymce/js/tinymce/tinymce.min.js') }}"></script>
@endpush
@section('title', 'Customer Issue')
@section('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/css/fileinput.min.css">
    <style>
        button:focus,
        input:focus{
            outline: none;
            box-shadow: none;
        }
        a,
        a:hover{
            text-decoration: none;
        }
        .input-group.file-caption-main{
            display: none;
        }
        .close.fileinput-remove{
            display: none;
        }
        .file-drop-zone{
            margin: 0px;
            border: 1px solid #fff;
            background-color: #fff;
            padding: 0px;
            display: contents;
        }
        .file-drop-zone.clickable:hover{
            border-color: #fff;
        }
        .file-drop-zone .file-preview-thumbnails{
            display: inline;
        }
        .file-drop-zone-title{
            padding: 15px;
            height: 120px;
            width: 120px;
            font-size: 12px;
        }
        .file-input-ajax-new{
            display: inline-block;
        }
        /* .file-input{
            display: flex !important;
        } */
        .file-input.theme-fas{
            display: inline-block;
            width: 100%;
        }
        .file-input.theme-fa{
            display: flex;
        }
        .file-preview{
            padding: 0px;
            border: none;
            display: inline;
            width: fit-content !important;

        }
        .file-drop-zone-title{
            display: none;
        }
        .file-footer-caption{
            display: none !important;
        }
        .kv-file-upload{
            display: none;
        }
        .file-upload-indicator{
            display: none;
        }
        .file-drag-handle.drag-handle-init.text-info{
            display: none;
        }
        .krajee-default.file-preview-frame .kv-file-content{
            width: 90px;
            height: 90px;
            display: flex;
            text-align: center;
            align-items: center;
        }
        .krajee-default.file-preview-frame{
            background-color: #fff;
            margin: 3px;
            border-radius: 15px;
            overflow: hidden;
        }
        .krajee-default.file-preview-frame:not(.file-preview-error):hover{
            box-shadow: none;
            border-color: #0074D9;
        }
        .krajee-default.file-preview-frame:not(.file-preview-error):hover .file-preview-image{
            transform: scale(1.1);
        }
        .krajee-default.file-preview-frame{
            box-shadow: none;
            border-color: #fff;
            max-width: 150px;
            margin: 5px;
            padding: 0px;
            transition: 0.5s;
        }
        .file-preview-status{
            display: none !important;
        }
        .file-thumbnail-footer,
        .file-actions{
            width: 20px;
            height: 20px !important;
            position: absolute !important;
            top: 3px;
            right: 3px;
        }
        .kv-file-remove:focus,
        .kv-file-remove:active{
            outline: none !important;
            box-shadow: none !important;
        }
        .kv-file-remove{
            border-radius: 50%;
            z-index: 1;
            right: 0;
            position: absolute;
            top: 0;
            text-align: center;
            color: #fff;
            background-color: #0074D9;
            border: 1px solid #0074D9;
            padding: 2px 6px;
            font-size: 11px;
            transition: 0.5s;
        }
        .kv-file-remove:hover{
            border-color: #DCF2FF;
            background-color: #DCF2FF;
            color: #0074D9;
        }
        .kv-preview-data.file-preview-video{
            width: 100% !important;
            height: 100% !important;
        }
        .btn-outline-secondary.focus, .btn-outline-secondary:focus{
            box-shadow: none;
        }
        .btn-toggleheader,
        .btn-fullscreen,
        .btn-borderless{
            display: none;
        }
        .btn-kv.btn-close{
            color: #fff;
            border: none;
            background-color: #0074D9;
            font-size: 11px;
            width: 18px;
            height: 18px;
            text-align: center;
            padding: 0px;
        }
        .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
        .btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
        .show>.btn-outline-secondary.dropdown-toggle:focus{
            background-color: #DCF2FF;
            color: #DCF2FF;
            box-shadow: none;
            color: #0074D9;
        }
        .kv-file-content .file-preview-image{
            width: 90px !important;
            height: 90px !important;
            max-width: 90px !important;
            max-height: 90px !important;
            transition: 0.5s;
        }
        .btn-danger.btn-file:focus{
            box-shadow: none !important;
            outline: none !important;
        }
        .btn-danger.btn-file{
            padding: 0px;
            height: 95px;
            width: 95px;
            display: inline-block;
            margin: 5px;
            /* border-color: #DCF2FF; */
            border: 1px solid #E4E4E4;
            background-color: white;
            color: #0074D9;
            border-radius: 15px;
            padding-top: 30px;
            transition: 0.5s;
        }
        .btn-danger.btn-file:active,
        .btn-danger.btn-file:hover{
            background-color: #DCF2FF;
            color: #0074D9;
            border-color: #DCF2FF;
            box-shadow: none;
        }
        .btn-danger.btn-file i{
            font-size: 30px;
            color: #0074D9 !important;
        }


        @media (max-width: 350px){
            .krajee-default.file-preview-frame:not([data-template=audio]) .kv-file-content{
                width: 90px;
            }
        }
        .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show>.btn-danger.dropdown-toggle {
            color: #0074D9 !important;
            background-color: #DCF2FF !important;
            border-color: #DCF2FF !important;
        }
        .select2-container{
            width: 118px !important;
            margin-top: 28px !important;
        }
        .open_detail {
            display: flex;
            justify-content: center;
            align-items: center;
            /* text-align: center; */
            /* color: rgba(239, 141, 3, 1); */
            background-color: white !important;
            width: 25%;
            font-size: 15px;
            border-radius: 10px;
            height: 40px;
            box-sizing: border-box;
            border: none;
            cursor: pointer;
        }
        .basic-single-select + .select2 .select2-selection .select2-selection__rendered, .custom_selectBox + .select2 .select2-selection .select2-selection__rendered {
            padding: 0px 32px 0px 16px !important;

        }
        .select2-dropdown--below{
            width: 118px !important;
            margin-top: -45px !important;
        }
        .basic-single-select + .select2 .select2-selection, .custom_selectBox + .select2 .select2-selection {
            background: #ffffff;
            border: 1px solid var(--bordercolor);
            border-top-right-radius: 6px !important;
            border-top-left-radius: 6px !important;
            border-bottom-left-radius: 6px !important;
            border-bottom-right-radius: 6px !important;
            height: 40px;
        }
        .select2-container--default
        .select2-selection--single
        .select2-selection__rendered {
            height: 30px !important;
            display: flex;
            align-items: center;
        }
        .basic-single-select + .select2 .select2-selection, .custom_selectBox + .select2 .select2-selection {
            background: #ffffff;
            border: 1px solid var(--bordercolor);
            border-top-right-radius: 6px !important;
            border-top-left-radius: 6px !important;
            border-bottom-left-radius: 6px !important;
            border-bottom-right-radius: 6px !important;
            height: 34px !important;
        }
        .basic-single-select + .select2 .select2-selection .select2-selection__arrow, .custom_selectBox + .select2 .select2-selection .select2-selection__arrow {

            height: 3.4rem !important;

        }
        .input {
            background: var(--white);
            border: 1px solid var(--bordercolor);
            border-radius: 6px;
            width: 100%;
            padding: 8px 0px !important;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            height: 40px;
        }
        .dropzone_library_customize_complete .dz-preview {
            margin: 8px;
            min-height: 100px;
            border: 1px solid #90a0b7;
            border-radius: 4px;
            padding: 8px;
        }
        .dropzone_library_customize_complete .dz-preview .dz-remove {
            font-family: "Cabin";
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px 8px;
            background: #003366;
            border-radius: 6px;
            color: #ffffff;
            text-decoration: none;
            margin-top: 8px;
        }
        .preview-file-choose{

            gap: 0px;
            border-radius: 8px;
            border: 1px solid #E4E4E4;
            opacity: 0px;
            text-align: center;
            display: none;
        }
        .dz-started .preview-file-choose{
            display: inline !important;

            gap: 0px;
            border-radius: 8px !important;
            border: 1px solid #E4E4E4  !important;

            text-align: center !important;
        }
        .dz-preview{
            /* display: none !important; */
            min-height: 110px;
            width: 81px;
            margin: 8px !important;

        }
        .dz-started .dz-preview
        {
            display: inline-block !important;
        }
        .dropzone_library_customize{
            /* display: flex; */
        }
        .dz-filename span{
            /* display: none; */
        }
        .dz-details-imagess{
            /* width: 100%; */
        }
        button:focus{
            outline: none;
            box-shadow: none;
        }
        .required_field {
            color: red;
        }
        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }
        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }
        .hidemodelbtn:hover {
            cursor: pointer !important;
        }
        @media screen and (max-width: 580px) {
            .large-modal {
                max-width: 95% !important;
                width: 95% !important;
            }
            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }
        /* .bottombox p{
            display: flex !important;
            justify-content: end !important;
        } */
        #statusDropdown option {
            background-color: #ffffff;
        }
    </style>
@endsection
@section('section')

    <section class="dashboard_main pb-5">
        @php
            $statuses = [
                1 => ['name' => 'Pending'],
                2 => ['name' => 'Open'],
                3 => ['name' => 'Completed'],
            ];
        @endphp
        @foreach ($statuses as $key => $status)
            @php
                $style = getStatusStyles($issue->status, $key);
            @endphp
        @endforeach
        <div class="row pt-3 gy-4">

            <div class="col-lg-6 mb-md-3 mt-lg-0 col-md-12">
                <div class="panel">
                    <div class="row">
                        <div class="col-8">
                            <p class="label-text">Subject</p>
                            <p class="detail-heading" id="subject" contenteditable="false">{{$issue->subject}}</p>
                        </div>
                        <div class="col-4 d-flex justify-content-end align-items-center">
                            <div class="open_detail1 mr-3">
                                <select class="input" id="statusDropdown" style="padding: 0px 15px !important; height: 32px !important; width: fit-content !important; border: none;" data-id="{{ $issue->id }}">
                                    <!-- <option value="" selected disabled>Select Status</option> -->
                                    <option value="1" {{$issue->status == 1 ? 'selected' : ''}}>Pending</option>
                                    <option value="2" {{$issue->status == 2 ? 'selected' : ''}}>Open</option>
                                    <option value="3" {{$issue->status == 3 ? 'selected' : ''}}>Completed</option>
                                </select>
                            </div>
                            <span class="download_icon" data-toggle="modal"
                                  data-target="#selectPropertyModal">
                                        <img height="23px" width="23px" src="{{ asset('admin_assets/images/edit-icon.png') }}" alt="Edit" id="" style="cursor: pointer;">
                                    </span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-4">
                            <label for="" class="label-text">Issue Category</label>
                            <p class="detail-value" id="issue-category" contenteditable="false">{{$issue->category}}</p>
                        </div>
                        <div class="col-4">
                            <label for="" class="label-text">Issue Created By</label>
                            <p class="detail-value" id="issue-created-by" contenteditable="false">
                                {{ $issue->creator ? $issue->creator->name : 'N/A' }}
                            </p>
                        </div>
                        <div class="col-4">
                            <label for="" class="label-text">Assign To</label>
                            <p class="detail-value" id="assign-to" contenteditable="false">
                                {{ $issue->assignee ? $issue->assignee->name : 'N/A' }}
                            </p>
                        </div>

                    </div>
                    <div class="row mt-3 justify-content-end">
                        <button id="save-btn-subject" class="btn btn-primary" style="display: none;">Save</button>
                    </div>
                </div>

            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel" style="min-height: 165px">
                    <div class="row">
                        <div class="col-12 d-flex justify-content-between ">
                            <p class="label-text">Property Information</p>
                            <!-- <span class="download_icon pl-5 pt-2">
                                        <img height="23px" width="23px" id="edit-icon-property"
                                            src="{{ asset('admin_assets/images/edit-icon.png') }}"
                                            alt="Image">
                                    </span> -->
                        </div>
                    </div>

                    <!-- Editable Property Name -->
                    <p class="detail-heading mb-1" id="property-name" contenteditable="false">
                        {{ optional($issue->opportunitydata?->propertyInformation)->name ?? '---' }}
                    </p>

                    <!-- Editable Property Address -->
                    <!-- <p class="detail-subheading" id="property-address" contenteditable="false">

                    </p> -->

                    <!-- Editable Property City, State, and Zip -->
                    <!-- <p class="detail-subheading" id="property-location" contenteditable="false">
                            {{ optional($issue->opportunitydata?->propertyInformation)->address1 ?? '---' }}.
                                {{ optional($issue->opportunitydata?->propertyInformation)->city ?? '---' }},
                                {{ optional($issue->opportunitydata?->propertyInformation)->state ?? '---' }}
                    {{ optional($issue->opportunitydata?->propertyInformation)->zip ?? '---' }}
                    </p> -->
                    <p class="detail-subheading" id="property-location" contenteditable="false">
                        {{ \Illuminate\Support\Str::limit(
                            trim(
                                (optional($issue->opportunitydata?->propertyInformation)->address1 ?? '---') . '. ' .
                                (optional($issue->opportunitydata?->propertyInformation)->city ?? '---') . ', ' .
                                (optional($issue->opportunitydata?->propertyInformation)->state ?? '---') . ' ' .
                                (optional($issue->opportunitydata?->propertyInformation)->zip ?? '---')
                            ), 60
                        ) }}
                    </p>


                    <!-- Save Button -->
                    <button id="save-btn" class="btn btn-primary" style="display:none;">Save</button>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel" style="min-height: 165px">
                    <div class="row">
                        <div class="col-12 d-flex justify-content-between ">
                            <p class="label-text">Contact Information</p>
                            <!-- <span class="download_icon pl-5 pt-2">
                                        <img height="23px" width="23px" id="edit-contact-icon"
                                            src="{{ asset('admin_assets/images/edit-icon.png') }}"
                                            alt="Image">
                                    </span> -->
                        </div>
                    </div>

                    <!-- Editable Contact Information -->
                    <p class="detail-heading mb-1" id="contact-name" contenteditable="false">
                        {{ optional($issue->opportunitydata?->contactInformation)->first_name ?? '---' }} {{ optional($issue->opportunitydata?->contactInformation)->last_name ?? '----' }}
                        <span class="label-text ml-3" id="contact-role" contenteditable="false">

                                    {{ optional($issue->opportunitydata?->contactInformation)->role ? DB::table('roles')->where('id', optional($issue->opportunitydata?->contactInformation)->role)->value('name') : '---' }}

                                </span>
                    </p>
                    <p class="detail-subheading" id="contact-phone" contenteditable="false">
                        {{ optional($issue->opportunitydata?->contactInformation)->phone_number ?? '---' }}
                    </p>
                    <p class="detail-subheading" id="contact-email" contenteditable="false">
                        {{ optional($issue->opportunitydata?->contactInformation)->email ?? '---' }}
                    </p>

                    <!-- Save Button -->
                    <button id="save-btn-contact" class="btn btn-primary" style="display:none;">Save</button>
                </div>

            </div>
        </div>
        <form id="" action="{{ $action }}" method="POST" enctype="multipart/form-data">
            @method('put')
            @csrf
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Issue Details</h2>
                </div>
                <div class="px-14 mt-5">
                    <textarea class="wycwyg_editor issue-textarea" name="description" id="issue-textarea" style="height:200px">
                        {!! $issue?->description !!}
                        </textarea>
                    @error('description')
                    <label id="requsetTitle-error" class="error">{{ $message }}</label>
                    @enderror
                </div>

            </div>
            <div class="panel mt-4">
                <div class="panel_header mb-4">
                    <h2 class="panel_title">Documents</h2>
                </div>
                <div class="mt-2">
                    <div class="dropzone_library_customize dropzone_library_customize_regular dropzone">
                        <div class="dz-preview dz-file-preview dz-processing dz-success dz-complete needsclick"  id="my-dropzone" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                            <svg width="20" height="20" style="margin-top: 33px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>


                <!-- <div class="verify-sub-box d-flex">
                    <div class="file-loading">
                        <input id="multiplefileupload" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.csv" name="images[]" type="file" multiple>
                     </div>
               </div> -->

                <!-- <div class="upload-container">
                    <div id="upload-boxes-documents" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        @foreach ($issue->documents->where('document_status', 'regular') as $document)
                    <div class="upload-box uploaded-document">
                        <img src="{{ Storage::url($document->path) }}" alt="{{ $document->name }}" style="height: 140px; width: 140px;">

                            </div>
                        @endforeach
                </div>

                <div class="upload-box">
                    <span class="upload-icon">
                        <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <input type="file" name="images[]" id="file-upload-documents" class="file-input" multiple onchange="handleFileUpload(this, 'upload-boxes-documents')" style="opacity: 0; position: absolute; width: 100%; height: 100%; top: 0; left: 0; cursor: pointer;">
                    </div>
                </div> -->
            </div>

            <div class="panel mt-4">
                <div class="panel_header mb-4">
                    <h2 class="panel_title">Completed</h2>
                </div>

                <div class="mt-2">
                    <div class="dropzone_library_customize dropzone_library_customize_complete dropzone dropzonecomplete">
                        <div class="dz-preview dz-file-preview dz-processing dz-success needsclick dz-complete dz-delete-image"  id="my-dropzone-complete" style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                            <svg width="20" height="20" style="margin-top: 33px;" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div id="imageSizeError"></div>
                </div>
                <!-- <div class="verify-sub-box d-flex">
                    <div class="file-loading">
                        <input id="multiplefileupload2" name="completed_images[]" type="file" multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.csv">
                     </div>
               </div> -->

                <!-- <div class="upload-container mb-5 mt-4">
                    <div id="upload-boxes-completed" style="display: flex; gap: 1rem; flex-wrap: wrap;">

                        @foreach($issue->documents->where('document_status', 'completed') as $document)
                    <div class="upload-box uploaded-document">
                        <img src="{{ Storage::url($document->path) }}" alt="{{ $document->name }}" width="140px" height="140px">

                            </div>
                        @endforeach
                </div>


                <div class="upload-box">
                    <span class="upload-icon">
                        <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <input type="file" name="completed_images[]" id="file-upload-completed" class="file-input" multiple onchange="handleFileUpload(this, 'upload-boxes-completed')" style="opacity: 0; position: absolute; width: 100%; height: 100%; top: 0; left: 0; cursor: pointer;">
                    </div>
                </div> -->

                <div class="px-14 mt-3">
                    <textarea class="wycwyg_editor issue-textarea" name="completed_description" id="issue-textarea" style="height:200px">

                        {!! $issue?->completed_description !!}
                        </textarea>
                    @error('completed_description')
                    <label id="requsetTitle-error" class="error">{{ $message }}</label>
                    @enderror
                </div>
            </div>
            <div class="d-flex justify-content-between gap-3 flex-wrap mt-5">
                <a type="button"
                   {{-- href="{{ previous_route()->getName() == getRouteAlias() . '.customer-issues.index' ? route(getRouteAlias() . '.customer-issues.index') : route(getRouteAlias() . '.customer-issue.index') }}" --}}
                   class="btn cancel py-3 px-5 ">Cancel</a>
                <button type="submit" class="btn primaryblue">Complete</button>
            </div>
        </form>
    </section>
    <!-- add property Modal -->
    <div
        class="modal fade select-client"
        id="selectPropertyModal"
        data-backdrop="static"
        data-keyboard="false"
        tabindex="-1"
        aria-labelledby="selectClientModalLabel"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered  modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: #e1f4ff">
                    <h4 class="modal-title px-3" id="addItemModalLabel">
                        <b style="color: #0074d9 !important"
                        >Update Issue Information</b
                        >
                    </h4>
                    <button
                        type="button"
                        class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal"
                        aria-label="Close"
                        style="border: none; background-color: transparent"
                    >
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body" style="padding: 13px 20px !important">
                    <form
                        id=""
                        action="{{URL::route(getRouteAlias() . '.update.customer.issue', encodeId($issue->id))}}"
                        method="POST"
                    >
                        @csrf
                        <div
                            class="row px-2"
                            style=""
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 100% !important;">
                            <span for="item_name"
                                  style="font-size: 16px">Select Job
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <select name="assigned_job" id="assigned_job" style="height: 36px;
    font-size: 16px !important;" class="form-control">
                                        <option value="" selected disabled>Select Job</option>
                                        @foreach($operations as $job)
                                            <option value="{{$job->opportunityid->id}}" {{$issue->assigned_job == $job->opportunityid->id ? 'selected' : ''}}>{{$job->opportunityid->opportunity_name}}</option>
                                        @endforeach
                                        <!-- <option value="2">Project Manager</option>
                            <option value="3">UI/UX Designer</option>
                            <option value="4">Quality Assurance</option>
                            <option value="5">System Administrator</option> -->
                                    </select>
                                </div>
                            </div>

                            <!-- </div>
                        <div class="col"> -->

                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >

                            <div class="form-group" style="width: 100% !important;">
                            <span for="" style="font-size: 16px">
                                Select Category
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <select name="issue_category" id="issue_category" class="form-control" style="height: 36px;
    font-size: 16px !important;">
                                    <option value="" selected disabled>Select Category</option>
                                    <option value="Complaint" {{ $issue->category == "Complaint" ? 'selected' : '' }}>Complaint</option>
                                    <option value="Service Request" {{ $issue->category == "Service Request" ? 'selected' : '' }}>Service Request</option>
                                    <option value="QA" {{ $issue->category == "QA" ? 'selected' : '' }}>QA</option>
                                    <option value="Opportunity" {{ $issue->category == "Opportunity" ? 'selected' : '' }}>Opportunity</option>
                                    <option value="Punch List" {{ $issue->category == "Punch List" ? 'selected' : '' }}>Punch List</option>
                                    <option value="Other" {{ $issue->category == "Other" ? 'selected' : '' }}>Other</option>
                                </select>
                                <!-- </div> -->
                            </div>
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 100%">
                            <span for=""
                                  style="font-size: 16px">Subject
                                <label class="required_field" for="">*</label></span
                            >
                                <div class="input-group">
                                    <input type="text" placeholder="Subject" name="subject" id="subject"
                                           value="{{$issue->subject}}"
                                           class="input form-control" minlength="5" style="font-size: 16px; border-color: #ced4da !important; height: 37px;
    border-radius: 3px; padding: 0px 12px !important;" maxlength="100">
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->

                        </div>

                        <div
                            class="row px-2"
                            style=""
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 100%">
                            <span for=""
                                  style="font-size: 16px">Issue Created By
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <select name="issue_created_by" style="height: 36px;
    font-size: 16px !important;" id="issue_created_by" class="form-control">
                                        <option value="" selected disabled>Select Creator</option>
                                        @foreach($loginuser as $item)
                                            <option value="{{$item->id}}" {{ ($issue->creator?->id) == $item->id ? 'selected' : '' }}>
                                                {{$item->first_name}} {{$item->last_name}}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 100%">
                            <span for="" style="font-size: 16px">
                                Assign To
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <select name="assign_to" id="assign_to" style="height: 36px;
    font-size: 16px !important;" class="form-control">
                                    <option value="" selected disabled>Select Assignee</option>
                                    @foreach($user as $item)
                                        <option value="{{$item->id}}" {{ ($issue->assignee->id ?? null) == $item->id ? 'selected' : '' }}
                                        >
                                            {{$item->first_name}} {{$item->last_name}}
                                        </option>
                                    @endforeach
                                </select>
                                <!-- </div> -->
                            </div>
                        </div>



                        <div class="d-flex" style="gap: 17px; justify-content: end">
                            <button
                                type="button"
                                data-dismiss="modal"
                                aria-label="Close"
                                class="btn px-5 py-2 transparent"
                                style="

                            "
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                onclick="addNewItemModel()"
                                class="btn primaryblue px-5 py-2"
                            >
                                Add
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/fileinput.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/plugins/sortable.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/themes/fas/theme.min.js"></script>
        <!-- TinyMCE loaded via @push('tinymce-scripts') -->

        <script>
            $(document).ready(function() {
                $('.dropzone_library_customize_completess .dz-message').html(
                    '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )

                tinymce.init({
                    selector: '#issue-textarea',
                    plugins: 'lists',
                    toolbar: 'bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist',
                    height: 300,
                    menubar: false,
                    branding: false,
                    toolbar_mode: 'wrap',
                    content_style: "ul { list-style-type: disc; } ol { list-style-type: decimal; }",
                    /*setup: function (editor) {
                        editor.on('input', function () {
                            // Get content and set it to the div
                            document.getElementById('add_intro_letter').innerHTML = editor.getContent();
                        });
                    }*/
                });
            })
            // var imageClick = document.querySelectorAll('.dz-filename');
            // for (let index = 0; index < imageClick.length; index++) {
            //     imageClick[index].addEventListener("click", function() {
            //         console.log("hello test click")
            //     });

            // }
            $(document).on('click', '#my-dropzone-complete', function() {
                $('#imageSizeError').html('');
            });



            Dropzone.autoDiscover = false; // Disable auto-discovery



            var uploadedDocumentMapss = {};
            const myDropzoness = new Dropzone("#my-dropzone-complete", {
                url: "{{ route(getRouteAlias() . '.issue.file-store') }}", // Replace with your server upload URL
                addRemoveLinks: true,
                dictRemoveFile: "Remove", // Proper text for remove button
                previewTemplate: `
        <div class="dz-preview dz-file-preview dz-edit-preview-complete">
       <div class="dz-image-edit-complete"></div>
         <img class="dz-details-imagess-complete" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>


        </div>
    `,
                previewsContainer: ".dropzone_library_customize_complete",
                acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
                },
                init: function () {
                    var iconSrc = "";
                    @php
                        $index = 0; // Counter variable
                    @endphp

                    @foreach ($issue->documents->where('document_status', 'completed') as $doc)
                        <?php
                        $fileExtension = pathinfo($doc->name, PATHINFO_EXTENSION);
                        ?>
                    var exts = "{{$fileExtension}}";
                    let mockFiless{{ $index+1 }} = {
                        opp_id: {{ $doc->id }},
                        name: "{{ $doc->name }}", // File name
                        file: '{{ Storage::url($doc->path) }}' // Path to the file on the server
                    };

                    // Add the file to Dropzone
                    this.emit("addedfile", mockFiless{{ $index+1 }});
                    this.emit("complete", mockFiless{{ $index+1 }});

                    // Optionally, add a hidden input field for the file path
                    // $('form').append('<input type="text" name="images_opportunity_complete[]" value="{{ $doc->name }}">');

                    var iconSrc = "";
                    switch (exts) {
                        case "pdf":
                            iconSrc = "data:image/svg+xml;base64,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";
                            break;
                        case "xls":
                        case "xlsx":
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo="; // Add the SVG for XLS icon
                            break;
                        case "doc":
                        case "docx":
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4="; // Add the SVG for DOC icon
                            break;
                        default:
                            iconSrc = '{{ Storage::url($doc->path) }}'; // Default icon for unsupported formats
                            break;
                    }

                    // Find the specific Dropzone container for this file
                    var dropzoneItemss = document.querySelectorAll('.dropzone_library_customize_complete .dz-edit-preview-complete'); // Get all Dropzone items
                    var currentDropzoneItem = dropzoneItemss[{{ $index }}]; // Select the current file's container
                    var gg = {{ $index }};
                    console.info('completed' + currentDropzoneItem);
                    if (currentDropzoneItem) {
                        // Create the icon image element
                        var iconElement = document.createElement("img");
                        iconElement.src = iconSrc;
                        iconElement.alt = 'File icon';
                        iconElement.style.height = "60px";
                        iconElement.style.width = "100%";
                        $('.dropzone_library_customize_complete .dz-remove').css('margin-top', '-19px');
                        $('.dropzone_library_customize_complete').addClass('dz-started');
                        // console.info('main hello completed' + currentDropzoneItem );
                        // $('.dropzone_library_customize').addClass('dz-started');

                        // Append the icon to the current Dropzone container
                        var iconContainer = currentDropzoneItem.querySelector('.dz-image-edit-complete');
                        console.info(iconContainer);

                        if (iconContainer) {
                            iconContainer.appendChild(iconElement);
                        }
                    }
                    @php
                        $index++; // Increment counter
                    @endphp
                        @endforeach


                        this.on("success", function (file, response) {
                        // Assuming your API response contains a key 'filePath'
                        $('form').append('<input type="text" name="images_opportunity_complete[]" value="' + response.name + '">');
                        uploadedDocumentMapss[file.name] = response.name;
                    });
                    this.on("addedfile", function (file) {
                        $('.dropzone_library_customize_complete').addClass('dz-started');
                        // Extract file extension
                        let ext = file.name.split('.').pop().toLowerCase();

                        // Default icon (if no match found)
                        let iconSrc = "default-icon.png";

                        // Icons for specific file types
                        if (ext === "pdf") {
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz4KPHBhdGggZD0nTTcuNTAzIDEzLjAwMkM3LjM3MDM5IDEzLjAwMiA3LjI0MzIxIDEzLjA1NDcgNy4xNDk0NSAxMy4xNDg0QzcuMDU1NjggMTMuMjQyMiA3LjAwMyAxMy4zNjk0IDcuMDAzIDEzLjUwMlYxNi41MDJDNy4wMDMgMTYuNjM0NiA3LjA1NTY4IDE2Ljc2MTggNy4xNDk0NSAxNi44NTU2QzcuMjQzMjEgMTYuOTQ5MyA3LjM3MDM5IDE3LjAwMiA3LjUwMyAxNy4wMDJDNy42MzU2MSAxNy4wMDIgNy43NjI3OSAxNi45NDkzIDcuODU2NTUgMTYuODU1NkM3Ljk1MDMyIDE2Ljc2MTggOC4wMDMgMTYuNjM0NiA4LjAwMyAxNi41MDJWMTYuMDAySDguNUM4Ljg5NzgyIDE2LjAwMiA5LjI3OTM2IDE1Ljg0NCA5LjU2MDY2IDE1LjU2MjdDOS44NDE5NiAxNS4yODE0IDEwIDE0Ljg5OTggMTAgMTQuNTAyQzEwIDE0LjEwNDIgOS44NDE5NiAxMy43MjI2IDkuNTYwNjYgMTMuNDQxM0M5LjI3OTM2IDEzLjE2IDguODk3ODIgMTMuMDAyIDguNSAxMy4wMDJINy41MDNaTTguNSAxNS4wMDJIOC4wMDNWMTQuMDAySDguNUM4LjYzMjYxIDE0LjAwMiA4Ljc1OTc5IDE0LjA1NDcgOC44NTM1NSAxNC4xNDg0QzguOTQ3MzIgMTQuMjQyMiA5IDE0LjM2OTQgOSAxNC41MDJDOSAxNC42MzQ2IDguOTQ3MzIgMTQuNzYxOCA4Ljg1MzU1IDE0Ljg1NTZDOC43NTk3OSAxNC45NDkzIDguNjMyNjEgMTUuMDAyIDguNSAxNS4wMDJaTTE0Ljk5OCAxMy41MDJDMTQuOTk4IDEzLjM2OTQgMTUuMDUwNyAxMy4yNDIyIDE1LjE0NDQgMTMuMTQ4NEMxNS4yMzgyIDEzLjA1NDcgMTUuMzY1NCAxMy4wMDIgMTUuNDk4IDEzLjAwMkgxNy4wMDNDMTcuMTM1NiAxMy4wMDIgMTcuMjYyOCAxMy4wNTQ3IDE3LjM1NjYgMTMuMTQ4NEMxNy40NTAzIDEzLjI0MjIgMTcuNTAzIDEzLjM2OTQgMTcuNTAzIDEzLjUwMkMxNy41MDMgMTMuNjM0NiAxNy40NTAzIDEzLjc2MTggMTcuMzU2NiAxMy44NTU2QzE3LjI2MjggMTMuOTQ5MyAxNy4xMzU2IDE0LjAwMiAxNy4wMDMgMTQuMDAySDE1Ljk5N0wxNS45OTYgMTUuMDA0SDE3LjAwM0MxNy4xMzU2IDE1LjAwNCAxNy4yNjI4IDE1LjA1NjcgMTcuMzU2NiAxNS4xNTA0QzE3LjQ1MDMgMTUuMjQ0MiAxNy41MDMgMTUuMzcxNCAxNy41MDMgMTUuNTA0QzE3LjUwMyAxNS42MzY2IDE3LjQ1MDMgMTUuNzYzOCAxNy4zNTY2IDE1Ljg1NzZDMTcuMjYyOCAxNS45NTEzIDE3LjEzNTYgMTYuMDA0IDE3LjAwMyAxNi4wMDRIMTUuOTk2TDE1Ljk5OCAxNi41MDFDMTUuOTk4MSAxNi41NjY3IDE1Ljk4NTMgMTYuNjMxNyAxNS45NjAzIDE2LjY5MjRDMTUuOTM1MyAxNi43NTMxIDE1Ljg5ODYgMTYuODA4MyAxNS44NTIzIDE2Ljg1NDhDMTUuNzU4NyAxNi45NDg4IDE1LjYzMTYgMTcuMDAxNyAxNS40OTkgMTcuMDAyQzE1LjM2NjQgMTcuMDAyMyAxNS4yMzkxIDE2Ljk0OTggMTUuMTQ1MiAxNi44NTYzQzE1LjA1MTIgMTYuNzYyNyAxNC45OTgzIDE2LjYzNTYgMTQuOTk4IDE2LjUwM0wxNC45OTUgMTUuNTA1VjE1LjUwM0wxNC45OTggMTMuNTAyWk0xMS41IDEzLjAwMkgxMS45OThDMTIuNTI4NCAxMy4wMDIgMTMuMDM3MSAxMy4yMTI3IDEzLjQxMjIgMTMuNTg3OEMxMy43ODczIDEzLjk2MjkgMTMuOTk4IDE0LjQ3MTYgMTMuOTk4IDE1LjAwMkMxMy45OTggMTUuNTMyNCAxMy43ODczIDE2LjA0MTEgMTMuNDEyMiAxNi40MTYyQzEzLjAzNzEgMTYuNzkxMyAxMi41Mjg0IDE3LjAwMiAxMS45OTggMTcuMDAySDExLjVDMTEuMzY3NCAxNy4wMDIgMTEuMjQwMiAxNi45NDkzIDExLjE0NjQgMTYuODU1NkMxMS4wNTI3IDE2Ljc2MTggMTEgMTYuNjM0NiAxMSAxNi41MDJWMTMuNTAyQzExIDEzLjM2OTQgMTEuMDUyNyAxMy4yNDIyIDExLjE0NjQgMTMuMTQ4NEMxMS4yNDAyIDEzLjA1NDcgMTEuMzY3NCAxMy4wMDIgMTEuNSAxMy4wMDJaTTEyIDE2LjAwMkMxMi4yNjUyIDE2LjAwMiAxMi41MTk2IDE1Ljg5NjYgMTIuNzA3MSAxNS43MDkxQzEyLjg5NDYgMTUuNTIxNiAxMyAxNS4yNjcyIDEzIDE1LjAwMkMxMyAxNC43MzY4IDEyLjg5NDYgMTQuNDgyNCAxMi43MDcxIDE0LjI5NDlDMTIuNTE5NiAxNC4xMDc0IDEyLjI2NTIgMTQuMDAyIDEyIDE0LjAwMlYxNi4wMDJaTTEyIDhWMkg2QzUuNDY5NTcgMiA0Ljk2MDg2IDIuMjEwNzEgNC41ODU3OSAyLjU4NTc5QzQuMjEwNzEgMi45NjA4NiA0IDMuNDY5NTcgNCA0VjEwLjY2OEMzLjQwOSAxMC45NDkgMyAxMS41NTIgMyAxMi4yNVYxNy43NUMzIDE4LjQ0OCAzLjQwOSAxOS4wNSA0IDE5LjMzMlYyMEM0IDIwLjUzMDQgNC4yMTA3MSAyMS4wMzkxIDQuNTg1NzkgMjEuNDE0MkM0Ljk2MDg2IDIxLjc4OTMgNS40Njk1NyAyMiA2IDIySDE4QzE4LjUzMDQgMjIgMTkuMDM5MSAyMS43ODkzIDE5LjQxNDIgMjEuNDE0MkMxOS43ODkzIDIxLjAzOTEgMjAgMjAuNTMwNCAyMCAyMFYxOS4zMzJDMjAuNTkxIDE5LjA1MSAyMSAxOC40NDggMjEgMTcuNzVWMTIuMjVDMjEgMTEuNTUyIDIwLjU5MSAxMC45NSAyMCAxMC42NjhWMTBIMTRDMTMuNDY5NiAxMCAxMi45NjA5IDkuNzg5MjkgMTIuNTg1OCA5LjQxNDIxQzEyLjIxMDcgOS4wMzkxNCAxMiA4LjUzMDQzIDEyIDhaTTQuNzUgMTJIMTkuMjVDMTkuMzE2MyAxMiAxOS4zNzk5IDEyLjAyNjMgMTkuNDI2OCAxMi4wNzMyQzE5LjQ3MzcgMTIuMTIwMSAxOS41IDEyLjE4MzcgMTkuNSAxMi4yNVYxNy43NUMxOS41IDE3LjgxNjMgMTkuNDczNyAxNy44Nzk5IDE5LjQyNjggMTcuOTI2OEMxOS4zNzk5IDE3Ljk3MzcgMTkuMzE2MyAxOCAxOS4yNSAxOEg0Ljc1QzQuNjgzNyAxOCA0LjYyMDExIDE3Ljk3MzcgNC41NzMyMiAxNy45MjY4QzQuNTI2MzQgMTcuODc5OSA0LjUgMTcuODE2MyA0LjUgMTcuNzVWMTIuMjVDNC41IDEyLjE4MzcgNC41MjYzNCAxMi4xMjAxIDQuNTczMjIgMTIuMDczMkM0LjYyMDExIDEyLjAyNjMgNC42ODM3IDEyIDQuNzUgMTJaTTEzLjUgOFYyLjVMMTkuNSA4LjVIMTRDMTMuODY3NCA4LjUgMTMuNzQwMiA4LjQ0NzMyIDEzLjY0NjQgOC4zNTM1NUMxMy41NTI3IDguMjU5NzkgMTMuNSA4LjEzMjYxIDEzLjUgOFonIGZpbGw9J2JsYWNrJy8+Cjwvc3ZnPgo=";
                        } else if (ext === "xls" || ext === "xlsx") {
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
                        } else if (ext === "doc" || ext === "docx") {
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
                        }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "gif") {
                            const reader = new FileReader();
                            reader.onload = function (e) {
                                $(file.previewElement).find(".dz-details-imagess-complete").css("width", '100%');
                                // Find the preview image element and set the source
                                $(file.previewElement).find(".dz-details-imagess-complete").attr("src", e.target.result);
                            };
                            reader.readAsDataURL(file);
                        }
                        $(file.previewElement).find(".dz-details-imagess-complete").css("width", '100%');
                        // Update the preview element with the corresponding icon
                        if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
                            $(file.previewElement).find(".dz-details-imagess-complete").attr("src", iconSrc);
                        }
                    });

                    this.on("removedfile", function (file) {

                        console.log("File removed: ", file.name); // Log file removal
                        // You can add additional logic here to update UI or handle response data
                        // You can add additional logic here to update UI or handle response data


                        file.previewElement.remove();
                        var name = '';
                        if (typeof file.file_name !== 'undefined') {
                            name = file.file_name;
                        } else {
                            name = uploadedDocumentMapss[file.name];
                        }


                        // Send an AJAX request to delete the file from the server
                        fetch("{{ route(getRouteAlias() . '.issue.file-delete-server') }}", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ filename: name, file_id: file.opp_id })
                        })
                            .then(response => response.json())
                            .then(data => {
                                console.info('File deleted from server:', data);

                                var ty = document.querySelectorAll('.dz-image-edit-complete img');
                                var ty2 = document.querySelectorAll('.dz-details-imagess-complete');
                                console.info(ty.length, ty2.length);
                                var ty = document.querySelectorAll('.dz-image-edit-complete img');
                                var ty2 = document.querySelectorAll('.dz-details-imagess-complete');

                                console.info(ty.length, ty2.length);

// if (ty.length === 0 && file.opp_id !== undefined) {
//     // alert('Condition 1: No images found in .dz-image-edit-complete and file.opp_id exists.');
//     $('.dropzone_library_customize_complete').removeClass('dz-started');
//     // console.info('ty.length, ty2.length');
//     $('.dz-delete-image').css('display', 'none');
// } else if (ty2.length === 0 && file.opp_id !== undefined) {
//     // Check ty2 length instead of directly accessing src
//     // alert('Condition 2: No images found in .dz-details-imagess-complete and file.opp_id exists.');
//     $('.dropzone_library_customize_complete').removeClass('dz-started');
//     // console.info('ty.length, ty2.length');
//     $('.dz-delete-image').css('display', 'none');
// } else {
//     // alert('Condition 3: Images exist or file.opp_id is undefined.');
//     $('.dropzone_library_customize_complete').addClass('dz-started');
//     // console.info('ty.length, ty2.length');
//     $('.dz-delete-image').css('display', 'inline-block');
// }

                                // if (ty.length == 0 && file.opp_id != undefined) {
                                //     alert('yes');
                                //     $('.dropzone_library_customize_complete').removeClass('dz-started');
                                //     console.info('ty.length, ty2.length');
                                //     $('.dz-delete-image').css('display', 'none');
                                // }else if (ty2.src.length == 0 && file.opp_id != undefined) {
                                //     alert('yes');
                                //     $('.dropzone_library_customize_complete').removeClass('dz-started');
                                //     console.info('ty.length, ty2.length');
                                //     $('.dz-delete-image').css('display', 'none');

                                // }else{
                                //     alert('yes');
                                //     $('.dropzone_library_customize_complete').addClass('dz-started');
                                //     console.info('ty.length, ty2.length');
                                //     $('.dz-delete-image').css('display', 'inline-block');
                                // }

                                // You can add additional logic here to update UI or handle response data
                            })
                            .catch(error => {
                                console.error('Error deleting file from server:', error);
                            });
                        $('form').find('input[name="images_opportunity_complete[]"][value="' + name + '"]').remove();
                    });

                    // Add error handling if needed
                    this.on("error", function (file, errorMessage) {
                        console.error("Dropzone error:", errorMessage);
                    });
                }
            });

        </script>





        <script>
            $(document).ready(function() {
                $('.dropzone_library_customizess .dz-message').html(
                    '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )
            })
            // var imageClick = document.querySelectorAll('.dz-filename');
            // for (let index = 0; index < imageClick.length; index++) {
            //     imageClick[index].addEventListener("click", function() {
            //         console.log("hello test click")
            //     });

            // }
            $(document).on('click', '#my-dropzone', function() {
                $('#imageSizeError').html('');
            });



            Dropzone.autoDiscover = false; // Disable auto-discovery



            var uploadedDocumentMap = {};
            const myDropzone = new Dropzone("#my-dropzone", {
                url: "{{ route(getRouteAlias() . '.issue.file-store') }}", // Replace with your server upload URL
                addRemoveLinks: true,
                dictRemoveFile: "Remove", // Proper text for remove button
                previewTemplate: `
        <div class="dz-preview dz-file-preview dz-edit-preview">
       <div class="dz-image-edit"></div>
         <img class="dz-details-imagess" style="height: 60px;" src="" />
            <div class="dz-details">

                <div class="dz-icon"></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>


        </div>
    `,
                previewsContainer: ".dropzone_library_customize",
                acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Include CSRF token in the headers
                },
                init: function () {
                    var iconSrc = "";
                    @php
                        $indexss = 0; // Counter variable
                    @endphp
                    @foreach ($issue->documents->where('document_status', 'regular') as  $doc)
                        <?php
                        $fileExtension = pathinfo($doc->name, PATHINFO_EXTENSION);
                        ?>
                    var exts = "{{$fileExtension}}";
                    let mockFilessss{{ $indexss+1 }} = {
                        id: {{ $doc->id }},
                        name: "{{ $doc->name }}", // File name
                        file: '{{ Storage::url($doc->path) }}' // Path to the file on the server
                    };

                    // Add the file to Dropzone
                    this.emit("addedfile", mockFilessss{{ $indexss+1 }});
                    this.emit("complete", mockFilessss{{ $indexss+1 }});

                    // Optionally, add a hidden input field for the file path
                    // $('form').append('<input type="text" name="images_opportunity[]" value="{{ $doc->name }}">');

                    var iconSrc = "";


                    switch (exts) {
                        case "pdf":
                            iconSrc = "data:image/svg+xml;base64,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";
                            break;
                        case "xls":
                        case "xlsx":
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo="; // Add the SVG for XLS icon
                            break;
                        case "doc":
                        case "docx":
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4="; // Add the SVG for DOC icon
                            break;
                        default:
                            iconSrc = '{{ Storage::url($doc->path) }}'; // Default icon for unsupported formats
                            break;
                    }

                    // Find the specific Dropzone container for this file.dropzone .dz-preview
                    var dropzoneItemsss = document.querySelectorAll('.dropzone_library_customize .dz-edit-preview'); // Get all Dropzone items

                    var currentDropzoneItemss = dropzoneItemsss[{{ $indexss }}]; // Select the current file's container
                    // console.info('main hello regular' + iconContainers )
                    var ff = {{ $indexss }};
                    console.info('regular' + ff);
                    if (currentDropzoneItemss) {

                        // Create the icon image element
                        var iconElementss = document.createElement("img");
                        iconElementss.src = iconSrc;
                        iconElementss.alt = 'File icon';
                        iconElementss.style.height = "60px";
                        iconElementss.style.width = "100%";
                        $('.dz-remove').css('margin-top', '-19px');
                        $('.dropzone_library_customize').addClass('dz-started');
                        // $('.dropzone_library_customize').addClass('dz-started');


                        // Append the icon to the current Dropzone container
                        var iconContainers = currentDropzoneItemss.querySelector('.dz-image-edit');
                        console.info('main hello regular' + iconContainers );


                        if (iconContainers) {
                            iconContainers.appendChild(iconElementss);
                        }
                    }
                    @php
                        $indexss++; // Increment counter
                    @endphp
                        @endforeach


                        this.on("success", function (file, response) {
                        // Assuming your API response contains a key 'filePath'
                        $('form').append('<input type="text" name="images_opportunity[]" value="' + response.name + '">');
                        uploadedDocumentMap[file.name] = response.name;
                    });
                    this.on("addedfile", function (file) {
                        $('.dropzone_library_customize').addClass('dz-started');
                        // Extract file extension
                        let ext = file.name.split('.').pop().toLowerCase();

                        // Default icon (if no match found)
                        let iconSrc = "default-icon.png";

                        // Icons for specific file types
                        if (ext === "pdf") {
                            iconSrc = "data:image/svg+xml;base64,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";
                        } else if (ext === "xls" || ext === "xlsx") {
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
                        } else if (ext === "doc" || ext === "docx") {
                            iconSrc = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
                        }else if (ext === "png" || ext === "jpg" || ext === "webp" || ext === "jpeg" || ext === "gif") {
                            const reader = new FileReader();
                            reader.onload = function (e) {
                                $(file.previewElement).find(".dz-details-imagess").css("width", '100%');
                                // Find the preview image element and set the source
                                $(file.previewElement).find(".dz-details-imagess").attr("src", e.target.result);
                            };
                            reader.readAsDataURL(file);
                        }

                        // Update the preview element with the corresponding icon
                        if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext === "xls" || ext === "pdf") {
                            $(file.previewElement).find(".dz-details-imagess").attr("src", iconSrc);
                        }
                    });

                    this.on("removedfile", function (file) {

                        console.log("File removed: ", file.name); // Log file removal
                        // You can add additional logic here to update UI or handle response data
                        // You can add additional logic here to update UI or handle response data


                        file.previewElement.remove();
                        var name = '';
                        if (typeof file.file_name !== 'undefined') {
                            name = file.file_name;
                        } else {
                            name = uploadedDocumentMap[file.name];
                        }


                        // Send an AJAX request to delete the file from the server
                        fetch("{{ route(getRouteAlias() . '.issue.file-delete-server') }}", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ filename: name, file_id: file.id })
                        })
                            .then(response => response.json())
                            .then(data => {
                                console.info('File deleted from server:', data);

                                var ty = document.querySelectorAll('.dz-image-edit img');
                                console.info(ty.length);
                                // if (ty.length == 0 && file.id != undefined) {
                                //     $('.dropzone_library_customize').removeClass('dz-started');
                                //     $('.dz-preview').css('display', 'none');
                                // }else{
                                //     $('.dropzone_library_customize').addClass('dz-started');
                                //     $('.dz-preview').css('display', 'inline-block');
                                // }

                                // You can add additional logic here to update UI or handle response data
                            })
                            .catch(error => {
                                console.error('Error deleting file from server:', error);
                            });
                        $('form').find('input[name="images_opportunity[]"][value="' + name + '"]').remove();
                    });

                    // Add error handling if needed
                    this.on("error", function (file, errorMessage) {
                        console.error("Dropzone error:", errorMessage);
                    });
                }
            });

        </script>

        <script>
            $('#statusDropdown').on('change', function() {
                var status = $(this).val();
                var opportunityId = $(this).data('id');
                var csrfToken = $('meta[name="csrf-token"]').attr('content');
                var url = "{{ route('organization.issuesUpdateStatus', ['id' => ':id']) }}";
                url = url.replace(':id', opportunityId);
                $.ajax({
                    url: url, // Replace with your actual URL endpoint
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    data: { status: status },
                    success: function(data) {
                        toastr.success(data.message);
                        applyStatusColor(status);
                        location.reload();

                    },
                    error: function(error) {
                        toastr.warning('Error');
                    }
                });
            });

            $(document).ready(function() {
                var initialStatus = $('#statusDropdown').val();
                applyStatusColor(initialStatus);
            }); // Call the color application function here


            function applyStatusColor(status) {
                // alert('color');
                var $select = $('#statusDropdown');

                var styles = getStatusStyles(status, status);

                // Apply styles to the select element
                $select.css(styles);

                // Apply styles to individual options
                /*$select.find('option').each(function() {
                    var optionStatus = $(this).val();
                    var optionStyles = getStatusStyles(optionStatus, status);
                    $(this).css(optionStyles);
                });*/
            }


            function getStatusStyles(status, currentStatus) {
                //  alert(status);
                var originalStyles = {
                    1: { color: '#F1B602', backgroundColor: '#FFFAE8' },
                    2: { color: '#3B4159', backgroundColor: '#E2E4EA' },
                    3: { color: '#1BB6D5', backgroundColor: '#CFFBFF' },

                };

                // var defaultStyle = { color: 'rgba(81, 86, 108, 1)', backgroundColor: 'rgba(226, 228, 234, 1)' };

                // if (currentStatus <= status) {
                return originalStyles[currentStatus];
                // }

                // return defaultStyle;
            }
        </script>
        <script>
            $(document).ready(function() {
                var initialPreview = [
                    @foreach ($issue->documents->where('document_status', 'regular') as $document)
                    // "{{ Storage::url($document->path) }}",


                    @php
                        $filePath = Storage::url($document->path);
                        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                    @endphp
                        @if(in_array($extension, ['jpg', 'jpeg', 'png', 'gif']))
                        "{{ $filePath }}",
                    @elseif($extension == 'pdf')
                        "{{ asset('admin_assets/images/pdf.png') }}",
                    @elseif(in_array($extension, ['xls', 'xlsx', 'csv']))
                        "{{ asset('admin_assets/images/xlsx.png') }}",
                    @elseif(in_array($extension, ['doc', 'docx']))
                        "{{ asset('admin_assets/images/doc.png') }}",
                    @else
                        "{{ asset('admin_assets/images/file.png') }}",
                    @endif
                    @endforeach
                ];

                var initialPreviewConfig = [
                        @foreach ($issue->documents->where('document_status', 'regular') as $document)
                    {
                        caption: "{{ $document->name }}",
                        width: "120px",
                        url: "{{ route('organization.document.delete.issue', ['id' => encodeId($document->id)]) }}", // API to delete the file
                        key: "{{ $document->id }}"
                    },
                    @endforeach
                ];

                $("#multiplefileupload").fileinput({
                    'theme': 'fa',
                    'uploadUrl': '#',
                    showRemove: false,
                    showUpload: false,
                    showZoom: false,
                    showCaption: false,
                    browseClass: "btn btn-danger",
                    browseLabel: "",
                    browseIcon: `<svg width="20" height="20" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
                    overwriteInitial: false,
                    initialPreview: initialPreview,
                    initialPreviewAsData: true, // Show preview as data
                    initialPreviewConfig: initialPreviewConfig,
                    fileActionSettings: {
                        showUpload: false,
                        showZoom: false,
                        removeIcon: "<i class='fa fa-times'></i>"
                    },
                    deleteUrl: "#", // Set your delete URL here if needed
                    deleteExtraData: {
                        _token: "{{ csrf_token() }}" // Include CSRF token for delete
                    },
                    fileActionSettings: {
                        showUpload: false,
                        showZoom: false,
                        removeIcon: "<i class='fa fa-times'></i>",
                    },

                    filetypeSettings: {
                        'pdf': function(vType, vName) { return vType === 'application/pdf' || vName.match(/\.(pdf)$/i); },
                        'doc': function(vType, vName) { return vType === 'application/msword' || vName.match(/\.(doc|docx)$/i); },
                        'excel': function(vType, vName) { return vType === 'application/vnd.ms-excel' || vType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || vName.match(/\.(xls|xlsx|csv)$/i); }
                    },

                    previewFileIconSettings: {
                        'pdf': '<img src="{{ asset('admin_assets/images/pdf.png') }}" alt="PDF File" style="width:100%; height:100%; margin-top: 27px;">',
                        'doc': '<img src="{{ asset('admin_assets/images/doc.png') }}" alt="Word File" style="width:100%; height:100%; margin-top: 27px;">',
                        'excel': '<img src="{{ asset('admin_assets/images/xlsx.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',
                        'other': '<img src="{{ asset('admin_assets/images/file.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">'
                    },

                    previewFileExtSettings: {
                        'pdf': function(ext) { return ext.match(/(pdf)$/i); },
                        'doc': function(ext) { return ext.match(/(doc|docx)$/i); },
                        'excel': function(ext) { return ext.match(/(xls|xlsx|csv)$/i); }
                    }
                });
            });
        </script>


        <script>
            $(document).ready(function() {
                var initialPreview2 = [
                    @foreach ($issue->documents->where('document_status', 'completed') as $document)
                    // "{{ Storage::url($document->path) }}",
                    @php
                        $filePath = Storage::url($document->path);
                        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                    @endphp

                        @if(in_array($extension, ['jpg', 'jpeg', 'png', 'gif']))
                        "{{ $filePath }}",
                    @elseif($extension == 'pdf')
                        "{{ asset('admin_assets/images/pdf.png') }}",
                    @elseif(in_array($extension, ['xls', 'xlsx', 'csv']))
                        "{{ asset('admin_assets/images/xlsx.png') }}",
                    @elseif(in_array($extension, ['doc', 'docx']))
                        "{{ asset('admin_assets/images/doc.png') }}",
                    @else
                        "{{ asset('admin_assets/images/file.png') }}",
                    @endif
                    @endforeach
                ];

                var initialPreviewConfig2 = [
                        @foreach ($issue->documents->where('document_status', 'completed') as $document)
                    {
                        caption: "{{ $document->name }}",
                        width: "120px",
                        url: "{{ route('organization.document.delete.issue', ['id' => encodeId($document->id)]) }}", // API to delete the file
                        key: "{{ $document->id }}"
                    },
                    @endforeach
                ];
                console.info(initialPreviewConfig2);

                //     $("#multiplefileupload2").fileinput({
                //     'theme': 'fa',
                //     'uploadUrl': '#',
                //     showRemove: false,
                //     showUpload: false,
                //     showZoom: false,
                //     showCaption: false,
                //     browseClass: "btn btn-danger",
                //     browseLabel: "",
                //     browseIcon: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
                //     overwriteInitial: false,
                //     initialPreview: initialPreview2,
                //     initialPreviewAsData: true,
                //     initialPreviewConfig: initialPreviewConfig2,
                //     deleteUrl: "/file-delete",  // The URL to handle file deletion
                //     fileActionSettings: {
                //         showUpload: false,
                //         showZoom: false,
                //         removeIcon: "<i class='fa fa-times'></i>",
                //     },
                //     deleteUrl: "#", // Set your delete URL here if needed
                //         deleteExtraData: {
                //             _token: "{{ csrf_token() }}" // Include CSRF token for delete
                //         },

                //     fileActionSettings: {
                //         showUpload: false,
                //         showZoom: false,
                //         removeIcon: "<i class='fa fa-times'></i>",
                //     },

                //     filetypeSettings: {
                //         'pdf': function(vType, vName) { return vType === 'application/pdf' || vName.match(/\.(pdf)$/i); },
                //         'doc': function(vType, vName) { return vType === 'application/msword' || vName.match(/\.(doc|docx)$/i); },
                //         'excel': function(vType, vName) { return vType === 'application/vnd.ms-excel' || vType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || vName.match(/\.(xls|xlsx|csv)$/i); }
                //     },

                //     previewFileIconSettings: {
                //         'pdf': '<img src="{{ asset('admin_assets/images/pdf.png') }}" alt="PDF File" style="width:100%; height:100%; margin-top: 27px;">',
                //         'doc': '<img src="{{ asset('admin_assets/images/doc.png') }}" alt="Word File" style="width:100%; height:100%; margin-top: 27px;">',
                //         'excel': '<img src="{{ asset('admin_assets/images/xlsx.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',
                //         'other': '<img src="{{ asset('admin_assets/images/file.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">'
                //     },

                //     previewFileExtSettings: {
                //         'pdf': function(ext) { return ext.match(/(pdf)$/i); },
                //         'doc': function(ext) { return ext.match(/(doc|docx)$/i); },
                //         'excel': function(ext) { return ext.match(/(xls|xlsx|csv)$/i); }
                //     }
                // }).on('filedeleted', function(event, key, jqXHR, data) {
                //     console.log('File Deleted: ' + key);
                // });

                $("#multiplefileupload2").fileinput({
                    'theme': 'fa',
                    'uploadUrl': '#',
                    showRemove: false,
                    showUpload: false,
                    showZoom: false,
                    showCaption: false,
                    browseClass: "btn btn-danger",
                    browseLabel: "",
                    browseIcon: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
                    overwriteInitial: false,
                    initialPreview: initialPreview2,
                    initialPreviewAsData: true, // Show preview as data
                    initialPreviewConfig: initialPreviewConfig2,
                    fileActionSettings: {
                        showUpload: false,
                        showZoom: false,
                        removeIcon: "<i class='fa fa-times'></i>"
                    },
                    deleteUrl: "#", // Set your delete URL here if needed
                    deleteExtraData: {
                        _token: "{{ csrf_token() }}" // Include CSRF token for delete
                    },
                    fileActionSettings: {
                        showUpload: false,
                        showZoom: false,
                        removeIcon: "<i class='fa fa-times'></i>",
                    },

                    filetypeSettings: {
                        'pdf': function(vType, vName) { return vType === 'application/pdf' || vName.match(/\.(pdf)$/i); },
                        'doc': function(vType, vName) { return vType === 'application/msword' || vName.match(/\.(doc|docx)$/i); },
                        'excel': function(vType, vName) { return vType === 'application/vnd.ms-excel' || vType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || vName.match(/\.(xls|xlsx|csv)$/i); }
                    },

                    previewFileIconSettings: {
                        'pdf': '<img src="{{ asset('admin_assets/images/pdf.png') }}" alt="PDF File" style="width:100%; height:100%; margin-top: 27px;">',
                        'doc': '<img src="{{ asset('admin_assets/images/doc.png') }}" alt="Word File" style="width:100%; height:100%; margin-top: 27px;">',
                        'excel': '<img src="{{ asset('admin_assets/images/xlsx.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',
                        'other': '<img src="{{ asset('admin_assets/images/file.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">'
                    },

                    previewFileExtSettings: {
                        'pdf': function(ext) { return ext.match(/(pdf)$/i); },
                        'doc': function(ext) { return ext.match(/(doc|docx)$/i); },
                        'excel': function(ext) { return ext.match(/(xls|xlsx|csv)$/i); }
                    }
                });
            });
        </script>


        <script>
            function handleFileUpload(input, containerId) {
                const files = input.files;
                const uploadBoxesContainer = document.getElementById(containerId);

                // Clear previous previews

                Array.from(files).forEach(file => {
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewBox = document.createElement('div');
                            previewBox.classList.add('upload-box');
                            previewBox.innerHTML = `
                    <img src="${e.target.result}" alt="Uploaded File" class="file-preview">
                `;
                            uploadBoxesContainer.appendChild(previewBox);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
            $(document).ready(function() {
                $('#edit-icon').on('click', function() {
                    // Make the fields editable
                    $('#subject, #issue-category, #issue-created-by, #assign-to').attr('contenteditable', 'true').addClass('editable-field');

                    // Show the Save button
                    $('#save-btn-subject').show();

                    // Add some visual cue for edit mode (optional)
                    $('.editable-field').css({
                        'border': '1px dashed #0074D9',
                        'padding': '4px'
                    });
                });

                $('#save-btn-subject').on('click', function() {
                    // Get the updated values
                    var subject = $('#subject').text();
                    var issueCategory = $('#issue-category').text();
                    var issueCreatedBy = $('#issue-created-by').text();
                    var assignTo = $('#assign-to').text();


                    $('#subject, #issue-category, #issue-created-by, #assign-to').attr('contenteditable', 'false').removeClass('editable-field');

                    $('#subject, #issue-category, #issue-created-by, #assign-to').css({
                        'border': 'none',  // Reset or remove the border
                        'padding': '0'     // Reset or remove the padding
                    });
                    $('#save-btn-subject').hide();

                    // Optional: Remove the visual cue for edit mode
                    // $('#subject, #issue-category, #issue-created-by, #assign-to').removeClass('editable-field');
                });
                $('#edit-icon-property').on('click', function() {
                    // Make fields editable
                    $('#property-name, #property-address, #property-location').attr('contenteditable', 'true').css({
                        'border': '1px dashed #0074D9',
                        'padding': '4px'
                    });

                    // Show the Save button
                    $('#save-btn').show();
                });

                // Click event for the Save button
                $('#save-btn').on('click', function() {
                    // Make fields non-editable
                    $('#property-name, #property-address, #property-location').attr('contenteditable', 'false').css({
                        'border': 'none',
                        'padding': '0'
                    });

                    // Hide the Save button
                    $('#save-btn').hide();

                    // Get the updated values
                    var propertyName = $('#property-name').text();
                    var propertyAddress = $('#property-address').text();
                    var propertyLocation = $('#property-location').text();


                });
                $('#edit-contact-icon').on('click', function() {
                    // Make fields editable
                    $('#contact-name, #contact-role, #contact-phone, #contact-email').attr('contenteditable', 'true').css({
                        'border': '1px dashed #0074D9',
                        'padding': '4px'
                    });

                    // Show the Save button
                    $('#save-btn-contact').show();
                });

                // Click event for the Save button for contact information
                $('#save-btn-contact').on('click', function() {
                    // Make fields non-editable
                    $('#contact-name, #contact-role, #contact-phone, #contact-email').attr('contenteditable', 'false').css({
                        'border': 'none',
                        'padding': '0'
                    });

                    // Hide the Save button
                    $('#save-btn-contact').hide();

                    // Get the updated values
                    var contactName = $('#contact-name').text();
                    var contactRole = $('#contact-role').text();
                    var contactPhone = $('#contact-phone').text();
                    var contactEmail = $('#contact-email').text();

                    // Print values to the console for debugging
                    console.log('Contact Name:', contactName);
                    console.log('Contact Role:', contactRole);
                    console.log('Contact Phone:', contactPhone);
                    console.log('Contact Email:', contactEmail);

                    // Optionally, you can make an AJAX request to save the data to the server
                });
            });

        </script>


    @endpush
@endsection
