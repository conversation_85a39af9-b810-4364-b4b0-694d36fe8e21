@extends('layouts.admin.master')
@section('title', 'Client')
@section('styles')
    @include('organization.opportunity.create-css')
@endsection
@section('section')
    @foreach ($errors as $error)
        <label>{{ $error }}</label>
    @endforeach
    <section class="dashboard_main pb-5">
        <form id="add_client_form" action="{{ $action }}" method="POST"
            enctype="multipart/form-data">
            @method('post')
            @csrf
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Property Information</h2>

                    <div class="image_upload_btn upload_file_wrapper upload_image_btn w-fit">
                        <label class="btn primaryblue" for="upload_image"
                            title="{{ $client->image ?? '' }}" data-toggle="modal"
                            data-target="#propertyModal">
                            Select Property
                        </label>
                    </div>
                </div>
                <div class="row pt-3 gy-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Property Name<span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Property Name" name="property_name"
                                    value="{{ old('property_name', $client->property_name ?? '') }}"
                                    class="input form-control" minlength="2" maxlength="40" required>

                                @if ($errors->has('property_name'))
                                    <div class="laravel_error">{{ $errors->first('property_name') }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Address 1<span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Address 1" name="address1"
                                    value="{{ old('address1', $client->address1 ?? '') }}"
                                    class="input form-control" minlength="2" maxlength="40" required>
                                @if ($errors->has('address1'))
                                    <div class="laravel_error">{{ $errors->first('address1') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Address 2 <span
                                    class="placeholder-text">(Optional)</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Address 2" name="address2"
                                    value="{{ old('address2', $client->address2 ?? '') }}"
                                    class="input form-control" minlength="10" maxlength="150">
                                @if ($errors->has('address2'))
                                    <div class="laravel_error">{{ $errors->first('address2') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2">City <span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="City" name="city"
                                    value="{{ old('city', $client->city ?? '') }}"
                                    class="input form-control" minlength="10" maxlength="150">
                                @if ($errors->has('city'))
                                    <div class="laravel_error">{{ $errors->first('city') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2" for="state">State<span
                                    class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="State" name="state" id="state"
                                    value="{{ old('state', $client->state ?? '') }}"
                                    class="input form-control" maxlength="255" required>
                                @if ($errors->has('state'))
                                    <div class="laravel_error">{{ $errors->first('state') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <label class="label mb-2">Zip Code <span class="steric">*</span></label>
                            <div class="field_wrapper">
                                <input type="text" placeholder="Zip Code" name="zip_code"
                                    id="zip_code"
                                    value="{{ old('zip_code', $client->zip_code ?? '') }}"
                                    class="input form-control" maxlength="40" required>
                                @if ($errors->has('zip_code'))
                                    <div class="laravel_error">{{ $errors->first('zip_code') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-2 col-md-6">
                        <div class="field">
                            <div class="label-heading d-flex justify-content-between"> <label
                                    class="label">Account <span class="steric">*</span></label>
                                <div class="add-new" style="cursor: pointer;" data-toggle="modal"
                                    data-target="#selectClientModal"><b
                                        style="color: #0074D9; font-size: 14px;">+ Add new Account</b>
                                </div>
                            </div>
                            <div class="absolute-error">
                                <select name="company_id" onchange="getContact()"
                                    id="company_id_account"
                                    class="input compnyids select_placeholder custom_selectBox basic-single-select"
                                    required>
                                    <option selected disabled value="">Select Account</option>
                                    @foreach ($accounts as $item)
                                        <option value="{{ $item->id }}">{{ $item->company_name }}
                                        </option>
                                    @endforeach

                                </select>
                                @if ($errors->has('company_id'))
                                    <div class="laravel_error" style="">
                                        {{ $errors->first('company_id') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- contact information section -->
            <div class="panel mt-4">
                <div class="panel_header">
                    <h2 class="panel_title">Contact Information</h2>
                    <div class="image_upload_btn upload_file_wrapper upload_image_btn w-fit d-flex gap-4"
                        style="gap: 10px">
                        <label class="btn transparent" for="upload_image"
                            onclick="clearContactFields()">
                            Clear
                        </label>
                        <label class="btn primaryblue" for="upload_image"
                            title="{{ $client->image ?? '' }}" data-toggle="modal"
                            data-target="#contactModal">
                            Select Contact
                        </label>
                    </div>
                </div>

                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">First Name<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="First Name" name="first_name"
                                value="{{ old('first_name', $client->first_name ?? '') }}"
                                class="input form-control" required>
                            <input type="hidden" placeholder="First Name" id="contactidadd"
                                name="contact_ids" class="input form-control" readonly>
                            @if ($errors->has('first_name'))
                                <div class="laravel_error">{{ $errors->first('first_name') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Last Name<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Last Name" name="last_name"
                                value="{{ old('last_name', $client->last_name ?? '') }}"
                                class="input form-control">
                            @if ($errors->has('last_name'))
                                <div class="laravel_error">{{ $errors->first('last_name') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">Title<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Title / Role" name="title_role"
                                value="{{ old('title_role', $client->title_role ?? '') }}"
                                class="input form-control" required>
                            @if ($errors->has('title_role'))
                                <div class="laravel_error">{{ $errors->first('title_role') }}</div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="panel_fields mt-1">
                    <div class="field">
                        <label class="label">Email<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="email" placeholder="Email" name="email" id="email"
                                value="{{ old('email', $client->email ?? '') }}"
                                class="input form-control c_emaiil" required>

                            @if ($errors->has('email'))
                                <div class="laravel_error">{{ $errors->first('email') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">Phone Number<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Phone Number"
                                oninput="maskPhoneNumber(event)" maxlength="12" minlength="12"
                                name="phone_number" id="phone_number"
                                value="{{ old('phone_number', $client->phone_number ?? '') }}"
                                class="input form-control" minlength="5" maxlength="50" required>
                            @if ($errors->has('phone_number'))
                                <div class="laravel_error">{{ $errors->first('phone_number') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Second Phone<span> (Optional)</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Second phone" name="second_phone"
                                id="phone_number2" class="input form-control"
                                oninput="maskPhoneNumber(event)" maxlength="12" minlength="12">
                            @if ($errors->has('second_phone'))
                                <div class="laravel_error">{{ $errors->first('second_phone') }}</div>
                            @endif
                        </div>
                    </div>


                </div>
                <div class="panel_fields" style="">




                </div>
            </div>

            <!-- opportunity information section -->
            <div class="panel mt-4 client-billing-wrapper">
                <div class="panel_header">
                    <h2 class="panel_title">Opportunity Information</h2>
                </div>

                <div class="panel_fields mt-4">
                    <div class="field">
                        <label class="label">Opportunity Name<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="text" placeholder="Opportunity Name"
                                name="opportunity_name" id="opportunity_name"
                                value="{{ old('opportunity_name', $client->opportunity_name ?? '') }}"
                                class="input form-control" minlength="5" maxlength="50" required>
                            @if ($errors->has('opportunity_name'))
                                <div class="laravel_error">{{ $errors->first('opportunity_name') }}
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">Net New<span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select name="net_new" id="net_new" required
                                class="input select_placeholder custom_selectBox basic-single-select">
                                <option value="" selected disabled>Select Net New</option>
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                            @if ($errors->has('net_new'))
                                <div class="laravel_error">{{ $errors->first('net_new') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Lead Source<span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select name="lead_source" id="lead_source"
                                class="input select_placeholder custom_selectBox basic-single-select">
                                <option value="" selected disabled>Lead Source</option>
                                <option value="facebook">Facebook</option>
                                <option value="instagram">Instagram</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="twitter">Twitter</option>
                            </select>

                            @if ($errors->has('lead_source'))
                                <div class="laravel_error">{{ $errors->first('lead_source') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="panel_fields" style="">
                    <div class="field">
                        <label class="label">Opportunity Type<span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select name="opportunity_type" id="opportunity_type"
                                class="input select_placeholder custom_selectBox basic-single-select">
                                <option value="" disabled>Select Opportunity Type</option>
                                <!-- <option value="Enhancements">Enhancements</option>
                                <option value="Maintenance">Maintenance</option> -->
                                <option value="New">New</option>
                                <option value="Renew">Renew</option>
                            </select>
                            @if ($errors->has('opportunity_type'))
                                <div class="laravel_error">{{ $errors->first('opportunity_type') }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label">Customer Bid Form<span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select name="customer_bid_form" required id="customer_bid_form"
                                class="input custom_selectBox select_placeholder basic-single-select">
                                <option value="" selected disabled>Select Yes or No</option>
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                            @if ($errors->has('customer_bid_form'))
                                <div class="laravel_error">{{ $errors->first('customer_bid_form') }}
                                </div>
                            @endif
                        </div>
                    </div>


                    <div class="field">
                        <label class="label">Bid Due Date<span class="steric">*</span></label>
                        <div class="field_wrapper">
                            <input type="date" placeholder="Bid Due Date" name="bid_due_date"
                                id="bid_due_date"
                                value="{{ old('bid_due_date', $client->bid_due_date ?? '') }}"
                                class="input form-control" required>
                            @if ($errors->has('bid_due_date'))
                                <div class="laravel_error">{{ $errors->first('bid_due_date') }}</div>
                            @endif
                        </div>
                    </div>
                </div>




                <div class="panel_fields-two division-section" style="margin-bottom: -18px;">
                    <div class="field">
                        <label class="label mb-2">Division <span class="steric">*</span></label>
                        <div class="absolute-error">
                            <select required name="division" id="division"
                                class="input division-select custom_selectBox basic-single-select">
                                <option value="">Select Division</option>
                                @foreach ($divisions as $division)
                                    <option value="{{ $division->id }}">{{ $division->name }}
                                    </option>
                                @endforeach
                            </select>

                            @if ($errors->has('division'))
                                <div class="laravel_error">{{ $errors->first('division') }}</div>
                            @endif
                        </div>
                    </div>

                    <div class="field">
                        <label class="label mb-2">Service Line<span class="steric">*</span></label>
                        <div class="absolute-error serviceline">
                            <select required name="service_line" id="service_line"
                                class="selectpicker input service-line-select custom_selectBox select2-hidden-accessible basic-single-select"
                                aria-label="Default select example" data-live-search="true">
                            </select>

                            @if ($errors->has('service_line'))
                                <div class="laravel_error">{{ $errors->first('service_line') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label mb-2">Pricing Model <span class="steric">*</span></label>
                        <div class="absolute-error price-model">
                            <select required name="price_model" id="price-model"
                                class="selectpicker input custom_selectBox select2-hidden-accessible basic-single-select"
                                aria-label="Default select example" data-live-search="true">
                                <option value="" selected>Select Type</option>
                                <!-- Options will be populated dynamically based on Division and Service Line selection -->
                            </select>
                            @if ($errors->has('price_model'))
                                <div class="laravel_error">{{ $errors->first('price_model') }}</div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="field mt-5">
                    <label for="describeProject" class="reqest-text-label d-block fw-bold"
                        style="font-weight: 500;">Opportunity Request Information</label>
                    <textarea class="form-control" name="request_information" id="" style="height:200px"></textarea>
                    @error('request_information')
                        <label id="requsetTitle-error" class="error">{{ $message }}</label>
                    @enderror
                </div>
            </div>


            <div class="panel mt-4 client-billing-wrapper">
                <div class="panel_header">
                    <h2 class="panel_title">Assignment</h2>
                </div>

                <div class="panel_fields mt-4">
                    <div class="field">
                        <div class="label-heading d-flex justify-content-between"> <label
                                class="label">Opportunity Owner</label>
                        </div>
                        <div class="absolute-error">
                            <select name="opportunity_owner_id" id="opportunity_owner_id"
                                class="input custom_selectBox select_placeholder basic-single-select">
                                <option value="" selected>Select Manager</option>
                                @forelse ($opportunityOwner as $owner)
                                    <option value="{{ $owner->id }}"
                                        {{ old('opportunity_owner_id', $opportunity->opportunity_owner_id) == $owner->id ? 'selected' : '' }}>
                                        {{ $owner->first_name . ' ' . $owner->last_name }}
                                    </option>
                                @empty
                                @endforelse
                            </select>
                            @if ($errors->has('opportunity_owner_id'))
                                <div class="laravel_error">
                                    {{ $errors->first('opportunity_owner_id') }}</div>
                            @endif
                        </div>
                    </div>
                    <div class="field">
                        <label class="label mb-2">Sales Rep
                            <!-- <span class="steric">*</span> -->
                        </label>
                        <div class="absolute-error">
                            <select name="sale_person_ids" id=""
                                class="select_placeholder input basic-single-select">
                                <option value="" selected style="color: red !important;">Select
                                    Name</option>
                                @forelse ($sales as $sale)
                                    <option value="{{ $sale->id }}"
                                        {{ old('sale_person_id', $opportunity->sale_person_id) == $sale->id ? 'selected' : '' }}>
                                        {{ $sale->first_name . ' ' . $sale->last_name }}</option>

                                @empty
                                @endforelse
                            </select>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label mb-2">Estimator Name
                        </label>
                        <div class="absolute-error">
                            <select name="estimator_ids" id=""
                                class="input select_placeholder basic-single-select">
                                <option value="" selected>Select Name</option>
                                @forelse ($estimators as $estimator)
                                    <option value="{{ $estimator->id }}"
                                        {{ old('estimator_id', $opportunity->estimator_id) == $estimator->id ? 'selected' : '' }}>
                                        {{ $estimator->first_name . ' ' . $estimator->last_name }}
                                    </option>
                                @empty
                                @endforelse
                            </select>
                            @if ($errors->has('estimator_id'))
                                <div class="laravel_error">{{ $errors->first('estimator_id') }}</div>
                            @endif
                        </div>
                    </div>
                </div>


            </div>
            <div class="row">
                <div class="col-12 col-md-3">
                    <div class="panel mt-4">
                        <div class="panel_header mb-4">
                            <h2 class="panel_title">Upload Sitemap</h2>
                        </div>
                        <div class="mt-2">
                            <div class="dropzone_library_customize dropzone_library_customize_site dropzone">
                                <div class="dz-preview dz-file-preview"
                                    id="my-dropzone-sitemap"
                                    style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                                    <svg width="26" height="26" style="margin-top: 28px;"
                                        viewBox="0 0 26 26" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17"
                                            stroke="#0074D9" stroke-width="1.43011"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </div>
                            </div>
                            <div id="imageSizeError"></div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-9">
                    <div class="panel mt-4">
                        <div class="panel_header mb-4">
                            <h2 class="panel_title">Document</h2>
                        </div>

                        <div class="mt-2">
                            <div class="dropzone_library_customize dropzone_library_customize_doc dropzone">
                                <div class="dz-preview dz-file-preview dz-processing dz-success dz-complete needsclick"
                                    id="my-dropzone-document"
                                    style="width: 81px !important;
height: 110px !important; text-align: center; cursor: pointer;">
                                    <svg width="26" height="26" style="margin-top: 28px;"
                                        viewBox="0 0 26 26" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17"
                                            stroke="#0074D9" stroke-width="1.43011"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </div>
                            </div>
                            <div id="imageSizeError"></div>
                        </div>


                    </div>
                </div>
            </div>


            <div class="d-flex justify-content-between gap-3 flex-wrap mt-5">
                <a type="button"
                    href="{{ previous_route()->getName() == getRouteAlias() . '.opportunity.index' ? route(getRouteAlias() . '.opportunity.index') : route(getRouteAlias() . '.opportunity.index') }}"
                    class="btn cancel py-3 px-5 ">Cancel</a>

                @if (previous_route()->getName() == getRouteAlias() . '.invoices.index' ||
                        in_array(old('previous_route'), session()->getOldInput()))
                    <input type="hidden" name="previous_route"
                        value="{{ old('previous_route', previous_route()->getName()) }}">
                    <button type="submit" class="btn primaryblue px-5 min-w-174">Next</button>
                @else
                    <button type="submit" class="btn primaryblue">Create Opportunity</button>
                @endif
            </div>

        </form>

        <div class="modal fade propert-modal" id="propertyModal" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="propertyModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg"
                style="max-width: 700px !important">
                <div class="modal-content">
                    <div class="modal-header"
                        style="background-color: white; padding: 0px 0px !important;">
                        <h1 class="modal-title fs-5" id="propertyModalLabe"><b>Select Property</b>
                            <p style="color:#98A2B3; font-size: 14px;">Select any existing property
                                from here.</p>
                        </h1>
                        <button type="button" class="btn-close hidemodelbtn px-3"
                            data-dismiss="modal" aria-label="Close"
                            style="border: none; background-color: transparent">
                            x
                        </button>

                    </div>
                    <div class="modal-body" style="padding-left: 0px;
    padding-right: 0px;">
                        <div class="body-wraped" style="gap: 10px;">
                            <!-- Search Filter -->
                            <div class="search-filter mb-3">
                                <input type="text" id="propertySearch"
                                    style="width: 40%; border-radius: 12px; background: #F9FAFB;"
                                    class="form-control" placeholder="Search...">
                            </div>

                            <!-- Property List -->
                            <div class="table-wrapper" style="max-height: 250px; overflow-y: auto;">
                                <table class="table" style="width:100%; border-radius: 9px">
                                    <thead>
                                        <tr style="border-radius: 8px; background: #F2F4F7;">
                                            <th>{{ __('Name') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($properties as $property)
                                            <tr class ="table_row">
                                                <td class="property-item d-flex"
                                                    style="display: flex; ">
                                                    <input type="radio"
                                                        id="property_{{ $property->id }}"
                                                        name="property_id" style="margin-top: 13px"
                                                        value="{{ $property->id }}">
                                                    <label for="property_{{ $property->id }}"
                                                        style="margin-left: 10px !important; margin-top: 9px;">{{ $property->name }}</label>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-lg py-3"
                            data-dismiss="modal" style="border-radius: 8px">Cancel</button>
                        <button type="button" class="btn btn-primary btn-lg py-3"
                            style="border-radius: 8px" id="addClientBtn">Add Property</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade propert-modal" id="contactModal" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="propertyModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg"
                style="max-width: 700px !important">
                <div class="modal-content">
                    <div class="modal-header"
                        style="background-color: white; padding: 0px 0px !important;">
                        <h1 class="modal-title fs-5" id="propertyModalLabe"><b>Select Contact</b>
                            <p style="color:#98A2B3; font-size: 14px;">Select any existing property
                                from here.</p>
                        </h1>
                        <button type="button" class="btn-close hidemodelbtn px-3"
                            data-dismiss="modal" aria-label="Close"
                            style="border: none; background-color: transparent">
                            x
                        </button>
                    </div>
                    <div class="modal-body" style="padding-left: 0px;
    padding-right: 0px;">
                        <div class="body-wraped" style="gap: 20px;">
                            <!-- Search Filter -->
                            <div class="search-filter mb-3">
                                <input type="text" id="propertySearch"
                                    style="width: 40%; border-radius: 12px; background: #F9FAFB;"
                                    class="form-control" placeholder="Search...">
                            </div>


                            <!-- Property List -->
                            <div class="table-wrapper">
                                <table class="table" style="width:100%; border-radius: 9px">
                                    <thead>
                                        <tr style="border-radius: 8px; background: #F2F4F7;">
                                            <th>{{ __('Name') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="totalContact">

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-lg py-3"
                            data-dismiss="modal" style="border-radius: 8px">Cancel</button>
                        <button type="button" class="btn btn-primary btn-lg py-3"
                            style="border-radius: 8px" id="addContactBtn" disabled="disabled">Insert
                            Contact</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade select-client" id="selectClientModal" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background: #e1f4ff">
                        <h4 class="modal-title px-3" id="addItemModalLabel">
                            <b style="color: #0074d9 !important">Account</b>
                        </h4>
                        <button type="button" class="btn-close hidemodelbtn px-3"
                            data-dismiss="modal" aria-label="Close"
                            style="border: none; background-color: transparent">
                            x
                        </button>
                    </div>
                    <div class="modal-body" style="padding: 13px 20px !important">
                        <form id="companyForm" action="#" method="POST">
                            @csrf
                            <div class="row px-2"
                                style="justify-content: space-around; display: flex">
                                <div class="form-group" style="width: 47%">
                                    <label>Company Name <span class="required_field">*</span></label>
                                    <input type="text" id="company_name_request"
                                        name="company_name" required class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter company name" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="company_name_error"></span>
                                </div>

                                <div class="form-group" style="width: 47%">
                                    <label>Company Email <span class="required_field">*</span></label>
                                    <input type="email" id="company_email_request"
                                        name="company_email" required class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter company email" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="company_email_error"></span>
                                </div>
                            </div>

                            <div class="row px-2"
                                style="justify-content: space-around; display: flex">
                                <div class="form-group" style="width: 47%">
                                    <label>Company Phone <span class="required_field">*</span></label>
                                    <input type="text" id="company_phone_request"
                                        name="company_phone" required class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter company phone" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="company_phone_error"></span>
                                </div>

                                <div class="form-group" style="width: 47%">
                                    <label>Website</label>
                                    <input type="text" id="website_request" name="website"
                                        class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter website" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="website_error"></span>
                                </div>
                            </div>

                            <div class="row px-2"
                                style="justify-content: space-around; display: flex">
                                <div class="form-group" style="width: 97%">
                                    <label>Account Owner <span class="required_field">*</span></label>
                                    <select name="account_owner" id="account_owner_request"
                                        class="form-control" required style="height: 30px !important">
                                        <option value="" selected>Select Account Owner</option>
                                        @foreach ($user as $item)
                                            <option value="{{ $item->id }}">
                                                {{ $item->first_name }} {{ $item->last_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="account_owner_error"></span>
                                </div>
                            </div>

                            <div class="row px-2"
                                style="justify-content: space-around; display: flex">
                                <div class="form-group" style="width: 97%">
                                    <label>Address <span class="required_field">*</span></label>
                                    <input type="text" id="address_request" name="address"
                                        required class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter address" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="address_error"></span>
                                </div>
                            </div>

                            <div class="row px-2"
                                style="justify-content: space-around; display: flex">
                                <div class="form-group" style="width: 30%">
                                    <label>City <span class="required_field">*</span></label>
                                    <input type="text" id="city_request" name="city" required
                                        class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter city" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="city_error"></span>
                                </div>

                                <div class="form-group" style="width: 30%">
                                    <label>State <span class="required_field">*</span></label>
                                    <input type="text" id="state_request" name="state" required
                                        class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter state" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="state_error"></span>
                                </div>

                                <div class="form-group" style="width: 30%">
                                    <label>Zip Code <span class="required_field">*</span></label>
                                    <input type="text" id="zip_request" name="zip" required
                                        class="form-control"
                                        style="height: 30px !important; text-align: left;"
                                        placeholder="Enter zip code" />
                                    <span class="text-danger" style="font-size: 10px; !important;"
                                        id="zip_error"></span>
                                </div>
                            </div>

                            <div class="d-flex">
                                <input type="checkbox" id="sameaddress"><label for="sameaddress"
                                    style="color: #7C8091; margin-top: -4px; margin-left: 8px">Billing
                                    Address Same As Account Address</label>
                            </div>
                            <div class="row" id="hidebillingaddress"
                                style="padding: 13px 20px !important; display: block;">
                                <p><b>Billing Information</b></p>
                                <hr />
                                <div class="row px-2"
                                    style="justify-content: space-around; display: flex">
                                    <div class="form-group" style="width: 97%">
                                        <label>Address <span class="required_field">*</span></label>
                                        <input type="text" id="billing_address_request"
                                            name="billing_address" required class="form-control"
                                            style="height: 30px !important; text-align: left;"
                                            placeholder="Enter billing address" />
                                        <span class="text-danger" style="font-size: 10px; !important;"
                                            id="billing_address_error"></span>
                                    </div>
                                </div>

                                <div class="row px-2"
                                    style="justify-content: space-around; display: flex">
                                    <div class="form-group" style="width: 30%">
                                        <label>City <span class="required_field">*</span></label>
                                        <input type="text" id="billing_city_request"
                                            name="billing_city" required class="form-control"
                                            style="height: 30px !important; text-align: left;"
                                            placeholder="Enter billing city" />
                                        <span class="text-danger" style="font-size: 10px; !important;"
                                            id="billing_city_error"></span>
                                    </div>

                                    <div class="form-group" style="width: 30%">
                                        <label>State <span class="required_field">*</span></label>
                                        <input type="text" id="billing_state_request"
                                            name="billing_state" required class="form-control"
                                            style="height: 30px !important; text-align: left;"
                                            placeholder="Enter billing state" />
                                        <span class="text-danger" style="font-size: 10px; !important;"
                                            id="billing_state_error"></span>
                                    </div>

                                    <div class="form-group" style="width: 30%">
                                        <label>Zip Code <span class="required_field">*</span></label>
                                        <input type="text" id="billing_zip_request"
                                            name="billing_zip" required class="form-control"
                                            style="height: 30px !important; text-align: left;"
                                            placeholder="Enter billing zip code" />
                                        <span class="text-danger" style="font-size: 10px; !important;"
                                            id="billing_zip_error"></span>
                                    </div>
                                </div>

                                <div class="d-flex" style="gap: 17px; justify-content: end">
                                    <button type="button" data-dismiss="modal" aria-label="Close"
                                        class="btn px-5 py-2"
                                        style="color: #0074d9; border: 1px solid #0074d9; background-color: white;">
                                        Cancel
                                    </button>
                                    <button type="button" onclick="addNewItemModelAccount()"
                                        class="btn px-5 py-2" id="saveBtn"
                                        style="background-color: #0074d9; color: white;">
                                        Save
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </section>

    @push('scripts')
        <script>
            // Pass division and service line data to JavaScript
            window.divisionServiceLineConfig = {
                divisions: {
                    @foreach($divisions as $division)
                        {{ $division->id }}: '{{ $division->name }}',
                    @endforeach
                },
                priceModelRules: {
                    // Landscape + Maintenance = Recurring only
                    'Landscape_Maintenance': ['recurring'],
                    // Landscape + Enhancement = T&M, One time job
                    'Landscape_Enhancement': ['t_m', 'Project'],
                    // Default for all other cases (Snow + any service line, and any other divisions)
                    'default': [
                        {value: 't_m', text: 'T&M', enabled: true},
                        {value: 'per_Inch', text: 'Per Inch', enabled: true},
                        {value: 'per_Push', text: 'Per Push', enabled: true},
                        {value: 'fixed_fee', text: 'Fixed fee', enabled: true}
                    ]
                }
            };
        </script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/fileinput.min.js">
        </script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/js/plugins/sortable.min.js">
        </script>
        <script
            src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.0.8/themes/fas/theme.min.js">
        </script>
        <script>
            document.querySelector('textarea').addEventListener('focus', function() {
                this.selectionStart = 0;
                this.selectionEnd = 0;
            });

            const textarea = document.getElementById('addNoteSummer');

            textarea?.addEventListener('click', (e) => {
                // Set caret position to the beginning on click
                textarea.selectionStart = textarea.selectionEnd = 0;
            });

            textarea?.addEventListener('input', (e) => {
                // Ensure typing always happens at the beginning
                const cursorPosition = textarea.selectionStart;
                const text = textarea.value;

                textarea.value = text.substring(cursorPosition) + text.substring(0, cursorPosition);
                textarea.selectionStart = textarea.selectionEnd = 0;
            });
        </script>

        <script>
            const inputs = document.querySelectorAll('input[type="date"], input[type="time"]');

            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.value) {
                        this.classList.remove(
                            'placeholder-shown'); // Remove placeholder style
                    } else {
                        this.classList.add('placeholder-shown'); // Add placeholder style
                    }
                });

                // Initialize placeholder state on page load
                if (!input.value) {
                    input.classList.add('placeholder-shown');
                } else {
                    input.classList.remove('placeholder-shown');
                }
            });
        </script>

        <script>
            $("#multiplefileupload").fileinput({
                'theme': 'fa',
                'uploadUrl': '#',
                showRemove: false,
                showUpload: false,
                showZoom: false,
                showCaption: false,
                browseClass: "btn btn-danger",
                browseLabel: "",
                browseIcon: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 17V18.6C25 20.8402 25 21.9603 24.564 22.816C24.1805 23.5686 23.5686 24.1805 22.816 24.564C21.9603 25 20.8402 25 18.6 25H7.4C5.15979 25 4.03968 25 3.18404 24.564C2.43139 24.1805 1.81947 23.5686 1.43597 22.816C1 21.9603 1 20.8402 1 18.6V17M19.6667 7.66667L13 1M13 1L6.33333 7.66667M13 1V17" stroke="#0074D9" stroke-width="1.43011" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
                overwriteInitial: false,
                // initialPreview: initialPreview,
                initialPreviewAsData: true,
                // initialPreviewConfig: initialPreviewConfig,
                deleteUrl: "/file-delete", // The URL to handle file deletion
                fileActionSettings: {
                    showUpload: false,
                    showZoom: false,
                    removeIcon: "<i class='fa fa-times'></i>",
                },
                deleteUrl: "#", // Set your delete URL here if needed
                deleteExtraData: {
                    _token: "{{ csrf_token() }}" // Include CSRF token for delete
                },

                fileActionSettings: {
                    showUpload: false,
                    showZoom: false,
                    removeIcon: "<i class='fa fa-times'></i>",
                },

                filetypeSettings: {
                    'pdf': function(vType, vName) {
                        return vType === 'application/pdf' || vName.match(/\.(pdf)$/i);
                    },
                    'doc': function(vType, vName) {
                        return vType === 'application/msword' || vName.match(/\.(doc|docx)$/i);
                    },
                    'excel': function(vType, vName) {
                        return vType === 'application/vnd.ms-excel' || vType ===
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                            vName.match(/\.(xls|xlsx|csv)$/i);
                    }
                },

                previewFileIconSettings: {
                    'pdf': '<img src="{{ asset('admin_assets/images/pdf.png') }}" alt="PDF File" style="width:100%; height:100%; margin-top: 27px;">',
                    'doc': '<img src="{{ asset('admin_assets/images/doc.png') }}" alt="Word File" style="width:100%; height:100%; margin-top: 27px;">',
                    'excel': '<img src="{{ asset('admin_assets/images/xlsx.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',
                    'other': '<img src="{{ asset('admin_assets/images/file.png') }}" alt="Excel File" style="width:100%; height:100%; margin-top: 27px;">',

                },

                layoutTemplates: {
                    footer: '<div class="file-thumbnail-footer-section">\n' +
                        '<div class="file-caption-name" style="width:100%; margin-top: 68px; color: white;" title="{caption}">' +
                        '{caption}' +
                        '</div>\n' +
                        '</div>',
                },
                previewFileExtSettings: {
                    'pdf': function(ext) {
                        return ext.match(/(pdf)$/i);
                    },
                    'doc': function(ext) {
                        return ext.match(/(doc|docx)$/i);
                    },
                    'excel': function(ext) {
                        return ext.match(/(xls|xlsx|csv)$/i);
                    }
                }
            }).on('filedeleted', function(event, key, jqXHR, data) {
                console.log('File Deleted: ' + key);
            });
        </script>

        <script>
            function addNewItemModelAccount() {
                // Clear previous error messages
                $('.error-message').text(''); // Clear text of existing error messages

                // Collect form data
                var company_name = $('#company_name_request').val();
                var company_email = $('#company_email_request').val();
                var company_phone = $('#company_phone_request').val();
                var website = $('#website_request').val();
                var address = $('#address_request').val();
                var city = $('#city_request').val();
                var state = $('#state_request').val();
                var zip = $('#zip_request').val();
                var billing_address = $('#billing_address_request').val();
                var billing_city = $('#billing_city_request').val();
                var billing_state = $('#billing_state_request').val();
                var billing_zip = $('#billing_zip_request').val();
                var account_owner = $('#account_owner_request').val();
                var url = "{{ route('organization.save-account-details') }}";

                // AJAX request
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        _token: $('input[name="_token"]').val(), // Laravel CSRF token
                        company_name: company_name,
                        company_email: company_email,
                        company_phone: company_phone,
                        website: website,
                        address: address,
                        city: city,
                        state: state,
                        zip: zip,
                        billing_address: billing_address,
                        billing_city: billing_city,
                        billing_state: billing_state,
                        billing_zip: billing_zip,
                        account_owner: account_owner,
                    },
                    success: function(response) {

                        var company_id = response.company.id;
                        var company_name = response.company.company_name;

                        $('#company_id_account').append(
                            $('<option>', {
                                value: company_id,
                                text: company_name,
                                selected: true
                            })
                        );

                        // Hide the modal or reset the form
                        $('#selectClientModal').modal('hide');
                        $('#companyForm')[0].reset();
                    },
                    error: function(xhr) {
                        // Handle error
                        var errors = xhr.responseJSON.errors;
                        $.each(errors, function(key, value) {
                            $('#' + key + '_error').text(value[0]);
                        });
                    }
                });
            }
        </script>

        <script>

            $('#addContactBtn').on('click', function() {
                var contactId = $('input[name="contact_selected_id"]:checked').val();

                if (!contactId) {
                    $('#contactModal').modal('hide');
                    toastr.warning('Please select a contact first.');
                    return;
                }
                // alert(contactId);

                var url =
                    "{{ route('organization.get-contact-data-details', ['contact_id' => ':id']) }}";
                url = url.replace(':id', contactId);

                $.ajax({
                    method: "GET",
                    url: url,
                    success: function(response) {

                        $('input[name="first_name"]').val(response.fname).prop(
                            'readonly', true);
                        $('input[name="last_name"]').val(response.lname).prop(
                            'readonly', true);
                        $('input[name="title_role"]').val(response.title).prop(
                            'readonly', true);
                        $('input[name="phone_number"]').val(response.phone_number).prop(
                            'readonly', true);
                        $('input[name="second_phone"]').val(response.second_phone).prop(
                            'readonly', true);
                        $('input[name="email"]').val(response.email).prop('readonly',
                            true);
                        $('input[name="second_email"]').val(response.second_email).prop(
                            'readonly', true);
                        $('input[name="mailing_address"]').val(response.mailing_address)
                            .prop('readonly', true);
                        $('input[name="contact_ids"]').val(response.id);


                        // Set the value for the select field and disable it
                        $('select[name="role"]').val(response.role).prop('disabled',
                            true).trigger('change');

                        // alert(response.company_id);

                        $('#contactModal').modal('hide');
                        toastr.success('Contact details successfully loaded.');
                    },
                    error: function(response) {
                        $('#contactModal').modal('hide');
                        toastr.warning('Failed to fetch contact details.');
                    }
                });
            });

            $(document).on('change', '#totalContact input[name="contact_selected_id"]', function(event) {
                if ($(this).is(':checked')) {
                    // Enable something when checked
                    $('#addContactBtn').prop('disabled', false);
                } else {
                    // Disable it when unchecked
                    $('#addContactBtn').prop('disabled', true);
                }
            })
        </script>
        <script>
            function clearContactFields() {
                $('input[name="first_name"]').val('').prop('readonly', false);
                $('input[name="last_name"]').val('').prop('readonly', false);
                $('input[name="title_role"]').val('').prop('readonly', false);
                $('input[name="phone_number"]').val('').prop('readonly', false);
                $('input[name="second_phone"]').val('').prop('readonly', false);
                $('input[name="email"]').val('').prop('readonly', false);
                $('input[name="second_email"]').val('').prop('readonly', false);
                $('input[name="mailing_address"]').val('').prop('readonly', false);
                $('input[name="contact_ids"]').val('');
            }
        </script>
        <script>
            function getContact() {
                var acc_id = $('.compnyids').val();
                // alert(acc_id);
                var url = "{{ route('organization.get-contact-details', ['contact_id' => ':id']) }}";
                url = url.replace(':id', acc_id);

                $.ajax({
                    method: "GET",
                    url: url,
                    success: function(response2) {
                        console.info(response2);
                        var response = response2.contact; // Assuming this contains contact data
                        var userss = response2.users;

                        // Initialize select2 with templateResult and templateSelection functions
                        $('#opportunity_owner_id').select2({
                            templateResult: function(option) {
                                if (!option.id) {
                                    return option
                                        .text; // Return the default text for empty options
                                }
                                // Check if the option is the account owner (matches the user's ID)
                                if (option.id == userss.id) {
                                    // Add the "account owner" label
                                    return $('<span class="acont_own_whole">' +
                                        option.text +
                                        '<span class="acont_own"> Account owner</span></span>'
                                    );
                                }
                                return option
                                    .text; // Default text for other options
                            },
                            templateSelection: function(option) {
                                // For the selected option, if it's the account owner, append the label
                                if (option.id == userss.id) {
                                    return $('<span class="acont_own_whole">' +
                                        option.text +
                                        '<span class="acont_own">Account owner</span></span>'
                                    );
                                }
                                return option
                                    .text; // Default text for other options
                            }
                        });

                        // CSS to style the account owner label
                        var acont_own = 'acont_own';
                        $('<style>').prop('type', 'text/css').html(
                            '.acont_own { color: #A8ABB5 !important; font-size: 12px !important; margin-left: 5px !important;} .acont_own_whole:hover{width: 100% !important; background: #5897fb !important; color: white !important;"} .select2-results__option {background-color: #5897fb !important; color: white !important;}'
                        ).appendTo('head');

                        // Trigger the change event to refresh select2 if the data changed
                        $('#opportunity_owner_id').trigger('change');




                        var dr = ``;
                        $.each(response, function(index, item) {
                            dr += `<tr>
                                <td class="property-item d-flex">
                                    <input type="radio" style="margin-top: 13px" id="contact_${item.id}" name="contact_selected_id" value="${item.id}">
                                    <label style="margin-left: 10px !important; margin-top: 9px;" for="contact_${item.id}">${item.first_name} ${item.last_name}</label>
                                </td>
                            </tr>`;
                        });
                        $('#totalContact').html(dr);
                    },
                    error: function(response) {
                        // $('#contactModal').modal('hide');
                        toastr.warning('Failed to fetch contact details.');
                    }
                });
            }
        </script>
        <script>
            $(document).ready(function() {
                $('#propertySearch').on('input', function() {
                    var searchValue = $(this).val().toLowerCase();
                    var anyVisible = false;

                    $('.table_row').each(function () {
                        var propertyName = $(this).find('label').text().toLowerCase().trim();
                    if (propertyName.includes(searchValue)) {
                            $(this).show();
                            anyVisible = true;
                    } else {
                        $(this).hide();
                    }
        $('#noResultsRow').remove();

        if (!anyVisible) {
            $('tbody').append(
                '<tr id="noResultsRow"><td colspan="1" class="text-center text-muted">No results found</td></tr>'
            );
        }
    });
});
                $('#addClientBtn').on('click', function() {
                    var propertyId = $('input[name="property_id"]:checked').val();

                    if (!propertyId) {
                        $('#propertyModal').modal('hide');
                        toastr.warning('Please select a property first.');
                        return;
                    }
                    // alert(propertyId);

                    var url =
                        "{{ route('organization.get-property-details', ['property_id' => ':id']) }}";
                    url = url.replace(':id', propertyId);

                    $.ajax({
                        method: "GET",
                        url: url,
                        success: function(response) {
                            var response2 = response.default;
                            console.info(response2);
                            // Set the values and disable input fields
                            $('input[name="property_name"]').val(response.name);
                            $('input[name="address1"]').val(response.address1);
                            $('input[name="address2"]').val(response.address2);
                            $('input[name="city"]').val(response.city);
                            $('input[name="state"]').val(response.state);
                            $('input[name="zip_code"]').val(response.zip_code);

                            // Set the value for the select field and disable it
                            $('select[name="company_id"]').val(response
                                .company_id).trigger('change');

                            // alert(response.company_id);
                            if (response2 != null) {


                                $('input[name="first_name"]').val(response2
                                    .first_name).prop('readonly', true);
                                $('input[name="last_name"]').val(response2
                                    .last_name).prop('readonly', true);
                                $('input[name="title_role"]').val(response2
                                    .title).prop('readonly', true);
                                $('input[name="phone_number"]').val(response2
                                    .phone_number).prop('readonly', true);
                                $('input[name="second_phone"]').val(response2
                                    .second_phone).prop('readonly', true);
                                $('input[name="email"]').val(response2.email)
                                    .prop('readonly', true);
                                $('input[name="second_email"]').val(response2
                                    .second_email).prop('readonly', true);
                                $('input[name="mailing_address"]').val(response2
                                    .mailing_address).prop('readonly', true);
                                $('input[name="contact_ids"]').val(response2
                                    .id);


                                // Set the value for the select field and disable it
                                $('select[name="role"]').val(response2.role)
                                    .prop('disabled', true).trigger('change');

                            }

                            $('#propertyModal').modal('hide');
                            toastr.success(
                                'Property details successfully loaded.');
                        },
                        error: function(response) {
                            $('#propertyModal').modal('hide');
                            toastr.warning('Failed to fetch property details.');
                        }
                    });
                });


                $('#addDivision').click(function() {
                    var div = $('#division').val();
                    var serv = $('#service_line').val();

                    // Validate the first dropdowns
                    if (div == null || serv == null) {
                        alert('Please select all of the above first');
                        return; // Exit the function if validation fails
                    }

                    // Check if any of the existing divisions and service lines are unselected
                    var allValid = true;
                    $('.division-select').each(function() {
                        if ($(this).val() == null || $(this).val() == "") {
                            allValid = false;
                        }
                    });

                    $('.service-line-select').each(function() {
                        if ($(this).val() == null || $(this).val() == "") {
                            allValid = false;
                        }
                    });

                    if (!allValid) {
                        alert(
                            'Please fill all previous divisions and service lines first.'
                            );
                        return; // Exit the function if validation fails
                    }

                    // If validation passes, append a new division section
                    var newDivision = `
                                    <div class="division-section mt-4">
                                        <div class="row pl-3">
                                            <div class="" style="width: 49%;">
                                                <label class="label mb-2">Division <span class="steric">*</span></label>
                                                <div class="absolute-error">
                                                    <select name="division[]" class="division-select input select_placeholder custom_selectBox basic-single-select">
                                                        <option value="" selected disabled>Select Division</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="" style="width: 48%; margin-left: 1%">
                                                <label class="label mb-2">Service Line <span class="steric">*</span></label>
                                                <div class="absolute-error">
                                                    <select name="service_line[]" class="service-line-select select_placeholder input custom_selectBox basic-single-select">
                                                        <option value="" selected disabled>Select Service Line</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="mt-5"  style="width: 1%;">
                                                <div class="remove-division" style="cursor: pointer">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M18 6L6 18M6 6L18 18" stroke="#FF4B55" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    `;

                    $('#divisionsContainer').append(newDivision);
                });

                // Remove a division section
                $('#divisionsContainer').on('click', '.remove-division', function() {
                    $(this).closest('.division-section').remove();
                });
            });

            function handleFileUpload(input) {
                const files = input.files;
                const uploadBoxesContainer = document.getElementById('upload-boxes');
                Array.from(files).forEach(file => {
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewBox = document.createElement('div');
                            previewBox.classList.add('upload-box');
                            previewBox.innerHTML = `
                    <img src="${e.target.result}" alt="Uploaded File" class="file-preview">
                    `;
                            uploadBoxesContainer.appendChild(previewBox);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        </script>

        <script>
            $(document).ready(function() {
                $('.dropzone_library_customizess .dz-message').html(
                    // '<div class="drop-zone__prompt text-center"><svg width="34" height="34" viewBox="0 0 34 34" fill="none"xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd"d="M9.36275 2.85175C11.4866 1.02029 14.1955 0.00879434 17 0C22.7162 0 27.4614 4.25 27.9778 9.73038C31.3608 10.2085 34 13.0411 34 16.5176C34 20.3341 30.8168 23.375 26.9599 23.375H21.25C20.9682 23.375 20.698 23.2631 20.4987 23.0638C20.2994 22.8645 20.1875 22.5943 20.1875 22.3125C20.1875 22.0307 20.2994 21.7605 20.4987 21.5612C20.698 21.3619 20.9682 21.25 21.25 21.25H26.962C29.7054 21.25 31.875 19.0995 31.875 16.5176C31.875 13.9336 29.7075 11.7831 26.9599 11.7831H25.8974V10.7206C25.8995 6.00313 21.947 2.125 17 2.125C14.7047 2.13417 12.4883 2.96317 10.7504 4.4625C9.14175 5.848 8.30025 7.51825 8.30025 8.82938V9.78137L7.35462 9.8855C4.386 10.2106 2.125 12.648 2.125 15.5507C2.125 18.6681 4.73875 21.25 8.03463 21.25H12.75C13.0318 21.25 13.302 21.3619 13.5013 21.5612C13.7006 21.7605 13.8125 22.0307 13.8125 22.3125C13.8125 22.5943 13.7006 22.8645 13.5013 23.0638C13.302 23.2631 13.0318 23.375 12.75 23.375H8.03463C3.6295 23.375 0 19.9028 0 15.5507C0 11.8044 2.69025 8.70187 6.25175 7.91562C6.55562 6.08175 7.735 4.25425 9.36275 2.85175Z"fill="#90A0B7" /><path fill-rule="evenodd" clip-rule="evenodd"d="M16.2471 8.8102C16.3458 8.71125 16.4631 8.63275 16.5922 8.57918C16.7213 8.52562 16.8596 8.49805 16.9994 8.49805C17.1391 8.49805 17.2775 8.52562 17.4066 8.57918C17.5357 8.63275 17.6529 8.71125 17.7516 8.8102L24.1266 15.1852C24.3261 15.3847 24.4382 15.6553 24.4382 15.9374C24.4382 16.2196 24.3261 16.4902 24.1266 16.6897C23.9271 16.8892 23.6565 17.0013 23.3744 17.0013C23.0922 17.0013 22.8216 16.8892 22.6221 16.6897L18.0619 12.1273V30.8124C18.0619 31.0942 17.9499 31.3645 17.7507 31.5637C17.5514 31.763 17.2812 31.8749 16.9994 31.8749C16.7176 31.8749 16.4473 31.763 16.2481 31.5637C16.0488 31.3645 15.9369 31.0942 15.9369 30.8124V12.1273L11.3766 16.6897C11.1771 16.8892 10.9065 17.0013 10.6244 17.0013C10.3422 17.0013 10.0716 16.8892 9.87214 16.6897C9.67263 16.4902 9.56055 16.2196 9.56055 15.9374C9.56055 15.6553 9.67263 15.3847 9.87214 15.1852L16.2471 8.8102Z"fill="#90A0B7" /></svg><p class="placeholder-text font-14">Drop your logo here, or <span>browse</span></p><p class="placeholder-text font-14">PNG, JPEG, PDF , XlS, XLSX Max size: 5MB</p></div>'
                )
            })

            $(document).on('click', '#my-dropzone-sitemap', function() {
                $('#imageSizeError').html('');

            });

             $(document).on('click', '#my-dropzone-document', function() {
                $('#imageSizeError').html('');

            });

            Dropzone.autoDiscover = false; // Disable auto-discovery


            var uploadedDocumentMap = {};
            // Initialize Dropzone for the Sitemap
            const sitemapDropzone = new Dropzone("#my-dropzone-sitemap", {
                url: "{{ route(getRouteAlias() . '.opportunity.file-store') }}", // Replace with your server upload URL
                addRemoveLinks: true,
                dictRemoveFile: "Remove", // Proper text for remove button
                previewTemplate: `
                                    <div class="dz-preview dz-file-preview">

                                    <img class="dz-details-imagess" style="height: 60px;" src="" />
                                        <div class="dz-details">

                                            <div class="dz-icon"></div>
                                            <div class="dz-filename"><span data-dz-name></span></div>
                                        </div>
                                        <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
                                        <div class="dz-error-message"><span data-dz-errormessage></span></div>

                                    </div>
                                `,
                previewsContainer: ".dropzone_library_customize_site",
                acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content') // Include CSRF token in the headers
                },
                init: function() {


                    this.on("success", function(file, response) {
                        // Assuming your API response contains a key 'filePath'
                        $('form').append(
                            '<input type="hidden" name="images_opportunity[]" value="' +
                            response.name + '">');
                        uploadedDocumentMap[file.name] = response.name;

                    });
                    this.on("addedfile", function(file) {
                        // Extract file extension
                        let ext = file.name.split('.').pop().toLowerCase();
                        let iconSrc = "default-icon.png";

                        // Icons for specific file types
                        if (ext === "pdf") {
                            iconSrc =
                                "data:image/svg+xml;base64,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";
                        } else if (ext === "xls" || ext === "xlsx") {
                            iconSrc =
                                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
                        } else if (ext === "doc" || ext === "docx") {
                            iconSrc =
                                "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
                        } else if (ext === "png" || ext === "jpg" || ext === "webp" ||
                            ext === "jpeg" || ext === "jpeg") {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                $(file.previewElement).find(".dz-details-imagess")
                                    .css("width", '100%');
                                // Find the preview image element and set the source
                                $(file.previewElement).find(".dz-details-imagess")
                                    .attr("src", e.target.result);
                            };
                            reader.readAsDataURL(file);
                        }
                        $(file.previewElement).find(".dz-details-imagess").css("width",
                            '100%');
                        // Update the preview element with the corresponding icon
                        if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext ===
                            "xls" || ext === "pdf") {
                            $(file.previewElement).find(".dz-details-imagess").attr(
                                "src", iconSrc);
                        }
                    });

                    this.on("removedfile", function(file) {
                        console.log("File removed: ", file.name); // Log file removal

                        file.previewElement.remove();
                        var name = '';
                        if (typeof file.file_name !== 'undefined') {
                            name = file.file_name;
                        } else {
                            name = uploadedDocumentMap[file.name];
                        }

                        // Send an AJAX request to delete the file from the server
                        fetch("{{ route(getRouteAlias() . '.opportunity.file-delete') }}", {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify({
                                    filename: file.name
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                console.info('File deleted from server:', data);
                                // You can add additional logic here to update UI or handle response data
                            })
                            .catch(error => {
                                console.error('Error deleting file from server:',
                                    error);
                            });
                        $('form').find('input[name="images_opportunity[]"][value="' +
                            name + '"]').remove();
                    });

                    // Add error handling if needed
                    this.on("error", function(file, errorMessage) {
                        console.error("Dropzone error:", errorMessage);
                    });
                }
            });

            // Initialize Dropzone for the Document
            const documentDropzone = new Dropzone("#my-dropzone-document", {
                url: "{{ route(getRouteAlias() . '.opportunity.file-store') }}", // Replace with your server upload URL
                addRemoveLinks: true,
                dictRemoveFile: "Remove", // Proper text for remove button
                previewTemplate: `
                                    <div class="dz-preview dz-file-preview">

                                    <img class="dz-details-imagess" style="height: 60px;" src="" />
                                        <div class="dz-details">

                                            <div class="dz-icon"></div>
                                            <div class="dz-filename"><span data-dz-name></span></div>
                                        </div>
                                        <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
                                        <div class="dz-error-message"><span data-dz-errormessage></span></div>

                                    </div>
                                `,
                previewsContainer: ".dropzone_library_customize_doc",
                acceptedFiles: ".webp,.jpeg,.jpg,.png,.gif,.pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content') // Include CSRF token in the headers
                },
                init: function() {


                    this.on("success", function(file, response) {
                        // Assuming your API response contains a key 'filePath'
                        $('form').append(
                            '<input type="hidden" name="images_opportunity[]" value="' +
                            response.name + '">');
                        uploadedDocumentMap[file.name] = response.name;

                    });
                    this.on("addedfile", function(file) {
                        // Extract file extension
                        let ext = file.name.split('.').pop().toLowerCase();
                        let iconSrc = "default-icon.png";

                        // Icons for specific file types
                        if (ext === "pdf") {
                            iconSrc =
                                "data:image/svg+xml;base64,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";
                        } else if (ext === "xls" || ext === "xlsx") {
                            iconSrc =
                                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjQnIGhlaWdodD0nMjQnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMi44NTkwMiAyLjg3ODIyTDE1LjQyOSAxLjA4MjIyQzE1LjUgMS4wNzIwNCAxNS41NzIzIDEuMDc3MjMgMTUuNjQxIDEuMDk3NDRDMTUuNzA5OCAxLjExNzY1IDE1Ljc3MzQgMS4xNTI0IDE1LjgyNzUgMS4xOTkzNUMxNS44ODE3IDEuMjQ2MjkgMTUuOTI1MSAxLjMwNDMzIDE1Ljk1NDkgMS4zNjk1MkMxNS45ODQ2IDEuNDM0NzIgMTYgMS41MDU1NSAxNiAxLjU3NzIyVjIyLjQyNDJDMTYgMjIuNDk1OCAxNS45ODQ2IDIyLjU2NjUgMTUuOTU0OSAyMi42MzE2QzE1LjkyNTIgMjIuNjk2NyAxNS44ODE5IDIyLjc1NDcgMTUuODI3OSAyMi44MDE3QzE1Ljc3MzggMjIuODQ4NiAxNS43MTAzIDIyLjg4MzQgMTUuNjQxNyAyMi45MDM2QzE1LjU3MzEgMjIuOTIzOSAxNS41MDA5IDIyLjkyOTIgMTUuNDMgMjIuOTE5MkwyLjg1ODAyIDIxLjEyMzJDMi42MTk2NCAyMS4wODkzIDIuNDAxNTIgMjAuOTcwNCAyLjI0MzcxIDIwLjc4ODZDMi4wODU5MSAyMC42MDY3IDEuOTk5MDMgMjAuMzc0IDEuOTk5MDIgMjAuMTMzMlYzLjg2ODIyQzEuOTk5MDMgMy42Mjc0MyAyLjA4NTkxIDMuMzk0NzMgMi4yNDM3MSAzLjIxMjg2QzIuNDAxNTIgMy4wMzA5OSAyLjYyMDY0IDIuOTEyMTcgMi44NTkwMiAyLjg3ODIyWk0xNyAzLjAwMDIySDIxQzIxLjI2NTIgMy4wMDAyMiAyMS41MTk2IDMuMTA1NTcgMjEuNzA3MSAzLjI5MzExQzIxLjg5NDcgMy40ODA2NCAyMiAzLjczNSAyMiA0LjAwMDIyVjIwLjAwMDJDMjIgMjAuMjY1NCAyMS44OTQ3IDIwLjUxOTggMjEuNzA3MSAyMC43MDczQzIxLjUxOTYgMjAuODk0OSAyMS4yNjUyIDIxLjAwMDIgMjEgMjEuMDAwMkgxN1YzLjAwMDIyWk0xMC4yIDEyLjAwMDJMMTMgOC4wMDAyMkgxMC42TDkuMDAwMDIgMTAuMjg2Mkw3LjQwMDAyIDguMDAwMjJINS4wMDAwMkw3LjgwMDAyIDEyLjAwMDJMNS4wMDAwMiAxNi4wMDAySDcuNDAwMDJMOS4wMDAwMiAxMy43MTQyTDEwLjYgMTYuMDAwMkgxM0wxMC4yIDEyLjAwMDJaJyBmaWxsPSdibGFjaycvPjwvc3ZnPgo=";
                        } else if (ext === "doc" || ext === "docx") {
                            iconSrc =
                                "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgaWQ9IndvcmQiPjxwYXRoIGQ9Ik0yMy41IDIuNUgxNFYxYS41LjUgMCAwIDAtLjYxMi0uNDg3bC0xMyAzQS41LjUgMCAwIDAgMCA0djE3YS41LjUgMCAwIDAgLjQyNC40OTRsMTMgMmEuNDk2LjQ5NiAwIDAgMCAuNDAxLS4xMTVBLjQ5NC40OTQgMCAwIDAgMTQgMjN2LTEuNWg5LjVhLjUuNSAwIDAgMCAuNS0uNVYzYS41LjUgMCAwIDAtLjUtLjV6TTEwLjk4MSA5LjEzOGwtMiA3Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMEw2Ljk4IDEyLjUwM2MtLjA2LS4yMTUtLjI1Ni0uMzYzLS40OC0uMzYzcy0uNDIuMTQ4LS40OC4zNjNsLTEuMDM4IDMuNjM0Yy0uMTIzLjQyNy0uODQuNDI2LS45NjMgMGwtMi03YS41LjUgMCAwIDEgLjk2Mi0uMjc0bDEuMDM4IDMuNjM1Yy4xMjEuNDMuODQuNDMuOTYxIDBsMS4wMzgtMy42MzRjLjEyMy0uNDI3Ljg0LS40MjYuOTYzIDBsMS4wMzggMy42MzVjLjEyMS40My44NC40My45NjEgMGwxLjAzOC0zLjYzNGEuNS41IDAgMCAxIC45NjMuMjczek0yMyAyMC41aC05di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoNy41YS41LjUgMCAwIDAgMC0xSDE0di0yaDcuNWEuNS41IDAgMCAwIDAtMUgxNHYtMmg3LjVhLjUuNSAwIDAgMCAwLTFIMTR2LTJoOXYxN3oiPjwvcGF0aD48L3N2Zz4=";
                        } else if (ext === "png" || ext === "jpg" || ext === "webp" ||
                            ext === "jpeg" || ext === "jpeg") {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                $(file.previewElement).find(".dz-details-imagess")
                                    .css("width", '100%');
                                // Find the preview image element and set the source
                                $(file.previewElement).find(".dz-details-imagess")
                                    .attr("src", e.target.result);
                            };
                            reader.readAsDataURL(file);
                        }
                        $(file.previewElement).find(".dz-details-imagess").css("width",
                            '100%');
                        // Update the preview element with the corresponding icon
                        if (ext === "docx" || ext === "doc" || ext === "xlsx" || ext ===
                            "xls" || ext === "pdf") {
                            $(file.previewElement).find(".dz-details-imagess").attr(
                                "src", iconSrc);
                        }
                    });

                    this.on("removedfile", function(file) {
                        console.log("File removed: ", file.name); // Log file removal

                        file.previewElement.remove();
                        var name = '';
                        if (typeof file.file_name !== 'undefined') {
                            name = file.file_name;
                        } else {
                            name = uploadedDocumentMap[file.name];
                        }

                        // Send an AJAX request to delete the file from the server
                        fetch("{{ route(getRouteAlias() . '.opportunity.file-delete') }}", {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify({
                                    filename: file.name
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                console.info('File deleted from server:', data);
                                // You can add additional logic here to update UI or handle response data
                            })
                            .catch(error => {
                                console.error('Error deleting file from server:',
                                    error);
                            });
                        $('form').find('input[name="images_opportunity[]"][value="' +
                            name + '"]').remove();
                    });

                    // Add error handling if needed
                    this.on("error", function(file, errorMessage) {
                        console.error("Dropzone error:", errorMessage);
                    });
                }
            });

            // Function to open the popup and display the file content
            function openPreview(file) {
                var fileUrl = file.dataURL;
                var content;
                if (file.hasOwnProperty('original_url')) {
                    if (file.extension === "pdf") {
                        content =
                            `<object data="${file.original_url}" type="application/pdf" width="100%" height="500"></object>`;
                        $("#filePreviewModal .modal-dialog").addClass('pdf-preview');
                    } else if (['webp', 'jpg', 'jpeg', 'png', 'gif'].includes(file.extension)) {
                        content =
                            `<img src="${file.original_url}" alt="Preview" style="max-width: 100%; max-height: 500px;">`;
                        $("#filePreviewModal .modal-dialog").removeClass('pdf-preview');
                    }
                } else {
                    if (file.type === "application/pdf") {
                        var fileUrl = URL.createObjectURL(file);
                        // Customize the popup content based on file type
                        content =
                            `<iframe style="width:100%;min-height:72vh" src="${fileUrl}" frameborder="0"></iframe>`;
                        $("#filePreviewModal .modal-dialog").addClass('pdf-preview');
                    } else if (file.type.startsWith("image/")) {
                        content =
                            `<img src="${fileUrl}" alt="Preview" style="max-width: 100%; max-height: 500px;">`;
                        $("#filePreviewModal .modal-dialog").removeClass('pdf-preview');
                    }
                }

                // Update the popup content and show the popup

                $("#fileContent").html(content);
                $("#filePreviewModal").modal('show');
            }

            // Close the popup when clicking outside the modal content
            $(document).click(function(event) {
                if ($(event.target).is("#filePreviewModal")) {
                    $("#filePreviewModal").modal('hide');
                }
            });
            Dropzone.prototype.downloadFile = function(file) {
                var downloadUrl = null;
                if (file.hasOwnProperty('original_url')) {
                    downloadUrl = file.original_url;
                } else {
                    downloadUrl = URL.createObjectURL(file);
                }
                // Create a temporary anchor element and trigger a click event to download the file
                var link = document.createElement("a");
                link.href = downloadUrl;
                link.download = file.name;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();

                // After the download is triggered, revoke the Object URL to free resources
                URL.revokeObjectURL(downloadUrl);

                document.body.removeChild(link);
            };
        </script>
        <script>
            $(document).ready(function() {

                // Handle main division change
                $('#division').on('change', function() {
                    var divisionId = $(this).val();
                    var divisionText = $('#division option[value="' + divisionId + '"]').text();

                    console.log('Division changed:', divisionId, divisionText);

                    // Clear service line and price model when division changes
                    $('#service_line').empty().append('<option value="" disabled selected>Select Service Line</option>');
                    $('#price-model').empty().append('<option value="" selected>Select Type</option>');

                    // Fetch service lines for the selected division via AJAX
                    if (divisionId) {
                        var url = "{{ route('organization.get-service-lines', ['division_id' => ':id']) }}";
                        url = url.replace(':id', divisionId);

                        $.ajax({
                            method: "GET",
                            url: url,
                            success: function(response) {
                                $('#service_line').empty();
                                $('#service_line').append('<option value="" disabled>Select Service Line</option>');

                                if (response && response.length > 0) {
                                    $.each(response, function(key, serviceLine) {
                                        $('#service_line').append('<option value="' + serviceLine.id + '">' + serviceLine.name + '</option>');
                                    });

                                    // Auto-select the first service line
                                    var firstServiceLineId = response[0].id;
                                    $('#service_line').val(firstServiceLineId);

                                    // Trigger the change event to update price model
                                    $('#service_line').trigger('change');
                                }
                            },
                            error: function(response) {
                                toastr.warning('Failed to fetch service lines.');
                            }
                        });
                    }
                });

                // Handle main service line change
                $('#service_line').on('change', function() {
                    var serviceLineId = $(this).val();
                    var divisionId = $('#division').val();

                    console.log('Service line changed:', serviceLineId);
                    console.log('Current division:', divisionId);

                    // Update price model based on current division and service line IDs
                    if (window.updatePriceModel) {
                        window.updatePriceModel(divisionId, serviceLineId);
                    }
                });

                // Handle dynamically added division selects
                $(document).on('change', '.division-select', function(e) {
                    var divisionId = $(this).val();
                    var $serviceLineSelect = $(this).closest('.division-section').find(
                        '.service-line-select');

                    var url =
                        "{{ route('organization.get-service-lines', ['division_id' => ':id']) }}";
                    url = url.replace(':id', divisionId);

                    $.ajax({
                        method: "GET",
                        url: url,
                        success: function(response) {
                            $serviceLineSelect.empty();
                            $serviceLineSelect.append(
                                '<option value="" disabled>Select Service Line</option>'
                            );

                            if (response && response.length > 0) {
                                $.each(response, function(key, serviceLine) {
                                    $serviceLineSelect.append(
                                        '<option value="' + serviceLine
                                        .id + '">' + serviceLine.name +
                                        '</option>');
                                });

                                // Auto-select the first service line for dynamic sections too
                                var firstServiceLineId = response[0].id;
                                $serviceLineSelect.val(firstServiceLineId);
                            }
                        },
                        error: function(response) {
                            toastr.warning('Failed to fetch service lines.');
                        }
                    });
                });

                document.getElementById('sameaddress').addEventListener('change', function() {
                    const accountAddress = document.getElementById('address_request')
                        ?.value;
                    const account_city = document.getElementById('city_request')?.value;
                    const account_state = document.getElementById('state_request')?.value;
                    const account_zip = document.getElementById('zip_request')?.value;
                    const billingAddressField = document.getElementById(
                        'billing_address_request');
                    const same_billing_city = document.getElementById(
                        'billing_city_request');
                    const same_billing_state = document.getElementById(
                        'billing_state_request');
                    const same_billing_zip = document.getElementById('billing_zip_request');
                    const hidebillingaddress = document.getElementById(
                        'hidebillingaddress');

                    if (this.checked) {
                        // If checkbox is checked, copy the account address to billing address
                        billingAddressField.value = accountAddress ?? '';
                        same_billing_city.value = account_city ?? '';
                        same_billing_state.value = account_state ?? '';
                        same_billing_zip.value = account_zip ?? '';
                        hidebillingaddress.style.display = 'none';

                    } else {
                        hidebillingaddress.style.display = 'block';

                        billingAddressField.value = '';
                        same_billing_city.value = '';
                        same_billing_state.value = '';
                        same_billing_zip.value = '';
                    }
                });
            });
        </script>
        <script src="{{ asset('asset/assets/js/organization/opportunity/create-opportunity.js') }}"></script>
    @endpush
@endsection
