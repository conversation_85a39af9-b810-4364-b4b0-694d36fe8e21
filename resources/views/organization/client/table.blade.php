<x-jet.listing-table>
    <x-slot name="header">
        <th>{{ __('First Name') }}</th>
        <th>{{ __('Last Name') }}</th>
        <th>{{ __('Company') }}</th>
        <th>{{ __('Email') }}</th>
        <th>{{ __('Phone Number') }}</th>
        <th>{{ __('Action') }}</th>

    </x-slot>
    <x-slot name="body">
        @forelse($clients as $client)
            <tr>
                <td>{{ $client->first_name }}</td>
                <td>{{ $client->last_name }}
                </td>
                <td>{{ $client->company_name }}</td>
                <td>{{ $client->email }}</td>
                <td>{{ $client->mobile_no }}</td>
                <td>
                    <div class="dropdown mx-auto w-fit">
                        <div id="dropdown{{ $client->id }}" data-toggle="dropdown" aria-expanded="false">
                            <img height="24px" width="24px"
                                src="{{ asset('admin_assets/images/icons/vertical-dots.svg') }}" alt="vertical dots">
                        </div>
                        <ul class="dropdown-menu" aria-labelledby="dropdown{{ $client->id }}">
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.create.estimate', encodeId($client->id)) }}">Create
                                    Estimate Request</a></li>
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.client.detail', encodeId($client->id)) }}">View
                                    Details</a>
                            </li>
                            <li><a class="dropdown-item"
                                    href="{{ route(getRouteAlias() . '.client.edit', encodeId($client->id)) }}">Edit
                                    Details</a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        @empty
            @include('layouts.partials.no-data')
        @endforelse
    </x-slot>
    <x-slot name="pagination">
        @if (isset($clients))
            {{ $clients->links() }}
        @endif
    </x-slot>
</x-jet.listing-table>
