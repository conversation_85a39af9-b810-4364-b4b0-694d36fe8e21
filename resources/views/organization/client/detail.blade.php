@extends('layouts.admin.master')
@section('title', 'Client Details')
@section('section')
    <section class="dashboard_main pb-5">

        <div class="panel mt-4">
            @can('create_estimate_request')
                <div class="panel_header">
                    <h2 class="panel_title">Client Information</h2>
                    <a href="{{ route(getRouteAlias() . '.create.estimate', ['id' => encodeId($client->id)]) }}"
                        class="btn primaryblue">New
                        Estimate Request</a>

                </div>
            @endcan

            <div class="row mt-4">

                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Client Name</label>
                        <p class="text">{{ $client->full_name }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Title</label>
                        <p class="text">{{ $client->title ?? '---' }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Company Name</label>
                        <p class="text">{{ $client->company_name ?? '---' }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Office Number</label>
                        <p class="text">{{ $client->office_no }}</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Phone Number</label>
                        <p class="text">{{ $client->mobile_no }}</p>
                        @if ($client->alternate_no)
                            <p class="text">{{ $client->alternate_no }} <span>(Alternate)</span></p>
                        @endif
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-sm-6 pt-4">
                    <div class="detail_info">
                        <label for="" class="label">Email</label>
                        <p class="text">{{ $client->email }}</p>
                        @if ($client->alternate_email)
                            <p class="text" style="white-space: nowrap">{{ $client->alternate_email }}
                                <span>(Alternate)</span>
                            </p>
                        @endif
                    </div>
                </div>


            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 mt-4">
                <div class="panel">
                    <div class="panel_header">
                        <h2 class="panel_title">Address</h2>
                    </div>

                    <div class="row">
                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Address 1</label>
                                <p class="text">{{ $client->propertyAddress->address1 ?? '---' }}</p>
                            </div>
                        </div>

                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Address 2</label>
                                <p class="text">{{ $client->propertyAddress->address2 ?? '---' }}</p>
                            </div>
                        </div>

                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">City</label>
                                <p class="text">{{ $client->propertyAddress->city ?? '---' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">State</label>
                                <p class="text">{{ $client->propertyAddress->state ?? '---' }}</p>
                            </div>
                        </div>


                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Zip Code</label>
                                <p class="text">{{ $client->propertyAddress->zip ?? '---' }}</p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-lg-6 mt-4">
                <div class="panel">
                    <div class="panel_header">
                        <h2 class="panel_title">Billing Information</h2>
                    </div>

                    <div class="row">
                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Address 1</label>
                                <p class="text">{{ $client->billingAddress->address1 ?? '---' }}</p>
                            </div>
                        </div>

                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Address 2</label>
                                <p class="text">{{ $client->billingAddress->address2 ?? '---' }}</p>
                            </div>
                        </div>

                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">City</label>
                                <p class="text">{{ $client->billingAddress->city ?? '---' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">State</label>
                                <p class="text">{{ $client->billingAddress->state ?? '---' }}</p>
                            </div>
                        </div>


                        <div class="col-md-6 pt-4">
                            <div class="detail_info">
                                <label for="" class="label">Zip Code</label>
                                <p class="text">{{ $client->billingAddress->zip ?? '---' }}</p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        @canany(['estimate_listing', 'generate_estimate', 'operation_listing'])
            <div class="table_filter_header mt-4 estimates_table_filters">
                <h2 class="sub_heading">Estimates History</h2>

                <div class="filters">
                    <!-- Button trigger modal -->
                    <button type="button" class="btn primaryblue" data-toggle="modal" data-target="#emailModal">
                        <i class="fa fa-envelope" style="color: white;padding-right: 5px;"></i> Email
                    </button>
                    <input type="search" placeholder="Search" name="" id="filter_search"
                        class="clients_Detail_Search filter_search">
                    <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                        <option value="" selected>Filter</option>
                        <option value="Clear">Clear</option>
                        <option value="won">Won</option>
                        <option value="lost">Lost</option>
                        <option value="proposed">Proposed</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive  mt-4">
                <table class="table table-striped custom_datatable yajra-datatable w-100">
                    <thead>
                        <tr>
                            <th>Salesman Name</th>
                            <th>Estimator Name</th>
                            <th>Total Cost</th>
                            <th>Total Price</th>
                            <th>Status</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        @endcan
    </section>
    @include('organization.partials.send-email', ['to_email' => $client->email])
    @canany(['estimate_listing', 'generate_estimate', 'operation_listing'])
        @push('scripts')
            <script></script>
            @include('organization.client.estimates-script')
        @endpush
    @endcan
@endsection
