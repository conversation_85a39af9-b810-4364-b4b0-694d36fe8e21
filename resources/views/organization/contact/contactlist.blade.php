@extends('layouts.admin.master') @section('title', 'Contacts')
@section('styles')
    <style>
        textarea[readonly] {
            background-color: #f6f6f6 !important;
            border: 1px solid #e7e7e7 !important;
            opacity: 1 !important;
        }

        button:focus {
            outline: none;
            box-shadow: none;
        }

        button:hover {
            cursor: pointer;
        }

        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }

        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .hidemodelbtn:hover {
            cursor: pointer !important;
        }

        @media screen and (max-width: 580px) {
            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        .dropdown-toggle::after {
            display: none !important;

        }
    </style>
    <style>
        .select2-container--default {
            margin-top: 21px !important;
            margin-right: 7px !important;
        }

        .table_filter_dropdown {
            margin-top: -45px !important;
        }

        .select2-dropdown--below {
            margin-top: -45px !important;
            background: white !important;
        }

        .required_field {
            color: red;
        }

        .dropdown-toggle::after {
            display: inline-block;
            width: 0;
            height: 0;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            display: none !important;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        table.dataTable.display tbody tr:hover>.sorting_1,
        table.dataTable.order-column.hover tbody tr:hover>.sorting_1 {
            background-color: #e1f4ff !important;
        }

        table.dataTable.display tbody tr:hover,
        table.dataTable.order-column.hover tbody tr:hover {
            background-color: #e1f4ff !important;
        }

        /* .large-modal {
            max-width: 95% !important;
            width: 80% !important;
        } */
        @media screen and (max-width: 580px) {
            /* .large-modal {
                max-width: 95% !important;
                width: 95% !important;
            } */
        }

        span {
            font-size: 14px;
            color: #192a3e;
        }

        input::placeholder {
            font-size: 14px;
        }

        input {
            font-size: 14px !important;
        }

        button {
            font-size: 14px !important;
        }

        select {
            font-size: 14px !important;
        }

        .select2-selection__rendered {
            width: 170px !important;
        }

        .showMessage {
            display: none;
            padding: 0px 10px 7px 10px;
            background: #c3e6cb;
            color: #155724;
            text-align: left;
        }

        .showMessage p {
            color: #155724;
            padding: 8px 0px 0px 0px;
        }
    </style>
    @endsection @section('section')
    <section class="dashboard_main pb-5 contactTable">
        <div class="showMessage"></div>
        <div class="table_filter_header mb-4 contactListing mx-2">
            <h1 class="sub_heading">Contacts</h1>

            <div class="d-md-flex align-items-center gap-3">
                <div class="filters">
                    <input type="search" placeholder="Search" name="" id=""
                        class="contact_search filter_search" />
                    <!-- <a
                        type="button"
                        data-toggle="modal"
                        data-target="#selectClientModal"
                        class="text-decoration-none"
                        style="
                        padding: 9px 16px 5px 16px;
                        gap: 8px;
                        border-radius: 6px;
                        border: 1px 0px 0px 0px;
                        opacity: 0px;
                        background: #ffffff;
                        display: flex !important;
                        ><svg
                            style="margin-top: 3px"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <label class="">Import</label>
                    </a> -->
                    <form method="" action="" id="importaccount" style="margin-top: 3px;"
                        class="file_upload_button_form upload_file_wrapper w-fit"
                        enctype="multipart/form-data">
                        @csrf

                        <label class="btn primaryblue primaryblue22 transparent px-5"
                            for="import_file_data"
                            style="
                            border: 1px solid var(--lightgray);
                            background-color: white !important;
                            margin-top: 3px !important;
                        "><svg
                                style="margin-top: 3px" width="16" height="16"
                                viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                    stroke="#51566C" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            &nbsp;
                            <span
                                style="font-size: 12px; color: #514f6e !important">Import</span></label>
                        <input class="input_file d-none" type="file" name="file"
                            id="import_file_data" accept=".xls, .xlsx" />
                    </form>
                    <!-- <a
                        type="button"
                        data-toggle="modal"
                        data-target="#selectClientModal"
                        class="text-decoration-none"
                        style="
                        padding: 9px 16px 5px 16px;
                        gap: 8px;
                        border-radius: 6px;
                        border: 1px 0px 0px 0px;
                        opacity: 0px;
                        background: #ffffff;
                        display: flex !important;
                    "
                        ><svg
                            width="16"
                            height="16"
                            style="margin-top: 3px"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                stroke="#51566C"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <label class="">Export</label>
                    </a> -->
                    <form method="POST" id="export-opportunity"
                        action="{{ route(getRouteAlias() . '.export.contacts') }}">
                        @csrf
                        <button type="submit" {{ $contactsCount == 0 ? 'disabled' : '' }}
                            class="btn primaryblue transparent px-5"
                            style="border: 1px solid var(--lightgray); color: black !important">
                            <svg width="16" height="16" style="margin-top: 3px"
                                viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                    stroke="#51566C" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span class=""
                                style="font-size: 12px; color: #514f6e !important">&nbsp;Export</span>
                        </button>
                    </form>
                    <select name="" id="contact_filter" class="select-small basic-single-select">
                        <option value="" selected>Filter</option>
                        @foreach ($accounts as $item)
                            <option value="{{ $item->id }}">
                                {{ $item->company_name }}

                            </option>
                        @endforeach
                    </select>
                </div>

                <a type="button" data-toggle="modal" data-target="#selectClientModal"
                    class="btn primaryblue text-decoration-none text-white">+ New Contact</a>
            </div>
        </div>
        <div class="table-responsive">
            <table id="contact_list"
                class="table table-striped custom_datatable display yajra-datatable"
                style="width: 100%; border-radius: 12px">
                <thead>
                    <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Account</th>
                        <th>Phone Number</th>
                        <th>Email</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="">
                    <!-- <tr>
                        <td>********</td>
                        <td>Weeds need spraying</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <img
                                    src="{{ asset('admin_assets/images/userimg.jpeg') }}"
                                    alt="Jon Doe"
                                    class="rounded-circle"
                                    style="
                                    width: 40px;
                                    height: 40px;
                                    object-fit: cover;
                                "
                                />
                                <span class="mx-2">Jon Doe</span>
                            </div>
                        </td>

                        <td>(505) 555-0125</td>
                        <td>Enhancements</td>
                        <td class="text-center">
                            <div class="dropdown">
                                <button
                                    class="btn btn-sm bg-transparent dropdown-toggle"
                                    type="button"
                                    data-toggle="dropdown"
                                    aria-expanded="false"
                                >
                                    <svg
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M14 5C14 6.104 13.104 7 12 7C10.896 7 10 6.104 10 5C10 3.896 10.896 3 12 3C13.104 3 14 3.896 14 5Z"
                                            fill="#828282"
                                        />
                                        <path
                                            d="M12 10C10.896 10 10 10.896 10 12C10 13.104 10.896 14 12 14C13.104 14 14 13.104 14 12C14 10.896 13.104 10 12 10Z"
                                            fill="#828282"
                                        />
                                        <path
                                            d="M12 17C10.896 17 10 17.896 10 19C10 20.104 10.896 21 12 21C13.104 21 14 20.104 14 19C14 17.896 13.104 17 12 17Z"
                                            fill="#828282"
                                        />
                                    </svg>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a
                                            onclick="editSection()"
                                            class="dropdown-item"
                                            href="#"
                                            >Edit</a
                                        >
                                    </li>
                                    <li>
                                        <a
                                            onclick="deleteSection()"
                                            class="dropdown-item"
                                            href="#"
                                            >Delete</a
                                        >
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr> -->
                </tbody>
            </table>
        </div>
    </section>

    <!-- =Add Contact Modal -->
    <div class="modal fade select-client" id="selectClientModal" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: #e1f4ff">
                    <h4 class="modal-title px-3" id="addItemModalLabel">
                        <b style="color: #0074d9 !important">Contact</b>
                    </h4>
                    <button type="button" class="btn-close px-3 hidemodelbtn"
                        data-dismiss="modal" aria-label="Close"
                        style="
                        border: none;
                        background-color: transparent;
                        font-size: 21px;
                    ">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <form id="contactadd">
                    @csrf
                    <div class="modal-body" style="padding: 13px 20px !important">
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">                                <span for="item_name">First Name
                                    <label class="required_field" for="">*</label></span>


                                <div class="input-group">
                                    <!-- Dropdown as select -->
                                    <select id="name_title" name="name_title" class="form-select"
                                        style="
            border-bottom: 1px solid #ced4da;
            border-left: 1px solid #ced4da;
            border-top: 1px solid #ced4da;
            border-right: none;
            background-color: white;
            width:50px; /* Adjust width if needed */
        ">
                                        <option value="Mr." selected>Mr.</option>
                                        <option value="Mrs.">Mrs.</option>
                                        <option value="Ms.">Ms.</option>
                                        <option value="Dr.">Dr.</option>
                                    </select>

                                    <!-- Input field -->
                                    <input type="text" id="item_name" name="first_name" required
                                        class="form-control"
                                        style="
            height: 30px !important;
            text-align: left;
        "
                                        placeholder="Enter first name" />

                                </div>
                                <small class="text-danger" id="first_name_error"></small>

                                <!-- JavaScript to update the title in the hidden input -->
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%;margin-left:3%;">
                                <span for="">
                                    Last Name
                                    <label class="required_field" for="">*</label></span>
                                <input type="text" id="item_name" name="last_name" required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control" placeholder="Enter last name" />

                                <!-- </div> -->
                            </div>
                            <small class="text-danger" id="last_name_error"></small>
                        </div>
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="item_name">Suffix
                                    <label class="" for=""> (Optional)</label></span>
                                <input type="text" name="suffix" style="height: 30px !important"
                                    placeholder="suffix" class="form-control" id="" />


                                <!-- <select

                                >
                                    <option value="Mr.">Mr.</option>
                                    <option value="Mrs.">Mrs.</option>
                                    <option value="Dr.">Dr.</option>
                                    <option value="Ms.">Ms.</option>
                                </select> -->
                            </div>
                            <small class="text-danger" id="suffix_error"></small>
                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%;margin-left:3%; >
                                <span for="">
                                    Title
                                    <label class="required_field" for="">*</label></span>
                                <input type="text" id="item_name" name="title" required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "                                    class="form-control" placeholder="Enter title" />

                                <!-- </div> -->
                            </div>
                            <small class="text-danger" id="title_error"></small>
                        </div>
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Email
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <input type="email" name="email" id="" required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "                                        placeholder="Email" />

                                </div>
                                <small class="text-danger" id="email_error"></small>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%;margin-left:3%;">
                                <span for="">
                                    Second Email
                                    <label class="" for="">(Optional)</label>
                                </span>
                                <input type="email" name="second_email" id=""
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "                                    class="form-control" placeholder="Second email" />

                                <!-- </div> -->
                            </div>
                            <small class="text-danger" id="second_email_error"></small>
                        </div>
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Phone number
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <input type="text" name="phone"
                                        oninput="maskPhoneNumber(event)" maxlength="12"
                                        minlength="12" id="" required class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "                                        placeholder="Phone number" />

                                </div>
                                <small class="text-danger" id="phone_error"></small>
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%;margin-left:3%;">
                                <span for="">
                                    Second phone
                                    <label class="" for="">(Optional)</label>
                                </span>
                                <input type="text" name="second_phone"
                                    oninput="maskPhoneNumber(event)" maxlength="12" minlength="12"
                                    id=""
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "                                    class="form-control" placeholder="Second phone" />
                                <small class="text-danger" id="second_phone_error"></small>
                                <!-- </div> -->
                            </div>

                        </div>

                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group col-md-12" style="width: 47%;  padding-left: 5px; padding-right: 5px;" >
                                <span for="">Account
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <select name="account" style="height: 30px !important"
                                        class="form-control" required id="category_list">
                                        <option value="" selected>Select Account</option>
                                        @foreach ($accounts as $item)
                                            <option value="{{ $item->id }}">
                                                {{ $item->company_name }}
                                            </option>
                                        @endforeach
                                    </select>

                                </div>
                                <small class="text-danger" id="account_error"></small>
                            </div>

                            <div class="form-group d-none" style="width: 47%">
                                <span for="">Property
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <select name="propertyt_id" style="height: 30px !important"
                                        class="form-control" id="">
                                        <option value="" selected>Select Property</option>
                                        @foreach ($property as $item)
                                            <option value="{{ $item->id }}">
                                                {{ $item->name }}
                                            </option>
                                        @endforeach
                                    </select>

                                </div>
                                <small class="text-danger" id="propertyt_id_error"></small>
                            </div>


                            <!-- </div>
                    <div class="col"> -->
                            <!-- <div class="form-group" style="width: 47%"> -->
                            <!-- <span for="">
                                    Role
                                    <label class="required_field" for=""
                                        >*</label
                                    ></span
                                > -->
                            <!-- <select
                                    name="role"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id=""
                                >
                                <option value="" selected>Select Role</option>
                                    @foreach ($roles as $item)
    <option value="{{ $item->id }}">
                                        {{ $item->name }}
                                    </option>
    @endforeach
                                </select> -->
                            <!-- <input
                                        type="hidden"

                                        name="propertyt_id"
                                    /> -->
                            <!-- <input
                                    type="text"
                                    name="role"
                                    id=""
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Role"
                                /> -->
                            <!-- </div> -->
                            <!-- </div> -->
                        </div>
                        <div class="row px-2" style="justify-content: space-around; display: flex; margin-top:15px">
                            <!-- <div class="col"> -->
                            <!-- <div class="form-group" style="width: 47%"> -->
                            <!-- <span for=""
                                    >Mailing Address
                                    <label class="required_field" for=""
                                        >*</label
                                    ></span
                                > -->
                            <!-- <div class="input-group">
                                    <input
                                        type="text"
                                        name="mailing_address"
                                        id=""
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Mailing Address"
                                    />
                                </div> -->
                                    <!-- placeholder="Mailing Address"
                                />
                            </div> -->
                            <!-- </div> -->


                            <!-- </div>
                <div class="col"> -->
                        </div>

                        <!-- <div class="form-group">
                <b for="">Gross Margin</b>
              <input type="number" id="item_gross_margin" style="height: 30px !important;" class="form-control " placeholder="Item Gross Margin">
            </div> -->
                        <div class="d-flex" style="gap: 17px; justify-content: end">
                            <button type="button" data-dismiss="modal" aria-label="Close"
                                class="btn px-5 py-2"
                                style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            ">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary px-5 py-2">
                                Add
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- =Update Contact Modal -->
    <div class="modal fade select-client" id="updateContactModal" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="selectClientModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: #e1f4ff">
                    <h4 class="modal-title px-3" id="addItemModalLabel">
                        <b style="color: #0074d9 !important; font-size: 14px">Edit Contact</b>
                    </h4>
                    <button type="button" class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal" aria-label="Close"
                        style="border: none; background-color: transparent">
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <form action="{{ URL::route(getRouteAlias() . '.contacts.update') }}"
                    id="contactupdate" method="post">
                    @csrf
                    <div class="modal-body" style="padding: 13px 20px !important">
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="item_name">First Name
                                    <label class="required_field" for="">*</label></span>



                                <div class="input-group">
                                    <!-- Dropdown as select -->
                                    <select id="name_title2" name="name_title"
                                        class="form-select select-prefix"
                                        style="
            border-bottom: 1px solid #ced4da;
            border-left: 1px solid #ced4da;
            border-top: 1px solid #ced4da;
            border-right: none;
            background-color: white;
            width: 45px; /* Adjust width if needed */
        ">
                                        <option value="Mr." selected>Mr.</option>
                                        <option value="Mrs.">Mrs.</option>
                                        <option value="Ms.">Ms.</option>
                                        <option value="Dr.">Dr.</option>
                                    </select>
                                    <input type="hidden" id="contactt_id" name="contactt_id" />
                                    <!-- Input field -->
                                    <input type="text" id="first_name" name="first_name" required
                                        class="form-control"
                                        style="
            height: 30px !important;
            text-align: left;
        "                                        placeholder="Enter first name" />
                                </div>

                                <!-- JavaScript to update the title in the hidden input -->
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">
                                    Last Name
                                    <label class="required_field" for="">*</label></span>
                                <input type="text" id="last_name" name="last_name" required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "                                    class="form-control" placeholder="Enter last name" />
                                <!-- </div> -->
                            </div>
                        </div>

                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="item_name">Suffix
                                    <label class="" for=""> (Optional)</label></span>
                                <input type="text" name="suffix" style="height: 30px !important"
                                    class="form-control" id="suffix" />
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">
                                    Title
                                    <label class="required_field" for="">*</label></span>
                                <input type="text" id="title" name="title" required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control" placeholder="Enter title" />
                                <!-- </div> -->
                            </div>
                        </div>
                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Email
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <input type="email" name="email_update" id="email" required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "                                        placeholder="Email" />


                                </div>
                                <small class="text-danger" id="email_update_error"></small>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">
                                    Second Email
                                    <label class="" for=""> (Optional)</label></span>
                                <input type="email" name="second_email" id="second_email"
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control" placeholder="Second Email" />
                                <!-- </div> -->
                            </div>
                        </div>

                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Phone number
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <input type="text" name="phone_update"
                                        oninput="maskPhoneNumber(event)" maxlength="12"
                                        minlength="12" id="phone" required class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "                                        placeholder="Phone number" />
                                </div>
                                <small class="text-danger" id="phone_update_error"></small>
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">
                                    Second phone
                                    <label class="" for=""> (Optional)</label></span>
                                <input type="text" name="second_phone_update" id="second_phone"
                                    oninput="maskPhoneNumber(event)" maxlength="12" minlength="12"
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "                                    class="form-control" placeholder="Second phone" />
                                <small class="text-danger" id="second_phone_update_error"></small>
                                <!-- </div> -->
                            </div>
                        </div>

                        <div class="row px-2" style="justify-content: space-around; display: flex">
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Account
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <select name="account" style="height: 30px !important"
                                        class="form-control" required id="account">
                                        <option value="" selected>Select Account</option>
                                        @foreach ($accounts as $item)
                                            <option value="{{ $item->id }}">
                                                {{ $item->company_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for="">Property
                                    <label class="required_field" for="">*</label></span>
                                <div class="input-group">
                                    <select name="propertyt_id" style="height: 30px !important"
                                        class="form-control" required id="propertyt_id">
                                        <option value="" selected>Select Property</option>
                                        @foreach ($property as $item)
                                            <option value="{{ $item->id }}">
                                                {{ $item->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex" style="gap: 17px; justify-content: start">
                            <input type="checkbox" id="make_default" name="default"
                                class="form-control" />
                            <label for="make_default"
                                style="
                                color: #3b4159;
                                margin-top: -5px !important;
                                margin-left: -14px !important;
                            ">Set
                                as default contact information
                            </label>
                        </div>


                        <!-- <div class="form-group">
                <b for="">Gross Margin</b>
              <input type="number" id="item_gross_margin" style="height: 30px !important;" class="form-control " placeholder="Item Gross Margin">
            </div> -->
                        <div class="d-flex" style="gap: 17px; justify-content: end">
                            <button type="button" data-dismiss="modal" aria-label="Close"
                                onclick="addNewItemModel()" class="btn px-5 py-2"
                                style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            ">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary px-5 py-2">
                                Update
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!--Delete Modal -->
    <div class="modal-small Delete-modal modal fade" id="DeleteContactModal" data-keyboard="false"
        tabindex="-1" aria-labelledby="DeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="padding: 0px; border-radius: 0px">
                <div class="modal-header" style="background: #f7f1f1; padding: 18px">
                    <h5 class="modal-title fw-bold">Delete Invoice?</h5>
                    <button type="button" class="btn-close" data-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 18px">
                    <div class="mb-4">
                        <p style="color: #231e1e">By deleting this Account:</p>
                        <ul class="mt-4">
                            <li style="list-style: disc; font-size: 14px">
                                Its total will be removed from your client's balance
                            </li>
                            <li style="list-style: disc; font-size: 14px">
                                It will be removed from reports, which could skew
                                your accounting
                            </li>
                        </ul>
                    </div>

                    <div class="modal-footer border-0 p-0">
                        <button type="button" class="btn primaryblue transparent mt-5"
                            data-dismiss="modal">
                            Cancel
                        </button>
                        <button type="button" class="btn primaryblue mt-5">
                            Delete Contact
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Delete Modal -->
    @include('layouts.admin.confirmation-modal')
    @include('layouts.partials.success-modal') @push('scripts')
        @include('organization.contact.script')



        <script>
            $(document).ready(function() {
                $('#contactupdate').on('submit', function(e) {
                    e.preventDefault();
                    // Clear previous errors
                    $('small.text-danger').text('');

                    let formData = $(this).serialize();

                    $.ajax({
                        url: '{{ URL::route(getRouteAlias() . '.contacts.update') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            sessionStorage.setItem('contactUpdated', 'true');
                            location.reload();
                            $('#updateContactModal').modal('hide');
                            // }
                        },
                        error: function(xhr) {
                            if (xhr.status === 422) {
                                let errors = xhr.responseJSON.errors;
                                for (let key in errors) {
                                    // console.info(key);
                                    $('#' + key + '_error').text(errors[key][
                                    0]);
                                }
                                $('#updateContactModal').modal(
                                'show'); // Ensure modal stays open
                            }
                        }
                    });
                });
            });
        </script>
        <script>
            $(document).ready(function() {
                if (sessionStorage.getItem('contactUpdated')) {
                    $('.showMessage').css('display', 'block');
                    $('.showMessage').html('<p>Contact Updated Successfully</p>');

                    // Hide the message after 3 seconds (3000 milliseconds)
                    setTimeout(function() {
                        $('.showMessage').fadeOut();
                    }, 3000);

                    // Remove the flag to ensure the message only shows once after the reload
                    sessionStorage.removeItem('contactUpdated');
                } else if (sessionStorage.getItem('contactAdded')) {
                    $('.showMessage').css('display', 'block');
                    $('.showMessage').html('<p>Contact Added Successfully</p>');

                    // Hide the message after 3 seconds (3000 milliseconds)
                    setTimeout(function() {
                        $('.showMessage').fadeOut();
                    }, 3000);

                    // Remove the flag to ensure the message only shows once after the reload
                    sessionStorage.removeItem('contactAdded');
                }
            });
        </script>


        <script>
            $(document).ready(function() {
                $('#contactadd').on('submit', function(e) {
                    e.preventDefault();
                    // Clear previous errors
                    $('small.text-danger').text('');

                    let formData = $(this).serialize();
                    $.ajax({
                        url: '{{ URL::route(getRouteAlias() . '.contact.add') }}',
                        method: 'POST',
                        data: formData,
                        success: function(response) {

                            sessionStorage.setItem('contactAdded', 'true');
                            location.reload();
                            $('#selectClientModal').modal('hide');
                            // }
                        },
                        error: function(xhr) {
                            if (xhr.status === 422) {
                                let errors = xhr.responseJSON.errors;
                                for (let key in errors) {
                                    $('#' + key + '_error').text(errors[key][
                                    0]);
                                }
                                $('#selectClientModal').modal(
                                'show'); // Ensure modal stays open
                            }
                        }
                    });
                });
            });
        </script>
        <script>
            var datepickerInstance;
            $(document).ready(function() {
                datepickerInstance = $("#customDateAppend")
                    .datepicker({
                        container: ".custom-appending",
                        format: "{{ getOrgDateFormat() }}", // Correct format tokens
                        // Other options for the datepicker
                    })
                    .data("datepicker");

                // Attach an event listener to the changeDate event
                $("#customDateAppend").on("changeDate", function() {
                    datepickerInstance.hide(); // Hide the datepicker
                });
            });
        </script>
        <script>
            function selectTitle(title) {
                document.getElementById("name_title").value = title; // Set hidden input value
                document.getElementById("dropdownMenuButton").innerText = title; // Update button text
            }

            function selectTitle2(title) {
                document.getElementById("name_title2").value = title; // Set hidden input value
                document.getElementById("dropdownMenuButton2").innerText = title; // Update button text
            }
        </script>
        @endpush @endsection
