<tr id="item-row-{{ $item->id }}">
    <td class="text-center">{{ $index + 1 }}.</td>
    <td>{{ $item->lineitem }}</td>
    <td class="text-center">${{ $item->unit_cost }}</td>
    <td>{{ $item->uom }}</td>
    <td>
        <div class="d-flex justify-content-around align-items-center">
            <!-- Delete Icon -->
            <a href="" class="text-danger open-delete-modal" data-id="{{ $item->id }}"
                data-toggle="modal" data-target="#deleteModal">
                <i class="delete-icon" style="background-image: url('{{ asset('trash.png') }}'); width: 20px; height: 20px; display: inline-block; background-size: contain; background-repeat: no-repeat; cursor: pointer;margin: 0px"></i>
            </a>

            <!-- Edit Icon -->
            <a href="javascript:void(0);" class="text-primary open-edit-modal"
                data-id="{{ $item->id }}" data-category="{{ $item->category }}"
                data-lineitem="{{ $item->lineitem }}" data-unitcost="{{ $item->unit_cost }}"
                data-uom="{{ $item->uom }}" data-toggle="modal"
                data-target="#addlaborModal">
                <i class="edit-icon" style="background-image: url('{{ asset('edit.png') }}'); width: 17px; height: 20px; display: inline-block; background-size: contain; background-repeat: no-repeat; cursor: pointer;margin-bottom: -3px"></i>
            </a>
        </div>
    </td>
</tr>
