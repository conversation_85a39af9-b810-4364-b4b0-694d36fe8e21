@extends('layouts.admin.master')
@section('title', 'Snow')
@section('section')
    <style>
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            /* optional if you want more control */
        }

        .rotate-icon {
            transition: transform 0.3s ease;
        }

        .rotate-icon.rotate {
            transform: rotate(180deg);
        }

        .text-primary {
            color: #0074d9 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .rotate-icon.collapsed {
            transform: rotate(180deg);
            transition: transform 0.3s;
        }

        .card-body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 15px;
        }

        .sub_heading {

            font-family: Poppins;
            font-weight: 600;
            font-size: 20px;
            line-height: 100%;
            letter-spacing: 0%;
            text-transform: capitalize;


        }

        .card-body ul li a {
            color: #51566C;
        }

        .card {
            border: none !important;
            /* border-radius: 13px !important; */

        }

        .card-header {
            padding: .75rem 1.25rem;
            margin-bottom: 0;
            background-color: white;
            border-bottom: none !important;
        }

        .card-header h5 {
            font-family: Poppins;
            font-weight: 500;
            font-size: 18px;
            line-height: 100%;
            letter-spacing: 0%;

        }

        thead {
            background-color: #F4F7FA !important
        }

        .fa-solid,
        .fass {
            font-weight: 900;
            color: #CD2F2F;
            background: none;

        }

        .edit_icon_color {
            color: #0074D9 !important;
        }

        .nav-link {
            transition: background-color 0.2s ease;
            border-radius: 4px;
        }

        .nav-link:hover {
            background-color: #E6F1FB;
            /* light gray background */
        }

        .nav-link.active {
            background-color: #E6F1FB !important;
            color: #0d6efd !important;
            /* optional: blue text */
            border-radius: 4px;
        }

        .buttons-wraper {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .cancel-btn,
        .conform-btn {
            padding: 8px 25px;
            border-radius: 6px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }

        .cancel-btn {
            background: #fff;
            color: #004080;
            border: 2px solid #000;
        }

        .cancel-btn:hover {
            background: #004080;
            color: #fff;
        }

        .conform-btn {
            background: #dc3545;
            color: white;
        }

        .conform-btn:hover {
            background: #c82333;
        }

        .delete-modal {
            max-width: 400px;
            margin-top: 40px;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .delete-icon {
            display: block;
            margin: 0 auto 20px;
            width: 60px;
            height: 60px;
        }

        .delete-request {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .are-sure {
            color: #666;
            margin-bottom: 25px;
        }
    </style>

    <section class="dashboard_main">
        <div class="settings_tab_grid">
            <x-settings_component.settings_tab />
            <div class="settings_content h-fit" style="margin-left: -18px;">

                <div class="col-lg-12 col-md-10">

                    <div class="table_filter_header oppListing mb-4">
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $err)
                                        <li>{{ $err }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <h2 class="sub_heading">Snow Setup</h2>
                        <div class="filters">

                            <form method="POST" id="import-snow"
                                action="{{ route('organization.snow.import') }}"
                                enctype="multipart/form-data">
                                @csrf

                                <input type="file" name="file" accept=".xlsx,.xls" required
                                    class="d-none" id="excelInput">

                                <button type="button"
                                    onclick="document.getElementById('excelInput').click()"
                                    class="btn primaryblue transparent px-5"
                                    style="border:1px solid var(--lightgray);color:black!important">
                                    <!-- same SVG -->
                                    <svg style="margin-top: 3px" width="16" height="16"
                                        viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                            stroke="#51566C" stroke-linecap="round"
                                            stroke-linejoin="round"></path>
                                    </svg>
                                    <span
                                        style="font-size:12px;color:#514f6e!important">&nbsp;Import</span>
                                </button>
                            </form>



                            @can('add_opportunity')
                                <form method="POST" action="" id="opportunity-import"
                                    class="file_upload_button_form upload_file_wrapper w-fit"
                                    enctype="multipart/form-data">
                                    @csrf
                                    <!-- <label class="btn primaryblue transparent px-5" for="import_file_data">Import Opportunities</label> -->
                                    <label class="btn primaryblue primaryblue22 transparent px-5 d-none"
                                        for="import_file_data"
                                        style="
                                            border: 1px solid var(--lightgray);
                                            background-color: white !important;
                                            margin-top: 4px;
                                        "><svg
                                            style="margin-top: 3px" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                                stroke="#51566C" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                        &nbsp;
                                        <span
                                            style="font-size: 12px; color: #514f6e !important">Import</span></label>
                                    <input class="input_file d-none" type="file" name="file"
                                        id="import_file_data" accept=".xls, .xlsx">
                                </form>
                                {{-- <a type="button" data-toggle="modal" data-target="#addlaborModal"
                                    class="btn primaryblue text-decoration-none text-white"
                                    style="height: 38px;">+ Add New
                                    Items</a> --}}

                                <button type="submit" data-toggle="modal"
                                    data-target="#addlaborModal" class="btn btn-primary"
                                    style="width: 146px; height: 38px; font-size: 15px;border-radius: 4px;">
                                    + Add New
                                    Items
                                </button>
                            @endcan
                        </div>
                    </div>


                    <div class="accordion" id="sectionAccordion">
                        @php
                            $sections = [
                                'Labor' => $laborItems,
                                'Material' => $materialItems,
                                'Equipments' => $equipmentItems,
                                'Stand By' => $standbyItems,
                            ];
                        @endphp

                        @foreach ($sections as $section => $items)
                            <div class="card mb-4 shadow-sm">
                                <div
                                    class="card-header d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">{{ $section }}</h4>
                                    <button class="btn btn-light toggle-section-btn" type="button"
                                        data-toggle="collapse"
                                        data-target="#{{ strtolower(str_replace(' ', '', $section)) }}Table"
                                        aria-expanded="{{ $loop->first ? 'true' : 'false' }}"
                                        aria-controls="{{ strtolower(str_replace(' ', '', $section)) }}Table">
                                        <i
                                            class="fas rotate-icon {{ $loop->first ? 'fa-chevron-up' : 'fa-chevron-down' }}"></i>
                                    </button>
                                </div>
                                <div id="{{ strtolower(str_replace(' ', '', $section)) }}Table"
                                    class="collapse {{ $loop->first ? 'show' : '' }}"
                                    data-parent="#sectionAccordion">
                                    <div class="card-body p-0">
                                        <table class="table table-bordered table-hover mb-0">
                                            <thead>
                                                <tr style="font-size: 12px;">
                                                    <th class="text-center" style="width: 50px;">#</th>
                                                    <th>Line Items</th>
                                                    <th class="text-center" style="width: 150px;">Unit
                                                        Cost</th>
                                                    <th style="width: 100px;">UoM</th>
                                                    <th style="width: 70px;"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($items as $index => $item)
                                                    @include('organization.snow.item-row')
                                                @empty
                                                    {{-- <tr>
                                                        <td colspan="5" class="text-center">No
                                                            records found</td>
                                                    </tr> --}}
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>



                </div>

            </div>
        </div>
    </section>


    <!-- #region  # -->
    <div class="modal fade select-client" id="addlaborModal" data-backdrop="static"
        data-keyboard="false" tabindex="-1" aria-labelledby="addlaborModalLabel" aria-hidden="true">
        <!-- change heading and select Uom options -->
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content border-0 rounded shadow">
                <div class="modal-header" style="background: #e1f4ff">
                    <h5 class="modal-title fw-semibold text-primary" id="addlaborModalLabel"
                        style="font-size: 19px;font-weight: 600;">Add
                        Snow Item</h5>
                    <button type="button" class="btn-close px-3" data-dismiss="modal"
                        aria-label="Close" style="background: transparent; border: none;">
                        <i class="fa fa-times text-muted fs-5" aria-hidden="true"></i>
                    </button>
                </div>

                <form id="addLaborForm" method="POST">
                    @csrf
                    <input type="hidden" name="edit_id" id="edit_id">

                    <div class="modal-body py-4 px-4">
                        <div class="row mb-3">

                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-semibold">Category <span
                                        class="text-danger">*</span></label>
                                <select name="category" id="category" class="form-control" required
                                    style="height: 33px;font-size: 15px;">
                                    <option value="" disabled selected>Select Category</option>
                                    <option value="Labor">Labor</option>
                                    <option value="Material">Material</option>
                                    <option value="Equipments">Equipments</option>
                                    <option value="Stand By">Stand By</option>
                                </select>
                                <small class="text-danger" id="uom_error"></small>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-semibold">Line Item <span
                                        class="text-danger">*</span></label>
                                <input type="text" name="line_item" id="line_item"
                                    class="form-control" placeholder="Enter line item" required
                                    style="height: 31px;font-size: 15px">
                                <small class="text-danger" id="line_item_error"></small>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-semibold">Unit Cost <span
                                        class="text-danger">*</span></label>
                                <input type="number" name="unit_cost" id="unit_cost"
                                    class="form-control" placeholder="e.g. 56" step="0.01"
                                    required style="height: 31px;font-size: 15px">
                                <small class="text-danger" id="unit_cost_error"></small>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-semibold">Unit of Measure <span
                                        class="text-danger">*</span></label>
                                <select name="uom" id="uom" class="form-control" required
                                    style="height: 33px;font-size: 15px;">
                                    <option value="" disabled selected>Select UoM</option>
                                    <!-- <option value="Hr">Hr</option>
                                    <option value="Day">Day</option>
                                    <option value="SqFt">SqFt</option>
                                    <option value="Job">Job</option> -->
                                    <option value="Hr">Hr</option>
                                    <option value="Each">Each</option>
                                    <option value="Bag">Bag</option>
                                    <option value="Ton">Ton</option>
                                    <option value="Gal">Gal</option>
                                </select>
                                <small class="text-danger" id="unit_cost_error"></small>
                            </div>

                        </div>

                        <div class="d-flex justify-content-end gap-3">
                            <button type="button" class="btn btn-outline-primary"
                                data-dismiss="modal"
                                style="width: 126px; height: 40px; font-size: 15px; border-radius: 8px;">Cancel</button>
                            &nbsp;&nbsp;
                            <button type="submit" class="btn btn-primary"
                                style="width: 126px;height: 40px;font-size: 15px;border-radius: 8px;">Add</button>

                            {{-- <button type="submit" data-toggle="modal"
                                data-target="#addlaborModal" class="btn btn-primary"
                                style="width: 146px; height: 38px; font-size: 15px;border-radius: 4px;">
                                + Add New
                                Items
                            </button> --}}
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>

    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel"
        aria-hidden="true">
        <div class="modal-dialog delete-modal modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <img src="{{ asset('admin_assets/images/icons/delete-icon.svg') }}"
                        alt="delete icon" class="delete-icon mb-3">
                    <h2 class="delete-request">Delete</h2>
                    <p class="are-sure">Are you sure you want to delete this item?</p>

                    <!-- Hidden input to store ID -->
                    <input type="hidden" id="delete_item_id">

                    <div class="buttons-wraper">
                        <button type="button" class="cancel-btn" data-dismiss="modal"
                            style="height: 41px; color: #0074d9; border: 2px solid black;">Cancel</button>
                        <button type="button" class="conform-btn" id="confirmDeleteBtn"
                            style="background-color: #0074d9;height: 41px; ">Yes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('extra-scripts')
    <script>
        document.getElementById('excelInput').addEventListener('change', () => {
            document.getElementById('import-snow').submit();
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const toggles = document.querySelectorAll(".toggle-section-btn");

            toggles.forEach(button => {
                const targetId = button.getAttribute("data-target");
                const target = document.querySelector(targetId);
                const icon = button.querySelector("i");

                // Ensure icon is correct on load (for bootstrap 'show')
                if (target.classList.contains("show")) {
                    icon.classList.remove("fa-chevron-down");
                    icon.classList.add("fa-chevron-up");
                }

                target.addEventListener("show.bs.collapse", () => {
                    icon.classList.remove("fa-chevron-down");
                    icon.classList.add("fa-chevron-up");
                });

                target.addEventListener("hide.bs.collapse", () => {
                    icon.classList.remove("fa-chevron-up");
                    icon.classList.add("fa-chevron-down");
                });
            });
        });
    </script>


    <script>
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                document.querySelectorAll('.nav-link').forEach(el => el.classList
                    .remove('active'));
                this.classList.add('active');
            });
        });
        // Store the ID in modal when trash icon is clicked
        $(document).on('click', '.open-delete-modal', function() {
            const id = $(this).data('id');
            $('#delete_item_id').val(id);
        });

        // On "Yes" confirm delete
        $('#confirmDeleteBtn').on('click', function() {
            const id = $('#delete_item_id').val();

            $.ajax({
                url: '/organization/snow/' + id,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#deleteModal').modal('hide');

                        $('#item-row-' + id).fadeOut(300, function() {
                            $(this).remove();
                        });
                    }
                },
                error: function() {
                    alert('Something went wrong while deleting the item.');
                }
            });
        });
    </script>

    <script>
        // Open modal in edit mode
        $(document).on('click', '.open-edit-modal', function() {
            // Set values for editing
            $('#edit_id').val($(this).data('id'));
            $('#category').val($(this).data('category'));
            $('#line_item').val($(this).data('lineitem'));
            $('#unit_cost').val($(this).data('unitcost'));
            $('#uom').val($(this).data('uom'));

            // Change modal heading and button text to "Update"
            $('#addlaborModalLabel').text('Edit Customize Item');
            $('#addLaborForm button[type="submit"]').text('Update');

            // Show modal
            $('#addlaborModal').modal('show');
        });

        // Submit form for both create/edit
    </script>

    <script>
        // Submit form for both create/edit
        $('#addLaborForm').on('submit', function(e) {
            e.preventDefault();

            let form = $(this);
            let formData = form.serialize();
            let editId = $('#edit_id').val();

            let url = editId ? '/organization/snow/' + editId :
                '{{ route('organization.snow.store') }}';
            let method = editId ? 'PATCH' : 'POST';

            $.ajax({
                url: url,
                method: method,
                data: formData,
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addlaborModal').modal('hide');
                        form[0].reset();
                        $('#edit_id').val('');

                        // Get category table based on the category and show it
                        let sectionId = response.category.toLowerCase().replace(
                            /\s/g, '');
                        let collapseDiv = $('#' + sectionId + 'Table');
                        collapseDiv.collapse(
                            'show'); // Show the section when data is added

                        // If editing, replace the existing row, otherwise add new row
                        if ($('#item-row-' + response.id).length) {
                            // Edit case: Replace the row
                            $('#item-row-' + response.id).replaceWith(response
                                .html);
                        } else {
                            // Create case: Prepend the new row to the appropriate table
                            collapseDiv.find('tbody').prepend(response.html);
                            collapseDiv.find(
                                    'tbody tr:contains("No records found")')
                                .remove();
                        }
                    }
                },
                error: function(xhr) {
                    let errors = xhr.responseJSON.errors;
                    $('#line_item_error').text(errors?.line_item ?? '');
                    $('#unit_cost_error').text(errors?.unit_cost ?? '');
                    $('#uom_error').text(errors?.uom ?? '');
                }
            });
        });
    </script>


@endsection
