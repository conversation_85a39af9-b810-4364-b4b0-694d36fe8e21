@extends('layouts.admin.master')
@section('title', 'Settings - Book of Business')
@section('section')
    <section class="dashboard_main">
        <div class="settings_tab_grid">
            <x-settings_component.settings_tab />
            <div class="settings_content">
                <div class="panel">
                    <div class="panel_header">
                        <h2 class="panel_title">Book of Business</h2>
                    </div>

                    <div class="panel_body">
                        <div class="showMessage"></div>

                        <!-- Import Progress and Results -->
                        <div id="import-progress" class="alert alert-info" style="display: none;">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm mr-2" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <span>Importing data, please wait...</span>
                            </div>
                        </div>

                        <div id="import-results" style="display: none;"></div>

                        <form id="import-form" enctype="multipart/form-data" style="display: none;">
                            @csrf
                            <input type="file" name="import_file" id="import_file_input" accept=".xlsx,.xls" multiple>
                        </form>

                        <div class="table_filter_header oppListing mb-4 justify-content-between">
                            <div class="search-wrapper">
                                {{--<input type="text" id="filter_search" class="form-control" placeholder="Search..." style="max-width: 300px;">--}}
                            </div>
                            <div class="filters">
                                <a href="{{ route(getRouteAlias() . '.settings.maintenance.book-of-business.export') }}" class="btn primaryblue transparent px-5" style="border: 1px solid var(--lightgray); color: black !important">
                                    <img class="mr-2" src="{{ asset('admin_assets/images/export-icon.svg') }}" alt="Export icon">
                                    Export
                                </a>
                                <button type="button" id="bulk-upload-btn" class="btn primaryblue transparent px-5" style="border: 1px solid var(--lightgray); color: black !important">
                                    <img class="mr-2" src="{{ asset('admin_assets/images/bulk-upload-icon.svg') }}" alt="bulk import icon">
                                    Bulk Upload
                                </button>
                                <input class="input_file d-none" type="file" name="file" id="import_file_data" accept=".xls, .xlsx">
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped custom_datatable bookOfBusinessDataTable" id="bookOfBusinessDataTable" style="width:100%">
                                <thead>
                                <tr>
                                    <th>{{ __('Job #') }}</th>
                                    <th>{{ __('Job Name') }}</th>
                                    <th>{{ __('Property Name') }}</th>
                                    <th>{{ __('Account') }}</th>
                                    <th>{{ __('Account Owner') }}</th>
                                    <th>{{ __('Maintenance Contract') }}</th>
                                    <th>{{ __('Operation Manager') }}</th>
                                    <th>{{ __('Contract Start Date') }}</th>
                                    <th>{{ __('Contract End Date') }}</th>
{{--                                    <th>{{ __('Status') }}</th>--}}
                                    <th class="fixed-col" style="background-color: #dcf2ff !important;">{{ __('Action') }}</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <!-- DataTable will populate this -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                    </div>
    </div>
    </section>

    <!-- Custom File Upload Modal -->
    <div class="modal fade file-upload-modal" id="fileUploadModal" tabindex="-1" role="dialog" aria-labelledby="fileUploadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-header-title">
                        <h5 class="modal-title" id="fileUploadModalLabel">Upload files</h5>
                        <p class="upload-description">Upload and attach files in bulk amount</p>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true" style="color: #A4A7AE !important;">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Upload Area -->
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <img src="{{ asset('admin_assets/images/Featured icon.svg') }}" alt="upload icon">
                        </div>
                        <div class="upload-text">
                            <span class="upload-trigger fs-16" id="uploadTrigger">Click to upload</span>
                            <span class="upload-drag-text"> or drag and drop</span>
                        </div>
                        <p class="upload-hint">excel sheet (max. 800x400px)</p>
                    </div>

                    <!-- File List -->
                    <div class="file-list" id="fileList">
                        <!-- Files will be dynamically added here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col-12">
                        <div class="row">
                            <div class="col-6 pr-2 p-0">
                                <button type="button" class="btn btn-cancel w-100 fs-14" data-dismiss="modal">Cancel</button>
                            </div>
                            <div class="col-6 p-0">
                                <button type="button" class="btn btn-attach w-100 fs-14" id="attachFilesBtn">Attach files</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade changeAccountOwner" id="changeAccountOwner" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="changeAccountOwnerModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered model-sm">
            <div class="modal-content">
                <div class="modal-header custom-model-header px-5">
                    <h5 class="modal-title fs-18 custom-model-header-title">Assign Account Owner</h5>
                    <button type="button" class="close opacity-1 hideChangeAccountOwnerModel border-0" data-dismiss="modal" aria-label="Close">
                        <img src="{{ asset('admin_assets/images/model-close-icon.svg') }}" alt="cross icon for modal">
                    </button>
                </div>
                <div class="modal-body custom-modal-body px-5">
                    <div class="body-wraped">
                        <!-- Search Filter -->
                        <div class="search-filter mb-3">
                            <input type="text" id="account-owner-search" class="form-control modal-custom-search fs-16" placeholder="Search">
                        </div>
                        <input type="hidden" id="account-owner-oppor-id" >
                        <!-- Account Owners List -->
                        <div class="table-wrapper">
                            <table class="table table-striped" style="width:100%; border-radius: 9px">
                                <tbody id="accountOwnersList">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="custom-footer pb-4 pt-2">
                        <button type="button" class="btn btn-primary fs-14 w-100" id="addAccountOwner">Select</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade changeOPManager" id="changeOPManager" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="changeOPManagerModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered model-sm">
            <div class="modal-content">
                <div class="modal-header custom-model-header px-5">
                    <h5 class="modal-title fs-18 custom-model-header-title">Assign Operation Manager</h5>
                    <button type="button" class="close opacity-1 hideChangeOPManagerModel border-0" data-dismiss="modal" aria-label="Close">
                        <img src="{{ asset('admin_assets/images/model-close-icon.svg') }}" alt="cross icon for modal">
                    </button>
                </div>
                <div class="modal-body custom-modal-body px-5">
                    <div class="body-wraped">
                        <!-- Search Filter -->
                        <div class="search-filter mb-3">
                            <input type="text" id="account-manager-search" class="form-control modal-custom-search fs-16" placeholder="Search">
                        </div>
                        <input type="hidden" id="account-manager-oppor-id" >
                        <!-- Operation Managers List -->
                        <div class="table-wrapper">
                            <table class="table table-striped" style="width:100%; border-radius: 9px">
                                <tbody id="operationManagersList"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="custom-footer pb-4 pt-2">
                        <button type="button" class="btn btn-primary fs-14 w-100" id="addOPManager">Select</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('extra-scripts')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('asset/assets/js/organization/book-of-business.js') }}"></script>
@endsection
