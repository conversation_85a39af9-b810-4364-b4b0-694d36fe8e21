@extends('layouts.admin.master')
@section('title', 'Archives')
@section('section')
    <section class="dashboard_main pb-5 estimates_table_filters">

        <div class="table_filter_header mb-4">
            <h2 class="sub_heading">Archives Estimate List</h2>

            <div class="filters">
                <input type="search" placeholder="Search" name="" id="filter_search"
                    class="clients_Detail_Search filter_search">
                {{-- <select name="" id="select_filter" class="table_filter_select select-small custom_selectBox">
                    <option value="" selected>Filter</option>
                    <option value="Clear">Clear</option>
                    <option value="won">Won</option>
                    <option value="lost">Lost</option>
                    <option value="proposed">Proposed</option>
                </select> --}}
            </div>
        </div>


        <div class="table-responsive">
            <table id="clients_Detail" class="table table-striped custom_datatable yajra-datatable display"
                style="width:100%">
                <thead>
                    <tr>
                        <th>ER #</th>
                        <th>Project Name</th>
                        <th>Client Name</th>
                        <th>Salesman Name</th>
                        <th>Estimator Name</th>
                        <th>Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>


            </table>
        </div>


    </section>
    <!-- View Note Modal -->
    <div class="modal fade propert-modal" id="ViewNoteModal" data-backdrop="static" data-keyboard="false"
        tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="requestChangeModalLabel">View Note</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="body-wraped">
                        <div class="filed-wraper" id="Notes-preview">
                            {{-- <textarea class="modal-textarea" name="reason_for_change" id="Notes-preview" placeholder="Enter you reason"
                            readonly></textarea> --}}
                        </div>
                        <button type="button" class="add-btn" data-dismiss="modal" aria-label="Close">Close</button>

                    </div>
                </div>

            </div>
        </div>
    </div>

    @push('scripts')
        @include('organization.archive.script')
        <script></script>
    @endpush
@endsection
