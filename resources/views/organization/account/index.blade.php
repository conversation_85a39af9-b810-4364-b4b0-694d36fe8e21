@extends('layouts.admin.master')
@section('title', 'Accounts')
@section('styles')
    <style>
        textarea[readonly] {
            background-color: #f6f6f6 !important;
            border: 1px solid #e7e7e7 !important;
            opacity: 1 !important;
        }
    </style>
    <style>
        .select2-container--default {
            margin-top: 21px !important;
            margin-right: 7px !important;
        }
        .table_filter_dropdown {
            margin-top: -45px !important;
        }
        .select2-dropdown--below {
            margin-top: -45px !important;
            background: white !important;
        }
        .required_field {
            color: red;
        }
        .dropdown-toggle::after {
            display: inline-block;
            width: 0;
            height: 0;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            display: none !important;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }
        .even:hover {
            background-color: #e1f4ff !important;
        }
        table.dataTable.display tbody tr:hover > .sorting_1,
        table.dataTable.order-column.hover tbody tr:hover > .sorting_1 {
            background-color: #e1f4ff !important;
        }
        table.dataTable.display tbody tr:hover,
        table.dataTable.order-column.hover tbody tr:hover {
            background-color: #e1f4ff !important;
        }
        .large-modal {
            max-width: 95% !important;
            width: 80% !important;
        }
        .hidemodelbtn {
            font-size: 18px !important;
            color: #7e8a9d !important;
        }
        .hidemodelbtn:focus {
            outline: none !important;
            box-shadow: none !important;
        }
        .hidemodelbtn:hover {
            cursor: pointer !important;
        }
        @media screen and (max-width: 580px) {
            .large-modal {
                max-width: 95% !important;
                width: 95% !important;
            }
            .hidemodelbtn {
                font-size: 15px !important;
                color: #7e8a9d !important;
            }
        }

        span {
            font-size: 14px;
            color: #192a3e;
        }
        input::placeholder {
            font-size: 14px;
        }
        input {
            font-size: 14px !important;
        }
        button {
            font-size: 14px !important;
        }
        select {
            font-size: 14px !important;
        }
        /* .table-responsive {
        position: relative;
        overflow-x: auto;
    } */

        .fixed-col {
            position: sticky !important;
            right: 0px !important;
            width: 10px !important;
            /* padding: 16px 5px !important; */
            background: white !important;
        }
        .select2-selection__rendered{
            width: 170px !important;
        }
        .dropdown-toggle::after {
            display: none !important;

        }
        td{
            line-height: 15px !important;
        }
        .showMessage {
            display: none;
            padding: 0px 10px 7px 10px;
            background: #c3e6cb;
            color: #155724;
            text-align: left;
        }
        .showMessage p{
            color: #155724;
        }
    </style>
@endsection
@section('section')
    <section class="dashboard_main pb-5 accountTable" style="margin-top: -16px">
        <div class="showMessage"></div>
        <div class="table_filter_header mb-4 accountListing mx-2">
            <h1 class="sub_heading">Accounts</h1>

            <div class="d-flex align-items-center gap-3">
                <div class="filters">
                    <input
                        type="search"
                        placeholder="Search"
                        name=""
                        id=""
                        class="account_search filter_search"
                    />

                    <form
                        method=""
                        action=""
                        id="importaccount"
                        style="margin-top: 3px"
                        class="file_upload_button_form upload_file_wrapper w-fit"
                        enctype="multipart/form-data"
                    >
                        @csrf

                        <label
                            class="btn primaryblue primaryblue22 transparent px-5"
                            for="import_file_data"
                            style="
                            border: 1px solid var(--lightgray);
                            background-color: white !important;
                        "
                        ><svg
                                style="margin-top: 3px"
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M5 5L8 8M8 8L11 5M8 8V1.25M14 4.44287C14.9161 5.19947 15.5 6.34405 15.5 7.625C15.5 9.90317 13.6532 11.75 11.375 11.75C11.2111 11.75 11.0578 11.8355 10.9746 11.9767C9.99654 13.6364 8.19082 14.75 6.125 14.75C3.0184 14.75 0.5 12.2316 0.5 9.125C0.5 7.57542 1.12659 6.17219 2.14021 5.15485"
                                    stroke="#51566C"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                            &nbsp;
                            <span style="font-size: 12px; color: #514f6e !important"
                            >Import</span
                            ></label
                        >
                        <input
                            class="input_file d-none"
                            type="file"
                            name="file"
                            id="import_file_data"
                            accept=".xls, .xlsx"
                        />
                    </form>

                    <form
                        method="POST"
                        id="export-opportunity"
                        action="{{ route(getRouteAlias() . '.export.accounts') }}"
                    >
                        @csrf
                        <button
                            type="submit"
                            {{ $accounts == 0 ? 'disabled' : '' }}
                            class="btn primaryblue transparent px-5"
                            style="border: 1px solid var(--lightgray); color: black !important"
                        >
                            <svg
                                width="16"
                                height="16"
                                style="margin-top: 3px"
                                viewBox="0 0 18 18"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M6 12L9 9M9 9L12 12M9 9V15.75M15 12.5571C15.9161 11.8005 16.5 10.656 16.5 9.375C16.5 7.09683 14.6532 5.25 12.375 5.25C12.2111 5.25 12.0578 5.1645 11.9746 5.0233C10.9965 3.36363 9.19082 2.25 7.125 2.25C4.0184 2.25 1.5 4.7684 1.5 7.875C1.5 9.42458 2.12659 10.8278 3.14021 11.8451"
                                    stroke="#51566C"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                            <span
                                class=""
                                style="font-size: 12px; color: #514f6e !important"
                            >&nbsp;Export</span
                            >
                        </button>
                    </form>
                    <select
                        name=""
                        id="account_filter"
                        class="select-small basic-single-select"
                    >
                        <option value="" selected>Filter</option>
                        @foreach($user as $item)

                            <option value="{{$item->id}}">
                                {{$item->first_name}} {{$item->last_name}}
                            </option>
                        @endforeach
                    </select>
                </div>

                <a
                    type="button"
                    data-toggle="modal"
                    data-target="#selectClientModal"
                    class="btn primaryblue text-decoration-none text-white"
                    style="border: 1px solid var(--lightgray);"
                >+ New Account</a
                >
            </div>
        </div>
        <div class="table-responsive">
            <table
                id="contact_list"
                class="table table-striped custom_datatable display yajra-datatable"
                style="width: 100%; position: relative !important;"
            >
                <thead>
                <tr>
                    <th>Account</th>
                    <!-- <th>Email</th>
                    <th>Phone</th> -->
                    <!-- <th>Website</th> -->

                    <th>Address</th>
                    <th>City</th>
                    <th>State</th>
                    <th>Zip</th>
                    <th>Account Owner</th>
                    <th class="text-center fixed-col" style="background-color: #DCF2FF !important;">Action</th>
                </tr>
                </thead>
                <tbody id=""></tbody>
            </table>
        </div>
        @include('layouts.admin.confirmation-modal')
        @include('layouts.partials.success-modal')
    </section>

    <!-- =add account Modal -->
    <div
        class="modal fade select-client"
        id="selectClientModal"
        data-backdrop="static"
        data-keyboard="false"
        tabindex="-1"
        aria-labelledby="selectClientModalLabel"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: #e1f4ff">
                    <h4 class="modal-title px-3" id="addItemModalLabel">
                        <b style="color: #0074d9 !important">Account</b>
                    </h4>
                    <button
                        type="button"
                        class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal"
                        aria-label="Close"
                        style="border: none; background-color: transparent"
                    >
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body" style="padding: 13px 20px !important">
                    <form id="" action="{{ $action }}" method="POST">
                        @csrf
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for="item_name"
                            >Company Name
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <!-- <div class="input-group-prepend">
                                    <button
                                        class="btn dropdown-toggle"
                                        style="
                                            border-bottom: 1px solid #ced4da;
                                            border-left: 1px solid #ced4da;
                                            border-top: 1px solid #ced4da;
                                            border-right: none;
                                            background-color: white;
                                        "
                                        type="button"
                                        id="dropdownMenuButton"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                    >
                                        Mr.
                                    </button>
                                    <div
                                        class="dropdown-menu"
                                        aria-labelledby="dropdownMenuButton"
                                    >
                                        <a class="dropdown-item" href="#">Mr.</a>
                                        <a class="dropdown-item" href="#">Mrs.</a>
                                        <a class="dropdown-item" href="#">Ms.</a>
                                        <a class="dropdown-item" href="#">Dr.</a>
                                    </div>
                                </div> -->
                                    <input
                                        type="text"
                                        id=""
                                        required
                                        class="form-control"
                                        name="company_name"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Enter company name"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for="">
                                Company Email
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="email"
                                    id=""
                                    name="company_email"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Email"
                                />
                                <!-- </div> -->
                            </div>
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for=""
                            >Company Phone
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="tel"
                                        minlength="12" maxlength="12"
                                        oninput="maskPhoneNumber(event)"
                                        id=""
                                        required
                                        name="company_phone"
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Phone number"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for=""> Website </span>
                                <input
                                    type="text"
                                    id=""
                                    name="website"
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Website"
                                />
                                <!-- </div> -->
                            </div>
                        </div>

                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 97%">
                            <span for="item_name"
                            >Account Owner
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >

                                <select
                                    name="account_owner"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id="category_list"
                                >
                                    <option value="" selected>Select Account Owner</option>
                                    @foreach($user as $item)
                                        <option value="{{$item->id}}">
                                            {{$item->first_name}} {{$item->last_name}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 97%">
                            <span for=""
                            >Address
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="account_address"
                                        name="address"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Address"
                                    />
                                </div>
                            </div>

                            <!-- </div>
            <div class="col"> -->
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for=""
                            >City
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="account_city"
                                        name="city"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="City"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="account_state"
                                    name="state"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="State"
                                />
                                <!-- </div> -->
                            </div>

                            <div class="form-group" style="width: 30%">
                            <span for="">
                                Zip Code
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="account_zip"
                                    name="zip"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Zip Code"
                                />
                                <!-- </div> -->
                            </div>
                        </div>
                        <div class="d-flex">
                            <input type="checkbox" id="sameaddress"><label for="sameaddress" style="color: #7C8091; margin-top: -4px; margin-left: 8px" >Billing Address Same As Account Address</label>

                        </div>
                        <div class="row " id="hidebillingaddress" style="padding: 13px 20px !important; display: block;">
                            <p><b>Billing information</b></p>
                            <hr />
                            <div class="row px-2" style="justify-content: space-around; display: flex">
                                <!-- <div class="col"> -->
                                <div class="form-group" style="width: 97%">
                            <span for="">Address <label class="required_field" for="">*</label></span>
                                    <div class="input-group">
                                        <input
                                            type="text"
                                            id="same_billing_address"
                                            name="billing_address"
                                            required
                                            class="form-control"
                                            style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                            placeholder="Address"
                                        />
                                    </div>
                                </div>

                                <!-- </div>
            <div class="col"> -->
                            </div>
                            <div
                                class="row px-2"
                                style="justify-content: space-around; display: flex"
                            >
                                <!-- <div class="col"> -->
                                <div class="form-group" style="width: 30%">
                            <span for=""
                            >City
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                    <div class="input-group">
                                        <input
                                            type="text"
                                            id="same_billing_city"
                                            name="billing_city"
                                            required
                                            class="form-control"
                                            style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                            placeholder="City"
                                        />
                                    </div>
                                </div>

                                <!-- </div>
                        <div class="col"> -->
                                <div class="form-group" style="width: 30%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                    <input
                                        type="text"
                                        id="same_billing_state"
                                        name="billing_state"
                                        required
                                        style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                        class="form-control"
                                        placeholder="State"
                                    />
                                    <!-- </div> -->
                                </div>

                                <div class="form-group" style="width: 30%">
                            <span for="">
                                Zip Code
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                    <input
                                        type="text"
                                        id="same_billing_zip"
                                        name="billing_zip"
                                        required
                                        style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                        class="form-control"
                                        placeholder="Zip Code"
                                    />
                                    <!-- </div> -->
                                </div>
                            </div>
                        </div>
                        <div class="d-flex" style="gap: 17px; justify-content: end">
                            <button
                                type="button"
                                data-dismiss="modal"
                                aria-label="Close"
                                class="btn px-5 py-2"
                                style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                                border-radius: 6px;
                            "
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                onclick="Add"
                                class="btn primaryblue px-5 py-2"
                            >
                                Add
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <!-- =edit account Modal -->
    <div
        class="modal fade select-client"
        id="selectClientModalUpdateAccount"
        data-backdrop="static"
        data-keyboard="false"
        tabindex="-1"
        aria-labelledby="selectClientModalLabel"
        aria-hidden="true"
    >
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: #e1f4ff">
                    <h4 class="modal-title px-3" id="addItemModalLabel">
                        <b style="color: #0074d9 !important"> Edit Account</b>
                    </h4>
                    <button
                        type="button"
                        class="btn-close hidemodelbtn px-3"
                        data-dismiss="modal"
                        aria-label="Close"
                        style="border: none; background-color: transparent"
                    >
                        <i class="fa fa-times" style="color: #7E8A9D;" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="modal-body" style="padding: 13px 20px !important">
                    <form
                        id=""
                        action="{{ URL::route(getRouteAlias() . '.accounts.update') }}"
                        method="POST"
                    >
                        @csrf
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for="item_name"
                            >Company Name
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="hidden"
                                        id="account_id"
                                        required
                                        class="form-control"
                                        name="account_id"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                    />

                                    <input
                                        type="text"
                                        id="company_name"
                                        required
                                        class="form-control"
                                        name="company_name"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Enter company name"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                        <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for="">
                                Company Email
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="email"
                                    id="company_email"
                                    name="company_email"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Email"
                                />
                                <!-- </div> -->
                            </div>
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                            <span for=""
                            >Company Phone
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="tel"
                                        id="company_phone"
                                        required
                                        minlength="12" maxlength="12"
                                        oninput="maskPhoneNumber(event)"
                                        name="company_phone"
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Phone number"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 47%">
                                <span for=""> Website </span>
                                <input
                                    type="text"
                                    id="website"
                                    name="website"
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Website"
                                />
                                <!-- </div> -->
                            </div>
                        </div>

                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 97%">
                            <span for="item_name"
                            >Account Owner
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >

                                <select
                                    name="account_owner"
                                    style="height: 30px !important"
                                    class="form-control"
                                    required
                                    id="account_owner"
                                >
                                    <option value="" selected>Select Account Owner</option>
                                    @foreach($user as $item)
                                        <option value="{{$item->id}}">
                                            {{$item->first_name}} {{$item->last_name}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 97%">
                            <span for=""
                            >Address
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="address"
                                        name="address"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Address"
                                    />
                                </div>
                            </div>

                            <!-- </div>
            <div class="col"> -->
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for=""
                            >City
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="city"
                                        name="city"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="City"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="state"
                                    name="state"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="State"
                                />
                                <!-- </div> -->
                            </div>

                            <div class="form-group" style="width: 30%">
                            <span for="">
                                Zip Code
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="zip"
                                    name="zip"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Zip Code"
                                />
                                <!-- </div> -->
                            </div>
                        </div>
                        <p><b>Billing information</b></p>
                        <hr />
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 97%">
                            <span for=""
                            >Address
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="billing_address"
                                        name="billing_address"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="Address"
                                    />
                                </div>
                            </div>

                            <!-- </div>
        <div class="col"> -->
                        </div>
                        <div
                            class="row px-2"
                            style="justify-content: space-around; display: flex"
                        >
                            <!-- <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for=""
                            >City
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <div class="input-group">
                                    <input
                                        type="text"
                                        id="billing_city"
                                        name="billing_city"
                                        required
                                        class="form-control"
                                        style="
                                        height: 30px !important;
                                        text-align: left;
                                    "
                                        placeholder="City"
                                    />
                                </div>
                            </div>

                            <!-- </div>
                    <div class="col"> -->
                            <div class="form-group" style="width: 30%">
                            <span for="">
                                State
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="billing_state"
                                    name="billing_state"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="State"
                                />
                                <!-- </div> -->
                            </div>

                            <div class="form-group" style="width: 30%">
                            <span for="">
                                Zip Code
                                <label class="required_field" for=""
                                >*</label
                                ></span
                            >
                                <input
                                    type="text"
                                    id="billing_zip"
                                    name="billing_zip"
                                    required
                                    style="
                                    height: 30px !important;
                                    text-align: left;
                                "
                                    class="form-control"
                                    placeholder="Zip Code"
                                />
                                <!-- </div> -->
                            </div>
                        </div>

                        <div class="d-flex" style="gap: 17px; justify-content: end">
                            <button
                                type="button"
                                data-dismiss="modal"
                                aria-label="Close"
                                class="btn px-5 py-2"
                                style="
                                color: #0074d9;
                                border: 1px solid #0074d9;
                                background-color: white;
                            "
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                onclick="addNewItemModel()"
                                class="btn primaryblue px-5 py-2"
                            >
                                Update
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!--Delete Modal -->
    @push('scripts')
        @include('organization.account.script')
    <script>
        document.getElementById('sameaddress').addEventListener('change', function () {
            const accountAddress = document.getElementById('account_address').value;
            const account_city = document.getElementById('account_city').value;
            const account_state = document.getElementById('account_state').value;
            const account_zip = document.getElementById('account_zip').value;
            const billingAddressField = document.getElementById('same_billing_address');
            const same_billing_city = document.getElementById('same_billing_city');
            const same_billing_state = document.getElementById('same_billing_state');
            const same_billing_zip = document.getElementById('same_billing_zip');
            const hidebillingaddress = document.getElementById('hidebillingaddress');

            if (this.checked) {
                // If checkbox is checked, copy the account address to billing address
                billingAddressField.value = accountAddress;
                same_billing_city.value = account_city;
                same_billing_state.value = account_state;
                same_billing_zip.value = account_zip;
                hidebillingaddress.style.display = 'none';

                // billingAddressField.readOnly = true; // Make the field read-only
            } else {
                // If unchecked, clear the billing address and make it editable
                hidebillingaddress.style.display = 'block';

                billingAddressField.value = '';
                same_billing_city.value = '';
                same_billing_state.value = '';
                same_billing_zip.value = '';
                // billingAddressField.readOnly = false;
            }
        });
    </script>
    <script>
        var datepickerInstance;
        $(document).ready(function () {
            datepickerInstance = $("#customDateAppend")
                .datepicker({
                    container: ".custom-appending",
                    format: "{{ getOrgDateFormat() }}", // Correct format tokens
                    // Other options for the datepicker
                })
                .data("datepicker");

            // Attach an event listener to the changeDate event
            $("#customDateAppend").on("changeDate", function () {
                datepickerInstance.hide(); // Hide the datepicker
            });
        });
        $(document).ready(function() {
            const table = $('#contact_list').DataTable();

            // Add 'fixed-col' class to the last <td> in each row after DataTables has rendered the rows
            table.on('draw', function() {
                $('#contact_list tbody tr').each(function() {
                    $(this).find('td:last-child').addClass('fixed-col');
                });
            });
        });

    </script>
    @endpush
@endsection
