@extends('layouts.admin.master')
@section('title', 'Financial tools')
@section('section')
    <style>
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        /* .select2-container--default .select2-results>.select2-results__options */

        .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
            width: 4px;
        }

        .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
            background: #888;
        }

        .revenue-title {
            color: #868686;
            font-family: "Cabin";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-transform: capitalize;
        }
        .dropdown-toggle::after {
            display: none !important;

        }
        .revenue-value {
            font-family: "Cabin";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            color: #192A3E;
        }
        button:focus{
            outline: none;
            box-shadow: none;
        }
        .absolute-error .select2-container, .absolute-error .select2-container--default .select2-selection--single{
            height: 40px !important;
        }
    </style>
    <section class="dashboard_main pb-5">

        <div class="table_filter_header mb-4 invoiceListing">
            <h2 class="sub_heading">Financial tools</h2>

            <div class="d-flex align-items-center gap-3">
                <div class="filters">
                    {{-- <input type="search" placeholder="Search" name="" id=""
                        class="invoice_search filter_search"> --}}
                    <div class="absolute-error position-relative " style="height: 100%;width:120px;">
                        <input class="input clearFiltersBtn d-none" value="Clear Filters" readonly style="cursor: pointer">
                    </div>
                    <div class="absolute-error position-relative " style="height: 100%;width:120px;">
                        <select name="month_selector" id="month-selector"
                            class="input basic-single-select cost_summary_month_filter">
                            <option value="" disabled selected>Month</option>
                            <option value="January">January</option>
                            <option value="February">Febuary</option>
                            <option value="March">March</option>
                            <option value="April">April</option>
                            <option value="May">May</option>
                            <option value="June">June</option>
                            <option value="July">July</option>
                            <option value="August">August</option>
                            <option value="September">September</option>
                            <option value="October">October</option>
                            <option value="November">November</option>
                            <option value="December">December</option>
                        </select>
                    </div>
                    <div class="absolute-error position-relative " style="height: 100%;width:120px;">
                        <select name="year_selector" id="yearSelect"
                            class="input basic-single-select cost_summary_year_filter">
                            <option value="" disabled selected>Yearly</option>
                            @foreach ($organizationYears as $item)
                                <option value="{{ $item }}">{{ $item }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <input type="hidden" class="TargetCompletedPercentageStatus" value="">
        <ul class="nav company-client_tabs mt-5" id="company-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link tab-btn active tab-hidden" id="company-tab-btn" data-toggle="pill"
                    data-target="#company-created" type="button" role="tab" aria-controls="company-created"
                    aria-selected="true">Summary</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link tab-btn tab-hidden" id="client-created-tab" data-toggle="pill"
                    data-target="#client-created" type="button" role="tab" aria-controls="client-created"
                    aria-selected="false">Cost Summary</button>
            </li>
        </ul>

        <div class="tab-content mt-4" id="jobSummary">
            <div class="tab-pane show active" id="company-created" role="tabpanel" aria-labelledby="company-tab-btn"
                tabindex="0">
                <div class="risk-department mt-24">
                    <div class="bar-chart">
                        <div class="chart-header">
                            <h2 class="heat-map-heading">Actual Vs Target</h2>
                            <ul class="legends">
                                {{-- <li class="legend-list" onclick="toggleDataRisk(1)"> --}}
                                <li class="legend-list">
                                    <span class="legend-span" id="moderate2"></span>
                                    <span class="legend-button" id="two2"></span>
                                </li>
                                {{-- <li class="legend-list" onclick="toggleDataRisk(0)"> --}}
                                <li class="legend-list">
                                    <span class="legend-span" id="low2"></span>
                                    <span class="legend-button" id="one1"></span>
                                </li>



                            </ul>
                            <div class="field">
                                <div class="absolute-error position-relative " style="height: 100%;width:120px;">
                                    <select name="date_selector" id="actual-vs-target-year"
                                        class="input basic-single-select " style="background: #F8F8F8;">
                                        @foreach ($organizationYears as $item)
                                            <option value="{{ $item }}">Year {{ $item }}</option>
                                        @endforeach
                                    </select>
                                    <!-- Hidden input field for the datepicker to attach to -->
                                    <input type="text" id="customDateInput" value="" style="display: none;">
                                </div>
                            </div>

                        </div>
                        <div id="riskChart"></div>
                    </div>
                    <div class="dughnut-chart position-relative">
                        <div class="chart-header mb-3 d-flex justify-content-between align-items-center gap-2">
                            <h2 class="heat-map-heading">Target Status</h2>

                            <div class="field">
                                <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                    <select name="date_selector" id="dateSelector"
                                        class="input basic-single-select targetPercentageSelector"
                                        style="background: #F8F8F8;">
                                        @foreach ($organizationYears as $item)
                                            <option value="{{ $item }}">Year {{ $item }}</option>
                                        @endforeach
                                    </select>
                                    <!-- Hidden input field for the datepicker to attach to -->
                                    <input type="text" id="customDateInput" value="" style="display: none;">
                                </div>
                            </div>

                        </div>
                        <canvas id="dughnutChart" style="width: 100%;object-fit: contain;"></canvas>
                        <span class="dughnu-value" id="dughtnValue"></span>
                        <ul class="dughnut-legends d-flex flex-column">


                            <li class="dughnut-list" onclick="toggleDoughnut(0)">
                                <div class="dughnut-value">
                                    <span class="box" id="boxbg"></span>
                                    <span class="text" id="underView"></span>
                                </div>
                                <span class="percentage collectedRevenue">0%</span>
                            </li>

                            <li class="dughnut-list" onclick="toggleDoughnut(1)">
                                <div class="dughnut-value">
                                    <span class="box" id="boxbg2"></span>
                                    <span class="text" id="pendings"></span>
                                </div>
                                <span class="percentage totalTarget"></span>
                            </li>

                        </ul>
                    </div>
                </div>

                <div class="risk-department mt-5 w-100">
                    <div class="bar-chart px-0">
                        <div class="chart-header" style="padding: 0px 24px;">
                            <h2 class="heat-map-heading">Target Table</h2>

                            <div class="d-flex align-items-center">
                                <p class="backlog m-0" style="padding-right: 4px">BackLog</p>
                                {{-- <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                    viewBox="0 0 13 13" fill="none">
                                    <path
                                        d="M6.5 0C10.0841 0 13 2.91594 13 6.50004C13 10.0841 10.0841 13 6.5 13C2.9159 13 1.33365e-06 10.0841 1.33365e-06 6.50004C1.33365e-06 2.91594 2.9159 0 6.5 0ZM6.5 11.8182C9.43249 11.8182 11.8182 9.43248 11.8182 6.50004C11.8182 3.56759 9.43249 1.18182 6.5 1.18182C3.56752 1.18182 1.18182 3.56759 1.18182 6.50004C1.18182 9.43248 3.56756 11.8182 6.5 11.8182Z"
                                        fill="#94959B" />
                                    <path
                                        d="M6.50036 2.75781C6.93472 2.75781 7.28809 3.11141 7.28809 3.54605C7.28809 3.98029 6.93472 4.33357 6.50036 4.33357C6.06601 4.33357 5.71264 3.98029 5.71264 3.54605C5.71264 3.11141 6.06601 2.75781 6.50036 2.75781ZM6.50029 5.51539C6.82663 5.51539 7.09119 5.77996 7.09119 6.1063V9.65175C7.09119 9.97809 6.82663 10.2427 6.50029 10.2427C6.17395 10.2427 5.90938 9.97809 5.90938 9.65175V6.1063C5.90938 5.77996 6.17395 5.51539 6.50029 5.51539Z"
                                        fill="#94959B" />
                                </svg> --}}
                                <p class="price m-0" style="padding-left: 9px;padding-right:25px">
                                    ${{ custom_number_format($unScheduleEstimates->sum('total_price'), 2) }}</p>





                                <div class="field">
                                    <div class="absolute-error position-relative" style="height: 100%;width:120px;">
                                        <select name="date_selector" id="dateSelector"
                                            class="input basic-single-select targetTable" style="background: #F8F8F8;">
                                            @foreach ($organizationYears as $item)
                                                <option value="{{ $item }}">Year {{ $item }}</option>
                                            @endforeach
                                        </select>
                                        <!-- Hidden input field for the datepicker to attach to -->
                                        <input type="text" id="customDateInput" value=""
                                            style="display: none;">
                                    </div>
                                </div>

                            </div>

                        </div>
                        <div class="table-responsive p-0 mt-3">
                            <table id="clients_Detail"
                                class="table table-striped custom_datatable display job-summary text-center"
                                style="width:100%">
                                <thead>
                                    <tr>
                                        <th>Month</th>
                                        <th>Revenue</th>
                                        <th>Target</th>
                                        <th>Target %</th>
                                    </tr>
                                </thead>
                                <tbody id="monthsTargetTable">


                                </tbody>

                            </table>
                        </div>
                    </div>

                    <div class="backog-container">
                        <div class="d-flex align-items-center gap-3">
                            <h2 class="backlog-heading">BackLog</h2>
                            {{-- <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"
                                fill="none">
                                <path
                                    d="M9 0C13.9626 0 18 4.03745 18 9.00005C18 13.9627 13.9626 18 9 18C4.0374 18 2.41399e-06 13.9627 2.41399e-06 9.00005C2.41399e-06 4.03745 4.0374 0 9 0ZM9 16.3636C13.0604 16.3636 16.3636 13.0604 16.3636 9.00005C16.3636 4.93975 13.0604 1.63636 9 1.63636C4.93964 1.63636 1.63637 4.93975 1.63637 9.00005C1.63637 13.0604 4.93969 16.3636 9 16.3636Z"
                                    fill="#003366" />
                                <path
                                    d="M9.00013 3.81836C9.60155 3.81836 10.0908 4.30796 10.0908 4.90976C10.0908 5.51101 9.60155 6.00018 9.00013 6.00018C8.39871 6.00018 7.90944 5.51101 7.90944 4.90976C7.90944 4.30796 8.39871 3.81836 9.00013 3.81836ZM9.00002 7.63654C9.45187 7.63654 9.8182 8.00287 9.8182 8.45472V13.3638C9.8182 13.8157 9.45187 14.182 9.00002 14.182C8.54817 14.182 8.18184 13.8157 8.18184 13.3638V8.45472C8.18184 8.00287 8.54817 7.63654 9.00002 7.63654Z"
                                    fill="#003366" />
                            </svg> --}}
                        </div>
                        <p class="backlog-description mt-1">Work not yet scheduled.
                        </p>
                        <div class="d-flex align-items-center justify-content-between" style="margin-top: 22px">
                            <p class="backlog-description">unscheduled Job</p>
                            <p class="backlog-description p-0" style="min-width:20%;">Amount</p>
                        </div>

                        <div class="d-flex align-items-center justify-content-between mt-2" style="margin-top: 8px;">
                            <p class="backlog-project">{{ count($unScheduleEstimates) }} Project</p>
                            <p class="backlog-project" style="min-width:20%;">
                                ${{ custom_number_format($unScheduleEstimates->sum('total_price'), 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {{-- <div class="filters">
                <select name="" id="select_filter"
                    class="select-small basic-single-select cost_summary_year_filter">
                    <option value="" selected disabled>Year</option>
                    @foreach ($organizationYears as $item)
                        <option value="{{ $item }}">{{ $item }}</option>
                    @endforeach
                </select>
            </div>
            <div class="filters">
                <select name="cost_summary_month_filter" id="select_filter"
                    class="select-small basic-single-select cost_summary_month_filter">
                    <option value="" selected disabled>Month</option>
                    <option value="January">January</option>
                    <option value="February">Febuary</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="August">August</option>
                    <option value="September">September</option>
                    <option value="October">October</option>
                    <option value="November">November</option>
                    <option value="December">December</option>
                </select>
            </div> --}}
            <div class="tab-pane fade" id="client-created" role="tabpanel" aria-labelledby="client-created-tab"
                tabindex="0">
                <div class="d-flex gap-3 flex-wrap ml-auto" style="width: fit-content">

                    <div class="d-flex gap-4">

                        @php
                            // $forcasted_gp = $operations->sum(function ($item) {
                            //     $result = number_format((float) str_replace(',', '', $item->grand_total ?? 0) - (float) optional($item->cost_summary)->equipment_cost2 ?? (0 - (float) optional($item->cost_summary)->labor3 ?? (0 - (float) optional($item->cost_summary)->hard_material4 ?? (0 - (float) optional($item->cost_summary)->plant_material ?? (0 - (float) optional($item->cost_summary)->other_jobCost ?? (0 - (float) optional($item->cost_summary)->sub_contractor ?? (0 - (float) optional($item->cost_summary)->accurals ?? 0)))))), 2);
                            // });

                            $forcasted_gp = $operations->sum(function ($item) {
                                $grand_total = isset($item->grand_total)
                                    ? (float) str_replace(',', '', $item->grand_total)
                                    : 0;
                                $equipment_cost = optional($item->cost_summary)->equipment_cost2 ?? 0;
                                $labor_cost = optional($item->cost_summary)->labor3 ?? 0;
                                $hard_material_cost = optional($item->cost_summary)->hard_material4 ?? 0;
                                $plant_material_cost = optional($item->cost_summary)->plant_material ?? 0;
                                $other_job_cost = optional($item->cost_summary)->other_jobCost ?? 0;
                                $sub_contractor_cost = optional($item->cost_summary)->sub_contractor ?? 0;
                                $accurals_cost = optional($item->cost_summary)->accurals ?? 0;

                                $result =
                                    $grand_total -
                                    $equipment_cost -
                                    $labor_cost -
                                    $hard_material_cost -
                                    $plant_material_cost -
                                    $other_job_cost -
                                    $sub_contractor_cost -
                                    $accurals_cost;

                                return $result;
                            });
                            // dd($forcasted_gp);

                            $laborHours = $operations->sum(function ($item) {
                                $plantMaterialSum = $item->getestimatePlantMaterial
                                    ->where('generate_estimate_id', $item->id)
                                    ->map(function ($material) {
                                        return $material->quantity + optional($material->material)->install ?? 0;
                                    })
                                    ->sum();

                                $hardMaterialSum = $item->getestimateHardMaterial
                                    ->where('generate_estimate_id', $item->id)
                                    ->map(function ($material) {
                                        return $material->quantity + optional($material->material)->labor ?? 0;
                                    })
                                    ->sum();

                                return $plantMaterialSum + $hardMaterialSum;
                            });
                        @endphp
                        @php
                            $forcasted_revenue = $operations->sum(function ($item) {
                                $cleaned_value = str_replace(',', '', $item->grand_total);
                                return is_numeric($cleaned_value) ? $cleaned_value : 0.0;
                            });
                        @endphp
                        <p class="revenue-title">$/HR:</p>
                        <p class="revenue-value hr_value">
                            {{-- @dd($laborHours) --}}
                            {{ number_format($laborHours == 0 ? 0 : $forcasted_revenue / $laborHours, 2) }}
                        </p>


                    </div>

                    <div class="d-flex gap-4">
                        {{-- @php
                            $forcasted_revenue = $operations->sum(function ($item) {
                                return is_numeric(number_format(str_replace(',', '', $item->grand_total), 2)) ? number_format(str_replace(',', '', $item->grand_total), 2) : '0.00';
                            });
                        @endphp --}}
                        @php
                            $forcasted_revenue = $operations->sum(function ($item) {
                                $cleaned_value = str_replace(',', '', $item->grand_total);
                                return is_numeric($cleaned_value) ? $cleaned_value : 0.0;
                            });
                        @endphp
                        <p class="revenue-title">Forecasted Revenue:</p>
                        <p class="revenue-value forcasted_revenue_value">${{ number_format($forcasted_revenue, 2) }}</p>



                    </div>

                    <div class="d-flex gap-4">
                        <p class="revenue-title">Forecasted GP:</p>
                        <p class="revenue-value forcastedGpSum">${{ number_format($forcasted_gp, 2) }}</p>
                    </div>

                    <div class="d-flex gap-4">
                        <p class="revenue-title">GP%:</p>
                        <p class="revenue-value forcastedGpSumPercentage">
                            {{-- {{ number_format($forcasted_gp / ($forcasted_revenue == 0 ? 0 : $forcasted_revenue), 2) }}%</p> --}}
                            {{ number_format($forcasted_revenue == 0 ? 0 : ($forcasted_gp / $forcasted_revenue) * 100, 0) }}%
                        </p>
                    </div>
                </div>
                <div class="table-responsive horizontalScroll p-0 mt-5">
                    <table id="clients_Detail" class="table table-striped custom_datatable display field-table"
                        style="width:100%">
                        <thead>
                            <tr>
                                <th>Sales Order #</th>
                                <th>Client Name</th>
                                <th>Project Name</th>
                                <th>Month Revenue Forecasted</th>
                                <th>Scheduled Date</th>
                                <th>Completed Date</th>
                                <th>Contract Amount</th>
                                <th>Labor Hours</th>
                                <th>Labor Burden Dollars</th>
                                <th>Equipment Cost</th>
                                <th>Labor</th>
                                <th>Hard Material</th>
                                <th>Plant Material</th>
                                <th>Other job cost</th>
                                <th>Sub Contractor</th>
                                <th class="bg-lightgreen">Hours</th>
                                <th class="bg-lightgreen">Equipmet Cost2</th>
                                <th class="bg-lightgreen">Labor3</th>
                                <th class="bg-lightgreen">Hard Material4</th>
                                <th class="bg-lightgreen">Plant Material</th>
                                <th class="bg-lightgreen">Other Job Cost</th>
                                <th class="bg-lightgreen">Sub Contractor</th>
                                <th class="bg-lightgreen">Accurals Needed</th>
                                <th>Forcasted GP$</th>
                                <th>Forcasted GP%</th>
                                <th>Sales Person</th>
                                <th>Estimator</th>
                                <th>Operation Manager</th>
                                <th class="bg-lightgreen">Po #</th>
                                <th class="bg-lightgreen">Work Type</th>
                                <th class="bg-lightgreen">Service Line</th>
                                <th>Note</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody class="costSummaryTable">
                            @include('organization.financial_tools.partials.cost_summary')
                        </tbody>
                    </table>


                </div>

            </div>
        </div>
        <!-- view note modal -->
        <div class="modal fade propert-modal" id="ViewCostSummaryNoteModal" data-backdrop="static"
            data-keyboard="false" tabindex="-1" aria-labelledby="requestChangeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="requestChangeModalLabel">Add Note</h1>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="costSummaryNote">
                            <div class="body-wraped">
                                <div class="filed-wraper" id="Notes-preview">
                                    <textarea class="modal-textarea CostSummaryNotes" name="notes" id="CostSummaryNotes" placeholder="Enter notes"></textarea>
                                </div>
                                <input type="hidden" name="operation_id" class="costSummaryId" value="">
                                <button type="submit" class="add-btn">Add Note</button>
                        </form>

                    </div>
                </div>

            </div>
        </div>
        </div>


    </section>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://code.highcharts.com/highcharts.js"></script>
        <script src="https://code.highcharts.com/highcharts-more.js"></script>
        @include('organization.financial_tools.script')
        <script>
            const tabButtons = document.querySelectorAll('.nav-link');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    localStorage.setItem('activeTab', button.id);
                });
            });

            const storedActiveTab = localStorage.getItem('activeTab');
            if (storedActiveTab) {
                const tabToActivate = document.querySelector(`#${storedActiveTab}`);
                const tabContentId = tabToActivate.getAttribute('data-target');

                tabButtons.forEach(button => {
                    button.classList.remove('active');
                });
                const tabContents = document.querySelectorAll('.tab-pane');
                tabContents.forEach(content => {
                    content.classList.remove('active', 'show');
                });

                tabToActivate.classList.add('active');
                const tabContent = document.querySelector(tabContentId);
                if (tabContent) {
                    tabContent.classList.add('active', 'show');
                }
            }

            const tabContents = document.querySelectorAll('.tab-hidden');
            tabContents.forEach(content => {
                content.classList.remove('tab-hidden');
            });
        </script>
        <script>
            $(document).ready(function() {
                $('.table-field').on('keyup', function() {
                    var row = $(this).closest('tr');
                    var plantMaterial = parseFloat(row.find('[name="plant_material"]').val()) || 0;
                    var otherJobCost = parseFloat(row.find('[name="other_jobCost"]').val()) || 0;
                    var subContractor = parseFloat(row.find('[name="sub_contractor"]').val()) || 0;
                    var accurals = parseFloat(row.find('[name="accurals"]').val()) || 0;
                    var grandTotal = parseFloat(row.find('.grand_total').text().replace(/[\$,]/g, '')) || 1;
                    var equipmentCost2 = parseFloat(row.find('[name="equipment_cost2"]').val()) || 0;
                    var labor3 = parseFloat(row.find('[name="labor3"]').val()) || 0;
                    var hardMaterial4 = parseFloat(row.find('[name="hard_material4"]').val()) || 0;
                    var forcastedGp = grandTotal - equipmentCost2 - labor3 - hardMaterial4 - plantMaterial -
                        otherJobCost - subContractor - accurals;
                    var forcastedGp_percentage = ((grandTotal - equipmentCost2 - labor3 - hardMaterial4 -
                        plantMaterial -
                        otherJobCost - subContractor - accurals) / grandTotal) * 100
                    row.find('.forcasted_gp').text('$' + forcastedGp.toFixed(2));
                    row.find('.forcasted_gp_percentage').text(forcastedGp_percentage.toFixed(2) + '%');
                });
            });
        </script>
    @endpush
@endsection
