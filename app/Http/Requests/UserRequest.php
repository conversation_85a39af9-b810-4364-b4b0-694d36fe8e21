<?php

namespace App\Http\Requests;

use App\Rules\FilterEmail;
use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'email' => ['required', 'email', 'max:255', 'unique:users,email,'.decodeID($this->id), new FilterEmail],
            'mobile_no' => 'required|phone_format',
            'role_id' => 'required|array', // Please See Role Seeder for Check ID's
            'role_id.*' => 'in:1,2,3,5',
            'image' => 'nullable|image',
        ];
    }

    public function messages()
    {
        return [
            'required' => 'This field is required.',
            'size' => 'Image size must not be greater than 2 mb',
        ];
    }
}
