<?php

namespace App\Http\Requests\Client;

use Illuminate\Foundation\Http\FormRequest;

class ClientEstimateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'project_name' => 'required|max:150',
            'type' => 'required|in:phone,text,email',
            'property_name' => 'required|max:150',
            'address1' => 'required|max:150',
            'address2' => 'nullable|max:150',
            'state' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'city' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'czip' => 'required|integer',
            'description' => 'nullable|max:2000',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function messages()
    {
        return [
            'type.required' => 'The Communication type is Required',
            'type.in' => 'The Communication type is invalid',
            'address1.required' => 'The Address Field is Required',
            'address1.max' => 'The Address Field max length is :max',
            'czip.required' => 'The Zip Code Field is Required',
            'czip.integer' => 'Invalid Zip Code',
        ];
    }
}
