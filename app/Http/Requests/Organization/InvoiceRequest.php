<?php

namespace App\Http\Requests\Organization;

use Illuminate\Foundation\Http\FormRequest;

class InvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // validation for if Email Send
            'payment_mode' => 'required',
            'sentEmail' => 'sometimes|in:true,false',
            'send-email.*' => 'required_if:sentEmail,true|email',
            'send-message' => 'required_if:sentEmail,true',
            'send-subject' => 'required_if:sentEmail,true',
            'select_file.*' => 'nullable',
            'copy' => 'sometimes|in:yes',
            'invoiceAttachment' => 'sometimes|in:yes',
            'estimateAttachment' => 'sometimes|in:yes',
            'bcc_email.*' => 'sometimes|email',
            'cc_email.*' => 'sometimes|email',

            // Invoice
            'subject' => 'required|string|max:255',
            'issue_date' => 'required',
            'tax' => 'nullable|lt:101',
            'discount_method' => 'nullable|in:per,amount',
            'discount' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    $discountMethod = request()->input('discount_method');
                    if ($discountMethod === 'per' && ($value > 100)) {
                        $fail("Discount cannot be greater than 100% when using 'per' as the discount method.");
                    }
                },
            ],
            'due_date' => [
                'required',
                function ($attribute, $value, $fail) {
                    $issueDate = strtotime(request()->input('issue_date'));
                    $dueDate = strtotime($value);
                    if ($dueDate == true && $issueDate >= $dueDate) {
                        $fail('Due date must be after the issue date.');
                    }
                },
            ],
        ];
    }

    public function messages()
    {
        return [
            'subject.required' => 'Subject cannot be empty.',
            'issue_date.required' => 'Issue date cannot be empty.',
            'due_date.required' => 'Please select a valid due date.',
        ];
    }
}
