<?php

namespace App\Http\Requests\Organization;

use Illuminate\Foundation\Http\FormRequest;

class JobPackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'equipmentVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'equipmentCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'equipmentPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'equipmentEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
            'laborVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'laborCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'laborPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'laborEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
            'plantMaterialVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'plantMaterialCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'plantMaterialPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'plantMaterialEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
            'hardMaterialVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'hardMaterialCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'hardMaterialPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'hardMaterialEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
            'otherCostVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'otherCostCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'otherCostPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'otherCostEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
            'subcontractorVName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'subcontractorCName.*' => ['sometimes', 'nullable', 'string', 'max:255'],
            'subcontractorPhone.*' => ['sometimes', 'nullable', 'phone_format'],
            'subcontractorEmail.*' => ['sometimes', 'nullable', 'email', 'max:255'],
        ];

        if (request()->has('po_number')) {
            // If 'po_number' exists, validate it as numeric
            $rules['po_number'] = ['nullable', 'string', 'max:255'];
        }

        if (request()->has('scheduled_hours')) {
            $rules['scheduled_hours.*.*'] = ['nullable', 'numeric'];
        }

        if (request()->has('actual_hours')) {
            $rules['actual_hours.*.*'] = ['nullable', 'numeric'];
        }

        return $rules;

    }
}
