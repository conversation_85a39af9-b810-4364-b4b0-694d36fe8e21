<?php

namespace App\Http\Requests\Organization;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class StoreOpportunityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        // Ensure contact_ids is always available as null if not set
        $this->merge([
            'contact_ids' => $this->contact_ids ?? null,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'property_name' => 'required|string|max:255',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip_code' => 'required|string|max:10',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'title_role' => 'required|string|max:255',
            'phone_number' => 'required|string|max:12',
            'second_phone' => 'nullable|string|max:12',
            'opportunity_name' => 'required|string|max:255',
            'opportunity_type' => 'sometimes|string',
            'customer_bid_form' => 'required|in:yes,no',
            'bid_due_date' => 'required|date',
            'net_new' => 'required|in:yes,no',
            'lead_source' => 'nullable|string|max:255',
            'opportunity_owner_id' => 'nullable',
            'company_id' => 'required',
            'contact_ids' => 'nullable|integer|exists:contacts,id',
            'sale_person_ids' => 'nullable',
            'estimator_ids' => 'nullable',
            'request_information' => 'nullable|string|max:3000',
            'images.*' => 'required',
            'email' => 'required',
            'division' => 'required',
            'service_line' => 'required',
            'price_model' => 'required',
        ];

        // Add email validation if the flag `skip_email_validation` is not set or false
        if (! $this->skipEmailValidation()) {
            $rules['email'] = [
                'required',
                'email',
                Rule::unique('contacts', 'email')
                    ->when($this->contact_ids, function ($rule) {
                        // Ignore the record with specific contact ID
                        return $rule->ignore($this->contact_ids)
                            ->where(function ($query) {
                                $query->where('organization_id', getOrganizationId());
                            });
                    }),
                function ($attribute, $value, $fail) {
                    // Check if the email exists in the users table
                    $existsInUsers = DB::table('users')->where('email', $value)->exists();
                    if ($existsInUsers) {
                        $fail('This email is already registered in the users table.');
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * Customize the validation logic.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $contactIdToIgnore = $this->getContactIdToIgnore();

        // Merge the contactIdToIgnore into the request so it can be accessed in the validation rules
        $this->merge(['contactIdToIgnore' => $contactIdToIgnore]);
    }

    /**
     * Determine whether to skip email validation.
     *
     * @return bool
     */
    protected function skipEmailValidation()
    {
        // You can pass a flag (e.g., skip_email_validation) from the request
        return $this->input('skip_email_validation', false);
    }

    /**
     * Retrieve the contactIdToIgnore based on your existing logic.
     *
     * @return mixed
     */
    protected function getContactIdToIgnore()
    {
        $contact = DB::table('contacts')
            ->where('organization_id', getOrganizationId())
            ->where('first_name', $this->first_name)
            ->where('last_name', $this->last_name)
            ->where('title', $this->title_role)
            ->where('phone_number', $this->phone_number)
            ->where('email', $this->email)
            ->where('account', $this->company_id)
            ->first();

        return $contact ? $contact->id : null;
    }
}
