<?php

namespace App\Http\Requests\Organization;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerIssueRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'issue_category' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'assign_to' => 'required|string|max:255',
            'assigned_job' => 'required|string|max:255',
            'issue_created_by' => 'required|integer|max:255',
            // 'property_name' => 'required|string|max:255',
            // 'address1' => 'required|string|max:255',
            // 'address2' => 'nullable|string|max:255',
            // 'city' => 'required|string|max:255',
            // 'state' => 'required|string|max:255',
            // 'zip_code' => 'required|string|max:10',
            // 'first_name' => 'required|string|max:255',
            // 'last_name' => 'required|string|max:255',
            // 'title_role' => 'nullable|string|max:255',
            // 'phone_number' => 'required|string|max:15',
            // 'second_phone' => 'nullable|string|max:20',
            // 'email' => 'required|email|max:255',
            // 'second_email' => 'nullable|email|max:255',
            // 'mailing_address' => 'required|string|max:255',
            // 'role' => 'nullable|string|max:255',
            // 'company_id' => 'required',
        ];
    }
}
