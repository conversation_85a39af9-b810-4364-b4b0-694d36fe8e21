<?php

namespace App\Http\Requests;

use App\Rules\FilterEmail;
use App\Rules\UniqueEmailInOrganization;
use Illuminate\Foundation\Http\FormRequest;

class ClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $organizationId = getOrganizationId();

        return [
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'company_name' => 'nullable|max:150|unique:clients,company_name,'.decodeID($this->id),
            // 'company_name' => 'required|max:150|unique:clients,company_name,' . decodeID($this->id),
            'title' => 'max:100',
            // 'email' => 'required|email|max:255|unique:clients,email,' . decodeID($this->id).'|unique:users,email',
            'email' => [
                'required',
                'email',
                'max:255',
                new UniqueEmailInOrganization($organizationId, decodeID($this->id)),
                new FilterEmail,
            ],
            'alt_email' => 'nullable|email|max:255|unique:clients,alternate_email,'.decodeID($this->id),
            'off_num' => 'required|phone_format',
            'mob_num' => 'required|phone_format',
            'alt_num' => 'nullable|phone_format',
            'state' => 'required|max:100',
            'city' => 'required|max:100',
            'zip' => 'required|alpha_num',
            'property_address' => 'required|max:150',
            'property_address2' => 'nullable|max:150',
            'billing_address' => 'required_without:isSameBilling',
            'billing_address2' => 'nullable',
            'state2' => 'required_without:isSameBilling',
            'city2' => 'required_without:isSameBilling',
            'zip2' => 'required_without:isSameBilling|alpha_num|nullable',
            'image' => 'nullable|mimes:jpeg,png,jpg,svg,webp,|max:2048',

        ];
    }

    public function messages()
    {
        return [
            'required' => 'This field is required.',
            'required_without' => 'This field is required when billing address is not same.',
            'alt_num.digits_between' => 'Number length should between 9 to 20 characters.',
            'mob_num.digits_between' => 'Number length should between 9 to 20 characters.',
            'off_num.digits_between' => 'Number length should between 9 to 20 characters.',
            'regex' => 'Invalid Format',
            'zip2.numeric' => 'The zip code must be a number',
            'zip.numeric' => 'The zip code must be a number',
            'image.max' => 'The :attribute must not be greater than 2MB.',
        ];
    }
}
