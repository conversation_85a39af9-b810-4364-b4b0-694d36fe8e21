<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EstimateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'project_name' => 'required|max:150',
            'job_name' => 'required|max:255',
            'job_number' => 'required|max:255',
            'lead_source' => 'required|max:150',
            'probability' => 'required',
            'property_name' => 'required|max:150',
            'address1' => 'required|max:150',
            'address2' => 'nullable|max:150',
            'state' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'city' => 'required|regex: /[a-zA-Z]+$/|max:100',
            'zip' => 'required|alpha_num',
            'sale_person_id' => 'required',
            'estimator_id' => 'required',
            'description' => 'nullable|max:500',
            'file' => 'nullable|mimes:jpeg,png,jpg,svg,pdf,xls,xlsx,doc,docx|max:5048',

        ];
    }

    public function messages()
    {
        return [
            'required' => 'This field is required.',
        ];
    }
}
