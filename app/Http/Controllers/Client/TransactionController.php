<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    public function transactions(Request $request, $organization_id)
    {
        $userId = auth()->user('clients')->id;
        if ($request->ajax()) {
            $query = Transaction::whereHas('invoice', function ($query) use ($userId) {
                $query->where('client_id', $userId);
            })->latest()->select(['id', 'invoice_id', 'created_at', 'payment_method', 'status']);

            return DataTables::eloquent($query)
                ->addColumn('invoice_number', function (Transaction $transaction) {
                    return '<a href="'.route('client.invoice.view', [request()->route('organization_id'), encodeID($transaction->invoice->id)]).'" style="text-decoration:none" class="invoice_number">'.optional($transaction->invoice)?->invoice_number.'</a>';
                })
                ->addColumn('invoice_subject', function (Transaction $transaction) {

                    return optional($transaction->invoice)?->subject;
                })
                ->addColumn('invoice_issue_date', function (Transaction $transaction) {
                    return customDateFormat(Carbon::parse(optional($transaction->invoice)?->issue_date));
                })
                ->addColumn('transaction_date', function (Transaction $transaction) {
                    return customDateFormat($transaction?->created_at);
                })
                ->editColumn('payment_method', function (Transaction $transaction) {
                    return custom_title_case($transaction->payment_method);
                })

                ->addColumn('amount', function (Transaction $transaction) {
                    return '$'.optional($transaction->invoice)?->total;
                })
                ->addColumn('status', function (Transaction $transaction) {
                    return '<span class="approved status">'.ucfirst($transaction->status).'</span>';
                })
                ->filterColumn('invoice_subject', function ($query, $keyword) {
                    $query->whereHas('invoice', function ($q) use ($keyword) {
                        $q->where('subject', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->filterColumn('invoice_number', function ($query, $keyword) {
                    $query->whereHas('invoice', function ($q) use ($keyword) {
                        $q->where('invoice_number', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->rawColumns(['status', 'invoice_number'])
                ->only(['invoice_number', 'invoice_subject', 'invoice_issue_date', 'transaction_date', 'payment_method', 'amount', 'status'])
                ->toJson();
        }

        return view('client.transactions.index');
    }
}
