<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\ClientEstimateAction;
use App\Models\Estimate;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateItem;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateStatus;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\Labor;
use App\Models\Opportunity;
use App\Models\User;
use App\Services\EmailService;
use App\Services\EstimateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class GenerateEstimateController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $estimates = Opportunity::show()->with(['propertyInformation', 'generateEstimate.clientAction'])
                ->where(['contact_id' => auth('client')->user()->id])
                ->whereHas('generateEstimate', function ($q) {
                    $q->where('is_complete', true)->where('total_price', '>', 0);
                })
                ->when(request('status'), function ($query) {
                    $query->whereHas('generateEstimate', function ($q) {
                        if (request('status') == 'proposed') {
                            $q->where('client_status', GenerateEstimate::$CLIENT_PROPOSED_STATUS);
                        } elseif (request('status') == 'reject') {
                            $q->where('client_status', GenerateEstimate::$CLIENT_REJECT_STATUS);
                        } elseif (request('status') == 'approve') {
                            $q->where('client_status', GenerateEstimate::$CLIENT_APPROVE_STATUS);
                        } elseif (request('status') == 'request_change') {
                            $q->where('client_status', GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS);
                        }
                    });
                })->latest()->select('job_no as er_no', 'id', 'organization_id', 'opportunity_name', 'property_id');

            return DataTables::eloquent($estimates)
                ->filterColumn('propertyInformation', function ($query, $keyword) {
                    $query->wherehas('propertyInformation', function ($q) use ($keyword) {
                        $q->where('name', 'LIKE', '%'.$keyword.'%')->orWhere('address1', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->filterColumn('description', function ($query, $keyword) {
                    $query->where('opportunity_name', 'LIKE', '%'.$keyword.'%')->orWhere('description', 'LIKE', '%'.$keyword.'%');
                })
                ->addColumn('client_status', function (Opportunity $estimate) {
                    if ($estimate->generateEstimate->status == GenerateEstimate::$PROPOSED) {
                        if ($estimate->generateEstimate->client_status == GenerateEstimate::$CLIENT_PROPOSED_STATUS || $estimate->generateEstimate->client_status == GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS) {
                            return ' <div class="status primary">'.custom_title_case($estimate->generateEstimate->client_status).'</div>';
                        } elseif ($estimate->generateEstimate->client_status == GenerateEstimate::$CLIENT_REJECT_STATUS) {
                            return ' <div class="status danger">'.ucfirst($estimate->generateEstimate->client_status).'</div>';
                        } elseif ($estimate->generateEstimate->client_status == GenerateEstimate::$CLIENT_APPROVE_STATUS) {
                            return ' <div class="status success">'.ucfirst($estimate->generateEstimate->client_status).'</div>';
                        }
                    } elseif ($estimate->generateEstimate->status == 'won') {
                        return ' <div class="status success">'.custom_title_case(GenerateEstimate::$CLIENT_APPROVE_STATUS).'</div>';
                    } else {
                        return ' <div class="status danger">'.custom_title_case(GenerateEstimate::$CLIENT_REJECT_STATUS).'</div>';
                    }
                })->addColumn('property_name', function (Opportunity $estimate) {
                    // dd($estimate);
                    return $estimate->propertyInformation ? '
                            <h2 class="estimate-title">'.optional($estimate->propertyInformation)?->name.'</h2>
                            <p class="estimate-detail">'.optional($estimate->propertyInformation)?->address1.'</p>
                            ' : '';
                })->editColumn('description', function (Opportunity $estimate) {
                    return '
                                <h2 class="estimate-title">'.$estimate->opportunity_name.'</h2>

                            ';
                })->editColumn('recieved_at', function (Opportunity $estimate) {
                    return optional($estimate->generateEstimate)?->created_at ?: '';
                })->addColumn('grand_total', function (Opportunity $estimate) {
                    if ($estimate->generateEstimate?->grand_total && $estimate->generateEstimate?->grand_total > 0) {
                        return '$'.custom_number_format($estimate->generateEstimate->grand_total);
                    } elseif ($estimate->generateEstimate?->total_price) {
                        return '$'.custom_number_format($estimate->generateEstimate->total_price);
                    } else {
                        return '---';
                    }
                })->addColumn('action', function (Opportunity $estimate) {
                    return '<a class="view-request-btn2" style="border-radius:32px;"
                    href="'.route('client.estimates.view', [request()->route('organization_id'), encodeId($estimate->id)]).'">View
                    Estimate</a>';
                })

                ->rawColumns(['client_status', 'action', 'description', 'property_name', 'recieved_at', 'grand_total'])
                ->only(['er_no', 'description', 'property_name', 'recieved_at', 'grand_total', 'action', 'client_status'])
                ->toJson();
        }

        return view('client.estimates.index');
    }

    public function view(Request $request, $organization_id, $id)
    {

        $estimate = Opportunity::show()->with(['generateEstimate.clientAction', 'propertyInformation'])->whereHas('generateEstimate', function ($q) {
            $q->where('is_complete', true);
        })->where('id', decodeID($id))->firstorfail();
        // Check Reccord Owner
        $this->authorize('client-view-estimate', $estimate, 'client');
        if (is_object($estimate)) {
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
            $estimateLabor = EstimateLabor::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
            $estimateHardMaterial = EstimateHardMaterial::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
            $estimatePlantMaterial = EstimatePlantMaterial::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $estimate->generateEstimate->id)->get();
        } else {
            $estimateSubContractor = $estimateOtherCost = $estimatePlantMaterial = $estimateHardMaterial = $estimateLabor = $estimateMaterial = [];
        }
        $laborBurden = Labor::where(
            'organization_id',
            $organization_id
        )->value('labor_burden');
        $estimations = EstimateItem::where('opportunity_id', decodeId($id))->get();
        $data = [
            'estimate' => $estimate,
            'estimateMaterial' => $estimateMaterial,
            'estimateLabor' => $estimateLabor,
            'estimateHardMaterial' => $estimateHardMaterial,
            'estimatePlantMaterial' => $estimatePlantMaterial,
            'estimateOtherCost' => $estimateOtherCost,
            'estimateSubContractor' => $estimateSubContractor,
            'laborBurden' => $laborBurden,
            'estimations' => $estimations,
        ];

        if ($request->session()->has('success')) {
            $request->session()->flash('success', $request->session()->get('success') ?? 'Success');
        }
        if ($request->session()->has('error')) {
            $request->session()->flash('error', $request->session()->get('error') ?? 'error');
        }

        return view('client.estimates.detail')->with($data);
    }

    public function downloadEstimate($organization_id, $id)
    {

        $estimate = Opportunity::where('id', decodeID($id))->first();
        // Check Reccord Owner
        // dd(auth('client')->user()?->parent_id);
        $this->authorize('client-view-estimate', $estimate, 'client');
        $pdf = EstimateService::preparePdf($estimate->id, auth('client')->user()?->parent_id)['pdf'];
        // dd($pdf);
        // return $this->onlyDownloadEstimate($id);
        // return $pdf;
        // dd($pdf);
        set_time_limit(300); // Increase execution time to 5 minutes

        return $pdf->download('estimate_invoice.pdf');

    }

    public function onlyDownloadEstimate($id)
    {
        $organizationId = auth('client')->user()?->parent_id;

        return EstimateService::preparePdf(decodeId($id), $organizationId)['pdf'];
    }

    public function approveEstimate(Request $request, $organization_id, $id)
    {
        $request->validate([
            'signature' => 'required|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $estimate = Opportunity::with('generateEstimate')->where('id', decodeID($id))->firstorfail();
            $this->authorize('client-view-estimate', $estimate, 'client');

            // check If its status proposed
            if ($estimate->generateEstimate->client_status != GenerateEstimate::$CLIENT_PROPOSED_STATUS) {
                DB::commit();

                return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate  status is already changed!');
            }

            $estimateAction = ClientEstimateAction::updateOrCreate([
                'client_id' => auth('client')->user()->id,
                'generate_estimate_id' => $estimate->generateEstimate->id,
            ], [
                'status' => GenerateEstimate::$CLIENT_APPROVE_STATUS,
                'signature_text' => $request->signature,
                'signature_date' => now(),
            ]);

            $generate_estimate = GenerateEstimate::where('opportunity_id', $estimate->id)->firstorfail();
            $generate_estimate->update([
                'status' => GenerateEstimate::$WON,
                'won_date' => now(),
            ]);

            if ($estimate->sales_order_number == 0 || $estimate->sales_order_number == null) {
                $estimate->sales_order_number = DB::table('opportunities')->where('organization_id', $estimate->organization_id)->max('sales_order_number') + 1;
                $estimate->save();
            }

            $generateEstimate = $this->updateClientEstimateStatus(decodeID($id), GenerateEstimate::$CLIENT_APPROVE_STATUS, $estimateAction);
            $generateEstimate->loadMissing('client.organization', 'opportunityid:id,job_no');
            $company_email_template = 'email_template.client.company-estimate-approve-notify';
            $client_email_template = 'email_template.client.client-estimate-approve-notify';
            $payload['company_name'] = $generateEstimate->client->organization->company_name;
            $payload['company_phone_no'] = $generateEstimate->client->phone_number;
            $payload['company_address'] = $generateEstimate->client->mailing_address;
            $payload['primary_color'] = $generateEstimate->client->organization->primary_color;
            $payload['company_website'] = $generateEstimate->client->accountget?->website;
            $payload['company_email'] = $generateEstimate->client->organization->email;
            $payload['client_name'] = $generateEstimate->client->company_name;
            $payload['org_image'] = $generateEstimate->client->organization->profile_photo_path ? asset('storage/user_images/'.$generateEstimate->client->organization->profile_photo_path) : null;
            $payload['description'] = $estimateAction->description;
            $payload['amount'] = $generate_estimate->grand_total;
            $payload['sale_order_number'] = $generateEstimate->opportunityid->job_no;
            $files['dompdf'] = true;
            $files['serviceClass'] = 'App\Services\EstimateService';
            $files['classParams'] = [$generate_estimate->opportunity_id, $generateEstimate->client->organization_id];
            $files['attachment_name'] = 'EstimateInvoice.pdf';

            // Check If Class and Method Exists
            if (checkClassHasMethod($files['serviceClass'], 'preparePdf')) {
                EmailService::send($client_email_template, 'Thank you for approving Estimate '.$generateEstimate->opportunityid->job_no, $generateEstimate->client->email, $payload, true, $files);
            }
            // dd($payload);
            EmailService::send($company_email_template, 'Congratulations! Estimate '.$generateEstimate->opportunityid->job_no.' is approved', $generateEstimate->client->organization->email, $payload, true);
            $totalPriceSumss = DB::table('estimate_items')
                ->where('opportunity_id', decodeID($id))
                ->sum('total_price');
            EstimateStatus::create([
                'generate_estimate_id' => $generateEstimate->id,
                'status_name' => 'approve',
                'created_at' => now(),
                'opportunity_id' => decodeID($id),
                'estimate_total_price' => $totalPriceSumss,
            ]);
            Opportunity::where('id', decodeID($id))->update([
                'status' => 5,
            ]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate has not been approved successfully!');
        }

        return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('success', 'Estimate has been approved successfully!');
    }

    public function rejectEstimate(Request $request, $organization_id, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|in:budget,timing,timing,pricing,other',
            'reason_for_change' => ['required_if:rejection_reason,other', 'nullable', 'string'],
        ]);
        DB::beginTransaction();
        try {
            $estimate = Opportunity::with('generateEstimate')->where('id', decodeID($id))->firstorfail();

            $this->authorize('client-view-estimate', $estimate, 'client');

            // check If its status proposed
            if ($estimate->generateEstimate->client_status != GenerateEstimate::$CLIENT_PROPOSED_STATUS) {
                DB::commit();

                return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate  status is already changed!');
            }

            $estimateAction = ClientEstimateAction::updateOrCreate([
                'client_id' => auth('client')->user()->id,
                'generate_estimate_id' => $estimate->generateEstimate->id,
            ], [
                'status' => GenerateEstimate::$CLIENT_REJECT_STATUS,
                'description' => ($request->rejection_reason == 'other') ? $request->reason_for_change : $request->rejection_reason,
            ]);

            $generate_estimate = GenerateEstimate::where('opportunity_id', $estimate->id)->firstorfail();
            $generate_estimate->update([
                'status' => GenerateEstimate::$LOST,
                'reason' => $request->rejection_reason,
                'suggestion' => $estimateAction->description,
            ]);

            $generateEstimate = $this->updateClientEstimateStatus(decodeID($id), GenerateEstimate::$CLIENT_REJECT_STATUS, $estimateAction);
            $generateEstimate->loadMissing('client.organization', 'opportunityid:id,job_no');
            $company_email_template = 'email_template.client.company-estimate-reject-notify';
            $client_email_template = 'email_template.client.client-estimate-reject-notify';
            $payload['company_name'] = $generateEstimate->client->organization->company_name;
            $payload['company_phone_no'] = $generateEstimate->client->phone_number;
            $payload['company_address'] = $generateEstimate->client?->mailing_address;
            $payload['primary_color'] = $generateEstimate->client->organization->primary_color;
            $payload['company_website'] = $generateEstimate->client->accountget?->website;
            $payload['company_email'] = $generateEstimate->client->organization->email;
            $payload['client_name'] = $generateEstimate->client->first_name.' '.$generateEstimate->client->last_name;

            $payload['org_image'] = $generateEstimate->client->organization->profile_photo_path ? asset('storage/user_images/'.$generateEstimate->client->organization->profile_photo_path) : null;
            $payload['description'] = $estimateAction->description;
            $payload['sale_order_number'] = $generateEstimate->opportunityid->job_no;
            $payload['amount'] = $generateEstimate->grand_total;
            // $payload['date'] = now()->format('Y/m/d');
            $payload['date'] = now();

            $files['dompdf'] = true;
            $files['serviceClass'] = 'App\Services\EstimateService';
            $files['classParams'] = [$generate_estimate->opportunity_id, $generateEstimate->client->organization_id];
            $files['attachment_name'] = 'EstimateInvoice.pdf';
            // dd($generate_estimate);
            if (checkClassHasMethod($files['serviceClass'], 'preparePdf')) {

                EmailService::send($client_email_template, 'You rejected estimate '.$generateEstimate->opportunityid->job_no, $generateEstimate->client->email, $payload, true, $files);

            }
            // dd($generateEstimate->oppotunityid);
            EmailService::send($company_email_template, 'Estimate '.$generateEstimate->opportunityid->job_no.' is rejected', $generateEstimate->client->organization->email, $payload, true);
            $totalPriceSumss = DB::table('estimate_items')
                ->where('opportunity_id', decodeID($id))
                ->sum('total_price');
            EstimateStatus::create([
                'generate_estimate_id' => $generateEstimate->id,
                'status_name' => 'reject',
                'created_at' => now(),
                'opportunity_id' => decodeID($id),
                'estimate_total_price' => $totalPriceSumss,
            ]);
            Opportunity::where('id', decodeID($id))->update([
                'status' => 5,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate has not been rejected successfully!');
        }

        return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('success', 'Estimate has been rejected successfully!');
    }

    public function changeRequestEstimate(Request $request, $organization_id, $id)
    {
        $estimate = Opportunity::with('generateEstimate')->where('id', decodeID($id))->firstorfail();
        $this->authorize('client-view-estimate', $estimate, 'client');

        if ($estimate->generateEstimate->client_status != GenerateEstimate::$CLIENT_PROPOSED_STATUS) {
            return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate  status is already changed!');
        }

        // $totalFinalPayable = optional($estimate->generateEstimate)?->grand_total && optional($estimate->generateEstimate)?->grand_total > 0 ? optional($estimate->generateEstimate)?->grand_total : optional($estimate->generateEstimate)?->total_price;
        // $totalFinalPayable = str_replace(',', '', $totalFinalPayable);
        $request->validate([
            // 'change_price' => 'required|numeric|min:1|max:' . floatval($totalFinalPayable),
            'change_reason' => ['required', 'string'],
        ], [
            // 'change_price.max' => 'The change price must not be greater than estimate price'
        ]);

        DB::beginTransaction();
        try {
            // dd($estimate->generateEstimate->id);

            $estimate->load('generateEstimate');
            $estimateAction = ClientEstimateAction::updateOrCreate([
                'client_id' => auth('client')->user()->id,
                'generate_estimate_id' => $estimate->generateEstimate->id,
            ], [
                'status' => GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS,
                'price' => $request->change_price,
                'description' => $request->change_reason,
            ]);

            $generateEstimate = $this->updateClientEstimateStatus(
                decodeID($id),
                GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS,
                $estimateAction
            );

            $generateEstimate->loadMissing('client.organization', 'opportunityid:id,job_no');
            $company_email_template = 'email_template.client.company-estimate-request-change-notify';
            $client_email_template = 'email_template.client.client-estimate-request-change-notify';

            $payload['company_name'] = $generateEstimate->client->organization->company_name;

            $payload['company_phone_no'] = $generateEstimate->client->phone_number;
            $payload['primary_color'] = $generateEstimate->client->organization->primary_color;
            $payload['company_address'] = $generateEstimate->client?->mailing_address;

            $payload['company_website'] = $generateEstimate->client->accountget?->website;

            $payload['org_image'] = $generateEstimate->client->organization->profile_photo_path ? asset('storage/user_images/'.$generateEstimate->client->organization->profile_photo_path) : null;
            $payload['company_email'] = $generateEstimate->client->organization->email;
            $payload['client_name'] = $generateEstimate->client->first_name.' '.$generateEstimate->client->last_name;

            $payload['estimate_status'] = GenerateEstimate::$CLIENT_REQUEST_CHANGE_STATUS;

            $payload['description'] = $estimateAction->description;

            $payload['sale_order_number'] = $generateEstimate->opportunityid->job_no;
            $payload['price'] = $estimateAction->price;

            $files['dompdf'] = true;
            $files['serviceClass'] = 'App\Services\EstimateService';
            $files['classParams'] = [$generateEstimate->opportunity_id, $generateEstimate->client->organization_id];
            $files['attachment_name'] = 'EstimateInvoice.pdf';

            if (checkClassHasMethod($files['serviceClass'], 'preparePdf')) {

                EmailService::send($client_email_template, 'Your Change Request has been received', $generateEstimate->client->email, $payload, true, $files);
                // dd($estimate);

            }
            $totalPriceSumss = DB::table('estimate_items')
                ->where('opportunity_id', decodeID($id))
                ->sum('total_price');
            EstimateStatus::create([
                'generate_estimate_id' => $generateEstimate->id,
                'status_name' => 'request_change',
                'created_at' => now(),
                'opportunity_id' => decodeID($id),
                'estimate_total_price' => $totalPriceSumss,
            ]);

            Opportunity::where('id', decodeID($id))->update([
                'status' => 4,
            ]);

            EmailService::send($company_email_template, 'Change Request for Estimate '.$generateEstimate->opportunityid->job_no, $generateEstimate->client->organization->email, $payload, true);
            Opportunity::where('id', decodeID($id))->update([
                'status' => 4,
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            // dd($e);
            return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('error', 'Estimate  Change Request has not been forward successfully!');
        }

        return redirect()->action([self::class, 'view'], [$request->route('organization_id'), 'id' => $id])->with('success', 'Estimate  Change Request has been forward successfully!');
    }

    private function updateClientEstimateStatus($id, $status, $estimateAction)
    {
        $generateEstimate = GenerateEstimate::where('opportunity_id', $id)->firstorfail();
        if ($generateEstimate) {
            $generateEstimate->update(['client_status' => $status]);
        }

        return $generateEstimate;
    }
}
