<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Contact;
use App\Models\Opportunity;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    public function __construct()
    {
        $this->middleware('client.guest')->except('logout', 'updateProfile');
    }

    public function loginForm($organization_id)
    {
        if (! session()->has('url.clientIntended') && str_starts_with(url()->previous(), '/client')) {
            session(['url.clientIntended' => url()->previous()]);
        }

        $companyDetails = $this->companyDetails($organization_id);
        // dd($companyDetails);
        // Check if the companyDetails is empty and redirect to the landing page if necessary
        if (empty($companyDetails)) {
            return redirect()->route('landingHome');
        }

        return view('client.auth.login', compact('companyDetails'));
    }

    public function login(Request $request, $organization_id)
    {
        // Decode the organization ID if necessary
        $organization_id = decodeID($organization_id);

        // Validate the login form data
        $request->validate([
            'email' => [
                'required',
                'email',
                Rule::exists('contacts')->where(function ($query) use ($organization_id) {
                    // Ensure the email exists for the given organization_id
                    $query->where('organization_id', $organization_id);
                }),
            ],
            'password' => ['required'],
        ], [
            'email.exists' => 'Email or Password does not match',
        ]);

        // Get the credentials from the request
        $credentials = $request->only('email', 'password');

        // Step 1: Fetch the contact using both email and organization_id
        // $contact = \App\Models\Contact::where('email', $credentials['email'])
        //                               ->where('organization_id', $organization_id)
        //                               ->first();

        $credentials['organization_id'] = $organization_id;

        // Step 2: If the contact exists, attempt to login using those credentials
        if (Auth::guard('client')->attempt($credentials, $request->has('remember'))) {
            // Successfully authenticated
            // Update first login status if necessary
            if (Auth::guard('client')->user()->first_login == 0) {
                Contact::where('id', auth('client')->user()->id)->update(['first_login' => 1]);
            }

            // Redirect logic based on session or first login status
            if (session()->get('url.clientIntended') && str_starts_with(session()->get('url.clientIntended'), '/client')) {
                return redirect(session()->get('url.clientIntended'));
            }

            if (auth('client')->user()->first_login == 1) {
                return redirect()->route('client.estimates.index', [$request->route('organization_id')]);
            } else {
                // Handle first login redirection
                $estimate = Opportunity::with(['generateEstimate'])
                    ->where(['account_id' => auth('client')->user()->id])
                    ->whereHas('generateEstimate', function ($q) {
                        $q->where('is_complete', true);
                    })
                    ->latest()
                    ->first();

                if ($estimate) {
                    return redirect()->route('client.estimates.view', [$request->route('organization_id'), encodeId($estimate->id)]);
                }

                return redirect()->route('client.estimates.index', [$request->route('organization_id')]);
            }
        }

        // If authentication fails, return an error
        return back()->withErrors(['email' => 'Email or Password does not match']);
    }

    // public function login(Request $request, $organization_id)
    // {
    //     $organization_id = decodeID($organization_id);

    //     $request->validate([
    //         'email' => [
    //             'required', 'email', Rule::exists('contacts')->where(function ($query) use ($organization_id) {
    //                 // Use the organization_id from the request to perform the check
    //                 $query->where('organization_id', $organization_id);
    //                 // $query->where('parent_id', $organization_id)->where('is_active', true);
    //             }),
    //         ],
    //         'password' => ['required']
    //     ], [
    //         'email.exists' => 'Email or Password not match'
    //     ]);

    //     $credentials = $request->only('email', 'password');
    //     // dd(Auth::guard('client')->attempt($credentials, $request->has('remember')));
    //     if (Auth::guard('client')->attempt($credentials, $request->has('remember'))) {

    //         if (Auth::guard('client')->user()->first_login == 0) {
    //             Contact::where('id', auth('client')->user()->id)->update([
    //                 'first_login' => 1
    //             ]);
    //         }

    //         if (session()->get('url.clientIntended') && str_starts_with(session()->get('url.clientIntended'), '/client')) {
    //             return redirect(session()->get('url.clientIntended'));
    //         }

    //         if (auth('client')->user()->first_login == 1) {
    //             return redirect()->route('client.estimates.index', [$request->route('organization_id')]); // Redirect to the client dashboard or any other page
    //         } else {
    //             $estimate = Opportunity::with(['generateEstimate'])
    //                 ->where(['account_id' => auth('client')->user()->id])
    //                 ->whereHas('generateEstimate', function ($q) {
    //                     $q->where('is_complete', true);
    //                 })->latest()->first();
    //             // return $estimate;
    //             if ($estimate) {
    //                 return redirect()->route('client.estimates.view', [$request->route('organization_id'), encodeId($estimate->id)]);
    //             }
    //             return redirect()->route('client.estimates.index', [$request->route('organization_id')]);
    //         }
    //     }
    //     // Authentication failed
    //     return back()->withErrors(['email' => 'Email or Password not match']);
    // }

    // Handle the client logout request
    public function logout($organization_id)
    {
        Auth::guard('client')->logout();

        return redirect()->action([AuthController::class, 'loginForm'], ['organization_id' => $organization_id]);
    }

    public function showForgotPassword($organization_id)
    {
        $companyDetails = $this->companyDetails($organization_id);
        // Check if the companyDetails is empty and redirect to the landing page if necessary
        if (empty($companyDetails)) {
            return redirect()->route('landingHome');
        }

        return view('client.auth.forgot-password', compact('companyDetails'));
    }

    public function findaccount(Request $request, $organization_id)
    {
        $customMessages = [
            'email.exists' => "We can't find a user with that email address.",
        ];
        $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'min:8', Rule::exists('clients')->where(function ($query) use ($organization_id) {
                // Use the organization_id from the request to perform the check
                $query->where('organization_id', decodeId($organization_id))->where('is_active', true);
            }), ],
        ], $customMessages);

        DB::table('client_password_resets')->updateOrInsert(
            [
                'email' => $request->email,
                'organization_id' => decodeId($organization_id),
            ],
            [
                'token' => $request->_token,
                'created_at' => Carbon::now(),
            ]
        );
        $url = route('client.reset-password', [$organization_id, $request->email, $request->_token]);
        $email_template = 'email_template.client.reset-password';
        $this->sendMail($email_template, $request->email, $url, 'Password Reset', $organization_id);

        return Redirect::route('client.checkMail', [$organization_id, $request->email])->with('success', 'true');
    }

    private function sendMail($template, $email, $url, $subject, $organization_id, $is_queue = false)
    {
        $email_template = $template;
        $companyDetails = $this->companyDetails($organization_id);
        $company_image = $companyDetails->company_image;
        $company_name = $companyDetails->company_name;
        $payload['companyimage'] = asset($company_image);
        $payload['company_name'] = $company_name;
        $payload['company_email'] = $companyDetails->organization->email;
        $payload['company_phone_no'] = $companyDetails->organization->companyPropertyAddress->phone_no;
        $payload['primary_color'] = $companyDetails->organization->primary_color;
        $payload['company_address'] = $companyDetails->organization->companyPropertyAddress->address1;
        $payload['company_website'] = $companyDetails->organization->companyPropertyAddress->website_url;
        $payload['url'] = asset($url);

        try {
            EmailService::send($email_template, $subject, $email, $payload, $is_queue);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
        }
    }

    public function checkMail($organization_id, $email = false)
    {
        $companyDetails = $this->companyDetails($organization_id);
        // Check if the companyDetails is empty and redirect to the landing page if necessary
        if (empty($companyDetails)) {
            return redirect()->route('landingHome');
        }
        if ($email) {
            Client::where('email', $email)->where('organization_id', decodeId($organization_id))->firstorfail();
        }

        return view('client.auth.verify-email', ['email' => $email, 'success' => false, 'companyDetails' => $companyDetails]);
    }

    public function resendPasswordLink(Request $request, $organization_id, $email)
    {
        if ($email) {
            Client::where('email', $email)->where('organization_id', decodeId($organization_id))->firstorfail();
        }

        DB::table('client_password_resets')->updateOrInsert(
            [
                'email' => $email,
                'organization_id' => decodeId($organization_id),
            ],
            [
                'token' => Str::random(64),
                'created_at' => Carbon::now(),
            ]
        );
        $passwordReset = DB::table('client_password_resets')
            ->where([
                'email' => $email,
                'organization_id' => decodeId($organization_id),
            ])
            ->first();
        $url = route('client.reset-password', [$organization_id, $email, $passwordReset->token]);

        $email_template = 'email_template.client.reset-password';
        $this->sendMail($email_template, $email, $url, 'Password Reset', $organization_id);

        return redirect()->back()->with('success', 'true');
    }

    public function resetPasswordScreen(Request $request, $organization_id, $email, $token)
    {
        $email = $email;
        $token = $token;

        // Check if the email and token exist in the client_password_resets table
        $passwordReset = DB::table('client_password_resets')->where('email', $email)
            ->where('organization_id', decodeId($organization_id))
            ->where('token', $token)
            ->first();

        if (! $passwordReset) {
            return Redirect::route('client.checkMail', [$email, $organization_id])->with('error', 'Token has expired. Please request a new password reset link.');
        }

        // Check if the token was updated within the last 60 minutes
        $tokenUpdatedTime = Carbon::parse($passwordReset->created_at);
        $currentDateTime = Carbon::now();

        if (! $passwordReset || $tokenUpdatedTime->diffInMinutes($currentDateTime) > 60) {
            // Token expired
            return Redirect::route('client.checkMail', [$organization_id, $email])->with('error', 'Token has expired. Please request a new password reset link.');
        }
        // If everything is valid, fetch the company details and pass them to the view
        $companyDetails = $this->companyDetails($organization_id);
        // Check if the companyDetails is empty and redirect to the landing page if necessary
        if (empty($companyDetails)) {
            return redirect()->route('landingHome');
        }

        return view('client.auth.reset-password', compact('email', 'token', 'companyDetails'));
    }

    public function updatePassword(Request $request, $organization_id)
    {
        $validator = Validator::make($request->all(), [
            'password' => ['required', 'confirmed', 'min:8', 'max:40', 'alpha_num'],
            'password_confirmation' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        $updatePassword = DB::table('client_password_resets')
            ->where([
                'email' => $request->email,
                'token' => $request->token,
                'organization_id' => decodeId($organization_id),
            ])
            ->first();

        if (! $updatePassword) {
            return redirect()->back()->with(['error' => 'Invalid token!']);
        }
        // Check if the token was updated within the last 60 minutes
        $tokenUpdatedTime = Carbon::parse($updatePassword->created_at);
        $currentDateTime = Carbon::now();

        if (! $updatePassword || $tokenUpdatedTime->diffInMinutes($currentDateTime) > 60) {
            // Token expired
            return Redirect::route('client.checkMail', [$organization_id, $request->email])->with('error', 'Token has expired. Please request a new password reset link.');
        }
        $user = Client::where('email', request('email'))->where('organization_id', decodeId($organization_id))->first();
        if (is_object($user)) {
            $url = $user->company_name;

            $user->forceFill([
                'password' => Hash::make($request['password']),
            ])->save();
            DB::table('client_password_resets')->where(['email' => $request->email])->where('organization_id', decodeId($organization_id))->delete();
            $email_template = 'email_template.client.password-change';
            $this->sendMail($email_template, request('email'), $url, 'Password Changed', $organization_id, true);

            return redirect()->action([AuthController::class, 'loginForm'], [$organization_id])->with(['message' => 'Password Changed successfully!']);
        }
    }

    private function companyDetails($organization_id)
    {
        $companyID = decodeID($organization_id);
        $client = Contact::with('organization')->where('organization_id', $companyID)->first();

        // Check if the client is not found and return null
        if (! $client) {
            return null;
        }

        $company_name = $client->organization->company_name;
        $company_image = $client->organization->profile_photo_path ? asset('storage/user_images/'.$client->organization->profile_photo_path) : null;
        $organization = $client->organization;

        return (object) get_defined_vars();
    }

    public function updateProfile(Request $request, $organization_id)
    {
        $client = auth('client')->user();
        $request->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'mobile_no' => 'required',
            'profile_image' => 'sometimes|image',
            'company_name' => [
                'nullable',
                Rule::unique('clients', 'company_name')->ignore($client->id)->whereNotNull('company_name'),
            ],
        ], [
            'required' => ':attribute is required.',
            'image' => 'Please enter a value with a valid mimetype.',
            'company_name.unique' => 'The company name must be unique among non-null values.',
        ]);

        Client::where('id', auth('client')->user()->id)->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'company_name' => $request->company_name,
            'email' => $request->email,
            'alternate_email' => $request->alternate_email,
            'mobile_no' => $request->mobile_no,
            'alternate_no' => $request->alternate_no,
        ]);
        if ($request->hasfile('profile_image')) {
            $imageName = uniqid().'_'.trim($request->profile_image->getClientOriginalName());
            $request->profile_image->storePubliclyAs('client_images', $imageName, 'public');
            $client->image = $imageName;
        }
        $client->save();

        return response()->json([
            'message' => 'Profile Updated Successfully',
            'data' => $request->all(),
            'profile_image' => $client->image,
        ]);
    }
}
