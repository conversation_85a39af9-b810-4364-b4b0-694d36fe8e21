<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Jobs\InvoicePdfSendJob;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Transaction;
use App\Services\EmailService;
use App\Services\InvoiceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Throwable;
use Yajra\DataTables\Facades\DataTables;

class InvoiceController extends Controller
{
    public function index(Request $request, $organization_id)
    {
        if (request()->query('success') == 'payment') {
            session()->put('stripe_payment_success', 'Payment Done Successfully');
        }

        if ($request->ajax()) {

            $query = Invoice::where('client_id', auth('client')->user()->id)
                ->where('is_sent', 1)
                ->when(request('status'), function ($query, $status) {
                    if (! empty($status)) {
                        return $query->where('status', $status);
                    }

                    return $query;
                })->latest()->select(['invoice_number', 'subject', 'due_date', 'total', 'status', 'id']);

            return DataTables::eloquent($query)

                ->addColumn('invoice_number', function (Invoice $invoice) {
                    return $invoice?->invoice_number;
                })
                ->addColumn('subject', function (Invoice $invoice) {
                    return $invoice?->subject;
                })
                ->addColumn('due_date', function (Invoice $invoice) {
                    return $invoice->due_date;
                })
                ->addColumn('total', function (Invoice $invoice) {
                    return '$'.$invoice->total;
                })
                ->addColumn('status', function (Invoice $invoice) {
                    return '<span class="status '.($invoice->status === 'paid' ? 'approved' : 'warning').' status'.$invoice->id.'">'.ucfirst($invoice?->status).'</span>';
                })
                ->addColumn('action', function (Invoice $invoice) {
                    $action = '<div class="dropdown mx-auto w-fit estimate-dropdown">
                                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                    <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                                    </div>
                                    <ul class="dropdown-menu download-estimates" aria-labelledby="dropdown1">
                                        <li><a class="dropdown-item" href="'.route('client.invoice.view', [request()->route('organization_id'), encodeID($invoice->id)]).'">View invoice details</a></li>';

                    if ($invoice->status != 'paid') {
                        $action .= '<li><a class="dropdown-item" href="'.route('client.invoice.payment.screen', [request()->route('organization_id'), encodeID($invoice->id)]).'">Pay your invoice</a></li>';
                    }

                    $action .= '</ul>
                                </div>';

                    return $action;
                })

                ->filterColumn('subject', function ($query, $keyword) {
                    $query->where('subject', 'LIKE', '%'.$keyword.'%');
                })
                ->filterColumn('invoice_number', function ($query, $keyword) {
                    $query->where('invoice_number', 'LIKE', '%'.$keyword.'%');
                })
                ->rawColumns(['status', 'action'])
                ->only(['invoice_number', 'subject', 'due_date', 'total', 'status', 'action'])
                ->toJson();
        }

        return view('client.invoices.index');
    }

    public function view(Request $request, $organization_id, $id)
    {
        $invoice = Invoice::with('invoiceItems')->findOrFail(decodeID($id));
        $this->authorize('client-view-invoice', $invoice, 'client');

        return view('client.invoices.view', compact('invoice'));
    }

    public function paymentScreen($organization_id, $id)
    {
        $invoice = Invoice::findOrFail(decodeID($id));
        $this->authorize('client-view-invoice', $invoice, 'client');
        if ($invoice->status == 'paid') {
            return redirect()->route('client.invoice.list', [request()->route('organization_id')]);
        }

        return view('client.invoices.payment');
    }

    public function invoicePay($organization_id, $id)
    {
        try {
            $invoice = Invoice::findOrFail(decodeID($id));
            $this->authorize('client-view-invoice', $invoice, 'client');
            if ($invoice->status == 'paid') {
                return redirect()->route('client.invoice.list', [request()->route('organization_id')]);
            }

            $client = Client::with('organizationStripeAccount')->find(auth('client')->user()->id);

            $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

            $customerParam = [];
            if (! empty($client->stripe_customer_id)) {
                $customerParam = ['customer' => $client->stripe_customer_id];
            } else {
                $customerParam = ['customer_email' => $client->email];
            }

            $response = $stripe->checkout->sessions->create(array_merge([
                'success_url' => route('client.invoice.pay.success', [request()->route('organization_id')]).'?success=payment&session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('client.invoice.view', [request()->route('organization_id'), $id]),
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price_data' => [
                            'currency' => 'usd', // Replace with your desired currency
                            'unit_amount_decimal' => $invoice->total * 100,
                            'product_data' => [
                                'name' => 'Invoice # : '.$invoice->invoice_number,
                            ],
                        ],
                        'quantity' => 1,
                    ],
                ],
                'metadata' => [
                    'invoice_id' => $id,
                ],
                'mode' => 'payment',
                'client_reference_id' => encodeID($client->id),
                'payment_intent_data' => [
                    'application_fee_amount' => 0, // Fee for your platform
                ],
            ], $customerParam), ['stripe_account' => $client->organizationStripeAccount->acc_connected_id]);

            return Redirect::to($response['url']);
        } catch (Throwable $th) {
            // dd($th);
        }

        return redirect()->back()->with('error', 'Something went wrong please try again later');
    }

    public function checkInvoicePaymentSession(Request $request, $organization_id)
    {
        DB::beginTransaction();
        try {
            $client = Client::with('organizationStripeAccount')->find(auth('client')->user()->id);
            $stripe = new \Stripe\StripeClient([
                'api_key' => config('services.stripe.secret'),
                'stripe_account' => $client->organizationStripeAccount->acc_connected_id,
            ]);
            $session = $stripe->checkout->sessions->retrieve(request()->query('session_id'));
            if (! empty($session) && $session->payment_status == 'paid' && $session->status == 'complete') {
                $transaction = Transaction::updateOrCreate([
                    'invoice_id' => decodeID($session->metadata->invoice_id),
                ], [
                    'transaction_number' => Str::random(8),
                    'session_id' => $session->id,
                    'payment_intent' => $session->payment_intent,
                    'status' => $session->payment_status,
                    'payment_method' => 'stripe',
                    'transaction_date' => Carbon::now(),
                ]);

                $invoice = Invoice::with(['organization', 'client'])->find($transaction->invoice_id);
                $invoice->update(['status' => 'paid']);
                $invoice->transaction_date = customDateFormat(now(), $invoice->organization_id);
                $invoice->payment_method = 'Stripe';
                $organization_email_template = 'email_template.client.invoice-pay-notify';
                $client_email_template = 'email_template.client.invoice-pay-success';
                $payload['invoice'] = $invoice;
                $payload['org_image'] = $invoice->organization->profile_photo_path ? asset('storage/user_images/'.$invoice->organization->profile_photo_path) : null;
                $payload['company_name'] = $invoice->organization->comapny_name;
                $payload['company_phone_no'] = $invoice->organization->companyPropertyAddress->phone_no;
                $payload['primary_color'] = $invoice->organization->primary_color;
                $payload['company_address'] = $invoice->organization->companyPropertyAddress->address1;
                $payload['company_website'] = $invoice->organization->companyPropertyAddress->website_url;
                $payload['company_email'] = $invoice->organization->email;
                // EmailService::send($client_email_template, 'Invoice Paid '.$invoice->invoice_number, $invoice->client->email, $payload, true);
                $payload['email'] = $invoice->client->email;
                $payload['subject'] = 'Invoice Paid '.$invoice->invoice_number;
                $payload['sendAttachment'] = true;
                dispatch(new InvoicePdfSendJob($invoice, $client_email_template, $payload));
                EmailService::send($organization_email_template, 'Payment Received from '.$invoice->client->company_name, $invoice->organization->email, $payload, true);
            }
            if ($session && $session->customer && empty($client->stripe_customer_id)) {
                $client->update([
                    'stripe_customer_id' => $session->customer,
                ]);
            }
            DB::commit();

            return redirect()->route('client.invoice.list', [request()->route('organization_id'), 'success' => 'payment']);
        } catch (Throwable $th) {
            DB::rollBack();
            // dd($th);
            Log::info($th->getMessage());
        }

        return redirect()->route('client.invoice.list', [request()->route('organization_id')])->with('error', 'Invoice Doesn\'t Pay SuccessFully!');
    }

    public function downloadInvoice($organization_id, $invoiceID)
    {
        $invoice = Invoice::with('invoiceItems')->findorfail(decodeID($invoiceID));
        $this->authorize('client-view-invoice', $invoice, 'client');

        return InvoiceService::preparePdf($invoice, getOrganizationId(), true);
    }
}
