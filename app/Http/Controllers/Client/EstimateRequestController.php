<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Client\ClientEstimateRequest;
use App\Models\Adress;
use App\Models\Estimate;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class EstimateRequestController extends Controller
{
    public function index(Request $request, $organization_id)
    {
        if ($request->ajax()) {
            $estimateRequests = Estimate::withExists('generateEstimate')->with(['propertyAddress', 'client'])->where(['created_by' => 'client', 'client_id' => auth('client')->user()->id])->when(request('status'), function ($query) {
                if (request('status') == 'open') {
                    $query->where('is_active', 1);
                } elseif (request('status') == 'close') {
                    $query->where('is_active', 0);
                } elseif (request('status') == 'pending') {
                    $query->where('is_active', 2);
                }
            })->latest()
                ->select(['id', 'is_active', 'communication_type', 'created_at', 'description', 'project_name']);

            return DataTables::eloquent($estimateRequests)
                ->addIndexColumn()
                ->filterColumn('propertyAddress', function ($query, $keyword) {
                    $query->wherehas('propertyAddress', function ($q) use ($keyword) {
                        $q->where('property_name', 'LIKE', '%'.$keyword.'%')->orWhere('address1', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->filterColumn('description', function ($query, $keyword) {
                    $query->where('project_name', 'LIKE', '%'.$keyword.'%')->orWhere('description', 'LIKE', '%'.$keyword.'%');
                })
                ->addColumn('status', function (Estimate $estimate) {
                    if ($estimate->is_active == 1) {
                        return ' <div class="status success"> Open</div>';
                    } elseif ($estimate->is_active == 0) {
                        return ' <div class="status warning">Closed</div>';
                    } else {
                        return ' <div class="status primary">Pending</div>';
                    }
                })
                ->editColumn('created_at', function (Estimate $estimate) {
                    return customDateFormat($estimate->created_at);
                })
                ->editColumn('communication_type', function (Estimate $estimate) {
                    return ucfirst($estimate->communication_type);
                })->addColumn('property_name', function (Estimate $estimate) {
                    return $estimate->propertyAddress ? '
                          <h2 class="estimate-title">'.optional($estimate->propertyAddress)?->property_name.'</h2>
                          <p class="estimate-detail">'.optional($estimate->propertyAddress)?->address1.'</p>
                        ' : '';
                })->editColumn('description', function (Estimate $estimate) {
                    return '
                            <h2 class="estimate-title">'.$estimate->project_name.'</h2>
                            <p class="estimate-detail">'.Str::limit($estimate->description, 100, '...').'</p>
                        ';
                })->addColumn('action', function (Estimate $estimate) {
                    $html =
                        ' <div class="dropdown mx-auto w-fit">
                <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                    <img height="24px" width="24px"
                        src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                </div>
                <ul class="dropdown-menu" aria-labelledby="dropdown1">
                    <li><a class="dropdown-item"   href="'.route('client.estimates.requests.view', [request()->route('organization_id'), encodeId($estimate->id)]).'">View estimate request</a></li>';
                    if ($estimate?->generate_estimate_exists == 0 && $estimate->is_active != 1) {
                        $html .= '<li><a class="dropdown-item"
                            href="'.route('client.estimates.requests.edit', [request()->route('organization_id'), encodeId($estimate->id)]).'">Edit
                            Request</a></li>';
                    }
                    $html .= '<li>  <a href="'.route('client.estimates.requests.documents', [request()->route('organization_id'), encodeId($estimate->id)]).'" class="dropdown-item" id="attachedDocuments"
                            data-value='.encodeId($estimate->id).'>
                            View Documents
                        </a></li>';
                    if ($estimate->generate_estimate_exists == 0) {
                        $html .= '<li>
                                <a href="'.route('client.estimates.requests.delete', [request()->route('organization_id'), encodeId($estimate->id)]).'" class="dropdown-item deleteEstimate" id="deleteRequest" data-toggle="modal" data-target="#deleteModal"
                                    data-value='.encodeId($estimate->id).'>
                                    Delete Request
                                </a>
                            </li>';
                    }
                    $html .= '</ul> </div>';

                    return $html;
                })
                ->rawColumns(['status', 'action', 'created_at', 'communication_type', 'property_name', 'description'])
                ->only(['description', 'property_name', 'created_at', 'communication_type', 'status', 'action'])
                ->toJson();
        }
        $documents = [];

        return view('client.requests.index', compact('documents'));
    }

    public function view($organization_id, $id)
    {
        $estimate = Estimate::withExists('generateEstimate')->with('propertyAddress')->where('id', decodeID($id))->firstorfail();
        // Check Owner
        $this->authorize('client-view-request', $estimate, 'client');
        $client = auth('client')->user();

        return view('client.requests.view', compact('estimate', 'client'));
    }

    public function edit($organization_id, $id)
    {
        $client = auth('client')->user();
        $estimate = Estimate::with('propertyAddress')->where('id', decodeID($id))->firstorfail();
        // Check Owner
        $this->authorize('client-view-request', $estimate, 'client');

        return view('client.requests.edit', get_defined_vars());
    }

    public function create($organization_id)
    {
        $clientPropertyDetails = Estimate::with(['propertyAddress', 'client'])
            ->where(['client_id' => auth('client')->user()->id])
            ->oldest()->first();
        $client = auth('client')->user();
        // return $clientPropertyDetails;
        if (! is_object($client) || ! is_object($clientPropertyDetails)) {
            return redirect()->back()->with('error', 'Something went wrong!');
        }
        $estimate = new Estimate;
        $addresses = Adress::where('client_id', auth('client')->user()->id)
            ->where('type', 0)
            ->get();

        return view('client.requests.create', get_defined_vars());
    }

    public function store(ClientEstimateRequest $request, $organization_id)
    {
        DB::beginTransaction();
        try {
            $estimate = new Estimate;
            $estimate->client_id = auth('client')->user()->id;
            $estimate->project_name = $request->project_name;
            $estimate->lead_source = 'client';
            $estimate->probability = 1;
            $estimate->is_active = 2;
            $estimate->communication_type = $request->type;
            $estimate->description = $request->description;
            $estimate->organization_id = auth('client')->user()->organization_id;
            $estimate->created_by = 'client';
            $estimate->creator_id = auth('client')->user()->id;

            $estimate->save();
            if ($request->exists('document')) {
                foreach ($request->input('document', []) as $file) {
                    $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                }
            }
            $this->createAddress($estimate->id, PROPERTY);
            DB::commit();

            return redirect()->route('client.estimates.requests.index', [request()->route('organization_id')])->with('showModal', 'Your request has been successfully completed.');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function update(ClientEstimateRequest $request, $organization_id, $id)
    {
        DB::beginTransaction();
        try {
            $estimate = Estimate::where('id', decodeID($id))->firstorfail();
            // Check Owner
            $this->authorize('client-view-request', $estimate, 'client');
            $estimate->project_name = $request->project_name;
            $estimate->description = $request->description;
            $estimate->communication_type = $request->type;
            $estimate->save();
            if (count($estimate->getMedia('document'))) {
                foreach ($estimate->getMedia('document') as $media) {
                    if (! in_array($media->file_name, $request->input('document', []))) {
                        $media->delete();
                    }
                }
            }

            if (count($estimate->getMedia('document'))) {
                $media = $estimate->getMedia('document')->pluck('file_name')->toArray();
                foreach ($request->input('document', []) as $file) {
                    if (count($media) === 0 || ! in_array($file, $media)) {
                        $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                    }
                }
            } else {
                if ($request->exists('document')) {
                    foreach ($request->input('document', []) as $file) {
                        $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                    }
                }
            }
            $this->updateAddress($estimate->id, PROPERTY);
            DB::commit();

            return redirect()->route('client.estimates.requests.index', [request()->route('organization_id')])->with('success', 'Request Updated Successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            dd($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    private function createAddress($estimateId, $type)
    {
        $address = new Adress;
        $address->client_id = auth('client')->user()->id;
        $address->estimate_id = $estimateId;
        $address->property_name = request()->property_name;
        $address->address1 = request()->address1;
        $address->address2 = request()->address2;
        $address->state = request()->state;
        $address->city = request()->city;
        $address->zip = request()->czip;
        $address->type = $type;
        $address->save();

        return $address;
    }

    private function updateAddress($estimateId, $type)
    {
        $address = Adress::where(['estimate_id' => $estimateId, 'type' => $type])->firstorfail();
        if (is_object($address)) {
            $address->estimate_id = $estimateId;
            $address->property_name = request()->property_name;
            $address->address1 = request()->address1;
            $address->address2 = request()->address2;
            $address->state = request()->state;
            $address->city = request()->city;
            $address->zip = request()->czip;
            $address->type = $type;
            $address->save();
        }
    }

    public function imageStore(Request $request, $organization_id)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $request->file->storePubliclyAs('uploads', $name, 'public');

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function imageDelete($organization_id, $id = null)
    {
        if (File::exists(public_path('storage/uploads/'.request('name')))) {
            File::delete(public_path('storage/uploads/'.request('name')));
        }

        return response()->json('Image Removed Successfully!', HTTP_OK);
    }

    public function delete($organization_id, $id)
    {
        $estimateRequest = Estimate::withExists('generateEstimate')->where('id', decodeID($id))->firstorfail();
        // Check Owner
        $this->authorize('client-view-request', $estimateRequest, 'client');
        if ($estimateRequest->generate_estimate_exists == 0) {
            $estimateRequest->delete();

            return redirect()->back()->with('success', 'Request deleted Successfully');
        }

        return redirect()->back()->with('error', 'Request Not deleted Successfully');
    }

    public function getDocuments($organization_id, $id)
    {
        $estimate = Estimate::where('id', decodeId($id))->first();
        if (is_object($estimate)) {
            if (count($estimate->getMedia('document'))) {
                $documents = $estimate->getMedia('document');
                $tableView = View::make('client.partials.document-modal', compact('documents'))->render();

                return json_encode($tableView, HTTP_OK);
            } else {
                return response()->json('No attachments found against this request.', HTTP_BAD_REQUEST);
            }
        }

        return response()->json('No record found', HTTP_BAD_REQUEST);
    }

    public function clientAddress($organization_id, $id)
    {
        $address = Adress::where('client_id', auth('client')->user()->id)
            ->where('id', decodeID($id))
            ->select('address1', 'address2', 'city', 'state', 'zip', 'property_name')
            ->firstOrFail();

        return response()->json(['address' => $address], HTTP_OK);
    }
}
