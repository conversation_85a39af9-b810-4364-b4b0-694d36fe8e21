<?php

namespace App\Http\Controllers;

use App\Actions\Fortify\PasswordValidationRules;
use App\Http\Requests\ResetPasswordRequest;
use App\Models\CompanyAddress;
use App\Models\User;
use App\Models\UserVerify;
use App\Rules\FilterEmail;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class AuthController extends Controller
{
    use PasswordValidationRules;

    public function register(Request $request)
    {
        Validator::make($request->all(), [
            'company_name' => ['required', 'string', 'max:255', 'unique:users,company_name'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users', new FilterEmail],
            'address' => 'required',
            'city' => 'required',
            'state' => 'required',
            'zip' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'password' => ['required', 'confirmed', 'min:8', 'max:40', 'alpha_num'],
            'terms' => 'required',
        ], [
            'terms.required' => 'You must accept the Terms and Conditions to continue using the '.config('app.name').' system',
        ])->validate();
        DB::beginTransaction();
        try {
            $user = User::create([
                'company_name' => $request->company_name,
                'email' => $request->email,
                'address' => $request->address,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'password' => Hash::make($request->password),
            ]);

            $address = CompanyAddress::create([
                'company_id' => $user->id,
                'address1' => $request->address,
                'city' => $request->city,
                'zip' => $request->zip,
                'state' => $request->state,
                'time_format' => 'h:i A',
                'date_format' => 'Y/m/d',
                'type' => 0,
            ]);

            $token = Str::random(64);

            UserVerify::create([
                'user_id' => $user->id,
                'token' => $token,
            ]);

            $orgRole = Role::where('name', 'organization')->first();
            $user->assignRole($orgRole);
            $email_template = 'email_template.verification_email_template';
            $url = '/verify-account/'.$user->email.'/'.$token;
            $payload['url'] = asset($url);
            $payload['name'] = $user->company_name;

            // Log the user in after registration
            Auth::guard('web')->login($user);

            EmailService::send($email_template, 'Verification Email', $user->email, $payload);
            DB::commit();

            return Redirect::route('register.step1');
        } catch (\Exception $e) {
            Auth::guard('web')->logout();
            DB::rollback();
            dd($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $user = null;
        if ($request->email) {
            $user = User::where('email', $request->email)->firstorfail();
        }

        DB::table('password_resets')->updateOrInsert(
            [
                'email' => $request->email,
            ],
            [
                'token' => $request->_token,
                'created_at' => Carbon::now(),
            ]
        );
        $url = '/reset-password/'.$request->email.'/'.$request->_token;
        $email_template = 'email_template.reset-password';
        // Determine the name based on parent_id
        $payload['name'] = $user->parent_id == 0 ? $user->company_name : ($user->first_name.' '.$user->last_name);

        $this->sendMail($email_template, $request->email, $url, 'Password Reset', $payload);

        return Redirect::route('checkMail', $request->email)->with('success', 'true');
    }

    public function resetPasswordScreen($email, $token)
    {
        $email = $email;
        $token = $token;

        return view('auth.reset-password', get_defined_vars());
    }

    public function resendPasswordLink($email)
    {

        $user = User::where('email', $email)->firstorfail();
        DB::table('password_resets')->updateOrInsert(
            [
                'email' => $email,
            ],
            [
                'token' => Str::random(64),
                'created_at' => Carbon::now(),
            ]
        );
        $passwordReset = DB::table('password_resets')
            ->where([
                'email' => $email,
            ])
            ->first();
        // $url = '/reset-password/' . $email;
        $url = '/reset-password/'.$email.'/'.$passwordReset->token;

        $email_template = 'email_template.reset-password';
        // Determine the name based on parent_id
        $payload['name'] = $user->parent_id == 0 ? $user->company_name : ($user->first_name.' '.$user->last_name);
        $this->sendMail($email_template, $email, $url, 'Password Reset', $payload);

        return redirect()->back()->with('success', 'true');
    }

    public function resendMailVerifyLink()
    {

        $user = User::where('email', auth('web')->user()->email)->firstorfail();
        // Redirect to Plans page if user is already verified
        if ($user->email_verified_at) {
            return redirect()->route('plans');
        }

        $token = Str::random(64);
        UserVerify::updateOrCreate([
            'user_id' => $user->id,
        ], [
            'token' => $token,
        ]);
        $url = '/verify-account/'.$user->email.'/'.$token;

        $email_template = 'email_template.verification_email_template';
        $payload['name'] = auth('web')->user()->name;
        $this->sendMail($email_template, auth('web')->user()->email, $url, 'Verification Mail', $payload);

        return Redirect::route('register.step1')->with('success', 'true');
    }

    public function checkMail($email)
    {
        User::where('email', $email)->firstorfail();

        return view('auth.verify-email', ['email' => $email, 'success' => false]);
    }

    public function stepOne()
    {
        if (auth('web')->user()->email_verified_at) {
            return redirect()->route('plans')->with('success', 'Welcome to '.config('app.name').'.Your account has been verified successfully!');
        }

        return view('register.step1_check_mail', ['email' => auth('web')->user()->email, 'success' => false]);
    }

    public function verifyAccount($email, $token = false)
    {
        $verifyUser = UserVerify::where('token', $token)->first();

        $message = 'Please use latest sent email to verify your account.';

        if (! is_null($verifyUser)) {
            $user = User::where('email', $email)->first();
            if (! is_object($user)) {
                return redirect()->route('login')->with('warning', 'Account Not Found.');
            } else {
                $user->update([
                    'email_verified_at' => Carbon::now()->timestamp,
                ]);
                DB::table('users_verify')->where(['user_id' => $user->id])->delete();

                return redirect()->route('plans')->with('success', 'Welcome to '.config('app.name').'.Your account has been verified successfully!');
            }
        } elseif (auth('web')->check() && auth()->user()->isOrganization()) {
            return redirect()->route('register.step1')->with('error', $message);
        } else {
            return redirect()->route('login')->with('error', $message);
        }
    }

    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => ['required', 'confirmed', 'min:8', 'max:40', 'alpha_num'],
            'password_confirmation' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        $updatePassword = DB::table('password_resets')
            ->where([
                'email' => $request->email,
                'token' => $request->token,
            ])
            ->first();

        if (! $updatePassword) {
            return redirect()->back()->with(['error' => 'Invalid token!']);
        }
        $user = User::where('email', request('email'))->first();
        if (is_object($user)) {
            $url = $user->company_name;
            $user->forceFill([
                'password' => Hash::make($request['password']),
            ])->save();
            DB::table('password_resets')->where(['email' => $request->email])->delete();
            $email_template = 'email_template.password-change';

            $payload['name'] = $user->company_name;
            $this->sendMail($email_template, request('email'), $url, 'Password Changed', $payload);

            return redirect()->route('login')->with(['message' => 'Password Changed successfully!']);
        }
    }

    private function sendMail($template, $email, $url, $subject, array $payload = [])
    {
        $payload['url'] = asset($url);

        try {
            EmailService::send($template, $subject, $email, $payload);
        } catch (\Exception $e) {
            throw $e;
            // Handle the exception here, if needed.
        }
    }
}
