<?php

namespace App\Http\Controllers;

use App\Models\GenerateEstimate;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Throwable;

class DashboardController extends Controller
{
    public function lostSalesChart(Request $request)
    {
        $chartData = [
            'labels' => [],
            'data' => [],
        ];

        $startOfPeriod = null;
        $endOfPeriod = null;
        $filter = $request->input('filter');

        if ($filter === 'week') {
            $startOfPeriod = Carbon::now()->startOfWeek();
            $endOfPeriod = Carbon::now()->endOfWeek();
        } elseif ($filter === 'month') {
            $startOfPeriod = Carbon::now()->startOfMonth();
            $endOfPeriod = Carbon::now()->endOfMonth();
        } elseif ($filter === 'year') {
            $startOfPeriod = Carbon::now()->startOfYear();
            $endOfPeriod = Carbon::now()->endOfYear();
        }

        if ($startOfPeriod && $endOfPeriod) {
            $lostSales = GenerateEstimate::whereHas('opportunityid', function ($query) {
                $query->when(! auth()->user()->isAdmin(), function ($query) {
                    employeeAssociatedDateQuery($query)
                        ->where('organization_id', getOrganizationId());
                });
            })
                ->selectRaw($this->getSelectColumn($filter))
                ->where(['is_complete' => true, 'status' => 'lost'])
                ->whereBetween('created_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            $chartData['labels'] = $lostSales->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();

            $chartData['data'] = $lostSales->pluck('total_price')->toArray();

            if ($filter == 'week') {
                return $this->setWeekdayValues($chartData['data'], $chartData['labels']);
            }
        }

        return response()->json($chartData, 200);
    }

    public function totalSalesChart(Request $request)
    {
        $chartData = [
            'labels' => [],
            'data' => [],
        ];

        $startOfPeriod = null;
        $endOfPeriod = null;
        $filter = $request->input('filter');

        if ($filter === 'week') {
            $startOfPeriod = Carbon::now()->startOfWeek();
            $endOfPeriod = Carbon::now()->endOfWeek();
        } elseif ($filter === 'month') {
            $startOfPeriod = Carbon::now()->startOfMonth();
            $endOfPeriod = Carbon::now()->endOfMonth();
        } elseif ($filter === 'year') {
            $startOfPeriod = Carbon::now()->startOfYear();
            $endOfPeriod = Carbon::now()->endOfYear();
        }

        if ($startOfPeriod && $endOfPeriod) {
            $lostSales = GenerateEstimate::whereHas('opportunityid', function ($query) {
                $query->when(! auth()->user()->isAdmin(), function ($query) {
                    employeeAssociatedDateQuery($query)
                        ->where('organization_id', getOrganizationId());
                });
            })
                ->selectRaw($this->getSelectColumn($filter))
                ->where(['is_complete' => true, 'status' => 'won'])
                ->whereBetween('created_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            $chartData['labels'] = $lostSales->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();

            $chartData['data'] = $lostSales->pluck('total_price')->toArray();

            if ($filter == 'week') {
                return $this->setWeekdayValues($chartData['data'], $chartData['labels']);
            }

            if ($filter == 'month') {
                $labels = $chartData['labels'];
                $data = $chartData['data'];

                $endOfMonth = Carbon::now()->endOfMonth()->format('d');
                $currentDay = Carbon::now()->day;

                // Create an array with all month days initialized to 0
                $allDays = array_fill(1, $endOfMonth, 0);

                // Fill in the existing data values
                foreach ($labels as $index => $day) {
                    $allDays[$day] = $data[$index];
                }

                // Set future dates as null
                if ($currentDay < $endOfMonth) {
                    for ($day = $currentDay + 1; $day <= $endOfMonth; $day++) {
                        $allDays[$day] = null;
                    }
                }

                $newLabels = array_keys($allDays);
                $newDataset = array_values($allDays);
                // Output the resulting arrays
                array_unshift($newLabels, null);
                $newLabels[] = null;
                array_unshift($newDataset, null);
                $newDataset[] = null;

                return [
                    'labels' => $newLabels,
                    'data' => $newDataset,
                ];
            }

            if ($filter == 'year') {
                try {
                    $dataset = $chartData['data'];
                    $labels = $chartData['labels'];
                    $data = array_combine($labels, $dataset);
                    $previousMonth = Carbon::parse(head($labels))->subMonth()->format('F');
                    array_unshift($labels, $previousMonth);
                    $newDataset = $newLabels = [];
                    // Loop through the specified labels and assign values from the data array or set to 0 if not found
                    foreach ($labels as $label) {
                        $newDataset[] = isset($data[$label]) ? $data[$label] : 0;
                        $newLabels[] = $label;
                    }

                    array_unshift($newLabels, null);
                    $newLabels[] = null;
                    array_unshift($newDataset, null);
                    $newDataset[] = null;

                    return [
                        'labels' => $newLabels,
                        'data' => $newDataset,
                    ];
                } catch (Throwable $e) {
                }
            }
        }

        return response()->json($chartData, 200);
    }

    private function setWeekdayValues($dataset, $labels)
    {
        // Create an associative array mapping weekdays to values
        $data = array_combine($labels, $dataset);

        // Define an array of all weekdays
        $weekdays = ['Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat', 'Sun'];

        // Get the current weekday
        $currentWeekday = Carbon::now()->format('l');

        // Initialize a new array to store the final dataset
        $newDataset = [];
        // Loop through all weekdays and assign values from the data array, or set to 0 or null accordingly
        foreach ($weekdays as $weekday) {
            if (isset($data[$weekday])) {
                $newDataset[] = $data[$weekday];
            } elseif ($weekday === $currentWeekday) {
                $newDataset[] = null;
            } else {
                $newDataset[] = 0;
            }
        }

        // Add null values for the beginning and end of the arrays
        array_unshift($weekdays, null);
        array_unshift($newDataset, null);
        $weekdays[] = null;
        $newDataset[] = null;

        return [
            'labels' => $weekdays,
            'data' => $newDataset,
        ];
    }

    public function wipChartData(Request $request)
    {
        $chartData = [
            'labels' => [],
            'datasets' => [],
        ];

        $startOfPeriod = null;
        $endOfPeriod = null;
        $filter = $request->input('filter');

        if ($filter === 'week') {
            $startOfPeriod = Carbon::now()->startOfWeek();
            $endOfPeriod = Carbon::now()->endOfWeek();
        } elseif ($filter === 'month') {
            $startOfPeriod = Carbon::now()->startOfMonth();
            $endOfPeriod = Carbon::now()->endOfMonth();
        } elseif ($filter === 'year') {
            $startOfPeriod = Carbon::now()->startOfYear();
            $endOfPeriod = Carbon::now()->endOfYear();
        }

        if ($startOfPeriod && $endOfPeriod) {
            $operationCompleted = GenerateEstimate::whereHas('opportunityid', function ($query) {
                $query->when(! auth()->user()->isAdmin(), function ($query) {
                    employeeAssociatedDateQuery($query)
                        ->where('organization_id', getOrganizationId());
                });
            })
                ->selectRaw($this->getSelectColumn($filter, 'created_at'))
                ->where(['is_complete' => true, 'is_schedule' => true, 'status' => 'won', 'operation_status' => 'Completed'])
                ->whereBetween('completed_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            $operationPending = GenerateEstimate::whereHas('opportunityid', function ($query) {
                $query->when(! auth()->user()->isAdmin(), function ($query) {
                    employeeAssociatedDateQuery($query)
                        ->where('organization_id', getOrganizationId());
                });
            })
                ->selectRaw($this->getSelectColumn($filter, 'created_at'))
                ->where(['is_complete' => true, 'is_schedule' => true, 'status' => 'won', 'operation_status' => 'Pending'])
                ->whereBetween('created_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            // Prepare labels for completed operations
            $completedLabels = $operationCompleted->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();
            // Prepare labels for pending operations
            $pendingLabels = $operationPending->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();
            $chartData['labels'] = array_unique(array_merge($completedLabels, $pendingLabels));

            if ($filter === 'week') {
                $desiredLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                // Sort labels based on the desired sequence
                usort($chartData['labels'], function ($a, $b) use ($desiredLabels) {
                    return array_search($a, $desiredLabels) - array_search($b, $desiredLabels);
                });
            }

            if ($filter === 'month') {
                sort($chartData['labels']);
            }

            // // Prepare data for completed operations
            $completedData = $operationCompleted->pluck('total_price')->toArray();
            // Prepare data for completed operations
            $completedData = [];
            foreach ($chartData['labels'] as $label) {
                $index = array_search($label, $completedLabels);
                $completedData[] = $index !== false ? $operationCompleted[$index]->total_price : 0;
            }
            $completedDataset = [
                'label' => 'Completed',
                'backgroundColor' => '#1C7FD7',
                'data' => $completedData,
            ];
            $chartData['datasets'][] = $completedDataset;

            // Prepare data for pending operations
            $pendingData = [];
            foreach ($chartData['labels'] as $label) {
                $index = array_search($label, $pendingLabels);
                $pendingData[] = $index !== false ? $operationPending[$index]->total_price : 0;
            }
            $pendingDataset = [
                'label' => 'Pending',
                'backgroundColor' => '#FFA500',
                'data' => $pendingData,

            ];

            $chartData['labels'] = collect($chartData['labels'])->values();
            $chartData['datasets'][] = $pendingDataset;
        }

        return response()->json($chartData, 200);
    }

    public function adminWaveChart(Request $request)
    {
        $chartData = [
            'labels' => [],
            'data' => [],
        ];

        $startOfPeriod = null;
        $endOfPeriod = null;
        $filter = $request->input('filter');

        if ($filter === 'week') {
            $startOfPeriod = Carbon::now()->startOfWeek();
            $endOfPeriod = Carbon::now()->endOfWeek();
        } elseif ($filter === 'month') {
            $startOfPeriod = Carbon::now()->startOfMonth();
            $endOfPeriod = Carbon::now()->endOfMonth();
        } elseif ($filter === 'year') {
            $startOfPeriod = Carbon::now()->startOfYear();
            $endOfPeriod = Carbon::now()->endOfYear();
        }

        if ($startOfPeriod && $endOfPeriod) {
            $activeCompanies = User::wherehas('subscription', function ($query) {
                $query->whereDate('ends_at', '>', now());
            })
                ->selectRaw($this->getColumn($filter))
                ->whereBetween('created_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            $chartData['labels'] = $activeCompanies->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();

            $chartData['data'] = $activeCompanies->pluck('data')->toArray();

            if ($filter == 'week') {
                return $this->setWeekdayValues($chartData['data'], $chartData['labels']);
            }

            if ($filter == 'month') {
                $labels = $chartData['labels'];
                $data = $chartData['data'];

                $endOfMonth = Carbon::now()->endOfMonth()->format('d');
                $currentDay = Carbon::now()->day;

                // Create an array with all month days initialized to 0
                $allDays = array_fill(1, $endOfMonth, 0);

                // Fill in the existing data values
                foreach ($labels as $index => $day) {
                    $allDays[$day] = $data[$index];
                }

                // Set future dates as null
                if ($currentDay < $endOfMonth) {
                    for ($day = $currentDay + 1; $day <= $endOfMonth; $day++) {
                        $allDays[$day] = null;
                    }
                }

                $newLabels = array_keys($allDays);
                $newDataset = array_values($allDays);
                // Output the resulting arrays
                array_unshift($newLabels, null);
                $newLabels[] = null;
                array_unshift($newDataset, null);
                $newDataset[] = null;

                return [
                    'labels' => $newLabels,
                    'data' => $newDataset,
                ];
            }

            if ($filter == 'year') {
                try {
                    $dataset = $chartData['data'];
                    $labels = $chartData['labels'];
                    $data = array_combine($labels, $dataset);
                    $previousMonth = Carbon::parse(head($labels))->subMonth()->format('F');
                    array_unshift($labels, $previousMonth);
                    $newDataset = $newLabels = [];
                    // Loop through the specified labels and assign values from the data array or set to 0 if not found
                    foreach ($labels as $label) {
                        $newDataset[] = isset($data[$label]) ? $data[$label] : 0;
                        $newLabels[] = $label;
                    }

                    array_unshift($newLabels, null);
                    $newLabels[] = null;
                    array_unshift($newDataset, null);
                    $newDataset[] = null;

                    return [
                        'labels' => $newLabels,
                        'data' => $newDataset,
                    ];
                } catch (Throwable $e) {
                }
            }
        }

        return response()->json($chartData, 200);
    }

    public function earningsChart(Request $request)
    {
        $chartData = [
            'labels' => [],
            'data' => [],
        ];

        $startOfPeriod = null;
        $endOfPeriod = null;
        $filter = $request->input('filter');

        if ($filter === 'week') {
            $startOfPeriod = Carbon::now()->startOfWeek();
            $endOfPeriod = Carbon::now()->endOfWeek();
        } elseif ($filter === 'month') {
            $startOfPeriod = Carbon::now()->startOfMonth();
            $endOfPeriod = Carbon::now()->endOfMonth();
        } elseif ($filter === 'year') {
            $startOfPeriod = Carbon::now()->startOfYear();
            $endOfPeriod = Carbon::now()->endOfYear();
        }

        if ($startOfPeriod && $endOfPeriod) {
            $users = User::has('subscriptions.plan')
                ->join('subscriptions', 'users.id', '=', 'subscriptions.user_id')
                ->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
                ->selectRaw($this->getEarningColumn($filter))
                ->whereBetween('users.created_at', [$startOfPeriod, $endOfPeriod])
                ->groupBy($this->getGroupByColumn($filter))
                ->orderBy($this->getOrderByColumn($filter))
                ->get();

            $chartData['labels'] = $users->map(function ($item) use ($filter) {
                return $this->getLabel($item, $filter);
            })->toArray();

            $chartData['data'] = $users->pluck('data')->toArray();

            if ($filter == 'week') {
                return $this->setWeekdayValues($chartData['data'], $chartData['labels']);
            }
        }

        return response()->json($chartData, 200);
    }

    private function getSelectColumn($filter, $column = 'created_at')
    {
        switch ($filter) {
            case 'week':
                return 'DAYOFWEEK('.$column.') as label, '.calculateGrandTotalRawQuery('total_price');
            case 'month':
                return 'DAYOFMONTH('.$column.') as label, '.calculateGrandTotalRawQuery('total_price');
            case 'year':
                return 'MONTH('.$column.') as label, '.calculateGrandTotalRawQuery('total_price');
            default:
                return 'DAYOFWEEK('.$column.') as label, '.calculateGrandTotalRawQuery('total_price');
        }
    }

    private function getColumn($filter)
    {
        switch ($filter) {
            case 'week':
                return 'DAYOFWEEK(created_at) as label, COUNT(*) as data';
            case 'month':
                return 'DAYOFMONTH(created_at) as label, COUNT(*) as data';
            case 'year':
                return 'MONTH(created_at) as label, COUNT(*) as data';
            default:
                return 'DAYOFWEEK(created_at) as label, COUNT(*) as data';
        }
    }

    private function getEarningColumn($filter)
    {
        switch ($filter) {
            case 'week':
                return 'DAYOFWEEK(users.created_at) as label, SUM(plans.price) as data';
            case 'month':
                return 'DAYOFMONTH(users.created_at) as label, SUM(plans.price) as data';
            case 'year':
                return 'MONTH(users.created_at) as label, SUM(plans.price) as data';
            default:
                return 'DAYOFWEEK(users.created_at) as label, SUM(plans.price) as data';
        }
    }

    private function getGroupByColumn($filter)
    {
        switch ($filter) {
            case 'week':
                return 'label';
            case 'month':
                return 'label';
            case 'year':
                return 'label';
            default:
                return 'label';
        }
    }

    private function getOrderByColumn($filter)
    {
        switch ($filter) {
            case 'week':
                return 'label';
            case 'month':
                return 'label';
            case 'year':
                return 'label';
            default:
                return 'label';
        }
    }

    private function getLabel($item, $filter)
    {
        switch ($filter) {
            case 'week':
                $weekdays = [
                    'Sunday',
                    'Monday',
                    'Tuesday',
                    'Wednesday',
                    'Thursday',
                    'Friday',
                    'Saturday',
                ];
                $weekdayIndex = $item->label;

                return $weekdays[$weekdayIndex - 1];
            case 'month':
                return $item->label;
            case 'year':
                return Carbon::createFromFormat('n', $item->label)->format('F');
            default:
                return '';
        }
    }
}
