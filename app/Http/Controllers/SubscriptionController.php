<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;

class SubscriptionController extends Controller
{
    public function retreiveSession()
    {
        $stripe = new \Stripe\StripeClient(
            config('services.stripe.secret')
        );
        try {
            $session = $stripe->checkout->sessions->retrieve(request()->query('session_id'));
            if ($session->payment_status === 'paid' && $session->status == 'complete') {
                $planId = request()->query('plan_id') ?? 0;
                $userId = request()->query('organization_id') ?? null;
                $time = Plan::where('id', $planId)->value('interval');

                $this->createLocalSubscription($session, $userId, $planId, $time);
                User::where('id', $userId)->update([
                    'is_active' => true,
                    'payment_mode' => 'subscription',
                    'expire_untill' => $time == 'month' ? now()->addMonth(1) : now()->addMonth(1),
                ]);
                insertLabor($userId);
                insertMargin($userId);

                return redirect()->route('registered.success');
            }

            return redirect()->back()->with('error', 'Something went wrong please try again');
        } catch (\Exception $e) {
            throw ($e);
        }
    }

    private function createLocalSubscription($session, $userId, $planId, $time)
    {
        return Subscription::create([
            'user_id' => $userId,
            'plan_id' => $planId,
            'session_id' => $session->id,
            'subscription' => $session->subscription,
            'starts_at' => dateFromTimeStamp($session->created),
            'ends_at' => $time == 'month' ? now()->addMonth(1)->toDateTimeString() : now()->addDays(config('custom.plans.free.days')),
        ]);
    }

    public function upgradeSubscription()
    {
        $plan = Plan::where('name', 'Premium')->first();
        if (User::where('id', getOrganizationId())->value('payment_mode') != 'subscription') {
            return $this->createNewSubscription($plan);
        }
        $stripe = new \Stripe\StripeClient(
            config('services.stripe.secret')
        );

        try {
            $subscription = $stripe->subscriptions->retrieve(auth()->user()?->subscription?->subscription);

            if (in_array($subscription->status, ['canceled', 'incomplete_expired'])) {
                return $this->createNewSubscription($plan);
            }

            $response = $stripe->subscriptions->update(
                $subscription->id,
                [
                    'cancel_at_period_end' => false,
                    'items' => [
                        [
                            'id' => $subscription->items->data[0]->id,
                            'price' => $plan->stripe_price,
                        ],
                    ],
                ]
            );

            if ($response->status == 'active') {
                if ($response) {
                    User::where('id', getOrganizationId())->update([
                        'stripe_customer_id' => $response->customer,
                        'expire_untill' => now()->addMonth(1)->toDateTimeString(),
                    ]);
                    $this->updateLocalSubscription($response, $plan);
                }

                return redirect()->back()->with('success', 'Subscription upgrade Successfully!');
            }

            return redirect()->back()->with('error', 'Subscription Not upgrade Successfully!');
        } catch (\Exception $e) {
            throw ($e);
        }
    }

    private function updateLocalSubscription($response, $plan)
    {

        return Subscription::create([
            'user_id' => getOrganizationId(),
            'plan_id' => $plan->id,
            'is_main' => true,
            'subscription' => $response->id,
            'starts_at' => dateFromTimeStamp($response->start_date),
            'ends_at' => now()->addMonth(1)->toDateTimeString(),
        ]);
    }

    public function createNewSubscription($plan)
    {

        $baseUrl = URL::to('');
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        DB::beginTransaction();
        try {
            $user = User::find(getOrganizationId());

            if ($user) {
                if (empty($user->stripe_customer_id)) {
                    $customer = $stripe->customers->create([
                        'description' => $user->company_name,
                        'email' => $user->email,
                    ]);
                    $user->update(['stripe_customer_id' => $customer->id]);
                    $user = $user->refresh();
                }

                if (empty($user->setup_invoice_id)) {
                    $invoice = $stripe->invoiceItems->create([
                        'customer' => $user->stripe_customer_id,
                        'amount' => 39900,
                        'currency' => 'usd',
                        'description' => 'Setup Price (One Time)',
                    ]);
                    $user->update(['setup_invoice_id' => $invoice->id]);
                }

                $response = $stripe->checkout->sessions->create([
                    'success_url' => $baseUrl.'/organization/payment-success?session_id={CHECKOUT_SESSION_ID}&plan_id='.$plan->id.'&organization_id='.$user->id,
                    'cancel_url' => url()->previous(),
                    'payment_method_types' => ['card'],
                    'line_items' => [
                        [
                            'price' => $plan->stripe_price,
                            'quantity' => 1,
                        ],
                    ],
                    'mode' => 'subscription',
                    'customer' => $user->stripe_customer_id,
                ]);
                DB::commit();

                return Redirect::to($response['url']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // dd($e);
            return redirect()->back();
        }

        return redirect()->back();
    }

    public function newSubscriptionSucceeded(Request $request)
    {
        $stripe = new \Stripe\StripeClient(
            config('services.stripe.secret')
        );

        DB::beginTransaction();
        try {
            $session = $stripe->checkout->sessions->retrieve(request()->query('session_id'));
            if ($session->payment_status == 'paid' && $session->status == 'complete') {
                $planId = request()->query('plan_id') ?? 0;
                $userId = request()->query('organization_id') ?? null;
                $time = Plan::where('id', $planId)->value('interval');

                User::where('id', $userId)->update([
                    'is_active' => true,
                    'payment_mode' => 'subscription',
                    'expire_untill' => $time == 'month' ? now()->addMonth(1)->toDateTimeString() : now()->addDays(config('custom.plans.free.days')),
                ]);
                Subscription::updateOrCreate(
                    [
                        'user_id' => $userId,
                    ],
                    [
                        'plan_id' => $planId,
                        'session_id' => $session->id,
                        'subscription' => $session->subscription,
                        'canceled_at' => null,
                        'starts_at' => dateFromTimeStamp($session->created),
                        'ends_at' => $time == 'month' ? now()->addMonth(1)->toDateTimeString() : now()->addDays(config('custom.plans.free.days')),
                    ]
                );
            }
            DB::commit();

            return redirect()->route('organization.account.settings')->with('success', 'Plan purchased successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->route('organization.account.settings')->with('error', 'Something went wrong');
        }
    }
}
