<?php

namespace App\Http\Controllers;

use PDF;

class PDFController extends Controller
{
    public function generatePDF()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/estimate_invoice', $data);

        return $pdf->download('estimate_invoice.pdf');

        // return view('template/admin/frontend/pdf/estimate_invoice');
    }

    public function clientReportPdf()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/client_pdf_report', $data);

        return $pdf->download('client_report.pdf');

        // return view('template/admin/frontend/pdf/client_pdf_report');
    }

    public function estimatePdfReport()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/estimate_pdf_report', $data);

        return $pdf->download('Estimate_Report.pdf');

        // return view('template/admin/frontend/pdf/estimate_pdf_report');
    }

    public function operationsPdfReport()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/operations_pdf_report', $data);

        return $pdf->download('Operations_Report.pdf');

        // return view('template/admin/frontend/pdf/operations_pdf_report');
    }

    public function salesReport()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/sales_pdf_report', $data);

        return $pdf->download('Operations_Report.pdf');

        // return view('template/admin/frontend/pdf/sales_pdf_report');
    }

    public function estimateResquestPdf()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/estimate_request_pdf', $data);

        return $pdf->download('Operations_Report.pdf');

        // return view('template/admin/frontend/pdf/estimate_request_pdf');
    }

    public function generateEstimatePDF()
    {
        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'primary_color' => '#FE9B6A',
            'secondary_color' => '#003366',
        ];

        $pdf = PDF::loadView('template/admin/frontend/pdf/generate_estimate_report', $data);

        return $pdf->download('Generate_Estimate_Report.pdf');

        // return view('template/admin/frontend/pdf/generate_estimate_report');
    }
}
