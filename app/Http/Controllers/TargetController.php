<?php

namespace App\Http\Controllers;

use App\Models\Target;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class TargetController extends Controller
{
    public function setTarget(Request $request)
    {
        $organizationCreatedYear = auth()->user()->created_at->year;
        $currentYear = Carbon::now()->year;
        $organizationYears = collect(range($organizationCreatedYear, $currentYear))->reverse()->toArray();

        if ($request->ajax()) {
            $year = $request->input('year');
            $targets = Target::whereYear('month', $year)->where('organization_id', getOrganizationId())->get();
            $monthlyTargets = collect();

            for ($month = 1; $month <= 12; $month++) {
                $record = [
                    'month' => Carbon::createFromDate($year, $month, 1)->format('F'),
                    'price' => '',
                    'id' => 0,
                ];

                foreach ($targets as $target) {
                    if (Carbon::createFromFormat('Y-m-d', $target->month)->month == $month) {
                        if ($target->price !== null) {
                            $record['price'] = $target->price;
                        }
                        $record['id'] += $target->id;
                    }
                }

                $monthlyTargets->push($record);
            }

            $tableView = View::make('organization.settings.partials.monthly_targets', compact('monthlyTargets'))->render();

            return response()->json([
                'html' => $tableView,
            ], HTTP_OK);
        }

        return view('organization.settings.target', compact('organizationYears'));
    }

    // public function addTarget(Request $request)
    // {
    //     $request->validate([
    //         'price' => 'required',
    //         'month' => 'required'
    //     ]);
    //     $requestedDate = $request->month;
    //     $date = Carbon::createFromFormat('m/Y', $requestedDate)->startOfMonth();

    //     $existingRecord = Target::where('organization_id', getOrganizationId())
    //         ->where('month', $date)
    //         ->first();

    //     if (!$existingRecord) {
    //         Target::create([
    //             'organization_id' => getOrganizationId(),
    //             'price' => $request->price,
    //             'month' => $date
    //         ]);
    //         return response()->json(['message' => 'Target created successfully'], 200);
    //     } else {
    //         return response()->json(['message' => 'Target for the requested month already exists'], 419);
    //     }
    // }

    public function editTarget(Request $request)
    {
        $requestData = $request->all();
        $targetYear = $requestData['targetYear'];

        $monthsData = [];

        foreach ($requestData as $month => $value) {
            if ($month !== 'targetYear' && $value !== null) {
                $formattedDate = $targetYear.'-'.date('m', strtotime($month)).'-01';
                $monthsData[] = [
                    'month' => $formattedDate,
                    'value' => $value,
                ];
            }
        }

        foreach ($monthsData as $data) {
            Target::updateOrCreate([
                'month' => $data['month'],
                'organization_id' => getOrganizationId(),
            ], [
                'price' => $data['value'],
            ]);
        }

        return response()->json(['message' => 'Targets udpated successfully'], 200);
    }

    public function deleteTarget(Request $request)
    {
        $target = Target::where('id', $request->input('id'))->where('organization_id', getOrganizationId())->firstOrFail();
        $target->delete();

        return response()->json(['message' => 'Target deleted Successfully']);
    }
}
