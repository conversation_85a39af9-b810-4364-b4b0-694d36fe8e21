<?php

namespace App\Http\Controllers\Organization;

use App\Exports\CustomerIssuesExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Organization\StoreCustomerIssueRequest;
use App\Imports\CustomerIssueImport;
use App\Models\Account;
use App\Models\Contact;
use App\Models\ContactInformation;
use App\Models\CustomerIssue;
use App\Models\CustomerIssueDocument;
use App\Models\GenerateEstimate;
use App\Models\PropertyInformation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class CustomerIssueController extends Controller
{
    public function index(Request $request)
    {
        $statusMappings = [
            1 => ['name' => 'Pending', 'class' => 'status-pending'],
            2 => ['name' => 'Open', 'class' => 'status-open'],
            // 2 => ['name' => 'In Progress', 'class' => 'status-in-progress'],
            3 => ['name' => 'Completed', 'class' => 'status-completed'],
            4 => ['name' => 'Closed', 'class' => 'status-closed'],
        ];
        // dd()

        $organizationId = getOrganizationId();

        if ($request->ajax()) {
            // $issues = CustomerIssue::where('organization_id', $organizationId)
            //     ->with(['propertyInformation', 'contactInformation', 'creator'])
            //     ->latest()
            //     ->select('id', 'category', 'subject', 'status', 'created_at');
            $issues = CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
                ->leftJoin('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
                ->where('customer_issues.organization_id', $organizationId)
                ->select(
                    'customer_issues.id',
                    'customer_issues.category',
                    'customer_issues.subject',
                    'customer_issues.status',
                    DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"), // Concatenating creator's full name
                    'customer_issues.created_at',
                    'opportunities.id as opp_id',
                    'opportunities.property_id as prop_id',
                    'opportunities.account_id as acc_id',
                )
                ->with('opportunitydata.propertyInformation', 'opportunitydata.account', 'opportunitydata.organizations')
                ->latest();

            // Apply search filter if a search term is provided
            if ($searchTerm = $request->input('searchTerm')) {
                $issues->where(function ($query) use ($searchTerm) {
                    $query->where('category', 'LIKE', "%$searchTerm%")
                        ->orWhere('subject', 'LIKE', "%$searchTerm%");
                    //   ->orWhereHas('propertyInformation', function($q) use ($searchTerm) {
                    //       $q->where('name', 'LIKE', "%$searchTerm%");
                    //   });
                    //   ->orWhereHas('contactInformation', function($q) use ($searchTerm) {
                    //       $q->where('first_name', 'LIKE', "%$searchTerm%")
                    //         ->orWhere('last_name', 'LIKE', "%$searchTerm%");
                    //   });
                });
            }

            return DataTables::eloquent($issues)
                ->filterColumn('property_name', function ($query, $keyword) {

                    $query->whereHas('opportunitydata.propertyInformation', function ($q) use ($keyword) {
                        $q->where('property_information.name', 'LIKE', "%{$keyword}%");
                    });

                })
                ->filterColumn('subject', function ($query, $keyword) {
                    $query->where('customer_issues.subject', 'LIKE', "%{$keyword}%");

                })
                ->filterColumn('category', function ($query, $keyword) {
                    $query->where('customer_issues.category', 'LIKE', "%{$keyword}%");

                })
                ->filterColumn('account', function ($query, $keyword) {

                    $query->whereHas('opportunitydata.account', function ($q) use ($keyword) {
                        $q->where('accounts.company_name', 'LIKE', "%{$keyword}%");
                    });

                })

                ->addColumn('issue_id', fn (CustomerIssue $issue) => $issue->id)

                ->addColumn('property_name', function (CustomerIssue $issue) {
                    $property = \App\Models\PropertyInformation::find($issue->prop_id);

                    // Return the property name if found, otherwise 'N/A'
                    return $property ? $property->name : 'N/A';
                })

                ->addColumn('account', function (CustomerIssue $issue) {
                    $account = \App\Models\Account::find($issue->acc_id);

                    // Return the property name if found, otherwise 'N/A'
                    return $account ? $account->company_name : 'N/A';
                })

                ->addColumn('category', fn (CustomerIssue $issue) => $issue->category ?? 'N/A')
                ->addColumn('subject', fn (CustomerIssue $issue) => $issue->subject ?? 'N/A')
                ->addColumn('created_by', fn (CustomerIssue $issue) => $issue->creator_full_name ?? 'N/A')

                ->addColumn('date_time', fn (CustomerIssue $issue) => $issue->created_at->format('F j, Y h:i A'))
                ->addColumn('status', function (CustomerIssue $issue) use ($statusMappings) {
                    $status = $statusMappings[$issue->status]['name'] ?? 'Unknown';
                    $class = $statusMappings[$issue->status]['class'] ?? 'status-default';

                    return '<div class="status-label '.$class.'">'.$status.'</div>';
                })
                ->addColumn('action', function (CustomerIssue $issue) {
                    $user = auth('web')->user();
                    if (! $user->canany(['add_customer_issue', 'edit_customer_issue', 'customer_issue_detail'])) {
                        return '';
                    }

                    $html = '<div class="dropdown mx-auto w-fit">
                <div id="dropdown'.encodeID($issue->id).'" data-toggle="dropdown" aria-expanded="false">
                    <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                </div>
                <ul class="dropdown-menu" aria-labelledby="dropdown'.encodeID($issue->id).'">';

                    // View Details option
                    if ($user->canany(['add_customer_issue', 'customer_issue_detail'])) {
                        $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.customer-issue.detail', encodeId($issue->id)).'">View Details</a></li>';
                    }

                    // Delete option
                    if ($user->can('edit_customer_issue')) {
                        $html .= '<li><a data-toggle="modal" data-target="#deleteModal" data-issue-id="'.encodeId($issue->id).'" class="dropdown-item">Delete</a></li>';
                    }

                    $html .= '</ul></div>';

                    return $html;
                })
                ->rawColumns(['status', 'action'])
                ->only(['issue_id', 'property_name', 'account', 'category', 'subject', 'created_by', 'date_time', 'status', 'action'])
                ->toJson();
        }
        $issues = CustomerIssue::join('property_information', 'customer_issues.property_id', '=', 'property_information.id')
            ->join('contacts', 'customer_issues.contact_id', '=', 'contacts.id')
            ->join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->where('customer_issues.organization_id', $organizationId)
            ->select(
                'customer_issues.id',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                'customer_issues.created_at',
                'property_information.name as property_name', // Adding property name
                DB::raw("CONCAT(contacts.first_name, ' ', contacts.last_name) as contact_full_name"), // Concatenating contact's full name
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name") // Concatenating creator's full name
            )
            ->latest()
            ->get();

        // dd($issues);

        return view('organization.customer-issue.index');
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|integer|between:1,7',
        ]);

        $issue = CustomerIssue::findOrFail($id);
        $issue->status = $request->input('status');
        $issue->save();

        return response()->json(['success' => true, 'message' => 'Status updated successfully.']);

    }

    public function create()
    {
        $issue = new CustomerIssue;
        $organizationId = getOrganizationId();
        $action = URL::route(getRouteAlias().'.customer-issue.store');
        $properties = PropertyInformation::where('organization_id', $organizationId)->get(['id', 'name']);
        $accounts = Account::where('parent_id', $organizationId)->get();
        $roles = DB::table('roles')->get();
        $user = User::where('type', EMPLOYEE)->where('parent_id', $organizationId)->orWhere('id', getOrganizationId())->get();
        $loginuser = User::where('id', $organizationId)->get();

        $Organization = User::where('id', getOrganizationId())->firstorfail();
        $operations = GenerateEstimate::with(['opportunityid:id,opportunity_name,account_id,sale_person_id,estimator_id,sales_order_number as sale_no,organization_id', 'manager:id,first_name,last_name,image', 'latestScheduleDate', 'invoice'])->latest()

            ->wherehas('opportunityid', function ($query) {
                $query->where('organization_id', getOrganizationId());
                employeeAssociatedDateQuery($query);
            })->where(['status' => 'won', 'is_schedule' => true])
            ->when(request('operation_status'), function ($query) {
                if (request('operation_status') !== 'Clear') {
                    $query->where('operation_status', request('operation_status'));
                }
            })
            ->select('id', 'opportunity_id', 'operation_status', 'total_price', 'manager_id', 'total_cost', 'desire_margin', 'final_price')->get();

        // dd($operations);
        return view('organization.customer-issue.create', compact('issue', 'accounts', 'action', 'properties', 'roles', 'user', 'loginuser', 'operations'));
    }

    public function store(StoreCustomerIssueRequest $request)
    {
        $validated = $request->validated();
        // dd($validated);
        DB::beginTransaction();

        try {

            // $prop=DB::table('property_information')->where('organization_id', getOrganizationId())->where('name', $validated['property_name'])->where('address1', $validated['address1'])->where('city', $validated['city'])->where('state', $validated['state'])->where('zip', $validated['zip_code'])->where('company_id', $validated['company_id'])->get();
            // if (count($prop)>0) {

            //   $prop_id=$prop[0]->id;
            //   PropertyInformation::where('id', $prop_id)->update([
            //     'organization_id'=>getOrganizationId(),
            //     'name' => $validated['property_name'],
            //     'address1' => $validated['address1'],
            //     'address2' => $validated['address2'],
            //     'city' => $validated['city'],
            //     'state' => $validated['state'],
            //     'zip' => $validated['zip_code'],
            //     'company_id' => $validated['company_id'],
            // ]);
            // }else{
            //    $props= PropertyInformation::create([
            //         'organization_id'=>getOrganizationId(),
            //         'name' => $validated['property_name'],
            //         'address1' => $validated['address1'],
            //         'address2' => $validated['address2'],
            //         'city' => $validated['city'],
            //         'state' => $validated['state'],
            //         'zip' => $validated['zip_code'],
            //         // 'opportunity_id' => $opportunity->id,
            //         'company_id' => $validated['company_id'],
            //     ]);
            //     $prop_id=$props->id;

            // }
            // $conts=DB::table('contacts')->where('organization_id', getOrganizationId())->where('first_name', $validated['first_name'])->where('last_name', $validated['last_name'])->where('title', $validated['title_role'])->where('phone_number', $validated['phone_number'])->where('email', $validated['email'])->where('account', $validated['company_id'])->where('role', $validated['role'])->where('mailing_address', $validated['mailing_address'])->get();
            // if (count($conts)> 0) {
            //     $cont_id=$conts[0]->id;
            //    $contacts= Contact::where('id', $cont_id)->update([
            //         'first_name' => $validated['first_name'],
            //         'last_name' => $validated['last_name'],
            //         'title' => $validated['title_role'],
            //         'phone_number' => $validated['phone_number'],
            //         'email' => $validated['email'],
            //         'role' => $validated['role'],
            //         'second_phone' => $validated['second_phone'],
            //         'second_email' => $validated['second_email'],
            //         'mailing_address' => $validated['mailing_address'],
            //         'property_id'=>$prop_id
            //     ]);
            // }else{
            //     $cnt=Contact::create([
            //         'organization_id' => getOrganizationId(),
            //         'first_name' => $validated['first_name'],
            //         'last_name' => $validated['last_name'],
            //         'title' => $validated['title_role'],
            //         'phone_number' => $validated['phone_number'],
            //         'email' => $validated['email'],
            //         'role' => $validated['role'],
            //         'second_phone' => $validated['second_phone'],
            //         'second_email' => $validated['second_email'],
            //         'mailing_address' => $validated['mailing_address'],
            //         'property_id'=>$prop_id
            //     ]);
            //     $cont_id=$cnt->id;
            // }

            $issue = CustomerIssue::create([
                'property_name' => 'test property',
                'category' => $validated['issue_category'],
                'subject' => $validated['subject'],
                'description' => $validated['description'],
                'status' => $request->status,
                'assign_to' => $validated['assign_to'],
                'assigned_job' => $validated['assigned_job'],
                // 'property_id' => $prop_id,
                // 'contact_id' => $cont_id,
                'organization_id' => getOrganizationId(),
                'created_by' => auth('web')->user()->id,
            ]);

            if ($request->images_opportunity) {
                $images = $request->images_opportunity;

                foreach ($images as $image) {
                    // dd($image);
                    // $randomPart = Str::random(10);

                    $fileExtension = pathinfo($image, PATHINFO_EXTENSION);

                    // $imageName = $randomPart . '.' . $fileExtension;

                    // $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv', 'doc', 'docx'];

                    // if (in_array($fileExtension, $validExtensions)) {

                    // $filePath = $image->storeAs('public/opportunity_documents', $imageName);
                    $filePath = 'public/customer_issue_documents/'.$image;

                    $issue->documents()->create([
                        'name' => $image,
                        'path' => $filePath,
                        'type' => $fileExtension,
                        'document_status' => 'regular',

                    ]);

                }
            }

            // if ($request->images_opportunity) {
            //     $images = $request->images_opportunity;

            //     foreach ($images as $image) {
            //         // dd($image);
            //         // $randomPart = Str::random(10);

            //         $fileExtension = pathinfo($image, PATHINFO_EXTENSION);

            //         // $imageName = $randomPart . '.' . $fileExtension;

            //         // $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv', 'doc', 'docx'];

            //         // if (in_array($fileExtension, $validExtensions)) {

            //             // $filePath = $image->storeAs('public/opportunity_documents', $imageName);
            //             $filePath = 'public/property_documents/' . $image;

            //                     $issue->documents()->create([
            //             'name' => $image,
            //             'path' => $filePath,
            //             'type' => $fileExtension,
            //             'document_status' => 'regular',

            //         ]);

            //     }
            // }

            DB::commit();

            return redirect()->route(getRouteAlias().'.customer-issues.index')->with('success', 'Issue has been successfully created.');
            // return redirect()->route(getRouteAlias() . '.customer-issues.index')->with('showModal', 'Issue has been successfully created.');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());
            dd($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function storeFiles(Request $request)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $file->storeAs('public/customer_issue_documents', $name);

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function deleteFiles($id = null)
    {
        // Ensure correct file path for deletion
        $filePath = storage_path('app'.DIRECTORY_SEPARATOR.'public'.DIRECTORY_SEPARATOR.'customer_issue_documents'.DIRECTORY_SEPARATOR.request('filename'));
        //    dd(request('filename'));

        // Check if the file exists and then delete it
        if (File::exists($filePath)) {
            File::delete($filePath);
            // Optionally, return a success response or handle further logic
        } else {
            // Handle the case when the file doesn't exist
            return response()->json(['message' => 'File not found: '.$filePath], 404);
        }

        return response()->json('File Removed Successfully!', HTTP_OK);
    }

    public function imageDeleteServer(Request $request)
    {
        $fileId = $request->input('file_id');
        $filePath = storage_path('app'.DIRECTORY_SEPARATOR.'public'.DIRECTORY_SEPARATOR.'customer_issue_documents'.DIRECTORY_SEPARATOR.request('filename'));

        if (isset($fileId)) {
            $document = CustomerIssueDocument::find($fileId);
            $filePathss = storage_path('app'.DIRECTORY_SEPARATOR.'public'.DIRECTORY_SEPARATOR.'customer_issue_documents'.DIRECTORY_SEPARATOR.$document->name);
            if ($filePathss) {
                File::delete($filePathss);
            } else {
                return response()->json(['message' => 'File not found: '.$filePathss]);
            }
            $document->delete();
        }

        File::delete($filePath);

        return response()->json(['message' => 'Document deleted successfully'], 200);
    }

    public function edit($id)
    {
        $issue = CustomerIssue::findOrFail($id);
        $action = URL::route(getRouteAlias().'.customer-issue.update', $id);

        return view('organization.issue.edit', compact('issue', 'action'));
    }

    public function update(Request $request, $id)
    {
        // dd($request->all());

        $validated = $request->validate([
            'description' => 'nullable|string',
            'completed_description' => 'nullable|string',
        ]);

        try {
            // Find the issue by ID
            $issue = CustomerIssue::findOrFail(decodeId($id));

            $issue->update($validated);

            if ($request->images_opportunity_complete) {
                $imagesss = $request->images_opportunity_complete;

                foreach ($imagesss as $image) {
                    // dd($image);
                    // $randomPart = Str::random(10);

                    $fileExtension = pathinfo($image, PATHINFO_EXTENSION);

                    // $imageName = $randomPart . '.' . $fileExtension;

                    // $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv', 'doc', 'docx'];

                    // if (in_array($fileExtension, $validExtensions)) {

                    // $filePath = $image->storeAs('public/opportunity_documents', $imageName);
                    $filePath = 'public/customer_issue_documents/'.$image;

                    $issue->documents()->create([
                        'name' => $image,
                        'path' => $filePath,
                        'type' => $fileExtension,
                        'document_status' => 'completed',

                    ]);

                }
            }

            // if ($request->hasFile('completed_images')) {
            //     $completedDocuments = $request->file('completed_images');
            //     // dd($completedDocuments);

            //     foreach ($completedDocuments as $document) {
            //         $randomPart = Str::random(10);
            //         $fileExtension = $document->getClientOriginalExtension();
            //         $documentName = $randomPart . '.' . $fileExtension;

            //         $validExtensions = ['pdf', 'csv', 'xlsx', 'xls', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];

            //         if (in_array($fileExtension, $validExtensions)) {
            //             $filePath = $document->storeAs('public/completed_images', $documentName);

            //             $issue->documents()->create([
            //                 'name' => $documentName,
            //                 'path' => $filePath,
            //                 'type' => $fileExtension,
            //                 'document_status' => 'completed',
            //             ]);
            //         }
            //     }
            // }

            if ($request->images_opportunity) {
                $images = $request->images_opportunity;

                foreach ($images as $image) {
                    // dd($image);
                    // $randomPart = Str::random(10);

                    $fileExtension = pathinfo($image, PATHINFO_EXTENSION);

                    // $imageName = $randomPart . '.' . $fileExtension;

                    // $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv', 'doc', 'docx'];

                    // if (in_array($fileExtension, $validExtensions)) {

                    // $filePath = $image->storeAs('public/opportunity_documents', $imageName);
                    $filePath = 'public/customer_issue_documents/'.$image;

                    $issue->documents()->create([
                        'name' => $image,
                        'path' => $filePath,
                        'type' => $fileExtension,
                        'document_status' => 'regular',

                    ]);

                }
            }

            // if ($request->hasFile('images')) {
            //     $images = $request->file('images');
            //     // dd($images);
            //     foreach ($images as $image) {
            //         $randomPart = Str::random(10);
            //         $fileExtension = $image->getClientOriginalExtension();
            //         $imageName = $randomPart . '.' . $fileExtension;

            //         $validExtensions = ['pdf', 'csv', 'xlsx', 'xls', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];

            //         if (in_array($fileExtension, $validExtensions)) {
            //             $filePath = $image->storeAs('public/issue_images', $imageName);

            //             $issue->documents()->create([
            //                 'name' => $imageName,
            //                 'path' => $filePath,
            //                 'type' => $fileExtension,
            //                 'document_status' => 'regular',
            //             ]);
            //         }
            //     }
            // }

            return redirect()->route(getRouteAlias().'.customer-issues.index')->with('success', 'Issue updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update the issue: '.$e->getMessage());
        }
    }

    public function updateCustomerIssue(Request $request, $id)
    {
        // Validate the input data
        // dd(decodeId($id));

        try {
            // Find the CustomerIssue by ID
            $issue = CustomerIssue::findOrFail(decodeId($id));

            // Update the issue with new data
            $issue->assigned_job = $request->input('assigned_job');
            $issue->category = $request->input('issue_category');
            $issue->subject = $request->input('subject');
            $issue->created_by = $request->input('issue_created_by');
            $issue->assign_to = $request->input('assign_to');

            // Save the changes to the database
            $issue->save();

            // Redirect or return a success response
            return redirect()->back()->with('success', 'Customer Issue updated successfully!');
        } catch (\Exception $e) {
            // Handle errors and return an error response
            return redirect()->back()->with('error', 'Failed to update Customer Issue: '.$e->getMessage());
        }
    }

    public function detail($id)
    {
        // Decode the ID
        $decodedId = decodeId($id);
        $action = URL::route(getRouteAlias().'.customer-issue.update', $id);
        $issue = CustomerIssue::with(['opportunitydata', 'creator', 'assignee'])
            ->findOrFail($decodedId);
        // dd($issue);

        $user = User::where('type', EMPLOYEE)->where('parent_id', getOrganizationId())->orWhere('id', getOrganizationId())->get();
        $loginuser = User::where('id', getOrganizationId())->get();

        $Organization = User::where('id', getOrganizationId())->firstorfail();
        $operations = GenerateEstimate::with(['opportunityid:id,opportunity_name,account_id,sale_person_id,estimator_id,sales_order_number as sale_no,organization_id', 'manager:id,first_name,last_name,image', 'latestScheduleDate', 'invoice'])->latest()

            ->wherehas('opportunityid', function ($query) {
                $query->where('organization_id', getOrganizationId());
                employeeAssociatedDateQuery($query);
            })->where(['status' => 'won', 'is_schedule' => true])
            ->when(request('operation_status'), function ($query) {
                if (request('operation_status') !== 'Clear') {
                    $query->where('operation_status', request('operation_status'));
                }
            })
            ->select('id', 'opportunity_id', 'operation_status', 'total_price', 'manager_id', 'total_cost', 'desire_margin', 'final_price')->get();

        return view('organization.customer-issue.details', compact('issue', 'action', 'user', 'loginuser', 'operations'));
    }

    public function deleteDocument($id)
    {
        try {
            // Decode the ID if encoded
            $decodedId = decodeId($id);

            // Find the document by ID
            $document = CustomerIssueDocument::findOrFail($decodedId);

            // Delete the file from storage
            Storage::delete($document->path);

            // Delete the document record from the database
            $document->delete();

            // Return success response
            return response()->json(['success' => 'Document deleted successfully!'], 200);
        } catch (\Exception $e) {
            // Handle error
            return response()->json(['error' => 'Document could not be deleted!'], 500);
        }
    }

    public function sendEmailToIssue(Request $request)
    {
        // Logic for sending an email related to a customer issue
    }

    public function customerIssueFileImport(Request $request)
    {
        Excel::import(new CustomerIssueImport, $request->file('file'));

        return response()->json(['success' => 'File imported successfully!'], 200);
    }

    public function exportIssues(Request $request)
    {
        $organizationId = getOrganizationId();
        $issues = CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->leftJoin('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
            ->where('customer_issues.organization_id', $organizationId)
            ->select(
                'customer_issues.id',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"),
                'customer_issues.created_at',
                'opportunities.id as opp_id',
                'opportunities.property_id as prop_id',
                'opportunities.account_id as acc_id'
            )
            ->with('opportunitydata.propertyInformation', 'opportunitydata.account', 'opportunitydata.organizations')
            ->latest()
            ->get();

        return Excel::download(new CustomerIssuesExport($issues), 'customer_issues.xlsx');
    }
}
