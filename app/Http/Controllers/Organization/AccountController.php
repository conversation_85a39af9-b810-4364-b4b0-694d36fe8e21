<?php

namespace App\Http\Controllers\Organization;

use App\Exports\AccountExport;
use App\Http\Controllers\Controller;
use App\Imports\AccountImport;
use App\Models\Account;
use App\Models\Contact;
use App\Models\Opportunity;
use App\Models\PropertyInformation;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\URL;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class AccountController extends Controller
{
    public function index()
    {

        $user = User::where('type', EMPLOYEE)->where('parent_id', getOrganizationId())->orWhere('id', getOrganizationId())->get();

        $data = DB::table('accounts')
            ->leftJoin('users', 'users.id', '=', 'accounts.account_owner')
            ->where('accounts.parent_id', getOrganizationId())
            ->select('accounts.*', 'users.first_name as fname', 'users.last_name as lname')
            ->get();
        $action = URL::route(getRouteAlias().'.account.add');
        $accounts = DB::table('accounts')->where('parent_id', getOrganizationId())->count();

        // dd($accounts);
        return view('organization.account.index', compact('user', 'action', 'data', 'accounts'));
    }

    public function storeAccount(Request $request)
    {
        $validatedData = $request->validate([
            'company_name' => 'required|string',
            'website' => 'nullable',
            'company_email' => 'required|email',
            'company_phone' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'zip' => 'required|string',
            'billing_address' => 'required|string',
            'billing_city' => 'required|string',
            'billing_state' => 'required|string',
            'billing_zip' => 'required|string',
            'address' => 'required|string',
            'account_owner' => 'required|integer',
        ]);
        Account::create([
            'company_name' => $validatedData['company_name'],
            'website' => $validatedData['website'],
            'email' => $validatedData['company_email'],
            'mobile_no' => $validatedData['company_phone'],
            'city' => $validatedData['city'],
            'state' => $validatedData['state'],
            'zip' => $validatedData['zip'],
            'billing_address' => $validatedData['billing_address'],
            'billing_city' => $validatedData['billing_city'],
            'billing_state' => $validatedData['billing_state'],
            'billing_zip' => $validatedData['billing_zip'],
            'address' => $validatedData['address'],
            'account_owner' => $validatedData['account_owner'],
            'parent_id' => getOrganizationId(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Account created successfully!');
    }

    public function saveAccount(Request $request)
    {
        $validatedData = $request->validate([
            'company_name' => 'required|string',
            'website' => 'nullable',
            'company_email' => 'required|email',
            'company_phone' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'zip' => 'required|string',
            'billing_address' => 'required|string',
            'billing_city' => 'required|string',
            'billing_state' => 'required|string',
            'billing_zip' => 'required|string',
            'address' => 'required|string',
            'account_owner' => 'required|integer',
        ]);
        $company = Account::create([
            'company_name' => $validatedData['company_name'],
            'website' => $validatedData['website'],
            'email' => $validatedData['company_email'],
            'mobile_no' => $validatedData['company_phone'],
            'city' => $validatedData['city'],
            'state' => $validatedData['state'],
            'zip' => $validatedData['zip'],
            'billing_address' => $validatedData['billing_address'],
            'billing_city' => $validatedData['billing_city'],
            'billing_state' => $validatedData['billing_state'],
            'billing_zip' => $validatedData['billing_zip'],
            'address' => $validatedData['address'],
            'account_owner' => $validatedData['account_owner'],
            'parent_id' => getOrganizationId(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json(['success' => 'Account saved successfully!', 'company' => $company]);

    }

    public function getData(Request $request)
    {
        $invoices = Account::join('users', 'accounts.account_owner', '=', 'users.id')
            ->where('accounts.parent_id', getOrganizationId())
            ->orderBy('accounts.id', 'desc')
            ->select([
                'accounts.id',
                'accounts.company_name',
                'accounts.email',
                'accounts.mobile_no',
                'accounts.website',
                'users.first_name',
                'users.last_name',
                'accounts.address',
                'accounts.city',
                'accounts.state',
                'accounts.zip',
                'users.id as users_id',
            ]);

        if ($request->has('selectedValue') && ! empty($request->selectedValue)) {
            $invoices->where('users_id', $request->selectedValue);
        }

        return DataTables::of($invoices)
            ->filterColumn('account_owner_name', function ($query, $keyword) {
                // Use the correct SQL CONCAT function
                $query->whereRaw("CONCAT(users.first_name, ' ', users.last_name) LIKE ?", ["%{$keyword}%"]);
            })
            ->addColumn('account_owner_name', function ($row) {
                return $row->first_name.' '.$row->last_name;
            })
            ->addColumn('action', function ($row) {
                return '
                <div class="dropdown mx-auto w-fit">
                    <button
                        class="btn btn-sm bg-transparent dropdown-toggle"
                        id="dropdown'.$row->id.'" data-toggle="dropdown" aria-expanded="false"
                        type="button"
                        data-toggle="dropdown"
                        aria-expanded="false"
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M14 5C14 6.104 13.104 7 12 7C10.896 7 10 6.104 10 5C10 3.896 10.896 3 12 3C13.104 3 14 3.896 14 5Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 10C10.896 10 10 10.896 10 12C10 13.104 10.896 14 12 14C13.104 14 14 13.104 14 12C14 10.896 13.104 10 12 10Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 17C10.896 17 10 17.896 10 19C10 20.104 10.896 21 12 21C13.104 21 14 20.104 14 19C14 17.896 13.104 17 12 17Z"
                                fill="#828282"
                            />
                        </svg>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdown'.$row->id.'">
                        <li>
                            <a onclick="editSectionAccount('.$row->id.')" data-toggle="modal"
                data-target="#selectClientModalUpdateAccount" class="dropdown-item" href="#">Edit</a>
                        </li>
                        <li>
                            <a id="deleteAccount" data-toggle="modal" data-target="#DeleteModal" data-value='.encodeId($row->id).' class="dropdown-item" href="#">Delete</a>
                        </li>
                    </ul>
                </div>';
            })
            ->rawColumns(['action']) // This ensures that the HTML is rendered correctly
            ->make(true);
    }

    public function edit(Request $request)
    {
        $id = $request->id;
        $account = Account::find($id);

        return response()->json($account);

    }

    public function updateAccount(Request $request)
    {
        $validatedData = $request->validate([
            'company_name' => 'required|string',
            'website' => 'nullable',
            'company_email' => 'required|email',
            'company_phone' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'zip' => 'required|string',
            'billing_address' => 'required|string',
            'billing_city' => 'required|string',
            'billing_state' => 'required|string',
            'billing_zip' => 'required|string',
            'address' => 'required|string',
            'account_owner' => 'required|integer',
        ]);

        $id = $request->account_id;

        // Find the account by ID, if it exists, update it, otherwise, create a new one.
        $account = Account::find($id);

        $account->update([
            'company_name' => $validatedData['company_name'],
            'website' => $validatedData['website'],
            'email' => $validatedData['company_email'],
            'mobile_no' => $validatedData['company_phone'],
            'city' => $validatedData['city'],
            'state' => $validatedData['state'],
            'zip' => $validatedData['zip'],
            'billing_address' => $validatedData['billing_address'],
            'billing_city' => $validatedData['billing_city'],
            'billing_state' => $validatedData['billing_state'],
            'billing_zip' => $validatedData['billing_zip'],
            'address' => $validatedData['address'],
            'account_owner' => $validatedData['account_owner'],
            'updated_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Account updated successfully!');

    }

    // public function accountFileImport(Request $request)
    // {
    //     // try {
    //     if ($request->hasFile('file')) {
    //         $extension = File::extension($request->file->getClientOriginalName());
    //         if ($extension == "xls" || $extension == "xlsx") {
    //             $division = $request->input('division');
    //             Excel::import(new AccountImport, $request->file('file')->store('temp'));
    //             return response()->json(['success' => 'true'], HTTP_OK);
    //             //'Your file is a valid xls or csv file'
    //         } else {
    //             return response()->json([
    //                 'success' => 'false',
    //                 'message' => "File is a $extension file.!! Please upload a valid xls file..!"
    //             ], HTTP_BAD_REQUEST);
    //         }
    //     }
    // }
    public function accountFileImport(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xls,xlsx|max:2048', // 2MB max
        ]);

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            if (! in_array($extension, ['xls', 'xlsx'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Please upload an Excel file (.xls, .xlsx)',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $import = new AccountImport;
            Excel::import($import, $file);

            $importedCount = $import->getRowCount();
            $failedRows = $import->getFailures();

            if (count($failedRows) > 0) {
                $errors = [];
                foreach ($failedRows as $failure) {
                    $errors[] = [
                        'row' => $failure->row(),
                        'errors' => $failure->errors(),
                        'values' => $failure->values(),
                    ];
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Import completed with some errors',
                    'imported_count' => $importedCount,
                    'error_count' => count($failedRows),
                    'errors' => $errors,
                ], Response::HTTP_OK);
            }

            return response()->json([
                'success' => true,
                'message' => 'Accounts imported successfully',
                'imported_count' => $importedCount,
            ], Response::HTTP_OK);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'User not found: '.$e->getMessage(),
            ], Response::HTTP_NOT_FOUND);

        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values(),
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Validation errors occurred during import',
                'errors' => $errors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing file: '.$e->getMessage(),
                'file_error' => true,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function exportAccounts(Request $request)
    {
        return Excel::download(new AccountExport, 'organization.accounts.xlsx');
    }

    public function delete($id)
    {
        $contact = Account::findOrFail(decodeId($id));
        $contact->delete();
        Opportunity::where('account_id', decodeId($id))->delete();
        Contact::where('account', decodeId($id))->delete();
        PropertyInformation::where('company_id', decodeId($id))->delete();

        return response()->json(['success' => 'true'], HTTP_OK);
    }
}
