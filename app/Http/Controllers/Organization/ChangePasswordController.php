<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ChangePasswordController extends Controller
{
    public function changePasswordScreen()
    {
        return view('organization.change-password');
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', 'min:8', 'max:40', 'alpha_num'],
            'password_confirmation' => 'required',
        ]);

        $user = User::where('email', auth()->user('users')->email)->first();
        if (is_object($user)) {
            if (Hash::check($request['current_password'], $user->password)) {
                if (Hash::check($request['password'], $user->password)) {
                    return redirect()->route(getRouteAlias().'.changePasswordScreen')->with('error', 'Please enter a new password that not use before');
                } else {
                    $user->forceFill([
                        'password' => Hash::make($request['password']),
                    ])->save();

                    return redirect()->route(getRouteAlias().'.changePasswordScreen')->with('success', 'Password Updated Successfully! ');
                }
            } else {
                return redirect()->route(getRouteAlias().'.changePasswordScreen')->with('error', 'Current Password does not match! ');
            }
        }
    }
}
