<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\GenerateEstimate;
use App\Services\EstimateService;
use Illuminate\Http\Request;

class ArchiveEstimateController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:can_archive');
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return EstimateService::renderTableDate($request);
        }

        return view('organization.archive.index');
    }

    public function changeArchiveStatus($id)
    {
        $generateEstmate = GenerateEstimate::where('id', decodeID($id))->where('is_archive', 1)->firstorfail();
        $status = $generateEstmate->update([
            'is_archive' => 0,
        ]);
        if ($status) {
            return redirect()->back()->with('success', 'Estimate Unarchive Suucessfully');
        }

        return redirect()->back()->with('error', "Estimate Unarchive Doesn\'t Suucessfully");
    }
}
