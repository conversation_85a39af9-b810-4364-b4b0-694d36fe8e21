<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Models\User;
use App\Services\EmailService;
use App\Traits\PermissionMiddlewareTrait;
use Carbon\Carbon;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Spatie\Permission\Models\Role;
use Yajra\DataTables\Facades\DataTables;

class UserController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            'index' => ['users_setting'],
            'create' => ['users_setting'],
            'store' => ['users_setting'],
            'edit' => ['users_setting'],
            'update' => ['users_setting'],
            'delete' => ['users_setting'],
            'updateStatus' => ['users_setting'],
            'sendMail' => ['users_setting'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index(Request $request)
    {
        $totalUser = User::withTrashed()->where(['parent_id' => getOrganizationId(), 'type' => EMPLOYEE])->orWhere('id', getOrganizationId())->count();
        $onlineUsers = User::withTrashed()->where(['parent_id' => getOrganizationId(), 'type' => EMPLOYEE, 'is_online' => true])->orWhere('id', getOrganizationId())->count();
        if ($request->ajax()) {
            $model = User::withTrashed()->where(['parent_id' => getOrganizationId(), 'type' => EMPLOYEE])
                ->whereNot('id', auth('web')->user()->id)
                ->orWhere('id', getOrganizationId())
                ->latest()
                ->select('image', 'email', 'first_name', 'last_name', 'last_login_at', 'status', 'id', 'deleted_at');

            return DataTables::eloquent($model)
                ->addColumn('image_name', function (User $user) {
                    if ($user->image) {
                        return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('storage/user_images/'.$user->image).'" alt="profile image">
                        <h2 class="profile_name" style="white-space:nowrap;">'.$user->first_name.' '.$user->last_name.'</h2>
                    </div>';
                    } else {
                        return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name" style="white-space:nowrap;">'.$user->first_name.' '.$user->last_name.'</h2>
                    </div>';
                    }
                })
                ->addColumn('role', function (User $user) {
                    return '<span style="white-space: nowrap;">'.ucwords($user->roles->pluck('name')->implode(', ')).'</span>';
                })
                ->addColumn('last_login_at', function (User $user) {
                    if ($user?->last_login_at) {
                        return '<div style="white-space:nowrap">'.customDateFormat(\Carbon\Carbon::parse($user?->last_login_at)).' '.customTimeFormat(\Carbon\Carbon::parse($user?->last_login_at)).'</div>';
                    }
                })
                ->addColumn('status', function (User $user) {
                    if ($user->deleted_at != null) {
                        return ' <div class="status danger">Deleted</div>';
                    }

                    if ($user->status == 'Pending') {
                        return ' <div class="status warning"> Pending</div>';
                    } elseif ($user->status == 'Active') {
                        return ' <div class="status success">Active</div>';
                    } elseif ($user->status == 'Inactive') {
                        return ' <div class="status danger">Inactive</div>';
                    }
                })
                ->addColumn('action', function (User $user) {
                    if ($user->deleted_at != null) {
                        return '';
                    }

                    return ' <div class="dropdown mx-auto w-fit">
                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                        <img src="'.asset('admin_assets/images/icons/vertical-dots.svg').'"
                            alt="vertical dots" width="24px" height="24px">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown1">
                        <li><a class="dropdown-item" id="changeUserStatus" data-toggle="modal"  data-value="'.$user->status.'" data-id="'.encodeID($user->id).'" data-target="#changeStatus" >Change Status</a></li>
                        <li><a class="dropdown-item" href="'.route(getRouteAlias().'.users.edit', encodeId($user->id)).'">Edit</a></li>
                        <li>
                        <a class="dropdown-item" id="deleteUser" data-toggle="modal" data-target="#DeleteModal"
                        data-value='.encodeId($user->id).'>
                        Remove User
                    </a>
                        </li>
                    </ul>
                </div>';
                })
                ->rawColumns(['status', 'action', 'image_name', 'last_login_at', 'role'])
                ->only(['image_name', 'email', 'role', 'last_login_at', 'status', 'action'])
                ->toJson();
        }

        return view('organization.user.index', get_defined_vars());
    }

    public function create()
    {
        $roles = Role::whereNot('name', 'organization')->get();
        $user = new User;
        $action = URL::route(getRouteAlias().'.users.add');

        return view('organization.user.create', get_defined_vars());
    }

    public function store(UserRequest $request)
    {
        DB::beginTransaction();
        try {
            $user = new User;
            if ($request->has('image')) {
                $imageName = uniqid().'_'.trim($request->image->getClientOriginalName());
                $filePath = $request->image->storePubliclyAs('user_images', $imageName, 'public');
                $user->image = $imageName;
            }
            $password = 'User@'.uniqid();
            $user->first_name = request('first_name');
            $user->last_name = request('last_name');
            $user->email = request('email');
            $user->mobile_no = request('mobile_no');
            $user->password = bcrypt($password);
            $user->status = 'Active';
            $user->is_active = 1;
            $user->email_verified_at = Carbon::now()->timestamp;
            $user->parent_id = getOrganizationId();
            $user->type = EMPLOYEE;
            $user->save();
            $roles = Role::whereIn('id', request('role_id'))->pluck('name');
            if (is_object($roles)) {
                $user->assignRole($roles);
            }
            $this->sendMail($password, $user->first_name.' '.$user->last_name);
            DB::commit();

            return redirect()->route(getRouteAlias().'.users.list')->with('success', 'Invite sent to user successfully!.');
            // return back();
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function edit($id)
    {
        $user = User::where('id', decodeId($id))->first();
        if (is_object($user)) {
            $roles = Role::whereNot('name', 'organization')->get();
            $action = URL::route(getRouteAlias().'.user.update', ['id' => $id]);

            return view('organization.user.create', compact('user', 'roles', 'action'));
        } else {
            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function update(UserRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            $sendMail = false;
            $user = User::where('id', decodeId($id))->first();
            if (! is_object($user)) {
                return redirect()->back()->with('error', 'Something went wrong!');
            } else {
                if ($request->has('image')) {
                    if (File::exists(public_path('storage/user_images/'.$user->image))) {
                        File::delete(public_path('storage/user_images/'.$user->image));
                    }
                    $imageName = uniqid().'_'.trim($request->image->getClientOriginalName());
                    $filePath = $request->image->storePubliclyAs('user_images', $imageName, 'public');
                    $user->image = $imageName;
                }
                if ($user->email !== request('email')) {
                    $sendMail = true;
                }
                $user->first_name = request('first_name');
                $user->last_name = request('last_name');
                $user->email = request('email');
                $user->mobile_no = request('mobile_no');
                $user->save();
                $user->roles()->detach();
                $roles = Role::whereIn('id', request('role_id'))->pluck('name');
                if (is_object($roles)) {
                    $user->assignRole($roles);
                }
                if ($sendMail) {
                    $this->sendMail(null, $user->first_name.' '.$user->last_name);
                }
                DB::commit();

                return redirect()->route(getRouteAlias().'.users.list')->with('success', $sendMail ? 'Invite sent to user successfully!.' : 'User Data updated successfully!');
            }
        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function delete($id)
    {
        $user = User::find(decodeId($id));
        if (! is_object($user)) {
            return response()->json([
                'success' => 'false',
            ], HTTP_BAD_REQUEST);
        } else {
            if ($user->image) {
                if (File::exists(public_path('storage/user_images/'.$user->image))) {
                    File::delete(public_path('storage/user_images/'.$user->image));
                }
            }
            $user->delete();

            return response()->json(['success' => 'true'], HTTP_OK);
        }
    }

    public function updateStatus(Request $request)
    {
        $user = User::where('id', decodeID($request->id))->first();
        if (is_object($user)) {
            $user->update([
                'status' => $request->status,
            ]);

            return response()->json('success', HTTP_OK);
        } else {
            return response()->json('error', HTTP_BAD_REQUEST);
        }
    }

    private function sendMail($password = null, $name = null)
    {
        $OrgEmail = User::where('id', getOrganizationId())->first();
        $email_template = 'email_template.user-invite';
        $url = '/login';
        $payload['url'] = asset($url);
        $payload['email'] = request('email');
        $payload['password'] = $password;
        $payload['name'] = $name;
        $payload['org_email'] = $OrgEmail?->email ?? '';
        try {
            EmailService::send($email_template, 'Account Created', request('email'), $payload);
        } catch (\Exception $e) {
            // dd($e->getMessage());
        }
    }
}
