<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\Organization\InvoiceRequest;
use App\Models\Contact;
use App\Models\CostSummary;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateItem;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Transaction;
use App\Models\User;
use App\Services\EmailService;
use App\Services\InvoiceService;
use App\Traits\PermissionMiddlewareTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class InvoiceController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'index' => ['create_invoice', 'invoice_listing', 'delete_invoice', 'create_operational_invoice', 'invoice_detail', 'collect_invoice'],
            'create' => ['create_invoice'],
            'addProduct' => ['create_invoice'],
            'retriveProduct' => ['create_invoice'],
            'removeProduct' => ['create_invoice'],
            'generate' => ['create_invoice'],
            'removeInvoice' => ['delete_invoice'],
            'viewInvoice' => ['invoice_detail', 'create_operational_invoice'],
            'collectPayment' => ['collect_invoice'],
            'invoiceCollectAmountDetails' => ['collect_invoice'],
            'operationalInvoice' => ['create_operational_invoice'],
            'operationalInvoiceCreate' => ['create_operational_invoice'],
            'downloadInvoice' => ['invoice_detail'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index(Request $request, InvoiceService $invoiceService)
    {
        return $invoiceService->listing($request);
    }

    public function create($id)
    {

        $client = Contact::where('id', decodeID($id))->with('organization')->firstOrFail();
        // dd($client);
        $latest_invoice = Invoice::where('client_id', $client->id)
            ->where('is_sent', 0)
            ->orderByDesc('created_at')
            ->firstOrCreate(['client_id' => $client->id, 'organization_id' => getOrganizationId()]);
        $invoices_products = InvoiceItem::where('invoice_id', $latest_invoice ? $latest_invoice->id : null)->get();
        // $organizationInvoices = Invoice::with('invoiceItems')->where('organization_id', getOrganizationId())->pluck('id');
        $invoiceItems = InvoiceItem::where('invoice_id', $latest_invoice?->id)->select('id', 'name')->get();
        $organization = User::where('id', getOrganizationId())->first();

        return view('organization.invoices.create', compact('client', 'invoices_products', 'latest_invoice', 'invoiceItems', 'organization'));
    }

    public function addInvoiceItem(Request $request, $invoiceID)
    {
        $this->validate($request, [
            'product_name' => 'required|string|max:255',
            'quantity' => 'required|integer|gt:0',
            'unit_price' => 'required|numeric',
        ]);

        $latest_invoice = Invoice::where('id', decodeID($invoiceID))->firstOrFail();
        $invoice_item = InvoiceItem::updateOrCreate([
            'id' => decodeID($request->invoice_item_id),
        ], [
            'invoice_id' => $latest_invoice->id,
            'name' => $request->product_name,
            'quantity' => $request->quantity,
            'unit_price' => $request->unit_price,
            'total_price' => $request->quantity * $request->unit_price,
            'description' => $request->description,
        ]);
        $invoices_products = InvoiceItem::where('invoice_id', $latest_invoice->id)->get();
        $tableView = View::make('organization.invoices.partials.products_table', compact('invoices_products'))->render();

        return response()->json([
            'html' => $tableView,
        ], HTTP_OK);
    }

    public function retriveInvoiceItem($id)
    {
        $product_item = InvoiceItem::where('id', decodeID($id))->firstOrFail();

        return response()->json(['product' => $product_item]);
    }

    public function removeInvoiceItem(Request $request, $id)
    {
        $product_item = InvoiceItem::findOrFail(decodeID($id));
        $invoices_products = InvoiceItem::where('invoice_id', $product_item->invoice_id)->whereNot('id', $product_item->id)->get();
        $product_item->delete();
        $tableView = View::make('organization.invoices.partials.products_table', compact('invoices_products'))->render();

        return response()->json([
            'html' => $tableView,
        ], HTTP_OK);
    }

    public function store(InvoiceRequest $request, $invoiceID, InvoiceService $invoiceService)
    {
        $invoiceService->simpleinvoiceUpdateOrCreate($request, $invoiceID);

        return response()->json(['message' => 'Invoice Created Successfully']);
    }

    public function removeInvoice($id)
    {
        $invoice = Invoice::findOrFail(decodeID($id));
        if ($invoice->status == 'paid') {
            return response()->json(['message' => 'You cann\'t delete this paid invoice'], 303);
        }
        $invoice->delete();

        return response()->json(['message' => 'Invoice Deleted Successfully', 'invoice' => $invoice]);
    }

    public function viewInvoice($id)
    {
        $invoice = Invoice::with('client')->findOrFail(decodeID($id));
        // dd(decodeID($id));
        $invoiceItems = InvoiceItem::where('invoice_id', decodeID($id))->get();
        $statusMappings = [
            'paid' => 'approved',
            'due' => 'unpaid',
            'overdue' => 'overdue',
        ];
        if ($invoice->operation_id) {
            $invoice->load('operation.opportunityid');
        }

        return view('organization.invoices.view', compact('invoice', 'invoiceItems', 'statusMappings'));
    }

    public function editInvoice($id)
    {

        $invoice = Invoice::where('id', decodeID($id))->firstOrFail();

        if ($invoice->status == 'paid') {
            return redirect()->route(getRouteAlias().'.invoices.index')->with('error', 'You cann\'t edit this paid invoice');
        }

        $client = Contact::where('id', $invoice->client_id)->with('propertyAddress')->firstOrFail();
        // dd($invoice);
        $invoices_products = InvoiceItem::where('invoice_id', decodeID($id))->get();
        $organizationInvoices = Invoice::with('invoiceItems')->where('organization_id', getOrganizationId())->pluck('id');
        $invoiceItems = InvoiceItem::whereIn('invoice_id', $organizationInvoices)->select('id', 'name')->get();
        $costSummary = null;
        if ($invoice->operation_id) {
            $costSummary = CostSummary::where('organization_id', getOrganizationId())->where('operation_id', $invoice->operation_id)->first();
        }

        $organization = User::where('id', getOrganizationId())->first();

        $docs = DB::table('media')->where('model_type', 'App\Models\Invoice')->where('model_id', decodeID($id))->get();

        // dd($docs);
        return view('organization.invoices.edit', compact('client', 'invoice', 'invoices_products', 'invoiceItems', 'costSummary', 'organization', 'docs'));
    }

    public function updateInvoice(InvoiceRequest $request, $id, InvoiceService $invoiceService)
    {
        $invoiceService->simpleinvoiceUpdateOrCreate($request, $id, true);

        return response()->json(['message' => 'Invoice updated Successfully']);
    }

    public function collectPayment(Request $request, $id)
    {
        $invoice = Invoice::where('id', decodeID($id))->firstOrFail();
        if ($invoice->status == 'paid') {
            return redirect()->route(getRouteAlias().'.invoices.index')->with('error', "You can't update an paid invoice");
        }

        $request->validate([
            'transaction_date' => 'required',
            'payment_method' => 'required|string',
        ], [
            'transaction_date.required' => 'Transaction date cannot be empty.',
            'payment_method.required' => 'Payment method  cannot be empty.',
        ]);
        $convertedTransactionDate = str_replace('/', '-', $request->transaction_date);
        $transactionDate = Carbon::createFromFormat('m-d-Y', $convertedTransactionDate)->format('Y-m-d');
        $transaction = Transaction::create([
            'transaction_number' => Str::random(8),
            'payment_method' => $request->payment_method,
            'transaction_date' => $transactionDate,
            'notes' => $request->notes,
            'status' => 'paid',
            'invoice_id' => decodeID($id),
            'additional_information' => $request->additional_information,
        ]);

        $transaction->invoice->update(['status' => 'paid']);
        $company_email_template = 'email_template.client.company-payment-done';
        $client_email_template = 'email_template.client.client-payment-done';
        $payload['company_name'] = $invoice->client->organization->company_name;
        $payload['invoice_number'] = $invoice->invoice_number;
        $payload['payment_amount'] = $invoice->total;
        $payload['payment_method'] = $request->payment_method;
        $payload['transaction_date'] = $convertedTransactionDate;
        $payload['company_email'] = $invoice->client->organization->email;
        $payload['client_name'] = $invoice->client->full_name;
        $payload['org_image'] = $invoice->client->organization->profile_photo_path ? asset('storage/user_images/'.$invoice->client->organization->profile_photo_path) : null;
        EmailService::send($company_email_template, 'Payment Received from '.$invoice->client->first_name, $invoice->client->organization->email, $payload, true);
        EmailService::send($client_email_template, 'Payment Received from '.$invoice->client->organization->company_name, $invoice->client->email, $payload, true);

        return response()->json(['message' => 'Invoice Paid Successfully']);
    }

    public function invoiceCollectAmountDetails($id)
    {
        $ClientAvailableAmount = Invoice::where('client_id', decodeID(request()->query('clientId')))
            ->where('is_sent', 1)
            ->where('status', 'paid')
            ->pluck('total')
            ->sum();

        $invoiceDetails = Invoice::with('transaction:invoice_id,transaction_date,notes,payment_method,additional_information')->select('id')->findOrFail(decodeID($id));
        $invoiceDetails->makeHidden('id');
        $invoiceDetails->transaction?->makeHidden('invoice_id');

        return response()->json(['invoiceDetails' => $invoiceDetails, 'ClientAvailableAmount' => number_format($ClientAvailableAmount, 2)]);
    }

    public function createOperationalInvoice()
    {

        $generate_estimate = GenerateEstimate::with('manager', 'invoice')->where('id', decodeID(request()->operationId))->with('opportunityid.account')->first();

        if ($generate_estimate?->invoice) {
            return redirect()->back()->with('error', 'Invocie already created against this operation');
        }

        $client = Contact::where('id', $generate_estimate->opportunityid?->client?->id ?? '')->firstOrFail();
        // dd($client);

        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateLabor = EstimateLabor::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateHardMaterial = EstimateHardMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimatePlantMaterial = EstimatePlantMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
            $costSummary = CostSummary::where('organization_id', getOrganizationId())->where('operation_id', $generate_estimate->id)->first();
            $organization = User::where('id', getOrganizationId())->first();

            $opportunitydata = EstimateItem::where('opportunity_id', $generate_estimate->opportunity_id)->get();

            return view('organization.invoices.operational_invoice', get_defined_vars());
        }
    }

    public function storeOperationalInvoice(InvoiceRequest $request, $estimateID, InvoiceService $invoiceService)
    {
        return $invoiceService->storeOperationalInvoice($request, $estimateID);
    }

    public function downloadInvoice($invoiceID)
    {
        $invoice = Invoice::with('invoiceItems')->findorfail(decodeID($invoiceID));

        return InvoiceService::preparePdf($invoice, getOrganizationId(), true);
    }

    public function invoiceItemsDetails($id)
    {
        $invoiceItem = InvoiceItem::where('id', decodeID($id))->select('quantity', 'total_price', 'unit_price', 'description')->first();

        return response()->json($invoiceItem, HTTP_OK);
    }

    public function imageStore(Request $request)
    {
        // $invoice = Invoice::where('id', decodeID(request()->route('estimateID')))->first();
        $file = $request->file('file');
        // dd($file);
        $name = uniqid().'_'.str_replace(' ', '-', trim($file->getClientOriginalName()));

        $filePath = $request->file->storePubliclyAs('uploads', $name, 'public');

        return response()->json([
            'flname' => $name,
            'original_name' => $file->getClientOriginalName(),
            'invoice' => decodeID(request()->route('id')),
        ], HTTP_OK);
    }

    public function imageDelete($organization_id, $id = null)
    {
        if (File::exists(public_path('storage/uploads/'.request('name')))) {
            // dd(request('name'));
            File::delete(public_path('storage/uploads/'.request('name')));
        }

        return response()->json('Image Removed Successfully!', HTTP_OK);
    }

    public function imageDeleteServer(Request $request)
    {
        $fileId = $request->input('file_id'); // Fetch the file_id from the request

        // Find the document using the primary key (id)
        if (isset($fileId)) {
            $document = DB::table('media')->where('id', $fileId)->first();

            if (! $document) {
                return response()->json(['error' => 'Document not found'], 404); // Return error if not found
            }

            // Delete the file from storage
            if ($document->file_name) {
                File::delete(public_path('storage/uploads/'.$document->file_name));
                // \Storage::delete($document->path); // Delete the file from storage
            }
            DB::table('media')->where('id', $fileId)->delete();
        } else {
            // File::delete(storage_path('app/public/opportunity_documents/'. request('filename')));
            File::delete(public_path('storage/uploads/'.request('name')));
        }

        // Delete the record from the database

        return response()->json(['message' => 'Document deleted successfully'], 200);
    }

    public function imageStoreFile(Request $request)
    {
        // $invoice = Invoice::where('id', decodeID(request()->route('estimateID')))->first();
        $file = $request->file('file');

        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $request->file->storePubliclyAs('uploads', $name, 'public');

        return response()->json([
            'flname' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function viewAttachments($id)
    {
        $invoice = Invoice::where('id', decodeId($id))->first();
        if (is_object($invoice)) {
            if (count($invoice->getMedia('document'))) {
                $documents = $invoice->getMedia('document');
                $tableView = View::make('organization.invoices.partials.documents-modal', compact('documents'))->render();

                return json_encode($tableView, HTTP_OK);
            } else {
                return response()->json('No attachments found against this invoice.', HTTP_BAD_REQUEST);
            }
        }

        return response()->json('No record found', HTTP_BAD_REQUEST);
    }
}
