<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Jobs\ClientEstimateJob;
use App\Jobs\ClientInviteLoginJob;
use App\Models\Client;
use App\Models\ClientEstimateAction;
use App\Models\Equipment;
use App\Models\Estimate;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateItem;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateStatus;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\HardMaterial;
use App\Models\Labor;
use App\Models\Margin;
use App\Models\Opportunity;
use App\Models\OtherCost;
use App\Models\PlantMaterial;
use App\Models\ServiceLineWorkType;
use App\Models\SnowSetup;
use App\Models\Subcontractor;
use App\Models\User;
use App\Services\EstimateService;
use App\Traits\PermissionMiddlewareTrait;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class GenerateEstimateController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'index' => ['generate_estimate', 'change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'estimate_listing'],
            'listing' => ['generate_estimate', 'change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'estimate_listing'],
            'create' => ['generate_estimate', 'estimate_listing'],
            'downloadEstimate' => ['generate_estimate', 'estimate_detail', 'estimate_listing'],
            'onlyDownloadEstimate' => ['generate_estimate', 'estimate_detail', 'estimate_listing'],
            'storeEstimate' => ['generate_estimate'],
            'updateStatus' => ['change_estimate_status'],
            'addEquipment' => ['generate_estimate'],
            'addLabor' => ['generate_estimate'],
            'addMaterial' => ['generate_estimate'],
            'addOtherCost' => ['generate_estimate'],
            'addSubContractor' => ['generate_estimate'],
            'getEstimateEquipment' => ['generate_estimate'],
            'getEstimateLabor' => ['generate_estimate'],
            'getEstimateHardMaterial' => ['generate_estimate'],
            'getEstimatePlantMaterial' => ['generate_estimate'],
            'getEstimateOtherCost' => ['generate_estimate'],
            'getEstimateSubContractor' => ['generate_estimate'],
            'deleteEstimateEquipment' => ['generate_estimate'],
            'deleteEstimateLabor' => ['generate_estimate'],
            'deleteEstimateMaterial' => ['generate_estimate'],
            'deleteEstimateOtherCost' => ['generate_estimate'],
            'deleteEstimateSubContractor' => ['generate_estimate'],
            'addNote' => ['generate_estimate'],
            'notesView' => ['generate_estimate', 'estimate_detail'],
            'deleteNote' => ['generate_estimate'],
            'getMargin' => ['generate_estimate', 'estimate_detail'],
            'addDesireMargin' => ['generate_estimate'],
            'archiveEstimate' => ['can_archive', 'estimate_listing'],
            'viewChangeRequest' => ['view_estimate_change_request'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index()
    {
        return view('organization.generate-estimate.table');
    }

    public function archiveEstimates()
    {
        return view('organization.generate-estimate.archives');
    }

    public function listing(Request $request)
    {
        if ($request->ajax()) {
            return EstimateService::renderTableDate($request);
        }
    }

    public function archiveListing(Request $request)
    {
        if ($request->ajax()) {
            return EstimateService::renderTableArchive($request);
        }
    }

    public function create($id)
    {
        $estimate = Estimate::with('client')->where('id', decodeID($id))->first();
        if (! is_object($estimate)) {
            return redirect()->back()->with('error', 'Something went wrong!');
        }
        $equipments = Equipment::where('organization_id', getOrganizationId())->get();
        $equipmentMargin = $this->getMargin("Equipment's");
        $labors = Labor::where('organization_id', getOrganizationId())->get();
        $laborMargin = $this->getMargin('Labor');
        $plantMaterial = PlantMaterial::where('organization_id', getOrganizationId())->get();
        $plantMaterialMargin = $this->getMargin('Plant Material');
        $hardMaterialMargin = $this->getMargin('Hard Material');
        $hardMaterial = HardMaterial::where('organization_id', getOrganizationId())->get();
        $otherCost = OtherCost::where('organization_id', getOrganizationId())->get();
        $otherCostMargin = $this->getMargin('Other Job Costs');
        $contractorMargin = $this->getMargin('Contractor');
        $workTypes = ServiceLineWorkType::with('serviceLine:id,name')->whereHas('serviceLine', function ($query) {
            $query->where('organization_id', getOrganizationId());
        })->get();
        $generate_estimate = GenerateEstimate::where('opportunity_id', decodeID($id))->first();

        return view('organization.generate-estimate.index', get_defined_vars());
    }

    public function downloadEstimate(Request $request, $id)
    {
        // dd($id);
        $generateEstimate = GenerateEstimate::with('client')->updateOrCreate([
            'opportunity_id' => decodeId($id),
        ], [
            'is_complete' => true,
        ]);

        $estimate = Opportunity::where('id', decodeID($id))->first();

        // dd($estimate);

        // IF Request is created by client and company revise the estimate then delete the client action if exist
        //  against estimate and reset the statuses
        if (! $generateEstimate->wasRecentlyCreated) {
            $generateEstimate->load('opportunityid');
            if ($generateEstimate->client_status == 'request_change') {

                $generateEstimate->load('clientAction');
                $generateEstimate->update([
                    'client_status' => 'proposed',
                ]);
                optional($generateEstimate->clientAction)?->delete();
            }
        }
        // dd($generateEstimate->client->organization->companyPropertyAddress->phone_no);
        if ($request->has('sentEmail')) {

            GenerateEstimate::where('id', $generateEstimate->id)->update([
                'status' => 'proposed',
            ]);
            Opportunity::where('id', decodeID($id))->update([
                'client_show' => 1,
                'status' => 3,
            ]);
            $data = $request->except(['select_file', 'subject']);
            // dd(config('mail'));
            $this->sendEstimatePdfEmail($request, $generateEstimate, $data);
        }
        set_time_limit(300); // Increase to 5 minutes
        // dd($request->has('sentEmail'));

        $this->onlyDownloadEstimate($id, $request->has('sentEmail'));

        return back();

    }

    private function sendEstimatePdfEmail(Request $request, $estimate, $data = [])
    {

        $path = public_path('clientEmailUploads').'/'.collect($request->input('send-email'))->first().'EstimatePDF';
        $attachments = $request->select_file;
        // dd($attachments);
        $files = [];
        $path = storage_path('app/public/email_documents');
        // create folder
        if ($attachments != null) {
            if (count($attachments) > 0) {

                if (! File::exists($path)) {
                    File::makeDirectory($path, $mode = 0777, true, true);
                }

                foreach ($attachments as $attachment) {
                    // $name = $path . '/' . $attachment;
                    // $name = time() . '.' . $attachment->getClientOriginalExtension();
                    // $attachment->move($path, $name);
                    $files[] = $path.'/'.$attachment;
                }
            }
        }

        if (array_key_exists('copy', $data) && $data['copy'] == 'yes') {
            if (! array_key_exists('cc_email', $data)) {
                $data['cc_email'] = [];
            }
            $data['cc_email'] = array_unique([...$data['cc_email'], auth()->user()->email]);
        }
        // dd($files);

        $data['files'] = $files;
        $payload['cc_email'] = (array_key_exists('cc_email', $data)) ? $data['cc_email'] : [];
        $payload['bcc_email'] = (array_key_exists('bcc_email', $data)) ? $data['bcc_email'] : [];
        $payload['estimateAttachment'] = array_key_exists('estimateAttachment', $data);
        $payload['subject'] = $data['send-subject'];
        $payload['message'] = $data['send-message'];
        $payload['files'] = $data['files'];
        // dd($payload['estimateAttachment']);
        // dd($payload);
        // ClientEstimateJob::dispatch($estimate, $payload, $path);
        $this->sendClientEstimate($estimate, $payload, $path);
    }

    public function sendClientEstimate(GenerateEstimate $generateEstimate, array $payload, $path = null)
    {
        Log::info('Payload received: ', $payload);

        $pdf = null;
        // if (array_key_exists('estimateAttachment', $payload) && $payload['estimateAttachment'] === true) {
        $response = EstimateService::preparePdf($generateEstimate->opportunity_id, $generateEstimate->client->organization_id);
        $pdf = $response['pdf'];
        // }

        // dd($generateEstimate->client->invite_sent);

        // if (optional($generateEstimate->client)?->invite_sent === 0) {
        $generateEstimate->loadMissing('client');

        // dd($generateEstimate);
        $url = url('client/'.encodeID($generateEstimate->client->organization_id).'/login');
        $password = 'Client@'.uniqid();

        $payload = array_merge($payload, [
            'url' => $url,
            'email' => $generateEstimate->client->email,
            'company_name' => $generateEstimate->client->organization->company_name,
            'company_phone_no' => $generateEstimate->client->mobile_no,
            'primary_color' => $generateEstimate->client->organization->primary_color,
            'company_address' => $generateEstimate->client->address,
            'company_website' => $generateEstimate->client->website,
            'company_email' => $generateEstimate->client->email,
            'password' => $password,
            'name' => $generateEstimate->client->full_name,
            'org_name' => $generateEstimate->client->organization->company_name,
            'org_image' => $generateEstimate->client->organization->profile_photo_path ? asset('storage/user_images/'.$generateEstimate->client->organization->profile_photo_path) : null,
        ]);

        // dd($generateEstimate->client);

        $generateEstimate->client->update([
            'password' => bcrypt($password),
        ]);

        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $generateEstimate->opportunity_id)
            ->sum('total_price');

        $totalCostSumss = DB::table('estimate_items')
            ->where('opportunity_id', $generateEstimate->opportunity_id)
            ->sum('total_cost');

        $generateEstimate->update([
            'total_price' => $totalPriceSumss,
            'total_cost' => $totalCostSumss,
        ]);

        Mail::send('email_template.client.invite', $payload, function ($message) use ($payload, $pdf) {
            $message->to($payload['email'], $payload['email'])
                ->subject('Estimate')
                ->from(config('mail.from.address'), config('mail.from.name'));

            if ($pdf) {
                $message->attachData($pdf->output(), 'estimate.pdf');
            }
        });

        Log::info('Client invite email sent successfully.');
        // }

        $data = [
            'email' => $generateEstimate->client->email,
            'company' => $generateEstimate->client->company_name,
            'message' => $payload['message'],
            'company_name' => $generateEstimate->client->organization->company_name,
            'company_phone_no' => $generateEstimate->client->mobile_no,
            'primary_color' => $generateEstimate->client->organization->primary_color,
            'company_address' => $generateEstimate->client->address,
            'company_website' => $generateEstimate->client->website,
            'company_email' => $generateEstimate->client->organization->email,
            'grand_total' => $generateEstimate->GrandTotal,
            'client_name' => $generateEstimate->client->company_name,
            'url' => url('client/'.encodeID($generateEstimate->client->organization_id).'/estimate/view/'.encodeID($generateEstimate->opportunityid->id)),
        ];

        Mail::send('email_template.estimate-invoice', $data, function ($message) use ($data, $payload, $pdf) {
            if (! empty($payload['cc_email'])) {
                $message->cc($payload['cc_email']);
            }

            if (! empty($payload['bcc_email'])) {
                $message->bcc($payload['bcc_email']);
            }

            $message->to($data['email'], $data['email'])
                ->subject('Estimate Invoice')
                ->from(config('mail.from.address'), config('mail.from.name'));

            if ($pdf) {
                $message->attachData($pdf->output(), 'estimate_invoice.pdf');
            }

            if (! empty($payload['files'])) {
                foreach ($payload['files'] as $file) {
                    if (File::exists($file)) {
                        $message->attach($file);
                    }
                }
            }
        });

        Log::info('Estimate invoice email sent successfully.');

        if (! empty($path) && File::exists($path)) {
            File::deleteDirectory($path);
        }
    }

    public function onlyDownloadEstimate($id, $mailss)
    {
        $organizationId = getOrganizationId();

        return EstimateService::preparePdf(decodeId($id), $organizationId, false, $mailss);
    }

    public function storeEstimate(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $generateEstimate = GenerateEstimate::with('client')->updateOrCreate([
                'opportunity_id' => decodeId($id),
            ], [
                'is_complete' => true,
            ]);

            // IF Request is created by client and company revise the estimate then delete the client action if exist
            //  against estimate and reset the statuses
            if (! $generateEstimate->wasRecentlyCreated) {
                $generateEstimate->load('request');
                // if(optional($generateEstimate)?->request?->created_by == 'client' && $generateEstimate->client_status == 'request_change'){

                if ($generateEstimate->client_status == 'request_change') {
                    $generateEstimate->load('clientAction');
                    $generateEstimate->update([
                        'client_status' => 'proposed',
                    ]);
                    optional($generateEstimate->clientAction)?->delete();
                }
            }

            // if (optional($generateEstimate->client)?->invite_sent == 0) {
            //     ClientInviteLoginJob::dispatch($generateEstimate);
            // }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            info('exception', $e->getMessage());

            return redirect()->back()->with('error', 'Data Not saved successfully!');
        }

        return redirect()->back()->with('success', 'Data saved successfully!');
    }

    public function addWorkTypeAndService(Request $request)
    {
        $generateEstimate = GenerateEstimate::with('client')->updateOrCreate([
            'opportunity_id' => decodeId($request->request_id),
        ], [
            'work_type_id' => decodeId($request->work_type_id),
            'service_line_id' => decodeId($request->service_line_id),
        ]);

        return response()->json(['status' => 'success'], HTTP_OK);
    }

    public function delete($id)
    {
        $estimate = GenerateEstimate::find(decodeId($id));
        if (! is_object($estimate)) {
            return response()->json([
                'success' => 'false',
            ], HTTP_BAD_REQUEST);
        } else {
            $estimate->delete();

            return response()->json(['success' => 'true'], HTTP_OK);
        }
    }

    public function updateStatus(Request $request)
    {

        DB::beginTransaction();
        try {

            $generate_estimate = GenerateEstimate::where('opportunity_id', decodeID($request->id))->first();
            // EstimateStatus::create([
            //     'generate_estimate_id' => $generate_estimate->id,
            //     'status_name' => $request->status,
            //     'created_at' => now(),
            //     'opportunity_id'=> decodeID($request->id),

            // ]);
            $generate_estimate->update([
                'status' => $request->status,
                'won_date' => ($request->status == 'won') ? now() : null,
                'reason' => $request->reason,
                'suggestion' => $request->suggestion,
            ]);

            if ($request->status == 'won') {
                $estimate = Opportunity::findorfail(decodeID($request->id));
                if ($estimate->sales_order_number == 0 || $estimate->sales_order_number == null) {
                    $estimate->sales_order_number = DB::table('estimates')->where('organization_id', $estimate->organization_id)->max('sales_order_number') + 1;
                    $estimate->save();
                }
            }
            // dd($generate_estimate);
            DB::commit();

            return response()->json('success', HTTP_OK);
        } catch (\Exception $e) {
            info(get_class($this).' updateStatus', $e->getMessage());

            return response()->json('error', HTTP_BAD_REQUEST);
        }
    }

    public function addEquipment(Request $request)
    {
        $margin = $this->getMargin("Equipment's");
        // if (!is_object($margin)) {
        //     return response()->json(['error' => 'Please set default and minimum gross margin in settings.'], 400);
        // }
        $defaultValue = is_object($margin) ? $margin->default : 1;
        $minimumValue = is_object($margin) ? $margin->minimum : 1;
        if ($minimumValue == null) {
            return response()->json([
                'message' => 'The gross margin value is not set yet.',
                'errors' => ['gross_margin' => ['The gross margin value is not set yet.']],
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            'equipment_id' => 'required',
            'quantity' => 'required|numeric|gt:0',
            'gross_margin' => 'required|numeric|gte:'.$minimumValue.'|lte:100',
            'cost' => 'required|numeric|gt:0',
            'uom' => 'required',
        ], [
            'required' => 'This field is required',
            'numeric' => 'This field can only contain numeric values',
        ])->validate();
        $generateEstimate = GenerateEstimate::updateOrCreate([
            'opportunity_id' => $request->request_id,
        ]);
        if ($request->updateId && is_numeric($request->updateId)) {
            $estimateMaterial = EstimateMaterial::where('id', $request->updateId)->first();
            if (is_object($estimateMaterial)) {
                $estimateMaterial->update([
                    'equipment_id' => $request->equipment_id,
                    'quantity' => $request->quantity,
                    'gross_margin' => $request->gross_margin,
                    'uom' => $request->uom,
                    'cost' => $request->cost,
                    'total_cost' => $request->quantity * $request->cost,
                ]);
            }
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generateEstimate->id)->get();
            $tableView = View::make('organization.generate-estimate.partials.equipment-table', compact('estimateMaterial'))->render();

            return json_encode($tableView, HTTP_OK);
        }
        $equipment = Equipment::where('id', $request->equipment_id)->first();

        EstimateMaterial::create([
            'generate_estimate_id' => $generateEstimate->id,
            'equipment_id' => $request->equipment_id,
            'quantity' => $request->quantity,
            'gross_margin' => $request->gross_margin,
            'uom' => $request->uom,
            'cost' => $request->cost,
            'total_cost' => $request->quantity * $equipment->cost,
        ]);
        $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generateEstimate->id)->get();

        $tableView = View::make('organization.generate-estimate.partials.equipment-table', compact('estimateMaterial'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function addLabor(Request $request)
    {
        $labor = $this->getMargin('Labor');
        $defaultValue = (is_object($labor) && ! empty($labor->default)) ? $labor->default : 1;
        $minimumValue = (is_object($labor) && ! empty($labor->minimum)) ? $labor->minimum : 1;
        if ($minimumValue == null) {
            return response()->json([
                'message' => 'The gross margin value is not set yet.',
                'errors' => ['gross_margin' => ['The gross margin value is not set yet.']],
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            'labor_id' => 'required',
            'description' => 'required|max:100',
            'labor_uom' => 'required',
            'labor_quantity' => 'required|numeric|gt:0',
            'labor_cost' => 'required|numeric|gt:0',
            'labor_gross_margin' => 'required|numeric|gte:'.$minimumValue.'|lte:100',
        ], [
            'required' => 'This field is required',
            'numeric' => 'This field can only contain numeric values',
        ])->validate();
        $laborBurden = Labor::where('organization_id', getOrganizationId())->value('labor_burden');
        $generateEstimate = GenerateEstimate::updateOrCreate([
            'opportunity_id' => $request->request_id,
        ]);
        $total_cost = ($request->labor_quantity * $request->labor_cost) + ((($request->labor_quantity * $request->labor_cost) * $laborBurden) / 100);
        $cost = $request->labor_cost;
        if ($laborBurden) {
            $grandTotal = (($total_cost) + (($request->labor_gross_margin / 100) * $total_cost)) + (($laborBurden / 100) * (($total_cost) + (($request->labor_gross_margin / 100) * $total_cost)));
            $grandTotal = totalPriceWithLaborBurden($request->labor_quantity, $cost, $request->labor_gross_margin, $laborBurden);
        } else {
            $grandTotal = $total_cost + ($request->labor_gross_margin / 100) * $total_cost;
        }

        if ($request->laborId && is_numeric($request->laborId)) {
            $estimateLabor = EstimateLabor::where('id', $request->laborId)->first();
            if (is_object($estimateLabor)) {
                $estimateLabor->update([
                    'generate_estimate_id' => $generateEstimate->id,
                    'labor_id' => $request->labor_id,
                    'quantity' => $request->labor_quantity,
                    'gross_margin' => $request->labor_gross_margin,
                    'description' => $request->description,
                    'unit_cost' => $cost,
                    'total_cost' => $total_cost,
                    'grand_total' => $grandTotal,
                ]);
            }
            $estimateLabor = EstimateLabor::where('generate_estimate_id', $generateEstimate->id)->get();
            $tableView = View::make('organization.generate-estimate.partials.labor-table', compact('estimateLabor'))->render();

            return json_encode($tableView, HTTP_OK);
        }
        EstimateLabor::create([
            'generate_estimate_id' => $generateEstimate->id,
            'labor_id' => $request->labor_id,
            'quantity' => $request->labor_quantity,
            'gross_margin' => $request->labor_gross_margin,
            'description' => $request->description,
            'unit_cost' => $cost,
            'total_cost' => $total_cost,
            'grand_total' => $grandTotal,
        ]);
        $estimateLabor = EstimateLabor::where('generate_estimate_id', $generateEstimate->id)->get();

        $tableView = View::make('organization.generate-estimate.partials.labor-table', compact('estimateLabor'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function addMaterial(Request $request)
    {
        $type = $request->type == 'hard' ? 'Hard Material' : 'Plant Material';
        $material = $this->getMargin($type);
        $defaultValue = is_object($material) ? $material->default : 1;
        $minimumValue = is_object($material) ? $material->minimum : 1;
        if ($minimumValue == null) {
            return response()->json([
                'message' => 'The gross margin value is not set yet.',
                'errors' => ['gross_margin' => ['The gross margin value is not set yet.']],
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            $request->type == 'hard' ? 'hard_material_id' : 'plant_material_id' => 'required',
            'material_uom' => 'required',
            'material_quantity' => 'required|numeric|gt:0',
            'material_cost' => 'required|numeric|gt:0',
            'material_gross_margin' => 'required|numeric|gte:'.$minimumValue.'|lte:100',
        ], [
            'required' => 'This field is required',
            'numeric' => 'This field can only contain numeric values',
        ])->validate();
        // $labor = Labor::where('id', $request->labor_id)->first();
        $generateEstimate = GenerateEstimate::updateOrCreate([
            'opportunity_id' => $request->request_id,
        ]);

        if ($request->type == 'hard') {
            if ($request->materialId && is_numeric($request->materialId)) {
                $estimateMaterial = EstimateHardMaterial::where('id', $request->materialId)->first();
                $labors = Labor::where('organization_id', getOrganizationId())->where('name', 'Laborers')->select('cost')->first();
                $laborBurden = Labor::where(['organization_id' => getOrganizationId()])->value('labor_burden');
                $labor_hr = $estimateMaterial->hardMaterial->labor;
                $total_labor_hours = $estimateMaterial->quantity * $labor_hr;
                $labor_cost = $total_labor_hours * $labors->cost;
                $labor_burden = ($labor_cost * $laborBurden) / 100;
                $total_labor_cost = $labor_cost + $labor_burden;
                if (is_object($estimateMaterial)) {
                    $estimateMaterial->update([
                        'generate_estimate_id' => $generateEstimate->id,
                        'hard_material_id' => request('hard_material_id'),
                        'quantity' => request('material_quantity'),
                        'gross_margin' => request('material_gross_margin'),
                        'uom' => request('material_uom'),
                        'unit_cost' => request('material_cost'),
                        'total_cost' => (request('material_quantity') * request('material_cost')) + $total_labor_cost,
                        'total_labor_cost' => $total_labor_cost,
                    ]);
                }
                $estimateMaterial = EstimateHardMaterial::where('generate_estimate_id', $generateEstimate->id)->get();
                $tableView = View::make('organization.generate-estimate.partials.material-table', compact('estimateMaterial'))->render();

                return json_encode($tableView, HTTP_OK);
            }
            $this->createEstimateHardMaterial($generateEstimate);
            $estimateMaterial = EstimateHardMaterial::where('generate_estimate_id', $generateEstimate->id)->get();
        } elseif ($request->type == 'plant') {
            if ($request->materialId && is_numeric($request->materialId)) {
                $estimateMaterial = EstimatePlantMaterial::where('id', $request->materialId)->first();
                $labors = Labor::where('organization_id', getOrganizationId())->where('name', 'Laborers')->select('cost')->first();
                $laborBurden = Labor::where(['organization_id' => getOrganizationId()])->value('labor_burden');
                $labor_hr = $estimateMaterial->plantMaterial->install;
                $total_labor_hours = $estimateMaterial->quantity * $labor_hr;
                $labor_cost = $total_labor_hours * $labors->cost;
                $labor_burden = ($labor_cost * $laborBurden) / 100;
                $total_labor_cost = $labor_cost + $labor_burden;
                if (is_object($estimateMaterial)) {
                    $estimateMaterial->update([
                        'generate_estimate_id' => $generateEstimate->id,
                        'plant_material_id' => request('plant_material_id'),
                        'quantity' => request('material_quantity'),
                        'gross_margin' => request('material_gross_margin'),
                        'uom' => request('material_uom'),
                        'unit_cost' => request('material_cost'),
                        'total_cost' => (request('material_quantity') * request('material_cost')) + $total_labor_cost,
                        'total_labor_cost' => $total_labor_cost,
                    ]);
                }
                $estimateMaterial = EstimatePlantMaterial::where('generate_estimate_id', $generateEstimate->id)->get();
                $tableView = View::make('organization.generate-estimate.partials.material-table', compact('estimateMaterial'))->render();

                return json_encode($tableView, HTTP_OK);
            }
            $this->createEstimatePlantMaterial($generateEstimate);
            $estimateMaterial = EstimatePlantMaterial::where('generate_estimate_id', $generateEstimate->id)->get();
        }

        $tableView = View::make('organization.generate-estimate.partials.material-table', compact('estimateMaterial'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function addOtherCost(Request $request)
    {
        $otherCostMargin = $this->getMargin('Other Job Costs');
        $defaultValue = is_object($otherCostMargin) ? $otherCostMargin->default : 1;
        $minimumValue = is_object($otherCostMargin) ? $otherCostMargin->minimum : 1;
        if ($minimumValue == null) {
            return response()->json([
                'message' => 'The gross margin value is not set yet.',
                'errors' => ['gross_margin' => ['The gross margin value is not set yet.']],
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            'other_cost_id' => 'required',
            'cost_description' => 'required|max:150',
            'cost_uom' => 'required',
            'cost_quantity' => 'required|numeric|gt:0',
            'cost_unit_cost' => 'required|numeric|gt:0',
            'cost_gross_margin' => 'required|numeric|gte:'.$minimumValue.'|lte:100',
        ], [
            'required' => 'This field is required',
            'numeric' => 'This field can only contain numeric values',
        ])->validate();
        // $labor = Labor::where('id', $request->labor_id)->first();
        $generateEstimate = GenerateEstimate::updateOrCreate([
            'opportunity_id' => $request->request_id,
        ]);
        if ($request->costId && is_numeric($request->costId)) {
            $estimateOtherCost = EstimateOtherCost::where('id', $request->costId)->first();
            if (is_object($estimateOtherCost)) {
                $estimateOtherCost->update([
                    'generate_estimate_id' => $generateEstimate->id,
                    'other_cost_id' => $request->other_cost_id,
                    'quantity' => $request->cost_quantity,
                    'gross_margin' => $request->cost_gross_margin,
                    'description' => $request->cost_description,
                    'unit_cost' => $request->cost_unit_cost,
                    'uom' => $request->cost_uom,
                ]);
            }
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generateEstimate->id)->get();

            $tableView = View::make('organization.generate-estimate.partials.cost-table', compact('estimateOtherCost'))->render();

            return json_encode($tableView, HTTP_OK);
        }
        EstimateOtherCost::create([
            'generate_estimate_id' => $generateEstimate->id,
            'other_cost_id' => $request->other_cost_id,
            'quantity' => $request->cost_quantity,
            'gross_margin' => $request->cost_gross_margin,
            'description' => $request->cost_description,
            'unit_cost' => $request->cost_unit_cost,
            'uom' => $request->cost_uom,
        ]);
        $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generateEstimate->id)->get();

        $tableView = View::make('organization.generate-estimate.partials.cost-table', compact('estimateOtherCost'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function addSubContractor(Request $request)
    {
        $contractor = $this->getMargin('Contractor');
        $defaultValue = is_object($contractor) ? $contractor->default : 1;
        $minimumValue = is_object($contractor) ? $contractor->minimum : 1;
        if ($minimumValue == null) {
            return response()->json([
                'message' => 'The gross margin value is not set yet.',
                'errors' => ['gross_margin' => ['The gross margin value is not set yet.']],
            ], 422);
        }
        $validator = Validator::make($request->all(), [
            'contractor_name' => 'required|max:150',
            'contractor_uom' => 'required|max:20',
            'contractor_quantity' => 'required|numeric|gt:0',
            'contractor_cost' => 'required|numeric|gt:0',
            'contractor_gross_margin' => 'required|numeric|gte:'.$minimumValue.'|lte:100',
        ], [
            'required' => 'This field is required',
            'numeric' => 'This field can only contain numeric values',
        ])->validate();
        $generateEstimate = GenerateEstimate::updateOrCreate([
            'opportunity_id' => $request->request_id,
        ]);
        if ($request->contractorId && is_numeric($request->contractorId)) {
            $estimateSubContractor = EstimateSubContractor::where('id', $request->contractorId)->first();
            if (is_object($estimateSubContractor)) {
                $estimateSubContractor->update([
                    'generate_estimate_id' => $generateEstimate->id,
                    'name' => $request->contractor_name,
                    'quantity' => $request->contractor_quantity,
                    'gross_margin' => $request->contractor_gross_margin,
                    'unit_cost' => $request->contractor_cost,
                    'uom' => $request->contractor_uom,
                ]);
            }
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generateEstimate->id)->get();

            $tableView = View::make('organization.generate-estimate.partials.contractor-table', compact('estimateSubContractor'))->render();

            return json_encode($tableView, HTTP_OK);
        }
        EstimateSubContractor::create([
            'generate_estimate_id' => $generateEstimate->id,
            'name' => $request->contractor_name,
            'quantity' => $request->contractor_quantity,
            'gross_margin' => $request->contractor_gross_margin,
            'unit_cost' => $request->contractor_cost,
            'uom' => $request->contractor_uom,
        ]);
        $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generateEstimate->id)->get();

        $tableView = View::make('organization.generate-estimate.partials.contractor-table', compact('estimateSubContractor'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    private function createEstimateHardMaterial($generateEstimate)
    {
        $labors = Labor::where('organization_id', getOrganizationId())->where('name', 'Laborers')->select('cost')->first();
        $laborBurden = Labor::where(['organization_id' => getOrganizationId()])->value('labor_burden');
        $hardMaterial = HardMaterial::where('id', request('hard_material_id'))->first();
        $labor_hr = $hardMaterial->labor;
        $total_labor_hours = request('material_quantity') * $labor_hr;
        $labor_cost = $total_labor_hours * $labors->cost;
        $labor_burden = ($labor_cost * $laborBurden) / 100;
        $total_labor_cost = $labor_cost + $labor_burden;

        return EstimateHardMaterial::create([
            'generate_estimate_id' => $generateEstimate->id,
            'hard_material_id' => request('hard_material_id'),
            'quantity' => request('material_quantity'),
            'gross_margin' => request('material_gross_margin'),
            'uom' => request('material_uom'),
            'unit_cost' => request('material_cost'),
            'total_cost' => (request('material_quantity') * request('material_cost')) + $total_labor_cost,
            'total_labor_cost' => $total_labor_cost,
        ]);
    }

    private function createEstimatePlantMaterial($generateEstimate)
    {
        $labors = Labor::where('organization_id', getOrganizationId())->where('name', 'Laborers')->select('cost')->first();
        $laborBurden = Labor::where(['organization_id' => getOrganizationId()])->value('labor_burden');
        $plantMaterial = PlantMaterial::where('id', request('plant_material_id'))->first();
        $labor_hr = $plantMaterial->install;
        $total_labor_hours = request('material_quantity') * $labor_hr;
        $labor_cost = $total_labor_hours * $labors->cost;
        $labor_burden = ($labor_cost * $laborBurden) / 100;
        $total_labor_cost = $labor_cost + $labor_burden;

        return EstimatePlantMaterial::create([
            'generate_estimate_id' => $generateEstimate->id,
            'plant_material_id' => request('plant_material_id'),
            'quantity' => request('material_quantity'),
            'gross_margin' => request('material_gross_margin'),
            'uom' => request('material_uom'),
            'unit_cost' => request('material_cost'),
            'total_cost' => (request('material_quantity') * request('material_cost')) + $total_labor_cost,
            'total_labor_cost' => $total_labor_cost,
        ]);
    }

    public function getEstimateEquipment($id)
    {
        // $equipment = session()->get('equipment', []);
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateMaterial = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.equipment-table', compact('estimateMaterial'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function getEstimateLabor($id)
    {
        // $equipment = session()->get('equipment', []);
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateLabor = EstimateLabor::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateLabor = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.labor-table', compact('estimateLabor'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function getEstimateHardMaterial($id)
    {
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        // return $generate_estimate;
        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimateHardMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateMaterial = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.material-table', compact('estimateMaterial'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function getEstimatePlantMaterial($id)
    {
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimatePlantMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateMaterial = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.material-table', compact('estimateMaterial'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function getEstimateOtherCost($id)
    {
        // $equipment = session()->get('equipment', []);
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
            // dd($estimateOtherCost);
        } else {
            $estimateOtherCost = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.cost-table', compact('estimateOtherCost'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function getEstimateSubContractor($id)
    {
        // $equipment = session()->get('equipment', []);
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateSubContractor = [];
        }
        $tableView = View::make('organization.generate-estimate.partials.contractor-table', compact('estimateSubContractor'))->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function deleteEstimateEquipment($id)
    {
        $estimateMaterial = EstimateMaterial::where('id', $id)->first();
        if (is_object($estimateMaterial)) {
            $estimateMaterial->delete();
        }

        return response()->json(true, HTTP_OK);
    }

    public function deleteEstimateLabor($id)
    {
        $estimateLabor = EstimateLabor::where('id', $id)->first();
        if (is_object($estimateLabor)) {
            $estimateLabor->delete();
        }

        return response()->json(true, HTTP_OK);
    }

    public function deleteEstimateMaterial($id)
    {
        $estimateMaterial = request('materialType') == 'plant' ? EstimatePlantMaterial::where('id', $id)->first() : EstimateHardMaterial::where('id', $id)->first();
        if (is_object($estimateMaterial)) {
            $estimateMaterial->delete();
        }

        return response()->json(true, HTTP_OK);
    }

    public function deleteEstimateOtherCost($id)
    {
        $estimateOtherCost = EstimateOtherCost::where('id', $id)->first();
        if (is_object($estimateOtherCost)) {
            $estimateOtherCost->delete();
        }

        return response()->json(true, HTTP_OK);
    }

    public function deleteEstimateSubContractor($id)
    {
        $estimateSubContractor = EstimateSubContractor::where('id', $id)->first();
        if (is_object($estimateSubContractor)) {
            $estimateSubContractor->delete();
        }

        return response()->json(true, HTTP_OK);
    }

    public function getEstimateInvoiceDetail($id)
    {
        $estimate = Estimate::with('client', 'generateEstimate.serviceLine:id,name', 'generateEstimate.workType:id,name')->where('id', decodeID($id))->firstorfail();
        $data = EstimateService::getEstimateInvoiceDetail($id);
        $data['estimate'] = $estimate;
        $organization = User::where('id', getOrganizationId())->first();

        return view('organization.generate-estimate.detail', compact('organization'), $data);
    }

    public function notesView($id)
    {
        $notes = GenerateEstimate::findOrFail($id);

        return response()->json([
            'message' => 'Notes retrieved successfully',
            'notes' => $notes,
        ]);
    }

    public function addNote($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'description' => 'required',
        ], [
            'required' => 'This field is required',
        ])->validate();
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        // Check if the notes field is being updated
        if (! empty($generate_estimate->notes) && $generate_estimate->notes != $request->description) {
            $action = 'edited';
        } else {
            $action = 'added';
        }
        $generate_estimate->update([
            'notes' => $request->description,
        ]);
        $html = view('organization.generate-estimate.partials.note', ['generateEstimate' => $generate_estimate])->render();
        $buttons = view('organization.generate-estimate.partials.note-button', ['generateEstimate' => $generate_estimate])->render();
        $successMessage = "Note has been {$action} successfully";

        return response()->json(['success' => $successMessage, 'html' => $html, 'buttons' => $buttons], HTTP_OK);
    }

    public function deleteNote($id)
    {
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        $generate_estimate->update([
            'notes' => null,
        ]);
        $html = view('organization.generate-estimate.partials.note', ['generateEstimate' => $generate_estimate])->render();
        $buttons = view('organization.generate-estimate.partials.note-button', ['generateEstimate' => $generate_estimate])->render();

        return response()->json(['success' => 'Note has been deleted successfully!', 'html' => $html, 'buttons' => $buttons], HTTP_OK);
    }

    private function getMargin($value)
    {
        return Margin::where([
            'organization_id' => getOrganizationId(), 'name' => $value,
        ])->first();
    }

    public function addDesireMargin($id, Request $request)
    {
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        // $desireMargin = ($request->desireMargin ? $generate_estimate->total_price / (1 - $request->desireMargin / 100) : $generate_estimate->total_price);
        $desireMargin = ($request->desireMargin ? $generate_estimate->total_cost / ((100 - $request->desireMargin) / 100) : $generate_estimate->total_price);
        if ($request->desireMargin > 0) {
            $generate_estimate->update([
                'desire_margin' => $request->desireMargin,
                // 'final_price' => ($request->desireMargin ? $generate_estimate->total_cost + $generate_estimate->total_cost * ($request->desireMargin / 100) : $generate_estimate->total_price)
                'final_price' => $desireMargin,
            ]);
        }
        $grandTotal = str_replace(',', '', $generate_estimate->grand_total);

        return response()->json(['status' => 'success', 'desire_margin' => $request->desireMargin, 'grand_total' => (float) $grandTotal], HTTP_OK);
    }

    public function archiveEstimate($id)
    {
        $generate_estimate = GenerateEstimate::where('id', decodeID($id))->first();

        if ($generate_estimate->is_archive == 1) {
            return response()->json('error', 400);
        }
        $generate_estimate->update([
            'is_archive' => 1,
        ]);

        return response()->json('success', HTTP_OK);
    }

    public function viewChangeRequest($id)
    {
        $estimate = Opportunity::with('generateEstimate.clientAction')->where('id', decodeID($id))->firstOrFail();
        $estimate->opportunity_id = $id;

        // dd($estimate);
        return response()->json([
            'estimate' => $estimate,
        ]);
    }

    public function addEstimateItem(Request $request)
    {
        $validatedData = $request->validate([
            // 'generate_estimate_id' => 'required|exists:generate_estimates,id',
            // 'category_type' => 'required|string',
            'quantity' => 'required|string',
            'uom' => 'nullable|string',
            'unit_cost' => 'nullable|string',
            'gross_margin' => 'nullable|string',
            'total_cost' => 'nullable|string',
            // 'description' => 'nullable|string',
            'section_name' => 'nullable|string',
        ]);
        $labor = Labor::find($request->labor_id);
        // dd($labor);
        // Create a new EstimateItem
        $estimateItem = new EstimateItem;
        $estimateItem->generate_estimate_id = $request->generate_estimate_id;
        $estimateItem->generate_estimate_id = 1;
        // $estimateItem->category_type = $request->category_type;
        $estimateItem->category_type = 'labour'; // This will distinguish the category
        // This will distinguish the category
        $estimateItem->related_item_id = null; // You can manage this if needed
        $estimateItem->section_name = $request->section_name;

        // Common data
        $estimateItem->quantity = $request->quantity;
        $estimateItem->uom = $request->uom;

        $estimateItem->unit_cost = $labor->cost;
        $estimateItem->gross_margin = $request->gross_margin;
        // $estimateItem->total_cost = $request->total_cost;
        $estimateItem->total_cost = $request->quantity * $labor->cost;

        // Save the data
        $estimateItem->save();

        return response()->json(['message' => 'Data saved successfully for '.$request->category_type], 200);
    }

    public function getItemId($itemId, $itemType)
    {
        switch ($itemType) {
            case 'labors':
                $item = Labor::find($itemId);
                break;
            case 'equipment':
                $item = Equipment::find($itemId);
                break;
            case 'hard_materials':
                $item = HardMaterial::find($itemId);
                break;
            case 'plant_materials':
                $item = PlantMaterial::find($itemId);
                break;
            case 'contractors':
                $item = Subcontractor::find($itemId);
                break;
            case 'other_costs':
                $item = OtherCost::find($itemId);
                break;
            case SnowSetup::MATERIAL_CATEGORY:
            case SnowSetup::MATERIALS_DISPLAY:
            case SnowSetup::STAND_BY_CATEGORY:
            case SnowSetup::LABOR_CATEGORY:
            case SnowSetup::EQUIPMENT_CATEGORY:
            case SnowSetup::EQUIPMENT_DISPLAY:
                $item = SnowSetup::find($itemId);
                break;
            default:
                return null; // Or handle unknown item type
        }

        return $item; // Return the ID or null if not found
    }

    public function getItemNameFromModel($itemId, $itemType)
    {
        switch ($itemType) {
            case 'labors':
                $item = Labor::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case 'equipment':
                $item = Equipment::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case 'hard_materials':
                $item = HardMaterial::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case 'plant_materials':
                $item = PlantMaterial::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case 'contractors':
                $item = Subcontractor::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case 'other_costs':
                $item = OtherCost::find($itemId);

                return $item?->name ?? "Item #$itemId";
            case SnowSetup::MATERIAL_CATEGORY:
            case SnowSetup::MATERIALS_DISPLAY:
            case SnowSetup::STAND_BY_CATEGORY:
            case SnowSetup::LABOR_CATEGORY:
            case SnowSetup::EQUIPMENT_CATEGORY:
            case SnowSetup::EQUIPMENT_DISPLAY:
                $item = SnowSetup::find($itemId);

                return $item?->lineitem ?? "Item #$itemId"; // Use lineitem field for SnowSetup
            default:
                return "Item #$itemId";
        }
    }

    public function addItemsBatch(Request $request)
    {
        // Get the items data from the request (can be single item or array of items)
        $itemsData = $request->input('items');

        if (! $itemsData) {
            return response()->json(['error' => 'No item data received.'], 400);
        }

        // Ensure items is always an array
        $items = is_array($itemsData) && isset($itemsData[0]) ? $itemsData : [$itemsData];

        $createdItems = [];
        $skippedItems = [];
        $opportunityId = null;
        $ge = null;

        foreach ($items as $item) {
            if (! $opportunityId) {
                $opportunityId = $item['opportunityId'];

                // Create or find GenerateEstimate record
                $gee = GenerateEstimate::where('opportunity_id', $opportunityId)->where('organization_id', getOrganizationId())->count();

                if ($gee == 0) {
                    $ge = GenerateEstimate::create([
                        'opportunity_id' => $opportunityId,
                        'organization_id' => getOrganizationId(),
                        'status' => 'draft',
                    ]);
                    Opportunity::where('id', $opportunityId)->update([
                        'status' => 2,
                    ]);
                } else {
                    $ge = GenerateEstimate::where('opportunity_id', $opportunityId)->where('organization_id', getOrganizationId())->first();
                }
            }

            // Process the item data - use item_name from request if provided
            $name = isset($item['item_name']) && ! empty($item['item_name'])
                ? $item['item_name']
                : $this->getItemNameFromModel($item['id'], $item['itemType']);

            // Map frontend category to database category for snow items
            $categoryType = $item['itemType'];
            if (in_array($categoryType, ['Equipment', 'Materials', 'Labor', 'Stand By'])) {
                $categoryType = \App\Models\SnowSetup::getDatabaseCategory($categoryType);
            }

            // Check for duplicate items (same item_name and category_type for this opportunity)
            $existingItem = EstimateItem::where('opportunity_id', $item['opportunityId'])
                ->where('item_name', $name)
                ->where('category_type', $categoryType) // Use mapped category
                ->first();

            if ($existingItem) {
                $skippedItems[] = [
                    'name' => $name,
                    'category' => $categoryType, // Use mapped category
                    'reason' => 'Already exists',
                ];

                continue; // Skip this item
            }

            if ($name == 'Supervision' || $name == 'Laborers') {
                $labtype = $name;
            } else {
                $labtype = '';
            }

            // Create the EstimateItem record
            try {
                $rt = EstimateItem::create([
                    'generate_estimate_id' => $ge->id,
                    'item_name' => $name,
                    'quantity' => $item['quantity'],
                    'opportunity_id' => $item['opportunityId'],
                    'uom' => $item['uom'],
                    'category_type' => $categoryType, // Use mapped category
                    'section_name' => $item['section_name'] ?? null,
                    'gross_margin' => $item['gross_margin'],
                    'unit_cost' => $item['uc'],
                    'depth' => isset($item['depth']) && $item['depth'] !== '' ? (float) $item['depth'] : null,
                    'sqft' => isset($item['sqft']) && $item['sqft'] !== '' ? (float) $item['sqft'] : null,
                    'total_cost' => $item['total_cost'],
                    'unit_price' => $item['up'],
                    'total_price' => $item['tp'],
                    'item_id' => $item['id'],
                    'labor_type' => $labtype,
                ]);

                $createdItems[] = $rt;
            } catch (\Exception $e) {
                $skippedItems[] = [
                    'name' => $name,
                    'category' => $item['itemType'],
                    'reason' => 'Error: '.$e->getMessage(),
                ];
            }
        }

        $response = [
            'message' => count($createdItems).' items saved successfully!',
            'created_count' => count($createdItems),
            'skipped_count' => count($skippedItems),
            'data' => $createdItems,
        ];

        if (! empty($skippedItems)) {
            $response['skipped_items'] = $skippedItems;
        }

        return response()->json($response);
    }

    public function updateItemsBatch(Request $request)
    {
        // Get the single item data from the request
        $item = $request->input('items');

        if ($item) {
            $id = $item['id'];
            $estimateItem = EstimateItem::find($id);
            //   dd($id);
            $estimateItem->update([
                'quantity' => $item['quantity'],
                'gross_margin' => $item['gross_margin'],
                'unit_cost' => $item['uc'],
                'total_cost' => $item['total_cost'],
                'unit_price' => $item['up'],
                'total_price' => $item['tp'],
            ]);

            // dd($ge->id);
            // dd($rt);
            return response()->json(['message' => 'Item update successfully!', 'data' => $estimateItem]);
        }

        return response()->json(['error' => 'No item data received.'], 400);
    }

    public function getEstimateItems(Request $request)
    {
        $opportunityId = $request->get('opportunity_id');

        if (! $opportunityId) {
            return response()->json(['error' => 'Opportunity ID is required.'], 400);
        }

        $items = EstimateItem::where('opportunity_id', $opportunityId)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json(['items' => $items]);
    }

    public function downloadEstimationPDF($opportunityId)
    {
        // Fetch any necessary data here
        $div = DB::table('divisions')->get();
        // $decodedId = decodeId($id);
        $organizationId = getOrganizationId();
        $organization = User::where('id', $organizationId)->first();
        $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // Check if this is a snow opportunity
        $opportunityRecord = \App\Models\Opportunity::find($opportunityId);
        $isSnowOpportunity = $opportunityRecord && $opportunityRecord->division && $opportunityRecord->division->name === \App\Models\Division::SNOW_DIVISION;

        // Load estimate items with appropriate relations based on opportunity type
        if ($isSnowOpportunity) {
            $opportunity = EstimateItem::with('opportunity')->where('opportunity_id', $opportunityId)->with(['snowSetup'])->get();
            $hardMaterialImages = collect(); // Snow items don't have images
        } else {
            $opportunity = EstimateItem::with('opportunity')->where('opportunity_id', $opportunityId)->with(['material'])->get();
            $hardMaterialImages = $opportunity->map(function ($item) {
                // Check if material relation exists and has an image
                if ($item->material && $item->material->image) {
                    return [
                        'name' => $item->material->name,
                        'image' => $item->material->image,
                    ];
                }

                return null;
            })->filter();
        }
        $opportunityName = Str::slug($opportunity[0]->opportunity->opportunity_name, '_');

        $cover = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'cover')
            ->first();
        $about = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'about')
            ->first();
        $intro = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'intro')
            ->first();
        $scope = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'scope')
            ->first();
        $terms = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'terms')
            ->first();
        $payment = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'payment')
            ->first();
        $logo = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'logo')
            ->first();

        $imagePaths = $scope ? json_decode($scope->project_image) : [];

        $gallerys = DB::table('default_settings')
            ->where('opportunity_id', $opportunityId)
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'gallery')
            ->get();

        $estimate = Opportunity::with('contactInformation', 'account', 'generateEstimate.serviceLine:id,name', 'generateEstimate.workType:id,name')->where('id', $opportunityId)->firstorfail();
        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $opportunityId)
            ->sum('total_price');
        // Load the Blade view and pass data to it
        // $pdf = PDF::loadView('organization.opportunity.downloadEstimation', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity'))->setOptions(['defaultFont' => 'Arial']);
        // return $pdf->download('Estimation_Preview.pdf');
        $template = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('opportunity_id', $opportunityId)
            ->where('setting_type', 'template')
            ->first();

        $coverorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        $aboutorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();

        $introorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();

        $scopeorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'scope')
            ->first();

        $termsorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();
        $paymentorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();

        // dd($gallerys);
        // dd($about?->cov_image, $aboutorg?->cov_image);

        $urld = config('app.url').'/public/storage/'.($about?->cov_image ?? $aboutorg?->cov_image ?? '');
        $bannersimg = config('app.url').'/storage/'.($cover?->cov_image ?? $coverorg?->cov_image ?? '');

        // dd($urld);
        $gec = GenerateEstimate::where('opportunity_id', $opportunityId)->first();
        $estimateAction = ClientEstimateAction::where('generate_estimate_id', $gec?->id)->first();

        //        return $html = view('organization.opportunity.downloadEstimation', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity', 'gallerys', 'totalPriceSumss', 'payment', 'estimate', 'organization', 'template', 'coverorg', 'aboutorg', 'introorg', 'scopeorg', 'termsorg', 'paymentorg', 'urld', 'bannersimg', 'estimateAction', 'hardMaterialImages'))->render(); // Pass $logo to the view

        $pdf = PDF::loadView('organization.opportunity.downloadEstimation', [
            'terms' => $terms,
            'scope' => $scope,
            'intro' => $intro,
            'about' => $about,
            'cover' => $cover,
            'imagePaths' => $imagePaths,
            'logo' => $logo,
            'opportunity' => $opportunity,
            'gallerys' => $gallerys,
            'totalPriceSumss' => $totalPriceSumss,
            'payment' => $payment,
            'estimate' => $estimate,
            'organization' => $organization,
            'template' => $template,
            'coverorg' => $coverorg,
            'aboutorg' => $aboutorg,
            'introorg' => $introorg,
            'scopeorg' => $scopeorg,
            'termsorg' => $termsorg,
            'paymentorg' => $paymentorg,
            'urld' => $urld,
            'bannersimg' => $bannersimg,
            'estimateAction' => $estimateAction,
            'hardMaterialImages' => $hardMaterialImages,
        ])->setPaper('a4')
            ->setOptions(['isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'isRemoteEnabled' => true,
                'base_path' => public_path(),
                'enable_remote' => true,
            ]);

        set_time_limit(300); // Increase to 5 minutes

        return $pdf->download($opportunityName.'.pdf');
    }

    public function getEstimateItemById($id)
    {
        $item = EstimateItem::where('id', $id)->first();
        if (! $item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        return response()->json(['message' => 'Item found', 'data' => $item], Response::HTTP_OK);
    }

    public function updateEstimateHardItem(Request $request)
    {
        $item = EstimateItem::where('id', $request->id)->first();
        if (! $item) {
            $response = [
                'message' => 'Item not found',
                'success' => false,
            ];

            return response()->json($response, Response::HTTP_NOT_FOUND);
        }
        $quantity = calculateDepthSqFt($request->depth ?? 0, $request->sqft ?? 0, $item->uom == 'Bag', $item->quantity > 1 ? $item->quantity : 0);
        $item->update(['depth' => $request->depth, 'sqft' => $request->sqft,
            'quantity' => $quantity,
            'total_price' => $quantity * $item->unit_price,
            'total_cost' => $quantity * $item->unit_cost,
        ]);

        return response()->json(['message' => 'Item updated successfully', 'data' => $item], Response::HTTP_OK);
    }
}
