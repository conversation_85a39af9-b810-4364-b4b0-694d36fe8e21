<?php

namespace App\Http\Controllers\Organization;

use App\Enums\BillCategoryType;
use App\Exports\OperationsExport;
use App\Http\Controllers\Controller;
use App\Models\EstimateItem;
use App\Models\GenerateEstimate;
use App\Models\Invoice;
use App\Models\ItemBill;
use App\Models\User;
use App\Traits\PermissionMiddlewareTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class OperationController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'index' => ['change_operation_status', 'operation_listing', 'assign_operation_om', 'operation_detail'],
            'listing' => ['change_operation_status', 'operation_listing', 'assign_operation_om', 'operation_detail'],
            'updateStatus' => ['change_operation_status'],
            'detail' => ['operation_detail'],
            'getManagers' => ['assign_operation_om'],
            'assignManager' => ['assign_operation_om'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index()
    {

        return view('organization.operation.index', get_defined_vars());
    }

    public function listing(Request $request)
    {
        $Organization = User::where('id', getOrganizationId())->firstorfail();

        $operations = GenerateEstimate::with([
            'opportunityid:id,opportunity_name,contact_id,account_id,sale_person_id,estimator_id,sales_order_number as sale_no,organization_id,property_id',
            'opportunityid.account:id,company_name,account_owner',
            'opportunityid.account.accountowner:id,first_name,last_name,image',
            'opportunityid.contactInformation',
            'opportunityid.propertyInformation',
            'manager:id,first_name,last_name,image',
            'latestScheduleDate',
            'invoice',
        ])->latest()
            ->wherehas('opportunityid', function ($query) {
                $query->where('organization_id', getOrganizationId());
                employeeAssociatedDateQuery($query);
            })->where(['status' => 'won', 'is_schedule' => true])
            ->when(request('operation_status'), function ($query) {
                if (request('operation_status') !== 'Clear') {
                    $query->where('operation_status', request('operation_status'));
                }
            })
            ->select('id', 'opportunity_id', 'operation_status', 'total_price', 'manager_id', 'total_cost', 'desire_margin', 'final_price');

        return DataTables::eloquent($operations)
            ->filterColumn('property', function ($query, $keyword) {
                $query->whereHas('opportunityid', function ($query) use ($keyword) {
                    $query->whereHas('propertyInformation', function ($q) use ($keyword) {
                        $q->whereRaw('property_information.name LIKE ?', ["%{$keyword}%"]);
                    });
                });
            })
            ->filterColumn('manager.name', function ($query, $keyword) {
                $query->wherehas('manager', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name) like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('request.sale_no', function ($query, $keyword) {
                $query->wherehas('opportunityid', function ($q) use ($keyword) {
                    $q->where('sales_order_number', 'LIKE', '%'.$keyword.'%');
                });
            })
            ->filterColumn('estimator.name', function ($query, $keyword) {
                $query->wherehas('opportunityid', function ($query) use ($keyword) {
                    $query->wherehas('estimator', function ($q) use ($keyword) {
                        $sql = "CONCAT(users.first_name,' ',users.last_name) like ?";
                        $q->whereRaw($sql, ["%{$keyword}%"]);
                    });
                });
            })
            ->filterColumn('account_owner_name', function ($query, $keyword) {
                $query->whereHas('opportunityid.account.accountowner', function ($q) use ($keyword) {
                    $q->whereRaw("CONCAT(first_name,' ',last_name) LIKE ?", ["%{$keyword}%"]);
                });
            })
            ->filterColumn('client.name', function ($query, $keyword) {
                $query->wherehas('opportunityid', function ($query) use ($keyword) {
                    $query->wherehas('contactInformation', function ($q) use ($keyword) {
                        $sql = "CONCAT(contacts.first_name,' ',contacts.last_name) like ?";
                        $q->whereRaw($sql, ["%{$keyword}%"]);
                    });
                });
            })
            ->filterColumn('account.company_name', function ($query, $keyword) {
                $query->whereHas('opportunityid.account', function ($q) use ($keyword) {
                    $q->where('company_name', 'LIKE', "%{$keyword}%");
                });
            })
            ->filterColumn('property', function ($query, $keyword) {
                $query->where('total_price', 'LIKE', '%'.$keyword.'%');
            })
            ->addColumn('client.name', function (GenerateEstimate $operation) {
                return '
                <div class="table_profile">
                    <h2 class="profile_name" style="white-space:nowrap;">'.
                    ($operation->opportunityid->contactInformation->first_name ?? '').' '.
                    ($operation->opportunityid->contactInformation->last_name ?? '').'</h2>
                </div>';
            })
            ->addColumn('account.company_name', function (GenerateEstimate $operation) {
                return $operation->opportunityid->account->company_name ?? '---';
            })
            ->addColumn('account_owner_name', function (GenerateEstimate $operation) {
                $accountOwner = $operation->opportunityid->account->accountowner ?? null;

                return $accountOwner ? $accountOwner->first_name.' '.$accountOwner->last_name : '---';
            })
            ->addColumn('manager.name', function (GenerateEstimate $operation) {
                if (! $operation->manager) {
                    return '<div>---</div>';
                }

                $image = $operation->manager->image
                    ? asset('storage/user_images/'.$operation->manager->image)
                    : asset('admin_assets/images/dummy_image.webp');

                return '
                <div class="table_profile">
                    <img class="image" height="24px" width="24px" src="'.$image.'" alt="profile image">
                    <h2 class="profile_name" style="white-space:nowrap;">'.
                    ($operation->manager->first_name ?? '').' '.
                    ($operation->manager->last_name ?? '').'</h2>
                </div>';
            })
            ->editColumn('property', function (GenerateEstimate $operation) {
                return $operation->opportunityid->propertyInformation?->name ?? '---';
            })
            ->addColumn('schedule_date', function (GenerateEstimate $operation) {
                return $operation->latestScheduleDate?->date ?: '---';
            })
            ->addColumn('operation_status', function (GenerateEstimate $estimate) {
                $status = ucfirst($estimate->operation_status ?? '');
                $class = match ($estimate->operation_status) {
                    'Completed' => 'success operation-completed',
                    'In Progress' => 'primary operation-progress',
                    default => 'warning operation-warning'
                };

                return '<div class="status '.$class.'">'.$status.'</div>';
            })
            ->addColumn('action', function (GenerateEstimate $estimate) use ($Organization) {
                $user = auth('web')->user();
                if (! $user->canany(['change_operation_status', 'assign_operation_om', 'operation_detail'])) {
                    return '';
                }

                $html = '<div class="dropdown mx-auto w-fit">
                   <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                       <img height="24px" width="24px" src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                  <ul class="dropdown-menu" aria-labelledby="dropdown1">';

                if ($user->can('change_operation_status')) {
                    $html .= '<li><a class="dropdown-item" href="#" data-value="'.$estimate->operation_status.'" data-id="'.encodeID($estimate->id).'" data-toggle="modal" id="changeOperationStatus" data-target="#changeStatus">Change Status</a></li>';
                }

                if ($user->can('assign_operation_om')) {
                    $html .= '<li><a class="dropdown-item" href="#" data-toggle="modal" data-managerID="'.encodeId($estimate->manager_id).'" id="esstimateAssignOm" data-id="'.encodeID($estimate->id).'" data-target="#assignOm">Assign OM</a></li>';
                }

                if ($user->can('operation_detail')) {
                    $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.operation.detail', encodeId($estimate->id)).'">View Details</a></li>';
                }

                if ($user->can('create_operational_invoice') && $estimate->operation_status == 'Completed') {
                    if (empty($estimate->invoice)) {
                        if (optional($Organization->stripeAccount)?->acc_connected == 1 && optional($Organization->stripeAccount)?->acc_connected_id != null) {
                            $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.invoices.operation', ['operationId' => encodeID($estimate->id)]).'">Generate Invoice</a></li>';
                        } else {
                            $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.account.settings', 'acc_connect=null').'">Generate Invoice</a></li>';
                        }
                    } else {
                        $html .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.invoices.view', encodeID($estimate->id)).'">View Invoice</a></li>';
                    }
                }

                $html .= '</ul></div>';

                return $html;
            })
            ->rawColumns(['client.name', 'action', 'operation_status', 'manager.name', 'account_owner_name'])
            ->only([
                'opportunityid.sale_no',
                'opportunityid.opportunity_name',
                'client.name',
                'account.company_name',
                'account_owner_name',
                'manager.name',
                'schedule_date',
                'property',
                'operation_status',
                'action',
            ])
            ->toJson();
    }

    public function updateStatus(Request $request)
    {
        $Organization = User::where('id', getOrganizationId())->firstorfail();
        $operation = GenerateEstimate::where('id', decodeID($request->id))->firstorfail();
        // dd(decodeID($request->id));
        if (is_object($operation)) {
            if (! empty($request->status)) {

                $operation->update([
                    'operation_status' => $request->status == 'InProgress' ? 'In Progress' : $request->status,
                    'completed_at' => $request->status == 'Completed' ? Carbon::now() : null,
                ]);
            }
            if (! empty($request->status) && $request->status == 'Completed') {
                $view = View::make('organization.operation.partials.generate_invoice_confirm', compact('Organization', 'operation'))->render();

                return response()->json(['status' => $request->status, 'view' => $view], HTTP_OK);
            }

            return response()->json(['status' => $request->status], HTTP_OK);
        } else {
            return response()->json('error', HTTP_BAD_REQUEST);
        }
    }

    public function detail($id)
    {
        // Fetching necessary data
        $Organization = User::where('id', getOrganizationId())->firstOrFail();
        $generate_estimate = GenerateEstimate::with([
            'opportunityid.serviceLine.division',
            'opportunityid.account',
            'latestScheduleDate',
            'manager',
            'serviceLine:id,name',
            'workType:id,name',
        ])->where('id', decodeId($id))->firstOrFail();

        $estimateInvoice = Invoice::where('operation_id', $generate_estimate->id)->first();
        $estimatesItems = EstimateItem::with('bills')->where('generate_estimate_id', $generate_estimate->id)->get();

        // Define all possible categories using enum
        $categories = BillCategoryType::values();
        $categoryColors = [];
        foreach (BillCategoryType::cases() as $category) {
            $categoryColors[$category->value] = $category->color();
        }

        // Initialize grouped data with 0 for each category
        $groupedData = array_fill_keys($categories, ['actual' => 0, 'estimated' => 0]);

        // First pass: Get estimated costs from EstimateItems
        foreach ($estimatesItems as $item) {
            $category = strtolower($item->category_type);
            if (array_key_exists($category, $groupedData)) {
                $groupedData[$category]['estimated'] += (float) $item->total_price;
            }
        }

        // Second pass: Get actual costs from bills grouped by their stored category
        foreach ($estimatesItems as $item) {
            // Group bills by their stored category (not the EstimateItem's category)
            foreach ($item->bills as $bill) {
                $billCategory = strtolower($bill->category ?? $item->category_type);
                if (array_key_exists($billCategory, $groupedData)) {
                    $groupedData[$billCategory]['actual'] += (float) $bill->invoice_amount;
                }
            }
        }

        // Calculate total actual cost for the pie chart percentages
        $totalActual = array_sum(array_column($groupedData, 'actual'));
        $totalEstimated = array_sum(array_column($groupedData, 'estimated'));

        // Calculate dynamic metrics
        $revenue = $estimateInvoice ? $estimateInvoice->total : 0; // Use invoice total if available
        $sellingPrice = $estimatesItems->sum('total_price');

        // Calculate Gross Profit percentage and dollar amount
        $gpDollar = $totalEstimated - $totalActual;
        $gpPercent = $totalEstimated > 0 ? (($gpDollar / $totalEstimated) * 100) : 0;

        // Calculate labor hours for Rev/mh calculation
        $laborHours = $estimatesItems->where('category_type', 'labors')->sum('quantity');
        $revPerMh = $laborHours > 0 ? ($revenue / $laborHours) : 0;

        // Ensure all categories are always shown, even if no EstimateItems exist
        // This will make charts display all categories consistently

        $itemCategoryMap = EstimateItem::pluck('category_type', 'id')->map(function ($category) {
            // Use enum to get radio button values
            $categoryEnum = BillCategoryType::tryFrom($category);

            return $categoryEnum ? $categoryEnum->radioValue() : 'Subcontractor'; // default fallback
        })->toArray();

        $totalPrice = $estimatesItems->sum('total_price');

        return view('organization.operation.detail', [
            'generate_estimate' => $generate_estimate,
            'estimateInvoice' => $estimateInvoice,
            'estimatesItems' => $estimatesItems,
            'groupedData' => $groupedData,
            'categoryColors' => $categoryColors,
            'totalActual' => $totalActual,
            'totalEstimated' => $totalEstimated,
            'itemCategoryMap' => $itemCategoryMap,
            'totalPrice' => $totalPrice,
            // Dynamic metrics
            'dynamicMetrics' => [
                'revenue' => $revenue,
                'sellingPrice' => $sellingPrice,
                'gpPercent' => round($gpPercent, 1),
                'gpDollar' => $gpDollar,
                'revPerMh' => $revPerMh,
                'laborHours' => $laborHours,
            ],
        ]);
    }

    public function getUpdatedData($id)
    {
        // Fetching fresh data (same as detail method but for AJAX)
        $generate_estimate = GenerateEstimate::with([
            'opportunityid.serviceLine.division',
            'opportunityid.account',
            'latestScheduleDate',
            'manager',
            'serviceLine:id,name',
            'workType:id,name',
        ])->where('id', decodeId($id))->firstOrFail();

        $estimatesItems = EstimateItem::with('bills')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateInvoice = Invoice::where('operation_id', $generate_estimate->id)->first();

        // Define all possible categories using enum
        $categories = BillCategoryType::values();
        $categoryColors = [];
        foreach (BillCategoryType::cases() as $category) {
            $categoryColors[$category->value] = $category->color();
        }

        // Initialize grouped data with 0 for each category
        $groupedData = array_fill_keys($categories, ['actual' => 0, 'estimated' => 0]);

        // First pass: Get estimated costs from EstimateItems
        foreach ($estimatesItems as $item) {
            $category = strtolower($item->category_type);
            if (array_key_exists($category, $groupedData)) {
                $groupedData[$category]['estimated'] += (float) $item->total_price;
            }
        }

        // Second pass: Get actual costs from bills grouped by their stored category
        foreach ($estimatesItems as $item) {
            // Group bills by their stored category (not the EstimateItem's category)
            foreach ($item->bills as $bill) {
                $billCategory = strtolower($bill->category ?? $item->category_type);
                if (array_key_exists($billCategory, $groupedData)) {
                    $groupedData[$billCategory]['actual'] += (float) $bill->invoice_amount;
                }
            }
        }

        // Calculate totals
        $totalActual = array_sum(array_column($groupedData, 'actual'));
        $totalEstimated = array_sum(array_column($groupedData, 'estimated'));
        $totalPrice = $estimatesItems->sum('total_price');

        // Calculate dynamic metrics (same as detail method)
        $revenue = $estimateInvoice ? $estimateInvoice->total : 0; // Use invoice total if available
        $sellingPrice = $totalPrice;

        // Calculate Gross Profit percentage and dollar amount
        $gpDollar = $totalEstimated - $totalActual;
        $gpPercent = $totalEstimated > 0 ? (($gpDollar / $totalEstimated) * 100) : 0;

        // Calculate labor hours for Rev/mh calculation
        $laborHours = $estimatesItems->where('category_type', 'labors')->sum('quantity');
        $revPerMh = $laborHours > 0 ? ($revenue / $laborHours) : 0;

        return response()->json([
            'success' => true,
            'data' => [
                'groupedData' => $groupedData,
                'categoryColors' => $categoryColors,
                'totalActual' => $totalActual,
                'totalEstimated' => $totalEstimated,
                'totalPrice' => $totalPrice,
                'metrics' => [
                    'revenue' => $revenue,
                    'sellingPrice' => $sellingPrice,
                    'gpPercent' => round($gpPercent, 1),
                    'gpDollar' => $gpDollar,
                    'revPerMh' => $revPerMh,
                    'laborHours' => $laborHours,
                ],
                'estimatesItems' => $estimatesItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'category_type' => $item->category_type,
                        'item_name' => $item->item_name,
                        'quantity' => $item->quantity,
                        'total_price' => $item->total_price,
                        'total_cost' => $item->total_cost,
                        'bills' => $item->bills->map(function ($bill) {
                            return [
                                'id' => $bill->id,
                                'vendor_name' => $bill->vendor_name,
                                'invoice_number' => $bill->invoice_number,
                                'actual_qty' => $bill->actual_qty,
                                'invoice_amount' => $bill->invoice_amount,
                                'due_date' => $bill->due_date,
                                'invoice_image' => $bill->invoice_image,
                            ];
                        }),
                    ];
                }),
            ],
        ]);
    }

    // In your controller
    public function getItemCategory($itemId)
    {
        $item = EstimateItem::findOrFail($itemId);

        return response()->json([
            'category_type' => $item->category_type,
        ]);
    }

    public function Billstore(Request $request)
    {
        // Convert radio button value to enum value for storage
        $categoryEnum = BillCategoryType::fromRadioValue($request->category);
        if (! $categoryEnum) {
            return response()->json([
                'message' => 'Invalid category selected',
                'errors' => ['category' => ['Invalid category selected']],
            ], 422);
        }

        $request->validate([
            'category' => 'required|string',
            'vendor_name' => 'required|string',
            'invoice_number' => 'required|string',
            'due_date' => 'nullable|date',
            'invoice_amount' => 'required|numeric',
            'invoice_image' => 'nullable|image|mimes:jpg,jpeg,png',
            'actual_qty' => 'required|numeric',
            'invoice_date' => 'nullable|date',
        ]);

        $invoiceImagePath = null;
        if ($request->hasFile('invoice_image')) {
            $invoiceImagePath = $request->file('invoice_image')->store('invoices', 'public');
        }

        $itemBill = \App\Models\ItemBill::create([
            'item_id' => $request->item_id,
            'category' => $categoryEnum->value, // Store enum value
            'vendor_name' => $request->vendor_name,
            'invoice_number' => $request->invoice_number,
            'due_date' => $request->due_date,
            'invoice_amount' => $request->invoice_amount,
            'invoice_image' => $invoiceImagePath,
            'actual_qty' => $request->actual_qty,
            'invoice_date' => $request->invoice_date ?? $request->due_date,
        ]);

        return response()->json([
            'message' => 'Bill added successfully',
            'bill' => [
                'id' => $itemBill->id,
                'item_id' => $itemBill->item_id,
                'vendor_name' => $itemBill->vendor_name,
                'invoice_number' => $itemBill->invoice_number,
                'actual_qty' => $itemBill->actual_qty,
                'invoice_amount' => $itemBill->invoice_amount,
                'due_date_formatted' => \Carbon\Carbon::parse($itemBill->due_date)->format('d-m-Y'),
                'invoice_image' => $itemBill->invoice_image ? asset('storage/'.$itemBill->invoice_image) : null,
            ],
        ]);
    }

    public function storeLaborBill(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:estimate_items,id',
            'actual_qty' => 'required|numeric',
            'actual_cost' => 'required|numeric',
        ]);

        $itemBill = \App\Models\ItemBill::updateOrCreate(
            ['item_id' => $request->item_id], // ✅ Just item_id
            [
                'vendor_name' => 'Labor Entry',
                'invoice_number' => 'N/A',
                'due_date' => now(),
                'invoice_amount' => $request->actual_cost,
                'actual_qty' => $request->actual_qty,
                'invoice_image' => null,
            ]
        );

        return response()->json([
            'message' => 'Labor bill saved successfully',
            'bill' => [
                'id' => $itemBill->id,
                'item_id' => $itemBill->item_id,
                'vendor_name' => $itemBill->vendor_name,
                'invoice_number' => $itemBill->invoice_number,
                'actual_qty' => $itemBill->actual_qty,
                'invoice_amount' => $itemBill->invoice_amount,
                'due_date_formatted' => \Carbon\Carbon::parse($itemBill->due_date)->format('d-m-Y'),
                'invoice_image' => $itemBill->invoice_image ? asset('storage/'.$itemBill->invoice_image) : null,
            ],
        ]);
    }

    public function Billedit($id)
    {
        $bill = ItemBill::findOrFail($id);
        $bill->invoice_image = $bill->invoice_image ? asset('storage/'.$bill->invoice_image) : null;

        return response()->json(['bill' => $bill]);
    }

    public function Billupdate(Request $request, $id)
    {
        $request->validate([
            'category' => 'required|string',
            'vendor_name' => 'required|string',
            'invoice_number' => 'required|string',
            'due_date' => 'required|date',
            'invoice_amount' => 'required|numeric',
            'invoice_image' => 'nullable|image|mimes:jpg,jpeg,png',
            'actual_qty' => 'required|numeric',
        ]);

        $itemBill = \App\Models\ItemBill::findOrFail($id);

        // Handle image update
        if ($request->hasFile('invoice_image')) {
            // Delete old image if exists
            if ($itemBill->invoice_image) {
                Storage::disk('public')->delete($itemBill->invoice_image);
            }
            $invoiceImagePath = $request->file('invoice_image')->store('invoices', 'public');
            $itemBill->invoice_image = $invoiceImagePath;
        }

        // Update other fields
        $itemBill->update([
            'item_id' => $request->item_id,
            'category' => $request->category,
            'vendor_name' => $request->vendor_name,
            'invoice_number' => $request->invoice_number,
            'due_date' => $request->due_date,
            'invoice_amount' => $request->invoice_amount,
            'actual_qty' => $request->actual_qty,
        ]);

        return response()->json([
            'message' => 'Bill updated successfully',
            'bill' => [
                'id' => $itemBill->id,
                'item_id' => $itemBill->item_id,
                'vendor_name' => $itemBill->vendor_name,
                'invoice_number' => $itemBill->invoice_number,
                'actual_qty' => $itemBill->actual_qty,
                'invoice_amount' => $itemBill->invoice_amount,
                'due_date_formatted' => \Carbon\Carbon::parse($itemBill->due_date)->format('d-m-Y'),
                'invoice_image' => $itemBill->invoice_image ? asset('storage/'.$itemBill->invoice_image) : null,
            ],
        ]);
    }

    public function Billdestroy($id)
    {
        $bill = ItemBill::findOrFail($id);

        // Delete image from storage if exists
        if ($bill->invoice_image && Storage::disk('public')->exists($bill->invoice_image)) {
            Storage::disk('public')->delete($bill->invoice_image);
        }

        $bill->delete();

        return response()->json(['success' => true]);
    }

    public function getManagers(Request $request)
    {
        // dd("abc");
        $operation = GenerateEstimate::where('id', decodeID($request->updateId))->first();
        $managers = User::wherehas('roles', function ($q) {
            $q->where('name', 'operation manager');
            $q->when(request('search'), function ($query) {
                $query->where(function ($query) {
                    $query->where(DB::raw('CONCAT(COALESCE(`first_name`, "")," ",COALESCE(`last_name`,""))'), 'like', '%'.request('search').'%');
                });
            });
        })->where('parent_id', getOrganizationId())
            ->where('status', 'Active')->get();
        $tableView = View::make('organization.operation.modal-list', get_defined_vars())->render();

        return json_encode($tableView, HTTP_OK);
    }

    public function assignManager(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'manager_id' => 'required'
        // ], [
        //     'required' => 'This field is required'
        // ])->validate();
        $generateEstimate = GenerateEstimate::where('id', decodeID($request->id))->first();
        if (is_object($generateEstimate)) {

            if (! empty($request->managerId) && $generateEstimate->manager_id != decodeId($request->managerId)) {
                $generateEstimate->update([
                    'manager_id' => decodeId($request->managerId),
                ]);

                return response()->json('success', HTTP_OK);
            } else {
                return response()->json('error', HTTP_BAD_REQUEST);
            }
        } else {
            return response()->json('error', HTTP_BAD_REQUEST);
        }
    }

    public function exportOperations(Request $request)
    {
        return Excel::download(new OperationsExport, 'organization.operations.xlsx');
    }
}
