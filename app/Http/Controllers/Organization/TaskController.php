<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\Organization\StoreTaskRequest;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TaskController extends Controller
{
    /**
     * Show the form for creating a new task.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Return the view for creating a new task
        return view('tasks.create'); // Make sure you have a view named 'tasks.create'
    }

    /**
     * Store a newly created task in the database.
     *
     * @param  \App\Http\Requests\StoreTaskRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreTaskRequest $request)
    {
        // Begin a database transaction
        DB::beginTransaction();

        try {
            $taskData = $request->validated();

            $taskData['organization_id'] = getOrganizationId();
            $assignTo = $taskData['assign_to'];

            if (str_starts_with($assignTo, 'user-')) {
                $userId = str_replace('user-', '', $assignTo);
                // Process as a System User
                $type = 'users';
            } elseif (str_starts_with($assignTo, 'contact-')) {
                $userId = str_replace('contact-', '', $assignTo);
                // Process as a Contact
                $type = 'contacts';
            } else {
                $userId = null;
                $type = null;
            }

            // $formattedDate = Carbon::createFromFormat('F j, Y', $request->start_date)->format('Y-m-d');
            $end_date = Carbon::createFromFormat('F j, Y', $request->end_date)->format('Y-m-d');
            $taskData['end_date'] = $end_date;
            $taskData['assign_to_type'] = $type;
            $taskData['assign_to'] = $userId;
            // $taskData['start_date'] = $formattedDate;
            // dd($taskData);
            // dd($taskData);
            Task::create($taskData);

            DB::commit();

            // return redirect()->back()->with('showModal', 'Your request has been successfully completed.');
            return redirect()->back()->with('success', 'Your request has been successfully completed.');
        } catch (\Exception $e) {

            DB::rollBack();

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function getTaskNow(Request $req)
    {
        $tasks = Task::where('id', $req->id)->first();

        return response()->json($tasks);

    }

    public function updateTasks(Request $request)
    {
        // Begin a database transaction
        DB::beginTransaction();

        try {

            // $formattedDate = Carbon::createFromFormat('F j, Y', $request->start_date)->format('Y-m-d');
            $end_date = Carbon::createFromFormat('F j, Y', $request->end_date)->format('Y-m-d');
            $assignTo = $request->assign_to;

            if (str_starts_with($assignTo, 'user-')) {
                $userId = str_replace('user-', '', $assignTo);
                // Process as a System User
                $type = 'users';
            } elseif (str_starts_with($assignTo, 'contact-')) {
                $userId = str_replace('contact-', '', $assignTo);
                // Process as a Contact
                $type = 'contacts';
            } else {
                $userId = null;
                $type = null;
            }

            // $taskData['start_date'] = $formattedDate;
            $dtt = [
                'end_date' => $end_date,
                'subject' => $request->subject,
                'end_time' => $request->end_time,
                'assign_to' => $userId,
                'assign_to_type' => $type,
            ];

            Task::where('id', $request->tasks_id)->update($dtt);

            DB::commit();

            // return redirect()->back()->with('showModal', 'Your request has been successfully completed.');
            return redirect()->back()->with('success', 'Your request has been successfully completed.');
        } catch (\Exception $e) {

            DB::rollBack();

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function taskCompleted(Request $req)
    {
        $task = Task::find($req->id);
        $task->is_completed = 1;
        $task->save();

        return response()->json(['success' => 'Task completed successfully']);
    }
}
