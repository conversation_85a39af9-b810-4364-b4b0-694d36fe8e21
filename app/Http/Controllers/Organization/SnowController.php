<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Imports\SnowImport;
use App\Models\SnowSetup;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;

class SnowController extends Controller
{
    public function index()
    {
        $laborItems = SnowSetup::where('category', SnowSetup::LABOR_CATEGORY)->get();
        $materialItems = SnowSetup::where('category', SnowSetup::MATERIAL_CATEGORY)->get();
        $equipmentItems = SnowSetup::where('category', SnowSetup::EQUIPMENT_CATEGORY)->get();
        $standbyItems = SnowSetup::where('category', SnowSetup::STAND_BY_CATEGORY)->get();

        return view('organization.snow.index', compact(
            'laborItems',
            'materialItems',
            'equipmentItems',
            'standbyItems'
        ));
    }

    public function store(Request $request)
    {
        $request->validate([
            'category' => 'required|in:Labor,Material,Equipments,Stand By',
            'line_item' => 'required|string',
            'unit_cost' => 'required|numeric',
            'uom' => 'required|string',
        ]);

        $item = new SnowSetup;
        $item->category = $request->category;
        $item->lineitem = $request->line_item;
        $item->unit_cost = $request->unit_cost;
        $item->uom = $request->uom;
        $item->save();

        // Return the row's HTML with item details
        return response()->json([
            'status' => 'success',
            'category' => $request->category,
            'html' => view('organization.snow.item-row', ['item' => $item, 'index' => 0])->render(),
        ]);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'category' => 'required',
            'line_item' => 'required',
            'unit_cost' => 'required|numeric',
            'uom' => 'required',
        ]);

        $item = SnowSetup::findOrFail($id);
        $item->category = $request->category;  // Update category dynamically
        $item->lineitem = $request->line_item;
        $item->unit_cost = $request->unit_cost;
        $item->uom = $request->uom;
        $item->save();

        return response()->json([
            'status' => 'success',
            'category' => $item->category,
            'id' => $item->id,
            'html' => view('organization.snow.item-row', ['item' => $item, 'index' => 0])->render(),
        ]);
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls',
        ]);

        try {
            Excel::import(new SnowImport, $request->file('file'));

            return back()->with('success', 'File imported successfully!');
        } catch (ValidationException $e) {
            // Safely collect all row-level validation messages
            $messages = collect($e->failures())->flatMap(function ($failure) {
                return collect($failure->errors())->map(function ($msg) use ($failure) {
                    return "Row {$failure->row()} ({$failure->attribute()}): {$msg}";
                });
            });

            return back()->withErrors($messages);
        } catch (\Throwable $th) {
            return back()->withErrors(['Import failed: '.$th->getMessage()]);
        }
    }

    public function destroy($id)
    {
        $item = SnowSetup::findOrFail($id);
        $item->delete();

        return response()->json(['status' => 'success']);
    }
}
