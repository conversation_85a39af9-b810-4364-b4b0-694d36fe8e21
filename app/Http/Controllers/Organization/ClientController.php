<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClientRequest;
use App\Imports\ClientImport;
use App\Jobs\SendClientTemplateEmailJob;
use App\Models\Adress;
use App\Models\Client;
use App\Models\GenerateEstimate;
use App\Models\User;
use App\Traits\PermissionMiddlewareTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\URL;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class ClientController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'index' => ['client_listing', 'client_detail', 'add_client', 'edit_client', 'create_estimate_request'],
            'create' => ['add_client'],
            'store' => ['add_client'],
            'edit' => ['edit_client'],
            'update' => ['edit_client'],
            'detail' => ['client_listing', 'client_detail', 'add_client', 'edit_client'],
            'clientEstimatesHistory' => ['client_listing', 'client_detail', 'add_client', 'edit_client', 'estimate_listing', 'generate_estimate', 'operation_listing'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index(Request $request)
    {
        $organization = User::where('id', getOrganizationId())->select('created_at')->first();
        if ($request->ajax()) {
            $clients = Client::where('organization_id', getOrganizationId())->latest()
                ->select('first_name', 'id', 'last_name', 'company_name', 'image', 'email', 'mobile_no');

            return DataTables::eloquent($clients)
                ->editColumn('company_name', function (Client $client) {
                    return $client->company_name ?? '--- ---';
                })
                ->editColumn('image', function (Client $client) {
                    if ($client->image) {
                        return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('storage/client_images/'.$client->image).'" alt="profile image">
                        <h2 class="profile_name">'.$client->first_name.'</h2>
                    </div>';
                    } else {
                        return '
                    <div class="table_profile">

                    <img class="image" height="24px" width="24px"
                        src="'.asset('admin_assets/images/dummy_image.webp').'" alt="profile image">
                    <h2 class="profile_name" style="white-space:nowrap;">'.$client->first_name.'</h2>
                </div>';
                    }
                })
                ->addColumn('action', function (Client $client) {
                    $user = auth('web')->user();
                    if (! $user->canany(['create_estimate_request', 'add_client', 'edit_client', 'client_detail'])) {
                        return '';
                    }

                    $html =
                        '  <div class="dropdown mx-auto w-fit">
                    <div id="dropdown'.encodeID($client->id).'" data-toggle="dropdown" aria-expanded="false">
                        <img height="24px" width="24px"
                            src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown{{ $client->id }}">';
                    if ($user->can('create_estimate_request')) {
                        $html .= '<li><a class="dropdown-item"
                        href="'.route(getRouteAlias().'.create.estimate', encodeId($client->id)).'">Create
                        Estimate Request</a></li>';
                    }

                    if ($user->canany(['add_client', 'edit_client', 'client_detail'])) {
                        $html .= '<li><a class="dropdown-item"
                        href="'.route(getRouteAlias().'.client.detail', encodeId($client->id)).'">View
                        Details</a>
                            </li>';
                    }

                    if ($user->can('edit_client')) {
                        $html .= '<li><a class="dropdown-item"
                                href="'.route(getRouteAlias().'.client.edit', encodeId($client->id)).'">Edit
                                Details</a>
                        </li>';
                    }
                    $html .= '


                    </ul>
                </div>';

                    return $html;
                })
                ->rawColumns(['image', 'action'])
                ->only(['image', 'last_name', 'company_name', 'email', 'mobile_no', 'action'])
                ->toJson();
        }

        return view('organization.client.index', compact('organization'));
    }

    public function create()
    {
        $client = new Client;
        $action = URL::route(getRouteAlias().'.client.store');

        return view('organization.client.create', compact('client', 'action'));
    }

    public function store(ClientRequest $request)
    {
        DB::beginTransaction();
        try {
            $imageName = null;
            if ($request->has('image')) {
                $imageName = uniqid().'_'.trim($request->image->getClientOriginalName());
                $filePath = $request->image->storePubliclyAs('client_images', $imageName, 'public');
            }
            $client = Client::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'organization_id' => getOrganizationId(),
                'company_name' => $request->company_name,
                'title' => $request->title,
                'email' => $request->email,
                'alternate_email' => $request->alt_email,
                'mobile_no' => $request->mob_num,
                'office_no' => $request->off_num,
                'alternate_no' => $request->alt_num,
                'added_by' => getOrganizationId(),
                'image' => $imageName,
            ]);

            if ($request->isSameBilling) {
                $this->updateOrCreateAddress($client->id, PROPERTY, true);
                $this->updateOrCreateAddress($client->id, BILLING, true);
            } else {
                $this->updateOrCreateAddress($client->id, PROPERTY, true);
                $this->updateOrCreateAddress($client->id, BILLING, false);
            }
            DB::commit();
            if ($request?->previous_route || $request?->previous_route == (getRouteAlias().'.invoices.index')) {
                $client = Client::where('id', $client->id)->with('propertyAddress')->first();

                return redirect()->route(getRouteAlias().'.invoices.create', encodeID($client->id))->with('success', 'Client Created Successfully!');
            }

            return redirect()->route(getRouteAlias().'.client.index')->with('success', 'Client created successfully!');
        } catch (\Exception $e) {
            DB::rollback();

            // throw $e;
            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function edit($id)
    {
        $client = $this->getClient($id);
        $action = URL::route(getRouteAlias().'.client.update', ['id' => $id]);

        return view('organization.client.create', compact('client', 'action'));
    }

    public function update(ClientRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            $client = Client::where('id', decodeID($id))->first();
            if (! is_object($client)) {
                return redirect()->back()->with('error', 'Something went wrong!');
            } else {
                if ($request->has('image')) {
                    if (File::exists(public_path('storage/client_images/'.$client->image))) {
                        File::delete(public_path('storage/client_images/'.$client->image));
                    }
                    $imageName = uniqid().'_'.trim($request->image->getClientOriginalName());
                    $filePath = $request->image->storePubliclyAs('client_images', $imageName, 'public');
                    $client->image = $imageName;
                }

                $client->update([
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'organization_id' => getOrganizationId(),
                    'company_name' => $request->company_name,
                    'title' => $request->title,
                    'email' => $request->email,
                    'alternate_email' => $request->alt_email,
                    'mobile_no' => $request->mob_num,
                    'office_no' => $request->off_num,
                    'alternate_no' => $request->alt_num,
                    'added_by' => getOrganizationId(),
                ]);
            }

            if ($request->isSameBilling) {
                $this->updateOrCreateAddress($client->id, PROPERTY, true);
                $this->updateOrCreateAddress($client->id, BILLING, true);
            } else {
                $this->updateOrCreateAddress($client->id, PROPERTY, true);
                $this->updateOrCreateAddress($client->id, BILLING, false);
            }
            DB::commit();

            return redirect()->route(getRouteAlias().'.client.index')->with('success', 'Client updated successfully!');
        } catch (\Exception $e) {

            DB::rollback();

            // dd($e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function detail($id)
    {
        $client = $this->getClient($id);
        $organization = User::where('id', getOrganizationId())->first();
        if (! is_object($client)) {
            return redirect()->back()->with('error', 'Something went wrong!');
        } else {
            $action = URL::route(getRouteAlias().'.client.store');

            return view('organization.client.detail', compact('client', 'action', 'organization'));
        }
    }

    public function clientEstimatesHistory(Request $request, $id)
    {
        if ($request->ajax()) {
            $estimates = GenerateEstimate::with(['request:sale_person_id,id,client_id,estimator_id', 'request.saleMan:id,first_name,last_name,image', 'request.estimator:id,first_name,last_name,image'])
                ->whereHas('request', function ($query) use ($id) {
                    $query->where('client_id', $id);
                })
                ->when(request('status'), function ($query) {
                    if (request('status') == 'Open') {
                        $query->where('is_active', true);
                    } elseif (request('status') == 'Close') {
                        $query->where('is_active', false);
                    }
                })->when(request('status'), function ($query) {
                    if (request('status') == 'proposed') {
                        $query->where('status', 'proposed');
                    } elseif (request('status') == 'won') {
                        $query->where('status', 'won');
                    } elseif (request('status') == 'lost') {
                        $query->where('status', 'lost');
                    }
                })->latest()->where('is_archive', 0)->where('total_price', '>', 0)
                ->select('total_price', 'total_cost', 'id', 'desire_margin', 'operation_status', 'status', 'request_id', 'final_price');

            return DataTables::eloquent($estimates)
                ->filterColumn('request.sale_man.name', function ($query, $keyword) {
                    $query->wherehas('request', function ($q) use ($keyword) {

                        $q->wherehas('saleMan', function ($q) use ($keyword) {
                            $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                            $q->whereRaw($sql, ["%{$keyword}%"]);
                        });
                    });
                })->filterColumn('request.estimator.name', function ($query, $keyword) {
                    $query->wherehas('request', function ($q) use ($keyword) {
                        $q->wherehas('estimator', function ($q) use ($keyword) {
                            $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                            $q->whereRaw($sql, ["%{$keyword}%"]);
                        });
                    });
                })
                ->filterColumn('total_price', function ($query, $keyword) {
                    $query->where('total_price', 'LIKE', '%'.$keyword.'%');
                })->filterColumn('total_cost', function ($query, $keyword) {
                    $query->where('total_cost', 'LIKE', '%'.$keyword.'%');
                })
                ->addColumn('request.sale_man.name', function (GenerateEstimate $estimate) {
                    if ($estimate?->request?->saleMan?->image) {
                        return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('storage/user_images/'.$estimate?->request?->saleMan?->image).'" alt="profile image">
                        <h2 class="profile_name">'.$estimate?->request?->saleMan?->name.'</h2>
                    </div>';
                    } else {
                        return $estimate?->request?->saleMan?->name ?? 'Not Assign';
                    }
                })
                ->addColumn('total_price', function (GenerateEstimate $estimate) {
                    return $estimate?->grand_total ? '$'.$estimate?->grand_total : '';
                })
                ->addColumn('total_cost', function (GenerateEstimate $estimate) {
                    return $estimate?->total_cost ? '$'.$estimate?->total_cost : '';
                })
                ->editColumn('generateEstimate.status', function (GenerateEstimate $estimate) {
                    if ($estimate->operation_status == 'In Progress' || $estimate->operation_status == 'Completed') {
                        if ($estimate->operation_status == 'In Progress') {
                            return ' <div class="status warning">'.ucfirst($estimate->operation_status).'</div>';
                        } else {
                            return ' <div class="status success">'.ucfirst($estimate->operation_status).'</div>';
                        }
                    }

                    if ($estimate->status == 'lost') {
                        return ' <div class="status danger">'.ucfirst($estimate->status).'</div>';
                    } elseif ($estimate->status == 'won') {
                        return ' <div class="status success">'.ucfirst($estimate->status).'</div>';
                    } else {
                        return ' <div class="status warning">'.ucfirst($estimate->status).'</div>';
                    }
                })
                ->addColumn('request.estimator.name', function (GenerateEstimate $estimate) {
                    if ($estimate?->request?->estimator?->image) {
                        return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('storage/user_images/'.$estimate->request?->estimator?->image).'" alt="profile image">
                        <h2 class="profile_name">'.$estimate?->request?->estimator?->name.'</h2>
                    </div>';
                    } else {
                        return $estimate?->request?->estimator?->name ?? 'Not Assign';
                    }
                })
                ->addColumn('action', function (GenerateEstimate $estimate) {
                    return
                        ' <div class="dropdown mx-auto w-fit">
                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                        <img height="24px" width="24px"
                            src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown1">
                        <li><a class="dropdown-item"    href="'.route(getRouteAlias().'.estimate.invoice-detail', encodeId($estimate?->request_id)).'">View Detail</a></li>

                    </ul>
                </div>';
                })
                ->rawColumns(['action', 'request.estimator.name', 'request.sale_man.name', 'generateEstimate.status'])
                ->only(['action', 'request.sale_man.name', 'request.estimator.name', 'total_cost', 'total_price', 'generateEstimate.status'])
                ->toJson();
        }
    }

    public function sendEmailToClient(Request $request)
    {

        $request->validate([
            'email.*' => 'required|email',
            'subject' => 'required|min:2|max:998',
            'message' => 'required|string',
            'select_file.*' => 'nullable|file',
            'type' => 'sometimes|in:yes',
            'bcc_email.*' => 'sometimes|email',
            'cc_email.*' => 'sometimes|email',
        ]);

        $data = $request->except('select_file');
        $path = public_path('clientEmailUploads').'/'.collect($request->email)->first();
        $attachments = $request->file('select_file') ?? [];
        $files = [];
        // create folder
        if (count($attachments) > 0) {

            if (! File::exists($path)) {
                File::makeDirectory($path, $mode = 0777, true, true);
            }

            foreach ($attachments as $attachment) {
                $name = time().'.'.$attachment->getClientOriginalExtension();
                $attachment->move($path, $name);
                $files[] = $path.'/'.$name;
            }
        }

        if (array_key_exists('copy', $data) && $data['copy'] == 'yes') {
            if (! array_key_exists('cc_email', $data)) {
                $data['cc_email'] = [];
            }
            $data['cc_email'] = array_unique([...$data['cc_email'], auth()->user()->email]);
        }
        // dd($data);
        SendClientTemplateEmailJob::dispatch($data, $files, $path, getOrganizationId());

        return response()->json(['message' => 'Email Sent Successfully']);
    }

    private function getClient($id)
    {
        return Client::with('billingAddress', 'propertyAddress')->where('id', decodeId($id))->first();
    }

    private function updateOrCreateAddress($clientId, $type, $isSameBilling)
    {
        $address = Adress::where(['client_id' => $clientId, 'type' => $type])->first();
        if (! $address) {
            $address = new Adress;
        }

        $address->client_id = $clientId;
        $address->address1 = ($isSameBilling === true) ? request()->property_address : request()->billing_address;
        $address->address2 = ($isSameBilling === true) ? request()->property_address2 : request()->billing_address2;
        $address->state = ($isSameBilling === true) ? request()->state : request()->state2;
        $address->city = ($isSameBilling === true) ? request()->city : request()->city2;
        $address->zip = ($isSameBilling === true) ? request()->zip : request()->zip2;
        $address->type = $type;
        $address->save();
    }

    // private function createAddress($clientId, $type, $isSameBilling)
    // {
    //     $address = new Adress();
    //     $address->client_id = $clientId;
    //     $address->address1 = ($isSameBilling === true) ? request()->property_address : request()->billing_address;
    //     $address->address2 = ($isSameBilling === true) ? request()->property_address2 : request()->billing_address2;
    //     $address->state = ($isSameBilling === true) ? request()->state : request()->state2;
    //     $address->city = ($isSameBilling === true) ? request()->city : request()->city2;
    //     $address->zip = ($isSameBilling === true) ? request()->zip : request()->zip2;
    //     $address->type = $type;
    //     $address->save();
    // }

    public function clientFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {

                Excel::import(new ClientImport, $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'success' => 'false',
        //         'message' => 'Something went wrong!'

        //     ], HTTP_BAD_REQUEST);
        // }
    }
}
