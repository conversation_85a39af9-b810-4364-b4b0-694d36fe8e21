  public function listing(Request $request)
    {
        // Get the logged-in user's organization ID
        $organizationId = getOrganizationId(); // Fetch organization ID based on logged-in user

        // Fetch employees based on status and search query
        $employees = Employee::latest()
            ->where('organization_id', $organizationId)  // Filter by organization_id
            ->when(request('employee_status'), function ($query) {
                if (request('employee_status') !== 'Clear') {
                    $query->where('status', request('employee_status'));
                }
            })
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                        ->orWhere('last_name', 'like', "%{$request->search}%")
                        ->orWhere('phone_number', 'like', "%{$request->search}%")
                        ->orWhere('email_address', 'like', "%{$request->search}%");
                });
            })
            ->with('division')
            ->select('id', 'employee_number', 'first_name', 'last_name', 'phone_number', 'email_address', 'title', 'division_id', 'status', 'pay_rate', 'overtime_rate', 'drivers_license_number', 'state_issued', 'date_issued', 'expiration_date', 'dot_medical_card_issued_date', 'dot_medical_card_expiration_date');

        // Return DataTables response
        return DataTables::eloquent($employees)
            ->addColumn('division', function (Employee $employee) {
                return $employee->division ? $employee->division->name : 'N/A';
            })
            ->addColumn('status', function (Employee $employee) {
                return '<div class="status ' . ($employee->status == 'Active' ? 'success' : 'warning') . '">' . ucfirst($employee->status) . '</div>';
            })
            ->addColumn('action', function (Employee $employee) {
                $user = auth('web')->user();
                $html = '<div class="dropdown border-none bg-none mx-auto w-fit">
                <button style="background: transparent !important; border: none !important; box-shadow: none !important;" 
                    class="btn btn-secondary dropdown-toggle" 
                    type="button" 
                    id="dropdown' . $employee->id . '" 
                    data-toggle="dropdown" 
                    aria-expanded="false">
                    <img height="24px" width="24px" src="' . asset('admin_assets/images/icons/vertical-dots.svg') . '" alt="vertical dots">
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdown' . $employee->id . '">';

                $html .= '<li><a class="dropdown-item view-employee" href="javascript:void(0)" data-id="' . encodeId($employee->id) . '">View</a></li>';
                $html .= '<li><a class="dropdown-item edit-employee" href="javascript:void(0)" data-id="' . encodeId($employee->id) . '">Edit</a></li>';
                $html .= '<li><a class="dropdown-item delete-employee" href="javascript:void(0)" data-id="' . encodeId($employee->id) . '">Delete</a></li>';
                $html .= '</ul></div>';
                return $html;
            })
            ->rawColumns(['status', 'action'])
            ->only(['employee_number', 'first_name', 'last_name', 'phone_number', 'email_address', 'division', 'status', 'action'])
            ->toJson();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'email_address' => 'required|email', // Removed unique validation for email
            'title' => 'nullable|string|max:255',
            'division_id' => 'required|exists:divisions,id',
            'status' => 'required|string',
            'pay_rate' => 'required|numeric',
            'overtime_rate' => 'required|numeric',
            'employee_number' => 'required|unique:employees,employee_number',
            'drivers_license_number' => 'nullable|string',
            'state_issued' => 'nullable|string',
            'date_issued' => 'nullable|date',
            'expiration_date' => 'nullable|date',
            'dot_medical_card_issued_date' => 'nullable|date',
            'dot_medical_card_expiration_date' => 'nullable|date',
        ]);

        // Get the organization ID based on the logged-in user
        $organizationId = getOrganizationId();  // Fetch organization ID

        // Check if the email address already exists in the same organization
        $existingEmployee = Employee::where('email_address', $request->email_address)
            ->where('organization_id', $organizationId)  // Check within the same organization
            ->first();

        // If email exists in the same organization, return an error
        if ($existingEmployee) {
            return response()->json(['message' => 'This email is already registered for this organization.'], 400);
        }

        // If email does not exist in the same organization, create the employee
        $employeeNumber = $request->employee_number ?: 'EMP' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

        $employee = Employee::create(array_merge($validated, [
            'employee_number' => $employeeNumber,
            'organization_id' => $organizationId, // Add the organization ID to the employee record
        ]));

        return response()->json(['message' => 'Employee created successfully.']);
    }