<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\StripeAccountDetail;
use App\Models\User;

class StripeController extends Controller
{
    public function createStripeStandardAccount()
    {
        $OrganizationId = getOrganizationId();
        $Organization = User::where('id', $OrganizationId)->firstOrFail();
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        $acc_connected_id = optional($Organization->stripeAccount)?->acc_connected_id;
        if (empty($acc_connected_id)) {
            $express_account = $stripe->accounts->create([
                'country' => 'US',
                'type' => 'standard',
                'email' => $Organization->email,
                // 'capabilities' => [
                //     'card_payments' => ['requested' => true],
                //     'transfers' => ['requested' => true],
                // ],
                'business_type' => 'company',
                'company' => [
                    'name' => $Organization->company_name,
                ],

            ]);

            // dd($express_account);
            $express_account_link = $stripe->accountLinks->create(
                [
                    'account' => $express_account->id,
                    'refresh_url' => route(getRouteAlias().'.account.settings'),
                    'return_url' => route(getRouteAlias().'.stripe.account.return_url'),
                    'type' => 'account_onboarding',
                ]
            );

            // storing account id to database
            StripeAccountDetail::updateOrCreate([
                'organization_id' => $Organization->id,
                'acc_connected_id' => $express_account->id,
            ], []);

            $express_account_url = $express_account_link->url;

            return redirect($express_account_url);
        } else {
            $express_account_link = $stripe->accountLinks->create(
                [
                    'account' => $acc_connected_id,
                    'refresh_url' => route(getRouteAlias().'.account.settings'),
                    'return_url' => route(getRouteAlias().'.stripe.account.return_url'),
                    'type' => 'account_onboarding',
                ]
            );

            $express_account_url = $express_account_link->url;

            return redirect($express_account_url);
        }
    }

    public function return_url()
    {
        try {
            $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
            $Organization = User::where('id', getOrganizationId())->firstOrFail();
            $acc_connected_id = optional($Organization->stripeAccount)?->acc_connected_id;
            $account = $stripe->accounts->retrieve($acc_connected_id, []);
            if ($acc_connected_id && $account && ($account->details_submitted == true)) {
                StripeAccountDetail::where('organization_id', $Organization->id)->update(['acc_connected' => 1]);

                return redirect()->route('frontend.dashboard')->with('success', 'Stripe Account Connected Successfully');
            }
        } catch (\Exception $e) {

        }

        return redirect()->route(getRouteAlias().'.account.settings')->with('error', 'Stripe Account Not Connected Successfully');
    }
}
