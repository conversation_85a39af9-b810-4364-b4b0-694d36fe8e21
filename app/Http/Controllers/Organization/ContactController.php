<?php

namespace App\Http\Controllers\Organization;

use App\Exports\ContactExport;
use App\Http\Controllers\Controller;
use App\Imports\ContactImport;
use App\Models\Contact;
use App\Models\PropertyInformation;
use DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class ContactController extends Controller
{
    public function contactList()
    {
        $accounts = DB::table('accounts')->where('parent_id', getOrganizationId())->get();
        $roles = DB::table('roles')->get();

        // $decodedId = decodeId($id);
        // $organizationId = getOrganizationId();
        // $organization = User::where('id', $organizationId)->first();
        // $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // $opportunity = Opportunity::with('propertyInformation', 'salesman', 'estimator', 'opportunityOwner', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines','proposal')
        //     ->findOrFail($decodedId);
        $contacts = Contact::join('accounts', 'contacts.account', '=', 'accounts.id')
            ->select([
                'contacts.id',
                'accounts.company_name',
                'contacts.email',
                'contacts.phone_number',
                'contacts.first_name',
                'contacts.last_name',
            ])->get();
        $property = PropertyInformation::where('organization_id', getOrganizationId())->get();
        $contactsCount = DB::table('contacts')->where('organization_id', getOrganizationId())->count();

        return view('organization.contact.contactlist', compact('accounts', 'roles', 'contacts', 'property', 'contactsCount'));
    }

    public function storeContact(Request $request)
    {
        $validatedData = $request->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => [
                'required',
                'email',
                // Check uniqueness in the 'contacts' table
                Rule::unique('contacts')->where(function ($query) {
                    return $query->where('organization_id', getOrganizationId());
                }),
                // Custom rule for 'users' table
                function ($attribute, $value, $fail) {
                    $existsInUsers = DB::table('users')->where('email', $value)->exists();
                    if ($existsInUsers) {
                        $fail('This email is already registered in the users table.');
                    }
                },
            ],
            'phone' => 'required|string|max:12',
            'title' => 'required',
            'second_email' => 'nullable',
            'second_phone' => 'nullable|string|max:12',
            'account' => 'required',
            'name_title' => 'nullable',
            'suffix' => 'nullable',
        ], [
            'email.unique' => 'You have already added a contact with this email.',
        ]);

        if ($request->default == 'on') {
            Contact::create([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'prefix' => $validatedData['name_title'],
                'email' => $validatedData['email'],
                'property_id' => $validatedData['propertyt_id'] ?? 0,
                'phone_number' => $validatedData['phone'],
                'second_phone' => $validatedData['second_phone'],
                'second_email' => $validatedData['second_email'],
                'account' => $validatedData['account'],
                'suffix' => $validatedData['suffix'],
                'title' => $validatedData['title'],
                'organization_id' => getOrganizationId(),
                'created_at' => now(),
                'updated_at' => now(),
                'property_default' => 1,
            ]);
        } else {
            Contact::create([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'prefix' => $validatedData['name_title'],
                'email' => $validatedData['email'],
                'property_id' => $validatedData['propertyt_id'] ?? 0,
                'phone_number' => $validatedData['phone'],
                'second_phone' => $validatedData['second_phone'],
                'second_email' => $validatedData['second_email'],
                'account' => $validatedData['account'],
                'suffix' => $validatedData['suffix'],
                'title' => $validatedData['title'],
                'organization_id' => getOrganizationId(),
                'created_at' => now(),
                'updated_at' => now(),
                'property_default' => 0,
            ]);
        }

        return response()->json(['success' => true, 'message' => 'Contact added successfully!']);
    }

    public function getData(Request $request)
    {
        $invoices = Contact::join('accounts', 'contacts.account', '=', 'accounts.id')
            ->where('contacts.organization_id', getOrganizationId())
            ->select([
                'contacts.id',
                'accounts.company_name as company_name',
                'contacts.email',
                'contacts.phone_number',
                'contacts.first_name',
                'contacts.last_name',
                'accounts.id as account_id',
            ]);

        if ($request->has('selectedValue') && ! empty($request->selectedValue)) {
            $invoices->where('account_id', $request->selectedValue);
        }

        return DataTables::of($invoices)
            ->addColumn('account_owner_name', function ($row) {
                return $row->company_name;
            })
            ->addColumn('action', function ($row) {
                return '
                <div class="dropdown text-center">
                    <button
                        class="btn btn-sm bg-transparent dropdown-toggle text-center"
                        type="button"
                        data-toggle="dropdown"
                        aria-expanded="false"
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M14 5C14 6.104 13.104 7 12 7C10.896 7 10 6.104 10 5C10 3.896 10.896 3 12 3C13.104 3 14 3.896 14 5Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 10C10.896 10 10 10.896 10 12C10 13.104 10.896 14 12 14C13.104 14 14 13.104 14 12C14 10.896 13.104 10 12 10Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 17C10.896 17 10 17.896 10 19C10 20.104 10.896 21 12 21C13.104 21 14 20.104 14 19C14 17.896 13.104 17 12 17Z"
                                fill="#828282"
                            />
                        </svg>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a onclick="editSectionContact('.$row->id.')" data-toggle="modal"
                data-target="#updateContactModal" class="dropdown-item" href="#">Edit</a>
                        </li>
                        <li>
                            <a id="deleteContact" data-toggle="modal" data-target="#DeleteModal" data-value='.encodeId($row->id).'
                class="dropdown-item" href="#">Delete</a>
                        </li>
                    </ul>
                </div>';
            })
            ->rawColumns(['action']) // This ensures that the HTML is rendered correctly
            ->make(true);
    }

    public function edit(Request $request)
    {
        $id = $request->id;
        $contact = Contact::find($id);

        return response()->json($contact);

    }

    public function updateContact(Request $request)
    {
        // Validate the incoming request data (without validating contactt_id)
        // $validatedData = $request->validate([
        //     'first_name' => 'required',
        //     'last_name' => 'required',
        //     'email' => 'required|email',
        //     'phone' => 'required',
        //     'title' => 'required',
        //     'second_email' => 'nullable',
        //     'second_phone' => 'nullable',
        //     'account' => 'required',
        //     'propertyt_id' => 'required',
        // ]);

        $id = $request->contactt_id;

        $validatedData = $request->validate([
            'first_name' => 'required',
            'last_name' => 'required',
            'email_update' => [
                'required',
                'email',
                // Check uniqueness in the 'contacts' table excluding the current contact's ID
                Rule::unique('contacts', 'email')->ignore($id)->where(function ($query) {
                    return $query->where('organization_id', getOrganizationId());
                }),
                // Custom rule for 'users' table
                function ($attribute, $value, $fail) {
                    $existsInUsers = DB::table('users')->where('email', $value)->exists();
                    if ($existsInUsers) {
                        $fail('This email is already registered in the users table.');
                    }
                },
            ],
            'phone_update' => 'required|string|max:12',
            'title' => 'required',
            'second_email' => 'nullable',
            'second_phone_update' => 'nullable|string|max:12',
            'account' => 'required',
            'propertyt_id' => 'required',
        ], [
            'email_update.unique' => 'You have already added a contact with this email.',
            'phone_update' => 'The phone must not be greater than 12 characters.',
            'second_phone_update' => 'The second phone must not be greater than 12 characters.',
        ]);

        if ($request->default == 'on') {
            $dt = [
                'property_default' => 0,
            ];
            DB::table('contacts')->where('property_id', $validatedData['propertyt_id'])->update($dt);

            // Combine the name title and first name
            $first = $validatedData['first_name'];
            $first2 = $request->name_title;
            $name = $first2.' '.$first;

            // Find the contact by its ID and update it
            $contact = Contact::findOrFail($id);

            // Update the contact fields
            $contact->update([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'prefix' => $first2, // Title (Mr., Mrs., etc.)
                'email' => $validatedData['email_update'],
                'property_id' => $validatedData['propertyt_id'],
                'phone_number' => $validatedData['phone_update'],
                'second_phone' => $validatedData['second_phone_update'],
                'second_email' => $validatedData['second_email'],
                'account' => $validatedData['account'],
                'suffix' => $request->suffix,
                'title' => $validatedData['title'],
                'updated_at' => now(),
                'property_default' => 1,
            ]);
        } else {
            $id = $request->contactt_id;

            // Combine the name title and first name
            $first = $validatedData['first_name'];
            $first2 = $request->name_title;
            $name = $first2.' '.$first;

            // Find the contact by its ID and update it
            $contact = Contact::findOrFail($id);

            // Update the contact fields
            $contact->update([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'prefix' => $first2, // Title (Mr., Mrs., etc.)
                'email' => $validatedData['email_update'],
                'property_id' => $validatedData['propertyt_id'],
                'phone_number' => $validatedData['phone_update'],
                'second_phone' => $validatedData['second_phone_update'],
                'second_email' => $validatedData['second_email'],

                'account' => $validatedData['account'],
                'suffix' => $request->suffix,
                'title' => $validatedData['title'],
                'updated_at' => now(),
                'property_default' => 0,
            ]);
        }

        // Get the contact ID from the request (without validation)
        return response()->json(['success' => true, 'contact' => $contact, 'message' => 'Contact updated successfully!']);

        // Redirect back with success message
        // return redirect()->back()->with('success', 'Contact updated successfully!');
    }

    public function delete($id)
    {
        $contact = Contact::findOrFail(decodeId($id));
        $contact->delete();

        return response()->json(['success' => 'true'], HTTP_OK);
    }

    public function contactFileImport(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xls,xlsx|max:2048', // 2MB max
        ]);

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            if (! in_array($extension, ['xls', 'xlsx'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Please upload an Excel file (.xls, .xlsx)',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $import = new ContactImport;
            Excel::import($import, $file);

            $importedCount = $import->getRowCount();
            $failedRows = $import->getFailures();

            if (count($failedRows) > 0) {
                $errors = [];
                foreach ($failedRows as $failure) {
                    $errors[] = [
                        'row' => $failure->row(),
                        'errors' => $failure->errors(),
                        'values' => $failure->values(),
                    ];
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Import completed with some errors',
                    'imported_count' => $importedCount,
                    'error_count' => count($failedRows),
                    'errors' => $errors,
                ], Response::HTTP_OK);
            }

            return response()->json([
                'success' => true,
                'message' => 'Contact imported successfully',
                'imported_count' => $importedCount,
            ], Response::HTTP_OK);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'User not found: '.$e->getMessage(),
            ], Response::HTTP_NOT_FOUND);

        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values(),
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Validation errors occurred during import',
                'errors' => $errors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing file: '.$e->getMessage(),
                'file_error' => true,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    //   public function contactFileImport(Request $request)
    //   {
    //       // try {
    //       if ($request->hasFile('file')) {
    //           $extension = File::extension($request->file->getClientOriginalName());
    //           if ($extension == "xls" || $extension == "xlsx") {
    //             //   $division = $request->input('division');
    //               Excel::import(new ContactImport, $request->file('file')->store('temp'));
    //               return response()->json(['success' => 'true'], HTTP_OK);
    //               //'Your file is a valid xls or csv file'
    //           } else {
    //               return response()->json([
    //                   'success' => 'false',
    //                   'message' => "File is a $extension file.!! Please upload a valid xls file..!"
    //               ], HTTP_BAD_REQUEST);
    //           }
    //       }
    //   }

    public function exportContacts(Request $request)
    {

        return Excel::download(new ContactExport, 'organization.contacts.xlsx');
    }
}
