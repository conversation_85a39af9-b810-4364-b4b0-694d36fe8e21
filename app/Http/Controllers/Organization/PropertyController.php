<?php

namespace App\Http\Controllers\Organization;

use App\Exports\PropertyExport;
use App\Http\Controllers\Controller;
use App\Imports\PropertyImport;
use App\Models\Contact;
use App\Models\CustomerIssue;
use App\Models\GenerateEstimate;
use App\Models\Opportunity;
use App\Models\Property;
use App\Models\PropertyDocument;
use App\Models\PropertyInformation;
use DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
use Yajra\DataTables\Facades\DataTables;

class PropertyController extends Controller
{
    public function index()
    {
        $accounts = DB::table('accounts')->where('parent_id', getOrganizationId())->get();
        $property = DB::table('property_information')->where('organization_id', getOrganizationId())->count();
        // dd($property);

        // $decodedId = decodeId($id);
        // $organizationId = getOrganizationId();
        // $organization = User::where('id', $organizationId)->first();
        // $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // $opportunity = Opportunity::with('propertyInformation', 'salesman', 'estimator', 'opportunityOwner', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines','proposal')
        //     ->findOrFail($decodedId);

        return view('organization.property.index', compact('accounts', 'property'));
    }

    public function propertyDetails($id)
    {
        $property = PropertyInformation::join('accounts', 'property_information.company_id', '=', 'accounts.id')
            ->join('users', 'accounts.account_owner', '=', 'users.id') // Join with users table
            ->where('property_information.id', decodeId($id))
            ->select([
                'property_information.id',
                'property_information.property_health',
                'property_information.company_notes',
                'property_information.company_id',
                'accounts.company_name as company_name',
                'property_information.name as property_name',
                'property_information.city',
                'property_information.state',
                'property_information.address1',
                'property_information.address2',
                'property_information.zip',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as account_owner_name"), // Concatenate first name and last name
            ])
            ->first();
        $contacts = Contact::where('property_id', $property->id)->get();

        $roles = DB::table('roles')->get();
        $pending = DB::table('customer_issues')->where('property_id', decodeId($id))->where('status', '1')->count();

        $open = DB::table('customer_issues')->where('property_id', decodeId($id))->where('status', '2')->count();
        $completed = DB::table('customer_issues')->where('property_id', decodeId($id))->where('status', '3')->count();
        // dd($open, $pending, $completed);
        $documents = DB::table('property_documents')->where('property_id', decodeId($id))->get();
        $accounts = DB::table('accounts')->where('parent_id', getOrganizationId())->get();

        $customerIssuesCount = getCustomerIssuesCount();

        $openissues = CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->join('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
            ->where('customer_issues.organization_id', getOrganizationId())
            ->where('customer_issues.status', '2')
            ->where('opportunities.property_id', decodeId($id))
            ->select(
                'customer_issues.id',
                'customer_issues.property_name',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                'opportunities.property_id as prop_id',
                'opportunities.account_id as acc_id',
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"), // Concatenating creator's full name
                'customer_issues.created_at',
            )
            ->latest()->get();

        $pendingissues = CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->join('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
            ->where('customer_issues.organization_id', getOrganizationId())
            ->where('customer_issues.status', '1')
            ->where('opportunities.property_id', decodeId($id))
            ->select(
                'customer_issues.id',
                'customer_issues.property_name',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                'opportunities.property_id as prop_id',
                'opportunities.account_id as acc_id',
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"), // Concatenating creator's full name
                'customer_issues.created_at',
            )
            ->latest()->get();

        $completedissues = CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->join('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
            ->where('customer_issues.organization_id', getOrganizationId())
            ->where('customer_issues.status', '3')
            ->where('opportunities.property_id', decodeId($id))
            ->select(
                'customer_issues.id',
                'customer_issues.property_name',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                'opportunities.property_id as prop_id',
                'opportunities.account_id as acc_id',
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"), // Concatenating creator's full name
                'customer_issues.created_at',
            )
            ->latest()->get();

        $operations = GenerateEstimate::with(['opportunityid:id,opportunity_name,account_id,sale_person_id,estimator_id,sales_order_number as sale_no,job_no,bid_due_date,organization_id', 'opportunityid.account:id,company_name', 'manager:id,first_name,last_name,image', 'latestScheduleDate', 'invoice'])->latest()
            ->wherehas('opportunityid', function ($query) use ($id) {
                $query->where('organization_id', getOrganizationId())
                    ->where('property_id', decodeId($id));
                employeeAssociatedDateQuery($query);
            })->where(['status' => 'won', 'is_schedule' => true])
            ->where('operation_status', 'In Progress')
            ->when(request('operation_status'), function ($query) {
                if (request('operation_status') !== 'Clear') {
                    $query->where('operation_status', 'In Progress');
                }
            })
            ->select('id', 'opportunity_id', 'operation_status', 'total_price', 'manager_id', 'total_cost', 'desire_margin', 'final_price')
            ->get();

        $operationscomplete = GenerateEstimate::with(['opportunityid:id,opportunity_name,account_id,sale_person_id,estimator_id,sales_order_number as sale_no,job_no,bid_due_date,organization_id', 'opportunityid.account:id,company_name', 'manager:id,first_name,last_name,image', 'latestScheduleDate', 'invoice'])->latest()
            ->wherehas('opportunityid', function ($query) use ($id) {
                $query->where('organization_id', getOrganizationId())
                    ->where('property_id', decodeId($id));
                employeeAssociatedDateQuery($query);
            })->where(['status' => 'won', 'is_schedule' => true])
            ->where('operation_status', 'Completed')
            ->when(request('operation_status'), function ($query) {
                if (request('operation_status') !== 'Clear') {
                    $query->where('operation_status', request('operation_status'));
                }
            })
            ->select('id', 'opportunity_id', 'operation_status', 'total_price', 'manager_id', 'total_cost', 'desire_margin', 'final_price')
            ->get();

        // $estimates =  Opportunity::with(['generateEstimate.clientAction:generate_estimate_id,status', 'account:id,company_name,mobile_no', 'salesman:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
        // ->where('organization_id', getOrganizationId())
        // ->where('opportunities.property_id', decodeId($id))
        // ->where(function ($query) {
        //     employeeAssociatedDateQuery($query);
        // })
        // ->whereHas('generateEstimate', function ($q) {
        //     $q->where('is_complete', true)->where('is_archive', request()->route()->getName() == (getRouteAlias() . '.archive.index') ? 1 : 0)->where('total_price', '>', 0);
        // })->when(request('status'), function ($query) {
        //     $query->whereHas('generateEstimate', function ($q) {
        //         if (request('status') == 'proposed') {
        //             $q->where('status',  'proposed')->whereNot('client_status', 'request_change');
        //         } else if (request('status') == 'won') {
        //             $q->where('status',  'won');
        //         } else if (request('status') == 'revision') {
        //             $q->where('status',  'proposed')->where('client_status', 'request_change');
        //         } else if (request('status') == 'lost') {
        //             $q->where('status',  'lost');
        //         }
        //     });
        // })->latest()->select('id', 'opportunity_name', 'id as er_no', 'account_id', 'sale_person_id', 'estimator_id', 'bid_due_date')->get();

        $estimates = Opportunity::with(['generateEstimate.clientAction:generate_estimate_id,status', 'account:id,company_name,mobile_no', 'salesman:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
            ->where('organization_id', getOrganizationId())
            ->where('opportunities.property_id', decodeId($id))
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })->latest()->select('id', 'opportunity_name', 'status', 'id as er_no', 'account_id', 'sale_person_id', 'estimator_id', 'bid_due_date')->get();

        $estimatescomplete = Opportunity::with(['generateEstimate.clientAction:generate_estimate_id,status', 'account:id,company_name,mobile_no', 'salesman:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
            ->where('organization_id', getOrganizationId())
            ->where('status', 5)
            ->where('opportunities.property_id', decodeId($id))
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })
            ->whereHas('generateEstimate', function ($q) {
                $q->where('is_complete', true)->where('is_archive', request()->route()->getName() == (getRouteAlias().'.archive.index') ? 1 : 0)->where('total_price', '>', 0)->where('client_status', ['approve', 'reject']);
            })->when(request('status'), function ($query) {
                $query->whereHas('generateEstimate', function ($q) {
                    if (request('status') == 'won') {
                        $q->where('status', 'won');
                    }
                });
            })->latest()->select('id', 'opportunity_name', 'id as er_no', 'account_id', 'sale_person_id', 'estimator_id', 'bid_due_date')->get();

        $oppdocuments = Opportunity::where('property_id', decodeId($id))
            ->with('documents')
            ->get()
            ->pluck('documents')
            ->flatten();

        $estimatedocuments = Opportunity::where('property_id', decodeId($id))
            ->with(['estimatedocuments' => function ($query) {
                $query->whereNotNull('pdf_path');
            }])
            ->get()
            ->pluck('estimatedocuments')
            ->flatten();

        // dd($estimatedocuments);
        return view('organization.property.propertyDetails', compact('property', 'contacts', 'roles', 'open', 'pending', 'completed', 'documents', 'customerIssuesCount', 'accounts', 'openissues', 'pendingissues', 'completedissues', 'operations', 'operationscomplete', 'estimates', 'estimatescomplete', 'oppdocuments', 'estimatedocuments'));
    }

    public function storeDocument(Request $request, $prop_id)
    {

        $propertyId = decodeId($prop_id);

        if ($request->images_opportunity) {
            $images = $request->images_opportunity;

            foreach ($images as $image) {
                // dd($image);
                // $randomPart = Str::random(10);

                $fileExtension = pathinfo($image, PATHINFO_EXTENSION);

                // $imageName = $randomPart . '.' . $fileExtension;

                // $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xls', 'xlsx', 'csv', 'doc', 'docx'];

                // if (in_array($fileExtension, $validExtensions)) {

                // $filePath = $image->storeAs('public/opportunity_documents', $imageName);
                $filePath = 'public/property_documents/'.$image;

                PropertyDocument::create([
                    'property_id' => $propertyId,
                    'document_name' => $request->file_name,
                    'name' => $image,
                    'path' => $filePath,
                    'type' => $fileExtension, // File extension
                ]);

                // dd($result);
                // }
                // else {
                //     $filePath = $image->storeAs('public/opportunity_documents', $imageName);

                //     $opportunity->documents()->create([
                //         'name' => $imageName,
                //         'path' => $filePath,
                //         'type' => $fileExtension,
                //     ]);
                // }
            }
        }

        return redirect()->back()->with('success', 'Files uploaded successfully.');
    }

    public function storeFiles(Request $request)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $file->storeAs('public/property_documents', $name);

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function deleteFiles($id = null)
    {
        // Ensure correct file path for deletion
        $filePath = storage_path('app'.DIRECTORY_SEPARATOR.'public'.DIRECTORY_SEPARATOR.'property_documents'.DIRECTORY_SEPARATOR.request('filename'));

        // Check if the file exists and then delete it
        if (File::exists($filePath)) {
            File::delete($filePath);
            // Optionally, return a success response or handle further logic
        } else {
            // Handle the case when the file doesn't exist
            return response()->json(['message' => 'File not found: '.$filePath], 404);
        }

        return response()->json('File Removed Successfully!', HTTP_OK);
    }

    public function create(Request $request)
    {
        PropertyInformation::create([
            'name' => $request->property_name,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip_code,
            'company_id' => $request->account,
            'organization_id' => getOrganizationId(),
        ]);

        return redirect()->back()->with('success', 'Property created successfully!');
    }

    public function getData(Request $request)
    {
        // Fetch invoices with necessary joins and select required columns
        $invoices = PropertyInformation::join('accounts', 'property_information.company_id', '=', 'accounts.id')
            ->join('users', 'accounts.account_owner', '=', 'users.id') // Join with users table
            ->orderBy('property_information.created_at', 'desc')
            ->where('property_information.organization_id', getOrganizationId())
            ->select([
                'property_information.id',
                'accounts.company_name as company_name',
                'property_information.name as property_name',
                'property_information.city',
                'property_information.state',
                'property_information.address1',
                'property_information.zip',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as account_owner_name"), // Concatenate first name and last name
            ])
            ->get();

        if ($searchTerm = $request->input('searchTerm')) {
            $issues->where(function ($query) use ($searchTerm) {
                $query->where('account_owner_name', 'LIKE', "%$searchTerm%");
                //   ->orWhere('subject', 'LIKE', "%$searchTerm%");
                //   ->orWhereHas('propertyInformation', function($q) use ($searchTerm) {
                //       $q->where('name', 'LIKE', "%$searchTerm%");
                //   });
                //   ->orWhereHas('contactInformation', function($q) use ($searchTerm) {
                //       $q->where('first_name', 'LIKE', "%$searchTerm%")
                //         ->orWhere('last_name', 'LIKE', "%$searchTerm%");
                //   });
            });
        }

        // Return data using DataTables
        return DataTables::of($invoices)

            // Add a column for the account owner name
            ->addColumn('account_owner_name', function ($row) {
                return $row->account_owner_name; // Show concatenated first and last name
            })
            ->addColumn('state', function ($row) {
                return \Illuminate\Support\Str::limit($row->state, 30, '...');
            })
            // Add an action column for the dropdown with edit and delete options
            ->addColumn('action', function ($row) {
                return '
                <div class="dropdown mx-auto w-fit">
                    <button
                        class="btn btn-sm bg-transparent dropdown-toggle text-center"
                        type="button"
                        id="dropdown'.$row->id.'" data-toggle="dropdown" aria-expanded="false"
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M14 5C14 6.104 13.104 7 12 7C10.896 7 10 6.104 10 5C10 3.896 10.896 3 12 3C13.104 3 14 3.896 14 5Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 10C10.896 10 10 10.896 10 12C10 13.104 10.896 14 12 14C13.104 14 14 13.104 14 12C14 10.896 13.104 10 12 10Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 17C10.896 17 10 17.896 10 19C10 20.104 10.896 21 12 21C13.104 21 14 20.104 14 19C14 17.896 13.104 17 12 17Z"
                                fill="#828282"
                            />
                        </svg>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdown'.$row->id.'">
                        <li>
                            <a onclick="editSectionProperty('.$row->id.')" data-toggle="modal"
                                data-target="#editPropertyModal" class="dropdown-item" href="#">Edit</a>
                        </li>
                            <li>
                            <a class="dropdown-item" href="'.route(getRouteAlias().'.property.detail', encodeId($row->id)).'">View Details</a>
                            </li>

                        <li>

                            <a id="deleteProperty" data-toggle="modal" data-target="#DeleteModal" data-value="'.encodeId($row->id).'"
                                class="dropdown-item" href="#">Delete</a>
                        </li>
                    </ul>
                </div>';
            })
            ->rawColumns(['action']) // Ensure the action column renders the HTML properly
            ->make(true);
    }

    public function edit(Request $request)
    {
        $id = $request->id;
        $property = PropertyInformation::find($id);

        return response()->json($property);

    }

    public function update(Request $request)
    {
        $property = PropertyInformation::findOrFail($request->property_id);

        // Update the property information
        $property->update([
            'name' => $request->property_name,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip_code,
            'company_id' => $request->account,
            'organization_id' => getOrganizationId(),
        ]);

        // Redirect back with a success message
        return redirect()->back()->with('success', 'Property updated successfully!');
    }

    public function propertyFileImport(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xls,xlsx|max:2048', // 2MB max
        ]);

        try {
            $file = $request->file('file');
            $extension = $file->getClientOriginalExtension();

            if (! in_array($extension, ['xls', 'xlsx'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Please upload an Excel file (.xls, .xlsx)',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $import = new PropertyImport;
            Excel::import($import, $file);

            $importedCount = $import->getRowCount();
            $failedRows = $import->getFailures();

            if (count($failedRows) > 0) {
                $errors = [];
                foreach ($failedRows as $failure) {
                    $errors[] = [
                        'row' => $failure->row(),
                        'errors' => $failure->errors(),
                        'values' => $failure->values(),
                    ];
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Import completed with some errors',
                    'imported_count' => $importedCount,
                    'error_count' => count($failedRows),
                    'errors' => $errors,
                ], Response::HTTP_OK);
            }

            return response()->json([
                'success' => true,
                'message' => 'Properties imported successfully',
                'imported_count' => $importedCount,
            ], Response::HTTP_OK);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Related record not found: '.$e->getMessage(),
            ], Response::HTTP_NOT_FOUND);

        } catch (ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values(),
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Validation errors occurred during import',
                'errors' => $errors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing file: '.$e->getMessage(),
                'file_error' => true,
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function exportProperties(Request $request)
    {
        return Excel::download(new PropertyExport, 'organization.properties.xlsx');
    }

    public function delete($id)
    {
        $property = PropertyInformation::findOrFail(decodeId($id));
        $property->delete();
        Opportunity::where('property_id', decodeId($id))->delete();
        Contact::where('property_id', decodeId($id))->delete();

        return response()->json(['success' => 'true'], HTTP_OK);
    }

    public function updatePropertyHealth(Request $request)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'property_health' => 'required|string',  // Make sure it's a valid string
        ]);
        $id = $request->id;

        // Assuming you're updating a specific property (use any logic to get the correct record)
        $property = PropertyInformation::find($id);  // Replace with actual logic to fetch the relevant record

        if ($property) {
            // Update the property_health column in the database
            $property->property_health = $validated['property_health'];
            $property->save();

            // Respond with success
            return response()->json(['success' => true]);
        }

        // Respond with failure if the property was not found
        return response()->json(['success' => false]);
    }

    public function saveCompanyNotes(Request $request)
    {
        // Validate the input
        $validated = $request->validate([
            'company_notes' => 'nullable|string|max:5000', // Adjust validation rules as needed
            'property_id' => 'required|exists:property_information,id', // Ensure the property exists
        ]);

        // Find the property by ID and update the company_notes
        $property = PropertyInformation::findOrFail($request->property_id);
        $property->company_notes = $request->company_notes;
        $property->save();

        // Return a success response
        return response()->json(['message' => 'Notes saved successfully!']);
    }
}
