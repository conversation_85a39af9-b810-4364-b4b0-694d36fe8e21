<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\CostSummary;
use App\Models\GenerateEstimate;
use App\Models\Target;
use App\Models\User;
use App\Rules\NoDataRule;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Yajra\DataTables\Facades\DataTables;

class FinancialToolController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:manage_financial_tools');
    }

    public function index()
    {
        $operations = GenerateEstimate::with([
            'cost_summary', 'opportunityid', 'client', 'manager', 'saleMan', 'estimator', 'latestScheduleDate', 'getEstimateMaterial', 'getEstimateHardMaterial', 'getEstimatePlantMaterial', 'getEstimateOtherCost', 'getEstimateSubContractor',
        ])
            ->where('operation_status', 'Completed')
            ->where(['status' => 'won', 'is_schedule' => true])
            ->whereHas('opportunityid', function ($q) {
                $q->where('organization_id', getOrganizationId());
            })
            ->get();
        $laborBurden = DB::table('labors')->where('organization_id', getOrganizationId())->value('labor_burden') ?? 0;
        $organizationCreatedYear = User::find(getOrganizationId())->created_at->year;
        $currentYear = Carbon::now()->year;
        $organizationYears = collect(range($organizationCreatedYear, $currentYear))->reverse()->toArray();
        $unScheduleEstimates = GenerateEstimate::whereHas('opportunityid', function ($q) {
            $q->where('organization_id', getOrganizationId());
        })
            ->where(['status' => 'won', 'is_schedule' => false])->where('operation_status', '!=', 'Completed')
            ->get();

        return view('organization.financial_tools.index', compact('laborBurden', 'operations', 'organizationCreatedYear', 'organizationYears', 'unScheduleEstimates'));
    }

    public function tableListing()
    {
        $operations = GenerateEstimate::with([
            'cost_summary', 'opportunityid', 'client', 'manager', 'saleMan', 'estimator', 'latestScheduleDate',
        ])
            ->where('operation_status', 'Completed')
            ->where(['status' => 'won', 'is_schedule' => true])
            ->whereHas('opportunityid', function ($q) {
                $q->where('organization_id', getOrganizationId());
            });

        return DataTables::eloquent($operations)
            ->rawColumns(['generateEstimate.status', 'action', 'client.full_name', 'sale_man.name', 'estimator.name', 'generateEstimate.notes'])
            ->toJson();
    }

    public function financeTargets(Request $request)
    {
        $monthlySums = $this->RevenueTargetCalculations($request);
        $monthlySums = collect($monthlySums);
        $tableView = View::make('organization.financial_tools.partials.months_target_table', compact('monthlySums'))->render();

        return response()->json([
            'html' => $tableView,
        ], HTTP_OK);
    }

    public function revenueTargetPercentage(Request $request)
    {
        $monthlySums = $this->RevenueTargetCalculations($request);
        $collection = collect($monthlySums);

        return response()->json([
            'message' => 'success',
            'totalTarget' => number_format($collection->sum('target'), 0),
            'totalRevenue' => number_format($collection->sum('revenue'), 0),
            'TargetCompletedPercentage' => ($collection->sum('target') == '0' ? '0%' : number_format(($collection->sum('revenue') / $collection->sum('target')) * 100, 0).'%'),

        ]);
    }

    public function actualVsTarget(Request $request)
    {
        $monthlySums = $this->RevenueTargetCalculations($request);
        $revenues = array_column($monthlySums, 'revenue');
        $targets = array_column($monthlySums, 'target');

        return response()->json([
            'revenue_array' => $revenues,
            'target_array' => $targets,
        ]);
    }

    private function RevenueTargetCalculations(Request $request)
    {
        $year = $request->input('year');

        $startDate = Carbon::createFromDate($year, 1, 1);
        $endDate = Carbon::createFromDate($year, 12, 31);

        // get target estimates within the given year
        $estimates = GenerateEstimate::with('opportunityid')
            ->where('operation_status', 'Completed')
            ->whereHas('opportunityid', function ($q) {
                $q->where('organization_id', getOrganizationId());
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
        // Create an array to store the monthly sums
        $monthlySums = [];

        for ($month = 1; $month <= 12; $month++) {
            $record['month'] = Carbon::createFromDate($year, $month, 1)->format('F');
            $record['revenue'] = 0;

            // Calculate sum for the current month
            foreach ($estimates as $estimate) {
                if ($estimate->created_at->month == $month) {
                    $record['revenue'] += $estimate->total_price;
                }
            }

            $record['target'] = Target::whereYear('month', $year)
                ->where('organization_id', getOrganizationId())
                ->whereMonth('month', $month)
                ->value('price');

            // If the target is 0, set it to null
            if ($record['target'] == 0) {
                $record['target'] = 0;
            }

            $monthlySums[] = $record;
        }

        return $monthlySums;
    }

    public function costSummary(Request $request)
    {
        CostSummary::updateOrCreate([
            'organization_id' => getOrganizationId(),
            'operation_id' => $request->operation_id,
        ], [
            'hours' => $request->hours,
            'equipment_cost2' => $request->equipment_cost2,
            'labor3' => $request->labor3,
            'hard_material4' => $request->hard_material4,
            'plant_material' => $request->plant_material,
            'other_jobCost' => $request->other_jobCost,
            'sub_contractor' => $request->sub_contractor,
            'accurals' => $request->accurals,
            'po_number' => $request->po_number,
            'work_type' => $request->work_type,
            'service_line' => $request->service_line,
        ]);

        $operations = GenerateEstimate::with([
            'cost_summary', 'opportunityid', 'client', 'manager', 'saleMan', 'estimator', 'latestScheduleDate', 'getEstimateMaterial', 'getEstimateHardMaterial', 'getEstimatePlantMaterial', 'getEstimateOtherCost', 'getEstimateSubContractor',
        ])
            ->where('operation_status', 'Completed')
            ->where(['status' => 'won', 'is_schedule' => true])
            ->whereHas('opportunityid', function ($q) {
                $q->where('organization_id', getOrganizationId());
            })
            ->get();
        $forcasted_gp = $operations->sum(function ($item) {
            return (float) str_replace(',', '', $item->grand_total ?? 0) - (float) optional($item->cost_summary)->equipment_cost2 - (float) optional($item->cost_summary)->labor3 - (float) optional($item->cost_summary)->hard_material4 - (float) optional($item->cost_summary)->plant_material - (float) optional($item->cost_summary)->other_jobCost - (float) optional($item->cost_summary)->sub_contractor - (float) optional($item->cost_summary)->accurals;
        });

        $forcasted_revenue = $operations->sum(function ($item) {
            return $item->grand_total ? str_replace(',', '', $item->grand_total) : 0;
        });

        $forcastedGpSumPercentage = ($forcasted_gp / $forcasted_revenue) * 100;

        return response()->json(['forcasted_gp' => $forcasted_gp, 'forcastedGpSumPercentage' => $forcastedGpSumPercentage]);
    }

    public function costSummaryNote(Request $request)
    {
        $request->validate([
            'notes' => [new NoDataRule],
        ]);
        CostSummary::updateOrCreate([
            'operation_id' => $request->operation_id,
            'organization_id' => getOrganizationId(),
        ], [
            'notes' => $request->notes,
        ]);

        return response()->json(['message' => 'Note Added Successfully', 'notes' => $request->notes, 'operation_id' => $request->operation_id]);
    }

    public function costSummaryViewNote($id)
    {
        $response = CostSummary::where('operation_id', $id)->select('notes')->first();

        return response()->json([$response?->notes]);
    }

    public function costSummaryFilter(Request $request)
    {
        $operations = GenerateEstimate::with([
            'cost_summary', 'opportunityid', 'client', 'manager', 'saleMan', 'estimator', 'latestScheduleDate', 'getEstimateMaterial', 'getEstimateHardMaterial', 'getEstimatePlantMaterial', 'getEstimateOtherCost', 'getEstimateSubContractor',
        ])
            ->where('operation_status', 'Completed')
            ->where(['status' => 'won', 'is_schedule' => true])
            ->when($request->month && $request->year, function ($query) use ($request) {
                return $query
                    ->whereMonth('completed_at', date('m', strtotime($request->month)))
                    ->whereYear('completed_at', $request->year);
            }, function ($query) use ($request) {
                if ($request->month) {
                    return $query->whereMonth('completed_at', date('m', strtotime($request->month)));
                } elseif ($request->year) {
                    return $query->whereYear('completed_at', $request->year);
                }
            })
            ->whereHas('opportunityid', function ($q) {
                $q->where('organization_id', getOrganizationId());
            })
            ->get();
        // $forcasted_gp = $operations->sum(function ($item) {
        //     $result = number_format((float) str_replace(',', '', $item->grand_total ?? 0) - (float) optional($item->cost_summary)->equipment_cost2 ?? 0 - (float) optional($item->cost_summary)->labor3 ?? 0 - (float) optional($item->cost_summary)->hard_material4 ?? 0 - (float) optional($item->cost_summary)->plant_material ?? 0 - (float) optional($item->cost_summary)->other_jobCost ?? 0 - (float) optional($item->cost_summary)->sub_contractor ?? 0 - (float) optional($item->cost_summary)->accurals ?? 0, 2);
        // });
        $forcasted_gp = $operations->sum(function ($item) {
            $grand_total = isset($item->grand_total) ? (float) str_replace(',', '', $item->grand_total) : 0;
            $equipment_cost = optional($item->cost_summary)->equipment_cost2 ?? 0;
            $labor_cost = optional($item->cost_summary)->labor3 ?? 0;
            $hard_material_cost = optional($item->cost_summary)->hard_material4 ?? 0;
            $plant_material_cost = optional($item->cost_summary)->plant_material ?? 0;
            $other_job_cost = optional($item->cost_summary)->other_jobCost ?? 0;
            $sub_contractor_cost = optional($item->cost_summary)->sub_contractor ?? 0;
            $accurals_cost = optional($item->cost_summary)->accurals ?? 0;

            $result = $grand_total - $equipment_cost - $labor_cost - $hard_material_cost - $plant_material_cost - $other_job_cost - $sub_contractor_cost - $accurals_cost;

            return $result;
        });
        $laborHours = $operations->sum(function ($item) {
            $plantMaterialSum = $item->getestimatePlantMaterial
                ->where('generate_estimate_id', $item->id)
                ->map(function ($material) {
                    return $material->quantity + optional($material->material)->install ?? 0;
                })
                ->sum();

            $hardMaterialSum = $item->getestimateHardMaterial
                ->where('generate_estimate_id', $item->id)
                ->map(function ($material) {
                    return $material->quantity + optional($material->material)->labor ?? 0;
                })
                ->sum();

            return $plantMaterialSum + $hardMaterialSum;
        });
        // $forcasted_revenue = $operations->sum(function ($item) {
        //     return is_numeric($item->grand_total)
        //         ? number_format(str_replace(',', '', $item->grand_total), 2)
        //         : '0.00';
        // });
        $forcasted_revenue = $operations->sum(function ($item) {
            $cleaned_value = str_replace(',', '', $item->grand_total);

            return is_numeric($cleaned_value) ? $cleaned_value : 0.0;
        });
        $forcasted_gp_per = number_format($forcasted_revenue == 0 ? 0 : ($forcasted_gp / $forcasted_revenue) * 100, 0);
        // number_format($forcasted_revenue == 0 ? 0 : $forcasted_gp / $forcasted_revenue, 2);

        $Hr = number_format($laborHours == 0 ? 0 : $forcasted_revenue / $laborHours, 2);
        $laborBurden = DB::table('labors')->where('organization_id', getOrganizationId())->value('labor_burden') ?? 0;

        $table = View::make('organization.financial_tools.partials.cost_summary', compact('operations', 'laborBurden', 'forcasted_gp', 'laborHours', 'forcasted_gp_per', 'forcasted_revenue', 'Hr'))->render();

        return response()->json(['table' => $table, 'forcasted_gp' => number_format($forcasted_gp, 2), 'laborHours' => $laborHours, 'forcasted_revenue' => $forcasted_revenue, 'forcasted_gp_per' => $forcasted_gp_per, 'Hr' => $Hr]);
    }
}
