<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Imports\EquipmentImport;
use App\Imports\HardMaterialImport;
use App\Imports\LaborImport;
use App\Imports\OtherCostsImport;
use App\Imports\PlantMaterialImport;
use App\Imports\ServiceLineMaterialImport;
use App\Imports\SubcontractorImport;
use App\Imports\WorkTypeServiceLineMaterialImport;
use App\Models\Equipment;
use App\Models\HardMaterial;
use App\Models\Labor;
use App\Models\OtherCost;
use App\Models\PlantMaterial;
use App\Models\ServiceLineMaterial;
use App\Models\ServiceLineWorkType;
use App\Models\Subcontractor;
use App\Models\UnitOfMeasurement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class MaterialController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:material_setting');
    }

    public function index()
    {
        $div = DB::table('divisions')->get();

        // dd($div);
        return view('organization.material.hard.index', compact('div'));
    }

    public function hardMaterialListing(Request $request)
    {
        if ($request->ajax()) {
            $model = HardMaterial::where('organization_id', getOrganizationId());
            $model = $model->when(request('status') && request('status') != 'Clear', function ($query) {
                $query->where(DB::raw('lower(uom)'), strtolower(request('status')));
            })->select('id', 'name', 'uom', 'cost', 'labor', 'image');

            return DataTables::eloquent($model)
                ->addColumn('image_column', function ($row) {
                    // Inline HTML template for image handling
                    $imageHtml = '';
                    if (isset($row->image)) {
                        $imageHtml = '
                    <div class="flex items-center">
                        <img src="'.asset('storage/'.$row?->image).'" alt="Material Image" class="mr-2" style="width:100px;">
                        <button onclick="deleteImage('.$row->id.')" class="btn btn-danger btn-danger delete-material-image">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>';
                    } else {
                        $imageHtml = '
                    <button onclick="openImageUpload('.$row->id.')" class="btn btn-primary text-white px-3 py-1 rounded">
                        Add Image
                    </button>';
                    }

                    return $imageHtml;
                })
                ->rawColumns(['image_column'])
                ->toJson();
        }
    }

    public function plantListing(Request $request)
    {
        if ($request->ajax()) {
            $model = PlantMaterial::where('organization_id', getOrganizationId());
            $model = $model->when(request('size') && request('size') != 'Clear2', function ($query) {
                // $size = request('size');
                // if (in_array($size, ['1 Gal', 'Quart'])) {
                $query->where(DB::raw('lower(size)'), strtolower(request('size')));
                // }
            })->select('name', 'type', 'size', 'cost', 'install');

            return DataTables::eloquent($model)
                ->toJson();
        }
    }

    public function getFilters(Request $request, $key = 'hardMaterial')
    {
        if ($request->ajax()) {
            if ($key == 'hardMaterial') {
                $model = HardMaterial::where('organization_id', getOrganizationId())->distinct()->pluck('uom')->map(function ($size) {
                    return ucwords($size);
                })->values();

                return response()->json(['data' => $model], HTTP_OK);
            }

            $model = PlantMaterial::where('organization_id', getOrganizationId())->distinct()->pluck('size')->map(function ($size) {
                return ucwords($size);
            })->values();

            return response()->json(['data' => $model], HTTP_OK);
        }
    }

    public function fileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                // $division = $request->input('division');
                Excel::import(new HardMaterialImport, $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'success' => 'false',
        //         'message' => $e->getMessage()

        //     ], HTTP_BAD_REQUEST);
        // }
    }

    public function plantFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                // $division = $request->input('division');
                Excel::import(new PlantMaterialImport, $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'success' => 'false',
        //         'message' => 'Something went wrong!'

        //     ], HTTP_BAD_REQUEST);
        // }
    }

    public function show()
    {
        $div = DB::table('divisions')->get();

        return view('organization.material.equipment.index', compact('div'));
    }

    public function labors()
    {
        $div = DB::table('divisions')->get();

        return view('organization.material.labor.index', compact('div'));
    }

    public function contractors()
    {
        $div = DB::table('divisions')->get();

        return view('organization.material.subcontractor.index', compact('div'));
    }

    public function showJobCost()
    {
        $div = DB::table('divisions')->get();

        return view('organization.material.job-cost.index', compact('div'));
    }

    public function equipmentFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                $division = $request->input('division');
                Excel::import(new EquipmentImport($division), $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function laborFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                $division = $request->input('division');
                Excel::import(new LaborImport($division), $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function subcontractorFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                $division = $request->input('division');
                Excel::import(new SubcontractorImport($division), $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function jobCostFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {
                $division = $request->input('division');
                Excel::import(new OtherCostsImport($division), $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function equipmentListing(Request $request)
    {
        if ($request->ajax()) {
            $model = Equipment::where('organization_id', getOrganizationId())->latest();
            $model = $model->when(request('status'), function ($query) {
                if (request('status') == 'Hours' || request('status') == 'Daily' || request('status') == 'Weekly') {
                    $query->where('uom', request('status'));
                }
            })->select('name', 'uom', 'cost', 'gross_margin');

            return DataTables::eloquent($model)

                ->toJson();
        }
    }

    public function contractorListing(Request $request)
    {
        if ($request->ajax()) {
            $model = Subcontractor::where('organization_id', getOrganizationId())->latest();
            $model = $model->when(request('status'), function ($query) {
                if (request('status') == 'Hours' || request('status') == 'Daily' || request('status') == 'Weekly') {
                    $query->where('uom', request('status'));
                }
            })->select('name', 'uom', 'cost', 'gross_margin');

            return DataTables::eloquent($model)

                ->toJson();
        }
    }

    public function laborListing(Request $request)
    {
        if ($request->ajax()) {
            $model = Labor::where('organization_id', getOrganizationId())->latest();
            $model = $model->when(request('status'), function ($query) {
                if (request('status') == 'Hours' || request('status') == 'Daily' || request('status') == 'Weekly') {
                    $query->where('uom', request('status'));
                }
            })->select('name', 'uom', 'cost', 'gross_margin');

            return DataTables::eloquent($model)

                ->toJson();
        }
    }

    public function jobCostListing(Request $request)
    {
        if ($request->ajax()) {
            $model = OtherCost::where('organization_id', getOrganizationId())->latest()->select('name', 'uom', 'cost');

            return DataTables::eloquent($model)
                ->toJson();
        }
    }

    public function serviceLinePageShow()
    {
        return view('organization.material.service-line.index');
    }

    public function serviceLineMaterialPluck(Request $request)
    {
        if ($request->ajax()) {
            $services = ServiceLineMaterial::where('organization_id', getOrganizationId())->pluck('name', 'id');

            return response()->json(['services' => $services], HTTP_OK);

        }
    }

    public function serviceLineMaterialListing(Request $request)
    {
        if ($request->ajax()) {
            $model = ServiceLineMaterial::where('organization_id', getOrganizationId())->select('name')->latest();

            return DataTables::eloquent($model)
                ->addIndexColumn()
                ->editColumn('name', function ($item) {
                    return ucwords($item->name);
                })
                ->toJson();
        }
    }

    public function serviceLineFileImport(Request $request)
    {
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {

                Excel::import(new ServiceLineMaterialImport, $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function workTypeFileImport(Request $request)
    {
        $request->validate([
            'service_line_id' => 'required|exists:service_line_materials,id',
        ]);
        // try {
        if ($request->hasFile('file')) {
            $extension = File::extension($request->file->getClientOriginalName());
            if ($extension == 'xls' || $extension == 'xlsx') {

                Excel::import(new WorkTypeServiceLineMaterialImport($request->service_line_id), $request->file('file')->store('temp'));

                return response()->json(['success' => 'true'], HTTP_OK);
                // 'Your file is a valid xls or csv file'
            } else {
                return response()->json([
                    'success' => 'false',
                    'message' => "File is a $extension file.!! Please upload a valid xls file..!",
                ], HTTP_BAD_REQUEST);
            }
        }
    }

    public function workTypeServiceLineListing(Request $request)
    {
        if ($request->ajax()) {
            $model = ServiceLineWorkType::with('serviceLine')->where('service_line_id', $request->type)->select('name', 'service_line_id')->latest();

            return DataTables::eloquent($model)
                ->addIndexColumn()
                ->editColumn('name', function ($item) {
                    return ucwords($item->name);
                })
                ->editColumn('serviceLine.name', function ($item) {
                    return ucwords($item->serviceLine->name);
                })
                ->only(['name', 'serviceLine.name'])
                ->toJson();
        }
    }

    public function hardMaterialImageUpload(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:2048',
            'material_id' => 'required|exists:hard_materials,id',
        ]);

        $material = HardMaterial::findOrFail($request->material_id);

        // Delete old image if exists
        if ($material->image) {
            Storage::delete($material->image);
        }

        // Store new image
        $imagePath = $request->file('image')->store('materials', 'public');

        $material->update(['image' => $imagePath]);

        return response()->json(['success' => true, 'message' => 'Image uploaded successfully']);
    }

    public function deleteImage($id)
    {
        $material = HardMaterial::findOrFail($id);

        if ($material->image) {
            Storage::disk('public')->delete($material->image);
            $material->update(['image' => null]);
        }

        return response()->json(['success' => true, 'message' => 'Image deleted successfully']);
    }

    public function getMaterialUOM($type)
    {
        $units = UnitOfMeasurement::where('type', $type)->get();
        $response = [
            'units' => $units,
        ];

        return response()->json($response);
    }
}
