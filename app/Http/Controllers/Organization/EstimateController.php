<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\EstimateRequest;
use App\Jobs\EstimateRequestNotifyJob;
use App\Models\Adress;
use App\Models\Client;
use App\Models\Estimate;
use App\Models\User;
use App\Services\EstimateRequestService;
use App\Traits\PermissionMiddlewareTrait;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;

class EstimateController extends Controller
{
    use PermissionMiddlewareTrait;

    public function __construct()
    {
        $permissionsMap = [
            // functionName => [permissions]
            'index' => ['create_estimate_request', 'edit_estimate_request', 'view_estimate_documents', 'estimate_request_listing'],
            'listing' => ['create_estimate_request', 'edit_estimate_request', 'view_estimate_documents', 'estimate_request_listing'],
            'create' => ['create_estimate_request'],
            'store' => ['create_estimate_request'],
            'getDocuments' => ['view_estimate_documents'],
            'delete' => ['delete_estimate_request'],
            'edit' => ['edit_estimate_request'],
            'update' => ['edit_estimate_request'],
        ];
        $this->applyPermissionMiddleware($permissionsMap);
    }

    public function index()
    {
        return view('organization.estimate.index');
    }

    public function listing(Request $request)
    {
        if ($request->ajax()) {
            return EstimateRequestService::renderTableData($request);
        }
    }

    public function create($id)
    {
        $client = Client::where('id', decodeID($id))->first();
        if (! is_object($client)) {
            return redirect()->back()->with('error', 'Something went wrong!');
        }
        $sales = $this->getSalesMan();
        $estimators = $this->getEstimator();
        $estimate = new Estimate;
        $action = URL::route(getRouteAlias().'.estimate.store');

        return view('organization.estimate.create', get_defined_vars());
    }

    public function store(EstimateRequest $request)
    {
        DB::beginTransaction();
        try {
            $estimate = new Estimate;
            $estimate->client_id = $request->client_id;
            $estimate->sale_person_id = $request->sale_person_id;
            $estimate->estimator_id = $request->estimator_id;
            $estimate->project_name = $request->project_name;
            $estimate->job_name = $request->job_name;
            $estimate->job_number = $request->job_number;
            $estimate->lead_source = $request->lead_source;
            $estimate->probability = $request->probability;
            $estimate->description = $request->description;
            $estimate->created_by = 'company';
            $estimate->organization_id = getOrganizationId();
            $estimate->creator_id = auth('web')->user()->id;

            $estimate->save();

            if ($request->exists('ext_document')) {
                foreach ($request->input('ext_document', []) as $file) {
                    $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                }
            }
            $address = $this->createAddress($estimate->id, PROPERTY);
            EstimateRequestNotifyJob::dispatch($estimate->id, $address->property_name);
            DB::commit();

            return redirect()->route(getRouteAlias().'.estimate.index')->with('showModal', 'Your request has been successfully completed.');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function edit($id)
    {
        $estimate = Estimate::with('propertyAddress')->where('id', decodeId($id))->first();
        $client = $estimate->client;
        $sales = $this->getSalesMan();
        $estimators = $this->getEstimator();
        $action = URL::route(getRouteAlias().'.estimate.update', ['id' => $id]);

        return view('organization.estimate.create', get_defined_vars());
    }

    public function getDocuments($id)
    {
        $estimate = Estimate::where('id', decodeId($id))->first();
        if (is_object($estimate)) {
            if (count($estimate->getMedia('document'))) {
                $documents = $estimate->getMedia('document');
                $tableView = View::make('organization.estimate.document-modal', compact('documents'))->render();

                return json_encode($tableView, HTTP_OK);
            } else {
                return response()->json('No attachments found against this request.', HTTP_BAD_REQUEST);
            }
        }

        return response()->json('No record found', HTTP_BAD_REQUEST);
    }

    public function update(EstimateRequest $request, $id)
    {

        DB::beginTransaction();
        try {
            $estimate = Estimate::where('id', decodeID($id))->first();

            $estimate->client_id = $request->client_id;
            $estimate->sale_person_id = $request->sale_person_id;
            $estimate->lead_source = $request->lead_source;
            $estimate->probability = $request->probability;
            $estimate->job_name = $request->job_name;
            $estimate->job_number = $request->job_number;
            $estimate->estimator_id = $request->estimator_id;
            if ($estimate->created_by == 'company') {
                $estimate->project_name = $request->project_name;
                $estimate->description = $request->description;

            } else {
                $estimate->is_active = ($estimate->is_active == 2) ? 1 : $estimate->is_active;
            }

            $estimate->save();

            if ($estimate->created_by == 'company') {
                if (count($estimate->getMedia('document'))) {
                    foreach ($estimate->getMedia('document') as $media) {
                        if (! in_array($media->file_name, $request->input('ext_document', []))) {
                            $media->delete();
                        }
                    }
                }

                if (count($estimate->getMedia('document'))) {
                    $media = $estimate->getMedia('document')->pluck('file_name')->toArray();
                    foreach ($request->input('ext_document', []) as $file) {
                        if (count($media) === 0 || ! in_array($file, $media)) {
                            $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                        }
                    }
                } else {
                    if ($request->exists('ext_document')) {
                        foreach ($request->input('ext_document', []) as $file) {
                            $estimate->addMedia(public_path('storage/uploads/'.$file))->toMediaCollection('document');
                        }
                    }
                }

                $this->updateAddress($estimate->id, PROPERTY);

            } else {
                EstimateRequestNotifyJob::dispatch($estimate->id, $estimate->load('propertyAddress:property_name,estimate_id,type')->propertyAddress->property_name);
            }
            DB::commit();

            return redirect()->route(getRouteAlias().'.estimate.index')->with('success', 'Request Updated Successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            dd($e->getMessage());

            return redirect()->back()->with('error', 'Something went wrong!');
        }
    }

    public function delete($id)
    {
        $estimate = Estimate::find(decodeId($id));
        if (! is_object($estimate)) {
            return response()->json([
                'success' => 'false',
            ], HTTP_BAD_REQUEST);
        } else {
            $estimate->delete();

            return response()->json(['success' => 'true'], HTTP_OK);
        }
    }

    public function imageStore(Request $request)
    {
        $file = $request->file('file');
        $name = uniqid().'_'.trim($file->getClientOriginalName());
        $filePath = $request->file->storePubliclyAs('uploads', $name, 'public');

        return response()->json([
            'name' => $name,
            'original_name' => $file->getClientOriginalName(),
        ], HTTP_OK);
    }

    public function imageDelete($id = null)
    {
        if (File::exists(public_path('storage/uploads/'.request('name')))) {
            File::delete(public_path('storage/uploads/'.request('name')));
        }

        return response()->json('Image Removed Successfully!', HTTP_OK);
    }

    private function getSalesMan()
    {
        return User::wherehas('roles', function ($q) {
            $q->where('name', 'salesman');
        })->where([
            'parent_id' => getOrganizationId(),
            'status' => 'Active',
        ])->get();
    }

    private function getEstimator()
    {

        return User::wherehas('roles', function ($q) {
            $q->where('name', 'estimator');
        })
            ->where([
                'parent_id' => getOrganizationId(),
                'status' => 'Active',
            ])->get();

    }

    private function createAddress($estimateId, $type, $clientId = null)
    {
        $address = new Adress;
        if (request()->client_id) {
            $address->client_id = request()->client_id;
        }
        $address->estimate_id = $estimateId;
        $address->property_name = request()->property_name;
        $address->address1 = request()->address1;
        $address->address2 = request()->address2;
        $address->state = request()->state;
        $address->city = request()->city;
        $address->zip = request()->zip;
        $address->type = $type;
        $address->save();

        return $address;
    }

    private function updateAddress($estimateId, $type)
    {
        $address = Adress::where(['estimate_id' => $estimateId, 'type' => $type])->first();
        if (is_object($address)) {
            $address->estimate_id = $estimateId;
            $address->property_name = request()->property_name;
            $address->address1 = request()->address1;
            $address->address2 = request()->address2;
            $address->state = request()->state;
            $address->city = request()->city;
            $address->zip = request()->zip;
            $address->type = $type;
            $address->save();
        }

        return $address;
    }
}
