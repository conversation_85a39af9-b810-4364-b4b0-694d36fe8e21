<?php

namespace App\Http\Controllers\Organization;

use App\Exports\ClientsExport;
use App\Exports\EstimatesExport;
use App\Exports\OperationsExport;
use App\Exports\RequestsExport;
use App\Exports\SalesLostExport;
use App\Exports\SalesStatusExport;
use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Estimate;
use App\Models\GenerateEstimate;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:manage_reports');
    }

    public function index()
    {
        return view('organization.reports.index');
    }

    public function generateReports(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // 'name' => 'required|max:100',
            'start_date' => 'required',
            'end_date' => 'required',
            'type' => 'required',
            'format_file' => 'required',
        ], [
            'required' => 'This field is required',
        ])->validate();

        if ($request->type == 'client') {
            $clients = $this->getClients();
            if ($clients->isNotEmpty()) {
                if ($request->format_file == 'xls') {
                    try {
                        return Excel::download(new ClientsExport($clients), request('type').'.xls');
                    } catch (\Exception $e) {
                        return Redirect::back()->with('error', 'Something went wrong!');
                    }
                } elseif ($request->format_file == 'pdf') {
                    $pdf = $this->downloadPdf($clients, 'organization.reports.client-report');

                    return $pdf->download(request('type').'.pdf');
                }
            }
        }
        if ($request->type == 'requests') {
            $requests = $this->getRequests();
            if ($requests->isNotEmpty()) {
                if ($request->format_file == 'xls') {
                    try {
                        return Excel::download(new RequestsExport($requests), request('type').'.xls');
                    } catch (\Exception $e) {
                        return Redirect::back()->with('error', 'Something went wrong!');
                    }
                } elseif ($request->format_file == 'pdf') {
                    $pdf = $this->downloadPdf($requests, 'organization.reports.request-report');

                    return $pdf->download(request('type').'.pdf');
                }
            }
        }
        if ($request->type == 'estimate') {
            $generate_estimate = $this->getEstimate();
            if ($generate_estimate->isNotEmpty()) {
                if ($request->format_file == 'xls') {
                    try {
                        return Excel::download(new EstimatesExport($generate_estimate), request('type').'.xls');
                    } catch (\Exception $e) {
                        return Redirect::back()->with('error', 'Something went wrong!');
                    }
                } elseif ($request->format_file == 'pdf') {
                    $pdf = $this->downloadPdf($generate_estimate, 'organization.reports.estimate-report');

                    return $pdf->download(request('type').'.pdf');
                }
            }
        }
        if ($request->type == 'operation') {
            $operations = $this->getOperations();
            if ($operations->isNotEmpty()) {
                if ($request->format_file == 'xls') {
                    try {
                        return Excel::download(new OperationsExport($operations), request('type').'.xls');
                    } catch (\Exception $e) {
                        return Redirect::back()->with('error', 'Something went wrong!');
                    }
                } elseif ($request->format_file == 'pdf') {
                    $pdf = $this->downloadPdf($operations, 'organization.reports.operations-report');

                    return $pdf->download(request('type').'.pdf');
                }
            }
        }
        if ($request->type == 'lost' || $request->type == 'won' || $request->type == 'proposed') {
            $generate_estimate = $this->getSalesStatus();
            if ($generate_estimate->isNotEmpty()) {
                if ($request->format_file == 'xls') {
                    try {
                        return Excel::download(request('type') == 'lost' ? new SalesLostExport($generate_estimate) : new SalesStatusExport($generate_estimate), request('type').'.xls');
                    } catch (\Exception $e) {
                        return Redirect::back()->with('error', 'Something went wrong!');
                    }
                } elseif ($request->format_file == 'pdf') {
                    if ($request->type == 'lost') {
                        $fileName = 'Sales Lost';
                    }
                    if ($request->type == 'won') {
                        $fileName = 'Sales Won';
                    }
                    if ($request->type == 'proposed') {
                        $fileName = 'Sales Proposed';
                    }
                    $pdf = $this->downloadPdf($generate_estimate, 'organization.reports.sales-report', $fileName);

                    return $pdf->download(request('type').'.pdf');
                }
            }
        }

        return redirect()->back()->with('error', 'No Record found between selected date range.');
    }

    private function downloadPdf($result, $fileName, $status = null)
    {

        $organization = User::find(getOrganizationId());
        $data = [
            'title' => request('name'),
            'date' => customDateFormat(Carbon::now()).''.customTimeFormat(Carbon::now()),
            'primary_color' => $organization->primary_color,
            'secondary_color' => $organization->secondary_color,
            'start_date' => customDateFormat(Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date'))),
            'end_date' => customDateFormat(Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))),
            'count' => $result->count(),
            'status' => $status,
            'organization' => $organization,
            'result' => $result,
        ];

        return Pdf::loadView($fileName, $data);
    }

    private function getClients()
    {
        return Client::with(['propertyAddress', 'billingAddress'])
            ->where('organization_id', getOrganizationId())
            ->when(
                auth('web')->user()->isEmployee() && ! auth('web')->user()->hasRole('manager'),
                function ($q) {
                    $q->whereExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('estimates')
                            ->whereRaw('clients.id = estimates.client_id')
                            ->where(function ($subQuery) {
                                employeeAssociatedDateQuery($subQuery);
                            });
                    });
                }
            )
            ->whereBetween('created_at', [Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date')), Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))])
            ->get();
    }

    private function getRequests()
    {
        return Estimate::with(['client', 'saleMan', 'estimator', 'propertyAddress'])
            ->where(function ($q) {
                employeeAssociatedDateQuery($q);

            })
            ->wherehas('client', function ($query) {
                $query->wherehas('organization', function ($q) {
                    $q->where('id', getOrganizationId());
                });
            })
            ->whereBetween('created_at', [Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date')), Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))])
            ->where('client_show', 1)
            ->get();
    }

    private function getEstimate()
    {
        return GenerateEstimate::with(['request.client', 'request.saleMan', 'request.estimator'])
            ->wherehas('request', function ($query) {
                employeeAssociatedDateQuery($query);
                $query->wherehas('client', function ($q) {
                    $q->wherehas('organization', function ($q) {
                        $q->where('id', getOrganizationId());
                    });
                });
            })
            ->whereBetween('created_at', [Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date')), Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))])
            ->where('is_complete', 1)
            ->get();
    }

    private function getOperations()
    {
        return GenerateEstimate::with(['request.client', 'manager', 'latestScheduleDate'])->wherehas('request', function ($query) {
            employeeAssociatedDateQuery($query);
            $query->wherehas('client', function ($q) {
                $q->wherehas('organization', function ($q) {
                    $q->where('id', getOrganizationId());
                });
            });
        })->where(['status' => 'won', 'is_schedule' => true])
            ->whereBetween('created_at', [Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date')), Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))])
            ->get();
    }

    private function getSalesStatus()
    {
        return GenerateEstimate::with(['request.client', 'request.saleMan', 'request.estimator'])
            ->wherehas('request', function ($query) {
                employeeAssociatedDateQuery($query);
                $query->wherehas('client', function ($q) {
                    $q->wherehas('organization', function ($q) {
                        $q->where('id', getOrganizationId());
                    });
                });
            })->where('status', request('type'))
            ->whereBetween('created_at', [Carbon::createFromFormat(getOrgDateFormat(null, false), request('start_date')), Carbon::createFromFormat(getOrgDateFormat(null, false), request('end_date'))])
            ->get();
    }
}
