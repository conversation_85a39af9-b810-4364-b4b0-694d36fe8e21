<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Http\Requests\Organization\JobPackRequest;
use App\Models\Adress;
use App\Models\CostSummary;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\GenerateEstimateSchedule;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;

class ScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:manage_schedules');
    }

    public function index()
    {
        $unscheduleEstimates = GenerateEstimate::wherehas('opportunityid', function ($query) {
            employeeAssociatedDateQuery($query)
                ->wherehas('client', function ($q) {
                    $q->wherehas('organization', function ($q) {
                        $q->where('id', getOrganizationId());
                    });
                });
        })->where(['status' => 'won', 'is_schedule' => false])->where('operation_status', '!=', 'Completed')->get();

        return view('organization.schedule.index', get_defined_vars());
    }

    public function getCalendarEstimates()
    {
        $events = [];
        $allEstimates = GenerateEstimate::with('schedules')->wherehas('opportunityid', function ($query) {
            employeeAssociatedDateQuery($query)
                ->wherehas('client', function ($q) {
                    $q->wherehas('organization', function ($q) {
                        $q->where('id', getOrganizationId());
                    });
                });
        })->where(['status' => 'won'])->where('operation_status', '!=', 'Completed')->get();
        foreach ($allEstimates as $generateEstimate) {
            $generateEstimate->schedules->each(function ($item) use (&$events, $generateEstimate) {
                $events[] = [
                    'id' => encodeID($generateEstimate->id),
                    'title' => ' <div class="request_id">Sales Order <span>#'.$generateEstimate?->opportunityid?->sales_order_number.'</span></div>
                    <div class="request_id request_name">'.$generateEstimate?->opportunityid?->opportunity_name.'</div>',
                    'start' => $item->date,
                ];
            });
        }

        return response()->json($events);
    }

    public function getScheduleEstimateDetail($id)
    {
        $generate_estimate = GenerateEstimate::with('opportunityid', 'estimator', 'manager', 'saleMan', 'client')->findorfail(decodeID($id));
        $propertyAddress = Adress::where('estimate_id', $generate_estimate->request_id)->where('type', PROPERTY)->first();
        $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateLabor = EstimateLabor::with('labor')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateHardMaterial = EstimateHardMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimatePlantMaterial = EstimatePlantMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
        $costSummary = CostSummary::where('organization_id', getOrganizationId())->where('operation_id', decodeID($id))->first();
        $org = User::find(getOrganizationId());
        $view = view('organization.schedule.partials.job-pack', compact('propertyAddress', 'org', 'costSummary', 'generate_estimate', 'estimateMaterial', 'estimateLabor', 'estimateHardMaterial', 'estimatePlantMaterial', 'estimateOtherCost', 'estimateSubContractor'))->render();

        return response()->json(['html' => $view], 200);
    }

    public function downloadJobPackPdf($id)
    {
        $generate_estimate = GenerateEstimate::with('opportunityid', 'estimator', 'manager', 'saleMan', 'client')->findorfail(decodeID($id));
        $propertyAddress = Adress::where('estimate_id', $generate_estimate->request_id)->where('type', PROPERTY)->first();
        $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateLabor = EstimateLabor::with('labor')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateHardMaterial = EstimateHardMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimatePlantMaterial = EstimatePlantMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
        $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
        $costSummary = CostSummary::where('organization_id', getOrganizationId())->where('operation_id', decodeID($id))->first();
        $organization = User::find(getOrganizationId())->first();
        $logo = $organization->profile_photo_path ? asset('storage/user_images/'.$organization->profile_photo_path) : asset('admin_assets/images/elmos-logo.png');

        $data = [
            'date' => date('m/d/Y'),
            'logo' => $logo,
            'primary_color' => $organization->primary_color,
            'secondary_color' => $organization->secondary_color,
            'generate_estimate' => $generate_estimate,
            'propertyAddress' => $propertyAddress,
            'estimateMaterial' => $estimateMaterial,
            'estimateLabor' => $estimateLabor,
            'estimateHardMaterial' => $estimateHardMaterial,
            'estimatePlantMaterial' => $estimatePlantMaterial,
            'estimateOtherCost' => $estimateOtherCost,
            'estimateSubContractor' => $estimateSubContractor,
            'costSummary' => $costSummary,
        ];
        //   return view('organization.schedule.job-pack-pdf')->with($data);
        $pdf = Pdf::loadView('organization.schedule.job-pack-pdf', $data);
        // $pdf->setOptions([
        //     'isPhpEnabled' => true, // Enable PHP processing for header and footer
        //     'isHtml5ParserEnabled' => true, // Enable HTML5 parser
        //     // 'fontDir' => storage_path('fonts/'), // Adjust the path to your font directory
        //     // 'fontCache' => storage_path('fonts/cache/'),
        //     // 'isJavascriptEnabled'=> true,
        // ]);

        // return $pdf->stream();
        return response()->make($pdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename=job_pack.pdf',
        ]);
    }

    public function saveJobPackSchedule(JobPackRequest $request, $id)
    {
        DB::beginTransaction();
        try {

            if ($request->has(['equipmentVName', 'equipmentCName', 'equipmentPhone', 'equipmentEmail'])) {
                $this->updateJsonInfo('estimate_materials', $request, ['equipmentVName', 'equipmentCName', 'equipmentPhone', 'equipmentEmail']);
            }

            if ($request->has(['laborVName', 'laborCName', 'laborPhone', 'laborEmail'])) {

                $this->updateJsonInfo('estimate_labors', $request, ['laborVName', 'laborCName', 'laborPhone', 'laborEmail']);
            }
            if ($request->has(['plantMaterialVName', 'plantMaterialCName', 'plantMaterialPhone', 'plantMaterialEmail'])) {

                $this->updateJsonInfo('estimate_plant_materials', $request, ['plantMaterialVName', 'plantMaterialCName', 'plantMaterialPhone', 'plantMaterialEmail']);
            }

            if ($request->has(['hardMaterialVName', 'hardMaterialCName', 'hardMaterialPhone', 'hardMaterialEmail'])) {

                $this->updateJsonInfo('estimate_hard_materials', $request, ['hardMaterialVName', 'hardMaterialCName', 'hardMaterialPhone', 'hardMaterialEmail']);
            }

            if ($request->has(['otherCostVName', 'otherCostCName', 'otherCostPhone', 'otherCostEmail'])) {

                $this->updateJsonInfo('estimate_other_costs', $request, ['otherCostVName', 'otherCostCName', 'otherCostPhone', 'otherCostEmail']);
            }

            if ($request->has(['subcontactorVName', 'subcontactorCName', 'subcontactorPhone', 'subcontactorEmail'])) {

                $this->updateJsonInfo('estimate_sub_contractors', $request, ['subcontactorVName', 'subcontactorCName', 'subcontactorPhone', 'subcontactorEmail']);
            }

            if ($request->has('po_number') && $request->po_number) {
                CostSummary::updateOrCreate(
                    [
                        'organization_id' => getOrganizationId(),
                        'operation_id' => decodeID($id),
                    ],
                    [
                        'po_number' => $request->po_number,
                    ]
                );
            }

            if ($request->has(['scheduled_hours', 'actual_hours'])) {
                $this->updateJsonInfo('estimate_labors', $request, ['scheduled_hours', 'actual_hours'], 'labor_hours_info');
            }

            if ($request->has('special_notes') && ! empty($request->special_notes)) {

                $generate_estimate = GenerateEstimate::findOrFail(decodeID($id));
                $generate_estimate->update([
                    'special_notes' => $request->special_notes,
                    'equipment_tools' => $request->equipment_tools,
                    'rental_information' => $request->rental_information,
                ]);
            }

            DB::commit();

            return response()->json(['success' => 'Data Saved Successfully!']);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json(['error' => 'Data Not Saved Sucessfully!'], 400);
        }
    }

    private function updateJsonInfo($table, $request, $fields, $column = 'vendor_info')
    {
        $vendorData = collect($request->only($fields));
        $tableData = collect($vendorData->first())->map(function ($value, $innerKey) use ($vendorData, $fields, $column) {
            $rowData = ['id' => decodeID($innerKey)];
            foreach ($fields as $field) {
                $rowData[$column][$field] = $vendorData[$field][$innerKey];
            }

            return $rowData;
        })->values();

        $tableData->each(function ($row) use ($table, $column) {
            DB::table($table)->where('id', $row['id'])->update([$column => json_encode($row[$column])]);
        });
    }

    public function listing(Request $request)
    {
        $unscheduleEstimates = GenerateEstimate::wherehas('opportunityid', function ($query) {
            $query->wherehas('client', function ($q) {
                $q->wherehas('organization', function ($q) {
                    $q->where('id', getOrganizationId());
                });
            });
            employeeAssociatedDateQuery($query)
                ->when(request('search'), function ($query) {
                    $query->where(function ($query) {
                        $query->where('job_no', 'like', '%'.request('search').'%')
                            ->orWherehas('client', function ($q) {
                                $q->where(DB::raw('CONCAT(COALESCE(`first_name`, "")," ",COALESCE(`last_name`,""))'), 'like', '%'.request('search').'%');
                            });
                    });
                });
        })->where(['status' => 'won', 'is_schedule' => false])->where('operation_status', '!=', 'Completed')
            ->when(request('timeInterval'), function ($q) {
                $q->whereMonth('created_at', request('timeInterval'));
            })
            ->get();
        $tableView = View::make('organization.schedule.unschedule-list', compact('unscheduleEstimates'))->render();

        return json_encode($tableView);
    }

    public function getSchedule(Request $request)
    {
        $Estimates = GenerateEstimate::with('schedules')->wherehas('opportunityid', function ($query) {
            employeeAssociatedDateQuery($query)->wherehas('client', function ($q) {
                $q->wherehas('organization', function ($q) {
                    $q->where('id', getOrganizationId());
                });
            });
            $query->when(request('search'), function ($query) {
                $query->where(function ($query) {
                    $query->where('job_no', 'like', '%'.request('search').'%')
                        ->orWherehas('client', function ($q) {
                            $q->where(DB::raw('CONCAT(COALESCE(`first_name`, "")," ",COALESCE(`last_name`,""))'), 'like', '%'.request('search').'%');
                        });
                });
            });
        })
            // ->whereHas('schedules',function($query) use ($request){
            //     $query->where(function ($query) use ($request) {
            //         $query->whereDate('date', date(request('scheduleDate')));
            //     });
            // })
            ->where(['status' => 'won'])->where('operation_status', '!=', 'Completed')
            ->when(request('status'), function ($q) {
                if (request('status') == 'Unscheduled') {
                    $q->whereDoesntHave('schedules', function ($query) {
                        $query->where(function ($query) {
                            $query->whereDate('date', date(request('scheduleDate')));
                        });
                    });
                }
                if (request('status') == 'Scheduled') {
                    $q->whereHas('schedules', function ($query) {
                        $query->where(function ($query) {
                            $query->whereDate('date', date(request('scheduleDate')));
                        });
                    });
                }
            })
            ->get();
        $scheduleDate = request('scheduleDate');
        $tableView = View::make('organization.schedule.modal-data', compact('Estimates', 'scheduleDate'))->render();

        return json_encode($tableView);
    }

    public function addSchedule(Request $request)
    {
        // dd($request->all());
        DB::beginTransaction();
        try {
            if (! empty(request('scheduleDate'))) {
                $scheduleDate = Carbon::parse(request('scheduleDate'));
            }

            if ($request->has('scheduleDates')) {
                $request->validate([
                    'scheduleDates' => 'required|array',
                    'scheduleDates.*' => 'date_format:Y-m-d',
                ]);
                $scheduleDates = $request->scheduleDates;
            }
            if (! empty($request->input('draggedId'))) {

                // Decode Encrypted Before Validaiton
                $request->merge(['draggedId' => decodeID($request->draggedId)]);
                $request->validate([
                    'draggedId' => 'required|exists:generate_estimates,id',
                ]);
                $generate_estimate = GenerateEstimate::where('id', $request->draggedId)->first();

                if ($generate_estimate) {

                    $generate_estimate->update([
                        'is_schedule' => true,
                    ]);
                    if (isset($scheduleDates)) {
                        $schedules = collect($scheduleDates)->map(function ($date) {
                            return new GenerateEstimateSchedule(['date' => $date]);
                        });

                        $generate_estimate->schedules()->createMany($schedules->toArray());
                    }
                }
            }
            if (! empty($request->input('scheduleEstimateIdArray'))) {
                // Decode Encrypted Before Validaiton
                $request->merge(['scheduleEstimateIdArray' => collect($request->scheduleEstimateIdArray)->map(function ($encryptedId) {
                    return decodeID($encryptedId);
                })]);
                $request->validate([
                    'scheduleEstimateIdArray.*' => 'required|exists:generate_estimates,id',
                ]);
                $generateEstimates = GenerateEstimate::with(['scheduleOneDate' => function ($query) {
                    $query->where('date', request('scheduleDate'));
                }])->where(['status' => 'won'])->where('operation_status', '!=', 'Completed')->whereIn('id', $request->scheduleEstimateIdArray)->get();

                $generateEstimates->each(function ($generateEstimate) {
                    if (! $generateEstimate->scheduleOneDate) {
                        $generateEstimate->scheduleOneDate()->create([
                            'date' => request('scheduleDate'),
                        ]);
                    }
                });
            }
            if (! empty($request->input('unScheduleEstimateIdArray'))) {
                // Decode Encrypted Before Validaiton
                $request->merge(['unScheduleEstimateIdArray' => collect($request->unScheduleEstimateIdArray)->map(function ($encryptedId) {
                    return decodeID($encryptedId);
                })]);
                $request->validate([
                    'unScheduleEstimateIdArray.*' => 'required|exists:generate_estimates,id',
                ]);
                $generateEstimates = GenerateEstimate::with(['scheduleOneDate' => function ($q) {
                    $q->whereDate('date', request('scheduleDate'));
                }])->where(['status' => 'won'])->where('operation_status', '!=', 'Completed')->whereIn('id', $request->unScheduleEstimateIdArray)->get();
                $generateEstimates->each(function ($generateEstimate) {
                    if ($generateEstimate->scheduleOneDate) {
                        $generateEstimate->scheduleOneDate->delete();
                    }
                });
            }
            $generateEstimates = GenerateEstimate::with('schedules')->where(['status' => 'won'])->where('operation_status', '!=', 'Completed')->get();
            $generateEstimates->each(function ($generateEstimate) {
                $generateEstimate->is_schedule = $generateEstimate->schedules->isNotEmpty();

                if ($generateEstimate->schedules->isNotEmpty()) {
                    $generateEstimate->operation_status = 'In Progress';
                } elseif ($generateEstimate->operation_status != 'Completed') {
                    $generateEstimate->operation_status = 'Pending';
                }

                $generateEstimate->save();
            });
            DB::commit();

            return response()->json(['success' => 'Estimate scheduled successfully!'], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            // dd($e);
            return response()->json(['error' => 'Something went wrong!'], 400);
        }
    }

    public function saveSpecialNotes(Request $request)
    {
        $request->validate([
            'special_notes' => 'nullable|string',
            'generate_id' => 'required',
        ]);
        $id = $request->generate_id;

        // Assuming $generate_estimate is the model instance
        $estimate = GenerateEstimate::find($id); // Replace with dynamic ID logic
        $estimate->special_notes = $request->special_notes;
        $estimate->save();

        return response()->json(['message' => 'Special notes saved successfully!']);
    }
}
