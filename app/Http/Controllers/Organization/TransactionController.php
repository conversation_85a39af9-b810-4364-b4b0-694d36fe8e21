<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:manage_transaction');
    }

    public function index(Request $request)
    {
        $organizationId = getOrganizationId();
        if ($request->ajax()) {
            $query = Transaction::with('invoice:id,subject,organization_id,invoice_number,issue_date,total')
                ->whereHas('invoice', function ($query) use ($organizationId) {
                    $query->where('organization_id', $organizationId);
                })->latest()
                ->select('transaction_number', 'id', 'invoice_id', 'payment_method', 'transaction_date', 'created_at', 'status');

            return DataTables::eloquent($query)
                ->addColumn('invoice_number', function (Transaction $transaction) {
                    if ($transaction->invoice) {
                        return '<a href="'.route(getRouteAlias().'.invoices.view', encodeID($transaction->invoice->id)).'" style="text-decoration:none" class="invoice_number">'.optional($transaction->invoice)?->invoice_number.'</a>';
                    }

                    return '---';
                })
                ->addColumn('invoice_subject', function (Transaction $transaction) {
                    return optional($transaction->invoice)?->subject;
                })
                ->addColumn('invoice_issue_date', function (Transaction $transaction) {
                    return $transaction->invoice?->issue_date;
                })
                ->addColumn('transaction_date', function (Transaction $transaction) {
                    return $transaction->created_at;
                })
                ->editColumn('payment_method', function (Transaction $transaction) {
                    return custom_title_case($transaction->payment_method);
                })
                ->addColumn('amount', function (Transaction $transaction) {
                    return '$'.optional($transaction->invoice)?->total;
                })
                ->addColumn('status', function (Transaction $transaction) {
                    return '<span class="approved status">'.ucfirst($transaction->status).'</span>';
                })
                ->filterColumn('invoice_subject', function ($query, $keyword) {
                    $query->whereHas('invoice', function ($q) use ($keyword) {
                        $q->where('subject', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->filterColumn('invoice_number', function ($query, $keyword) {
                    $query->whereHas('invoice', function ($q) use ($keyword) {
                        $q->where('invoice_number', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->filterColumn('amount', function ($query, $keyword) {
                    $query->whereHas('invoice', function ($q) use ($keyword) {
                        $q->where('total', 'LIKE', '%'.$keyword.'%');
                    });
                })
                ->rawColumns(['status', 'invoice_number'])
                ->only(['invoice_number', 'invoice_subject', 'invoice_issue_date', 'transaction_date', 'amount', 'status', 'payment_method'])
                ->toJson();
        }

        return view('organization.transactions.index');
    }
}
