<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ErrorPagesController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $uri = request()->getRequestUri();
        if ($uri && str_starts_with($uri, '/client') || str_starts_with($uri, '/organization') || str_starts_with($uri, '/dashboard') || str_starts_with($uri, '/employee')) {

            if (str_starts_with($uri, '/client')) {
                $guardName = 'client';
            } else {
                $guardName = 'organization';
            }

            return response()->view('custom-errors.'.$guardName.'.error', [
                'code' => 404,
                'heading' => 'Page not found',
                'message' => 'Sorry the page you are looking for does not exist, if you think something is broken, report a problem',
            ], 404);
        }
    }
}
