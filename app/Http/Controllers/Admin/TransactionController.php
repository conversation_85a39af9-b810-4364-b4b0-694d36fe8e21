<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
// use Yajra\DataTables\Facades\DataTables;
use Yajra\DataTables\DataTables;

class TransactionController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $model = User::with('subscription.plan')->latest()->wherehas('subscription.plan');

            // return DataTables::eloquent($model)
            return DataTables::of($model->get())
                ->addColumn('status', function (User $user) {

                    return ' <div class="status success">Paid</div>';
                })
                ->addColumn('subscription.plan.price', function (User $user) {

                    return '$'.$user->subscription?->plan?->price.'.00';
                })
                ->rawColumns(['status'])
                ->toJson();
        }

        return view('admin.transaction.index');
    }
}
