<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
// use Yajra\DataTables\Facades\DataTables;
use Ya<PERSON>ra\DataTables\DataTables;

class CompanyController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $model = User::with('subscription.plan')->where('parent_id', 0)->where('type', 1)->latest();
            $model = $model->when(request('planName') && ! empty(request('planName')), function ($query) {
                if (request('planName') == 'Basic') {
                    $query->WhereDoesntHave('subscription');
                } else {
                    $query->whereHas('subscription', function ($q) {
                        $q->whereHas('plan', function ($q) {
                            $q->where('name', 'like', '%'.request('planName').'%');
                        });
                    });
                }
            })->when(request('status'), function ($query) {
                $query->when(request('status') && request('status') != null, function ($query) {
                    $status = request('status');
                    if ($status === 'Active') {
                        $query->whereDate('expire_untill', '>=', now());
                    } elseif ($status === 'Expired') {
                        $query->whereDate('expire_untill', '<', now());
                    }
                });
            });

            // return DataTables::eloquent($model)
            return DataTables::of($model->get())
                ->addColumn('status', function (User $user) {
                    // if($user->subscription){
                    //     return Carbon::parse($user->subscription->getAttributes()['ends_at'])->gt(now())  ? ' <div class="status success">Active</div>' : ' <div class="status danger">Expired</div>';
                    // }
                    return Carbon::parse($user->expire_untill)->gt(now()) ? ' <div class="status success">Active</div>' : ' <div class="status danger">Expired</div>';
                })
                ->editColumn('subscription.plan.name', function (User $user) {
                    if ($user->subscription) {
                        return optional($user->subscription->plan)?->name;
                    }

                    return config('custom.plans.free.name');
                })
                ->addColumn('actions', function (User $user) {
                    // if($user->subscription){
                    //     return !Carbon::parse($user->subscription->getAttributes()['ends_at'])->gt(now())  ? ' <button  data-id="'.$user->id.'" class="btn btn-warning " data-end-at="'.$user->subscription->getAttributes()['ends_at'].'" onclick="extendTrialClick(event)">Extend Trial</button>' : '';
                    // }
                    return ! Carbon::parse($user->expire_untill)->gt(now()) ? ' <button  data-id="'.$user->id.'" class="btn btn-warning " data-end-at="'.$user->expire_untill.'" onclick="extendTrialClick(event)">Extend Trial</button>' : 'Trial Activated';
                })
                ->editColumn('subscription.ends_at', function (User $user) {
                    return Carbon::parse($user->expire_untill)->format('d/m/Y');
                    // return $user->subscription->ends_at->format('d/m/Y');
                })->rawColumns(['status', 'actions', 'subscription.plan.name'])
                ->toJson();
        }

        return view('admin.company.index');
    }

    public function extendTrial(Request $request, $id)
    {

        $request->validate([
            'date' => 'required|date|after_or_equal:today',
        ]);
        DB::beginTransaction();
        try {

            $company = User::with('subscription')->findorfail($id);
            $company->expire_untill = $request->date;
            $company->save();

            if ($company->subscription) {
                $company->subscription->update([
                    'ends_at' => $request->date,
                ]);
            }

            DB::commit();

            return redirect()->back()->with('success', 'User trial extended successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            info($e->getMessage());

            return redirect()->back();
        }
    }

    public function listing(Request $request)
    {
        if ($request->ajax()) {
            $model = User::with('subscription.plan')->latest()->where('parent_id', 0)->where('type', 1);
            $model = $model->when(request('status'), function ($query) {
                $query->whereHas('subscription', function ($q) {
                    if (request('status') == 'today') {
                        $q->whereDate('created_at', Carbon::today());
                    } elseif (request('status') == 'last_week') {
                        $q->whereBetween('created_at', [Carbon::now()->startOfWeek()->subWeek(), Carbon::now()->endOfWeek()->subWeek()]);
                    }
                });
            });

            // return DataTables::eloquent($model)
            return DataTables::of($model->get())
                ->addColumn('company_name', function (User $user) {
                    return $user->first_name.' '.$user->last_name;
                })
                ->addColumn('address', function (User $user) {
                    return $user->address ?? 'N/A';
                })
                ->addColumn('email', function (User $user) {
                    return $user->email;
                })
                ->addColumn('status', function (User $user) {
                    return Carbon::parse($user->expire_untill)->gt(now()) ? ' <div class="status success">Active</div>' : ' <div class="status danger">Expired</div>';
                })
                ->addColumn('start_at', function (User $user) {
                    if ($user->subscription) {
                        return Carbon::parse($user->subscription->start_at ?? $user->created_at)->format('d/m/Y');
                    }

                    return Carbon::parse($user->created_at)->format('d/m/Y');
                })->rawColumns(['status'])
                ->addColumn('ends_at', function (User $user) {
                    return Carbon::parse($user->expire_untill)->format('d/m/Y');
                })
                ->rawColumns(['status', 'company_name', 'address', 'email', 'ends_at', 'start_at'])
                ->toJson();
        }

        return view('admin.company.index');
    }
}
