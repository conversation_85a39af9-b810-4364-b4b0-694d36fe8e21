<?php

namespace App\Http\Controllers;

use App\Models\CustomerIssue;
use App\Models\GenerateEstimate;
use App\Models\Opportunity;
use App\Models\PropertyInformation;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::guard('web')->user();

        // Admin Dashboard
        if ($user->isAdmin()) {
            // Cache frequently accessed data
            $data = Cache::remember('admin_dashboard_data', 60 * 30, function () {
                $lastMonth = Carbon::now()->subMonth();

                return [
                    'totalCompanies' => User::where('parent_id', 0)->where('type', 1)->count(),
                    'activeCompanies' => User::wherehas('subscription', function ($query) {
                        $query->whereDate('ends_at', '>', now());
                    })->count(),
                    'lastMonthActives' => User::wherehas('subscription', function ($query) use ($lastMonth) {
                        $query
                            ->whereDate('ends_at', '>=', $lastMonth->startOfMonth())
                            ->whereDate('ends_at', '<=', $lastMonth->endOfMonth());
                    })->count(),
                ];
            });

            // Calculate earnings
            $users = User::with('subscriptions.plan')->has('subscriptions.plan')->get();
            $earnings = 0;

            if (! $users->isEmpty()) {
                $earnings = $users->flatMap(function ($user) {
                    return $user->subscriptions->map(function ($subscription) {
                        return optional($subscription->plan)->price ?: 0;
                    });
                })->sum();
            }

            $data['earnings'] = $earnings;

            return view('admin.admin-dashboard', $data);
        }
        // User Dashboard (Organization or Employee)
        elseif (auth('web')->check()) {
            // Handle Employee Authentication
            if ($user->isEmployee()) {
                if ($user->status == 'Inactive') {
                    auth()->guard('web')->logout();

                    return redirect()->route('login')->with('error', 'Your account is inactive. Please contact your organization!');
                }

                // Check Organization Subscription
                $organization = User::findorfail(getOrganizationId());

                if ($organization->payment_mode == 'trial' && now()->setTime(0, 0, 0) > $organization?->expire_untill?->setTime(0, 0, 0)) {
                    auth()->guard('web')->logout();

                    return redirect()->route('login')->with('error', 'Your organization is inactive. Please contact your organization!');
                } elseif (($organization->payment_mode == 'subscription') && (now()->setTime(0, 0, 0) > $organization?->subscription?->ends_at?->setTime(0, 0, 0))) {
                    return redirect()->route('login')->with('error', 'Your organization is inactive. Please contact your organization!');
                }
            }

            // Handle Organization Authentication
            if ($user->isOrganization()) {
                if ($user->email_verified_at == null) {
                    return redirect()->route('register.step1')->with('error', 'Please verify your account to continue.');
                } elseif (! $user->is_active || $user->expire_untill == null) {
                    return redirect()->route('plans')->with('message', 'Welcome to the '.config('app.name').'. Please choose a plan to continue!');
                }

                // Check Subscription Status
                if ($user->payment_mode == 'trial' && now()->setTime(0, 0, 0) > Carbon::parse($user?->expire_untill)->setTime(0, 0, 0)) {
                    return redirect()->route(getRouteAlias().'.account.settings');
                } elseif (($user->payment_mode == 'subscription') && (now()->setTime(0, 0, 0) > Carbon::parse($user?->subscription?->ends_at)?->setTime(0, 0, 0))) {
                    return redirect()->route(getRouteAlias().'.account.settings');
                }
            }

            // Track user activity
            saveUserOnlineStatus();
            saveUserLoginTime();

            $organizationId = getOrganizationId();
            $isEmployee = $user->isEmployee() && ! $user->hasRole('manager');

            // Get dashboard data with eager loading to reduce queries
            $dashboardData = $this->getDashboardData($organizationId, $isEmployee);

            return view('organization.company-dashboard', $dashboardData);
        }
    }

    /**
     * Get dashboard data for organization or employee
     *
     * @return array
     */
    private function getDashboardData(int $organizationId, bool $isEmployee)
    {
        // Get tasks
        $tasks = Task::where('organization_id', $organizationId)
            ->where('is_completed', 0)
            ->latest()
            ->get();

        // Get opportunities and clients
        $totalOpportunities = Opportunity::where('organization_id', $organizationId)
            ->select('id', 'status')
            ->where(function ($query) {
                $query
                    ->whereHas('generateEstimate', function ($q) {
                        $q->whereNotIn('client_status', ['reject', 'approve']);
                    })
                    ->orWhereDoesntHave('generateEstimate');
            })
            ->whereNotIn('status', [7, 3, 5]) // <- apply this to all matched opportunities, not just a subset
            ->with('generateEstimate')
            ->get();

        $totalClientsQuery = DB::table('contacts')
            ->where('organization_id', $organizationId);

        if ($isEmployee) {
            $totalClientsQuery->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('opportunities')
                    ->whereRaw('contacts.id = opportunities.contact_id')
                    ->where(function ($subQuery) {
                        employeeAssociatedDateQuery($subQuery);
                    });
            });
        }

        $totalClients = $totalClientsQuery->get();

        // Get estimates data in a single query
        $estimatesData = DB::table('opportunities')
            ->where('opportunities.organization_id', $organizationId)
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })
            ->select(
                DB::raw('SUM(CASE WHEN generate_estimates.is_complete = true AND generate_estimates.status = "won" THEN 1 ELSE 0 END) AS estimates_won_count'),
                DB::raw('SUM(CASE WHEN generate_estimates.client_status = "request_change" AND generate_estimates.status = "proposed" THEN 1 ELSE 0 END) AS change_request_count'),
            )
            ->leftJoin('generate_estimates', 'opportunities.id', '=', 'generate_estimates.opportunity_id')
            ->join('accounts', 'opportunities.account_id', '=', 'accounts.id')
            ->first();

        $requestsCount = $estimatesData->requests_count ?? 0;
        $changeRequestCount = $estimatesData->change_request_count ?? 0;
        $estimatesWonCount = $estimatesData->estimates_won_count ?? 0;

        // Get total won price
        $totalWonPrice = GenerateEstimate::withWherehas('opportunityid', function ($query) use ($organizationId) {
            employeeAssociatedDateQuery($query)
                ->where('organization_id', $organizationId);
        })
            ->where(['is_complete' => true, 'status' => 'won'])
            ->selectRaw(calculateGrandTotalRawQuery('total_won_price'))
            ->value('total_won_price');

        // Get operations data
        $operations = GenerateEstimate::withWherehas('opportunityid', function ($query) use ($organizationId) {
            employeeAssociatedDateQuery($query)
                ->where('organization_id', $organizationId);
        })->where(['is_complete' => true, 'is_schedule' => true, 'status' => 'won']);

        $operationCompletedPrice = $operations->clone()->where('operation_status', 'Completed')
            ->selectRaw(calculateGrandTotalRawQuery('total_won_price'))
            ->value('total_won_price');

        $operationPendingPrice = $operations->clone()->where('operation_status', 'Pending')
            ->selectRaw(calculateGrandTotalRawQuery('total_won_price'))
            ->value('total_won_price');

        // Get total estimate and calculate won percentage
        $totalEstimate = GenerateEstimate::withWherehas('opportunityid', function ($query) use ($organizationId) {
            employeeAssociatedDateQuery($query)
                ->where('organization_id', $organizationId);
        })->where(['is_complete' => true])->count();

        $wonPercentage = $this->calculateWonPercentage($totalEstimate, $estimatesWonCount);
        $customerIssuesCount = getCustomerIssuesCount();

        // Get customer issues with a single query pattern for each status
        $issueStatuses = [
            'openissues' => 2,
            'pendingissues' => 1,
            'completedissues' => 3,
        ];

        $customerIssues = [];

        foreach ($issueStatuses as $key => $status) {
            $customerIssues[$key] = $this->getCustomerIssuesByStatus($organizationId, $status);
        }

        // Get property health data
        $healthTypes = ['green', 'red', 'yellow'];
        $healthData = [];
        $healthDataDetails = [];

        // Get health totals in a single query
        $generateEstimatesSub = DB::table('generate_estimates')
            ->select('opportunity_id', DB::raw('SUM(total_price) as estimate_total'))
            ->groupBy('opportunity_id');

        $proposalsSub = DB::table('proposals')
            ->select('opportunity_id', DB::raw('SUM(contract_amount) as proposal_total'))
            ->groupBy('opportunity_id');

        $healthTotals = Opportunity::join('property_information', 'opportunities.property_id', '=', 'property_information.id')
            ->leftJoinSub($generateEstimatesSub, 'ge', function($join) {
                $join->on('opportunities.id', '=', 'ge.opportunity_id');
            })
            ->leftJoinSub($proposalsSub, 'pr', function($join) {
                $join->on('opportunities.id', '=', 'pr.opportunity_id');
            })
            ->where('opportunities.organization_id', $organizationId)
            ->whereIn('property_information.property_health', $healthTypes)
            ->select(
                'property_information.property_health',
                DB::raw('SUM(COALESCE(ge.estimate_total, 0) + COALESCE(pr.proposal_total, 0)) as total')
            )
            ->groupBy('property_information.property_health')
            ->pluck('total', 'property_health')
            ->toArray();

        foreach ($healthTypes as $type) {
            $healthData[$type.'health'] = $healthTotals[$type] ?? 0;
            $healthDataDetails[$type.'healthdata'] = $this->getPropertyHealthData($organizationId, $type);
        }

        // Get branding and users
        $brandings = User::findorfail($organizationId);
        $totalUsers = User::where('parent_id', $organizationId)->get();

        // Combine all data
        return array_merge(
            [
                'tasks' => $tasks,
                'totalOpportunities' => $totalOpportunities,
                'totalClients' => $totalClients,
                'requestsCount' => $requestsCount,
                'changeRequestCount' => $changeRequestCount,
                'estimatesWonCount' => $estimatesWonCount,
                'totalWonPrice' => $totalWonPrice,
                'operationCompletedPrice' => $operationCompletedPrice,
                'operationPendingPrice' => $operationPendingPrice,
                'totalEstimate' => $totalEstimate,
                'wonPercentage' => $wonPercentage,
                'customerIssuesCount' => $customerIssuesCount,
                'brandings' => $brandings,
                'totalUsers' => $totalUsers,
            ],
            $customerIssues,
            $healthData,
            $healthDataDetails
        );
    }

    /**
     * Get customer issues by status
     *
     * @return Collection
     */
    private function getCustomerIssuesByStatus(int $organizationId, int $status)
    {
        return CustomerIssue::join('users as creators', 'customer_issues.created_by', '=', 'creators.id')
            ->join('opportunities', 'customer_issues.assigned_job', '=', 'opportunities.id')
            ->where('customer_issues.organization_id', $organizationId)
            ->where('customer_issues.status', $status)
            ->select(
                'customer_issues.id',
                'customer_issues.property_name',
                'customer_issues.category',
                'customer_issues.subject',
                'customer_issues.status',
                'opportunities.property_id as prop_id',
                'opportunities.account_id as acc_id',
                DB::raw("CONCAT(creators.first_name, ' ', creators.last_name) as creator_full_name"),
                'customer_issues.created_at',
            )
            ->latest()
            ->get();
    }

    /**
     * Get property health data
     *
     * @return Collection
     */
    private function getPropertyHealthData(int $organizationId, string $healthType)
    {
        return PropertyInformation::join('accounts', 'property_information.company_id', '=', 'accounts.id')
            ->join('users', 'accounts.account_owner', '=', 'users.id')
            ->where('property_information.organization_id', $organizationId)
            ->where('property_information.property_health', $healthType)
            ->select([
                'property_information.id',
                'property_information.property_health',
                'property_information.company_notes',
                'property_information.company_id',
                'accounts.company_name as company_name',
                'property_information.name as property_name',
                'property_information.city',
                'property_information.state',
                'property_information.address1',
                'property_information.zip',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as account_owner_name"),
            ])
            ->get();
    }

    private function calculateWonPercentage($total, $toatlWon)
    {
        if ($total > 0) {
            return (100 * $toatlWon) / $total;
        }

        return 0;
    }

    public function privacyPolicyView()
    {
        return view('privacy-policy');
    }

    public function termConsitions()
    {
        return view('terms-conditions');
    }
}
