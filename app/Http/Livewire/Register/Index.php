<?php

namespace App\Http\Livewire\Register;

use App\Models\Plan;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Livewire\Component;

class Index extends Component
{
    public $plans;

    public $currentStep = 1;

    public $planId;

    public $selectedPlan;

    public $organizationId;

    public function mount()
    {
        $user = auth()->user();

        if (auth()->user()->email_verified_at == null) {
            return redirect()->route('register.step1')->with('error', 'Please verify your account to continue.');
        }
        $this->organizationId = $user->id;
        if ($user->is_active) {
            return redirect()->route('login');
        }
        $this->plans = Plan::all();
    }

    public function render()
    {
        return view('livewire.register.index')->layout('layouts.guest');
    }

    public function updating($name, $value)
    {
        //
    }

    public function goToStepTwo($planId)
    {
        $this->planId = $planId;
        $this->selectedPlan = Plan::where('id', $planId)->first();
        if ($this->selectedPlan->name == config('custom.plans.free.name')) {
            return $this->freeTrial();
        } else {
            $this->currentStep = 2;
        }
    }

    private function freeTrial()
    {
        $user = optional(User::find($this->organizationId));
        if ($user) {
            $user->update([
                'is_active' => true,
                'payment_mode' => 'trial',
                'expire_untill' => now()->addDays(config('custom.plans.free.days')),
            ]);
            insertLabor($user->id);
            insertMargin($user->id);

            return redirect()->route('registered.success');
        } else {
            $this->currentStep = 1;
        }
    }

    public function redirectToStripe()
    {
        $baseUrl = URL::to('');
        $stripe = new \Stripe\StripeClient(
            config('services.stripe.secret')
        );
        DB::beginTransaction();
        try {
            $user = optional(User::find($this->organizationId));
            if ($user) {
                if (empty($user->stripe_customer_id)) {
                    $customer = $stripe->customers->create([
                        'description' => $user->company_name,
                        'email' => $user->email,
                    ]);
                    $user->update(['stripe_customer_id' => $customer->id]);
                    $user = $user->refresh();
                }

                if (empty($user->setup_invoice_id)) {
                    $invoice = $stripe->invoiceItems->create([
                        'customer' => $user->stripe_customer_id,
                        'amount' => 39900,
                        'currency' => 'usd',
                        'description' => 'Setup Price (One Time)',
                    ]);
                    $user->update(['setup_invoice_id' => $invoice->id]);
                }

                $response = $stripe->checkout->sessions->create([
                    'success_url' => $baseUrl.'/payment-success?session_id={CHECKOUT_SESSION_ID}&plan_id='.$this->selectedPlan->id.'&organization_id='.$this->organizationId,
                    'cancel_url' => $baseUrl.'/plans',
                    'payment_method_types' => ['card'],
                    'line_items' => [
                        [
                            'price' => $this->selectedPlan->stripe_price,
                            'quantity' => 1,
                        ],
                    ],
                    'mode' => 'subscription',
                    'customer' => $user->stripe_customer_id,
                ]);
                DB::commit();

                return Redirect::to($response['url']);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            $this->addError('error', 'Something Went Wrong!');

            return redirect()->back();
        }

        return redirect()->back();
    }
}
