<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class CheckValidOrganizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $org = User::findOrFail(decodeID($request->route('organization_id')));
        if ($org) {
            $endsAt = now()->setTime(0, 0, 0);
            if ($org->payment_mode == 'trial') {
                $endsAt = Carbon::parse($org?->expire_untill)?->setTime(0, 0, 0);
            } elseif ($org->payment_mode == 'subscription') {
                $endsAt = Carbon::parse($org?->subscription?->ends_at)?->setTime(0, 0, 0);
            }
            // $date = Carbon::createFromFormat('d/m/Y', $endsAt)->format('Y-m-d');
            if ($org && now()->setTime(0, 0, 0)->gt($endsAt)) {
                auth('client')->logout();

                return redirect()->route('client.login', [$request->route('organization_id')]);
            }
        }

        return $next($request);
    }
}
