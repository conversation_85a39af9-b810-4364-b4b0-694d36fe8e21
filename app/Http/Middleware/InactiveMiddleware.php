<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InactiveMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::user() && Auth::guard('web')->user()->isEmployee()) {
            if (auth('web')->user()->status == 'Inactive') {
                auth()->guard('web')->logout();

                return redirect()->route('login')->with('error', 'Your account is inactive.Please contact your organization!');
            }
        }

        return $next($request);
    }
}
