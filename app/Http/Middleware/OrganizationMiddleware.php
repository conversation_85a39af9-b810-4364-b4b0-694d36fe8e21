<?php

namespace App\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrganizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->user() && ! Auth::user()->isOrganization()) {
            return redirect()->back();
        }

        $endsAt = now()->setTime(0, 0, 0);
        if (auth('web')->user()->payment_mode == 'trial') {
            $expireUntil = auth()->user()?->expire_untill;
            $endsAt = Carbon::parse($expireUntil)->setTime(0, 0, 0);
        } elseif (auth('web')->user()->payment_mode == 'subscription') {
            $subEndsAt = auth('web')->user()?->subscription?->ends_at;
            $endsAt = Carbon::parse($subEndsAt)->setTime(0, 0, 0);
        }
        // $date = Carbon::createFromFormat('d/m/Y', $endsAt)->format('Y-m-d');
        if (auth('web')->user() && now()->setTime(0, 0, 0)->gt($endsAt)) {
            return redirect()->route(getRouteAlias().'.account.settings');
        }

        return $next($request);
    }
}
