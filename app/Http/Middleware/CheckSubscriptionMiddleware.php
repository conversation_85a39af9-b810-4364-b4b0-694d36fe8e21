<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;

class checkSubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth('web')->user();
        $org = User::find(getOrganizationId());
        if ($user->id == $org->id) {
            if ($user && ($org->payment_mode != 'trial') && empty($org->subscription)) {
                return redirect()->route('plans');
            }
        } else {

        }

        return $next($request);
    }
}
