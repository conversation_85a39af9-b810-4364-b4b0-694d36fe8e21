<?php

namespace App\Imports;

use App\Models\SnowSetup;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class SnowImport implements ToModel, WithHeadingRow, WithValidation
{
    public function model(array $row)
    {
        return new SnowSetup([
            'category' => $row['category'] ?? null,
            'lineitem' => $row['lineitem'] ?? null,
            'unit_cost' => $row['unit_cost'] ?? null,
            'uom' => $row['uom'] ?? null,
        ]);
    }

    public function rules(): array
    {
        return [
            '*.category' => 'required|in:Labor,Material,Equipments,Stand By',
            '*.lineitem' => 'required|string',
            '*.unit_cost' => 'required|numeric',
            '*.uom' => 'required|string',
        ];
    }
}
