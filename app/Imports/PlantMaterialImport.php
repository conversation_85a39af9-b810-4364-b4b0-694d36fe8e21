<?php

namespace App\Imports;

use App\Models\PlantMaterial;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class PlantMaterialImport implements ToModel, WithStartRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    // protected $division;

    // // Constructor mein division value lein
    // public function __construct($division)
    // {
    //     $this->division = $division;
    // }
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {
        return new PlantMaterial([
            'organization_id' => getOrganizationId(),
            'name' => $row[0] ?? '',
            'type' => $row[1] ?? '',
            'size' => $row[2] ?? '',
            'cost' => $row[3] ?? '',
            'install' => $row[4] ?? '',
            // 'division_id' => $this->division
        ]);
    }

    public function rules(): array
    {
        return [
            '0' => 'required',
            '1' => 'required',
            '3' => 'required|numeric|gt:0',
            // '5' => 'required|numeric|gt:0',
            '4' => 'required|numeric|min:0',
            '2' => 'required',
            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'Name Field is empty in file',
            '1.required' => 'Type Field is empty in file',
            '2.required' => 'Size Field is empty in file',
            '3.required' => 'Cost Field is empty in file',
            '3.numeric' => 'Cost Field should contain positive numeric values',
            '3.gt' => 'Cost Field should contain positive numeric values',
            '4.required' => 'Install Field is empty in file',
            '4.numeric' => 'Install Field should contain positive numeric values',
            '4.min' => 'Install Field should contain positive numeric values',
            '5.required' => 'Gross Margin Field is empty in file',
            '5.numeric' => 'Gross Margin Field should contain positive numeric values',
            '5.gt' => 'Gross Margin Field should contain positive numeric values',
        ];
    }
}
