<?php

namespace App\Imports;

use App\Models\ServiceLineWorkType;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class WorkTypeServiceLineMaterialImport implements ToModel, WithStartRow, WithValidation
{
    public function __construct(public $serviceLineId) {}

    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {
        return new ServiceLineWorkType([
            'service_line_id' => $this->serviceLineId,
            'name' => $row[0] ?? '',
        ]);
    }

    public function rules(): array
    {
        return [
            '0' => 'required',
            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'Name Field is empty in file',
            '0.alpha_num' => 'The name must only contain letters or numbers. or combination',
        ];
    }
}
