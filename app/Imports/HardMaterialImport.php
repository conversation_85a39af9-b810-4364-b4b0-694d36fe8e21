<?php

namespace App\Imports;

use App\Models\HardMaterial;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class HardMaterialImport implements ToModel, WithStartRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    // protected $division;

    // Constructor mein division value lein
    // public function __construct($division)
    // {
    //     $this->division = $division;
    // }
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {
        return new HardMaterial([
            'organization_id' => getOrganizationId(),
            'name' => $row[0] ?? '',
            'uom' => $row[1] ?? '',
            'cost' => $row[2] ?? '',
            'labor' => $row[3] ?? '',
            // 'division_id' => $this->division
        ]);
    }

    public function rules(): array
    {
        return [
            '0' => 'required',
            '1' => 'required',
            '2' => 'required|numeric|gt:0',
            '3' => 'required|numeric|min:0',
            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'Name Field is empty in file',
            '1.required' => 'UoM Field is empty in file',
            '2.required' => 'Cost Field is empty in file',
            '2.numeric' => 'Cost Field should contain positive numeric values',
            '2.gt' => 'Cost Field should contain positive numeric values',
            '3.required' => 'Labor Field is empty in file',
            '3.numeric' => 'Labor Field should contain positive numeric values',
            '3.min' => 'Labor Field should contain positive numeric values or 0',
        ];
    }
}
