<?php

namespace App\Imports;

use App\Models\ServiceLineMaterial;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class ServiceLineMaterialImport implements ToModel, WithStartRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {
        return new ServiceLineMaterial([
            'organization_id' => getOrganizationId(),
            'name' => $row[0] ?? '',
        ]);
    }

    public function rules(): array
    {
        return [
            '0' => 'required',
            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'Name Field is empty in file',
            '0.alpha_num' => 'The name must only contain letters or numbers. or combination',
        ];
    }
}
