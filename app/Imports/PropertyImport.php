<?php

namespace App\Imports;

use App\Models\Account;
use App\Models\PropertyInformation;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class PropertyImport implements ToModel, WithStartRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2; // Assuming your data starts from the second row
    }

    public function model(array $row)
    {
        $company = $this->findCompanyName($row[5]);
        $this->rowCount++;

        return new PropertyInformation([
            'organization_id' => getOrganizationId(),
            'company_id' => $company ? $company->id : null,
            'name' => $row[0] ?? '',
            'address1' => $row[1] ?? '',
            'city' => $row[2] ?? '',
            'state' => $row[3] ?? '',
            'zip' => $row[4] ?? '',
            'account' => $row[5] ?? '',

        ]);
    }

    protected function findCompanyName($identifier)
    {

        if (empty($identifier)) {
            throw new \InvalidArgumentException('Identifier (name) cannot be empty.');
        }

        $owner = Account::where('company_name', $identifier)
            ->where('parent_id', getOrganizationId())
            ->first();

        if (! $owner) {
            throw new ModelNotFoundException("Account  not found with name: $identifier");
        }

        return $owner;
    }

    public function rules(): array
    {
        return [
            '0' => 'required',
            '1' => 'required',
            '4' => 'required',
            '5' => 'required',
            '6' => 'required',

            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'Property Name Field is empty in file',
            '1.required' => 'Address 1 Field is empty in file',
            '4.required' => 'City field is empty in file',
            '5.required' => 'State field is empty in file',
            '6.required' => 'Zip field is empty in file',
        ];
    }

    private $rowCount = 0;

    private $failures = [];

    public function getRowCount(): int
    {
        return $this->rowCount;
    }

    public function getFailures(): array
    {
        return $this->failures;
    }

    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->failures[] = $failure;
        }
    }
}
