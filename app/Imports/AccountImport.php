<?php

namespace App\Imports;

use App\Models\Account;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Validators\Failure;

class AccountImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {

        $accountOwner = $this->findAccountOwner($row['email']);
        $this->rowCount++;

        return new Account([
            'parent_id'        => getOrganizationId(),
            'company_name'     => $row['company_name'] ?? '',
            'website'          => $row['website'] ?? '',
            'email'            => $row['email'] ?? '',
            'mobile_no'        => $row['mobile_no'] ?? '',
            'city'             => $row['city'] ?? '',
            'state'            => $row['state'] ?? '',
            'zip'              => $row['zip'] ?? '',
            'address'          => $row['address'] ?? '',
            'billing_address'  => $row['billing_address'] ?? '',
            'billing_city'     => $row['billing_city'] ?? '',
            'billing_state'    => $row['billing_state'] ?? '',
            'billing_zip'      => $row['billing_zip'] ?? '',
            'account_owner'    => $accountOwner ? $accountOwner->id : null,
        ]);
    }

    protected function findAccountOwner($identifier)
    {

        if (empty($identifier)) {
            throw new \InvalidArgumentException('Identifier (email) cannot be empty.');
        }

        $owner = User::where('email', $identifier)
            // ->where('parent_id', getOrganizationId())
            ->first();

        if (! $owner) {
            throw new ModelNotFoundException("User not found with email: $identifier");
        }

        return $owner;
    }

    public function rules(): array
    {
        return [
            'company_name'     => 'required',
            'email'            => 'required|email',
            'mobile_no'        => 'required',
            'city'             => 'required',
            'state'            => 'required',
            'zip'              => 'required',
            'address'          => 'required',
            
        ];
    }

    public function customValidationMessages()
    {
        return [
            'company_name.required'    => 'Company Name field is required.',
            'email.required'           => 'Email field is required.',
            'email.email'              => 'Email must be a valid email address.',
            'mobile_no.required'       => 'Mobile number is required.',
            'city.required'            => 'City field is required.',
            'state.required'           => 'State field is required.',
            'zip.required'             => 'ZIP field is required.',
            'address.required'         => 'Address field is required.',
            'billing_address.required' => 'Billing Address field is required.',
            'billing_city.required'    => 'Billing City field is required.',
            'billing_state.required'   => 'Billing State field is required.',
            'billing_zip.required'     => 'Billing ZIP field is required.',
            'account_owner.required'   => 'Account Owner field is required.',
        ];
    }

    private $rowCount = 0;

    private $failures = [];

    public function getRowCount(): int
    {
        return $this->rowCount;
    }

    public function getFailures(): array
    {
        return $this->failures;
    }

    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->failures[] = $failure;
        }
    }
}
