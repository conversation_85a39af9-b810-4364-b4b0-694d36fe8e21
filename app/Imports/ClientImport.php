<?php

namespace App\Imports;

use App\Models\Adress;
use App\Models\Client;
use App\Rules\FilterEmail;
use App\Rules\UniqueEmailInOrganization;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class ClientImport implements ToModel, WithStartRow, WithValidation
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2;
    }

    public function model(array $row)
    {
        $client = Client::create([
            'organization_id' => getOrganizationId(),
            'first_name' => $row[0] ?? '',
            'last_name' => $row[1] ?? '',
            'company_name' => $row[2] ?? null,
            'title' => $row[3] ?? '',
            'email' => $row[4] ?? '',
            'office_no' => $row[5] ?? '',
            'mobile_no' => $row[6] ?? '',
            'alternate_no' => $row[7] ?? '',
        ]);

        $Propertyaddress = new Adress([
            'client_id' => $client->id,
            'address1' => $row[8] ?? '',
            'city' => $row[9] ?? '',
            'state' => $row[10] ?? '',
            'zip' => $row[11] ?? '',
        ]);

        $Billingaddress = new Adress([
            'client_id' => $client->id,
            'address1' => $row[12] ?? '',
            'city' => $row[13] ?? '',
            'state' => $row[14] ?? '',
            'zip' => $row[15] ?? '',
            'type' => 1,
        ]);

        return ['client' => $client, 'Propertyaddress' => $Propertyaddress, 'Billingaddress' => $Billingaddress];
    }

    public function rules(): array
    {
        $organizationId = getOrganizationId();

        return [
            '0' => 'required|max:100',
            '1' => 'required|max:100',
            '2' => 'max:150|unique:clients,company_name',
            // '3' => 'required',
            '4' => [
                'required',
                'email',
                'max:255',
                new UniqueEmailInOrganization($organizationId, 0),
                new FilterEmail,
            ],
            '5' => 'required',
            '6' => 'required',
            // '7' => 'required',
            '8' => 'required',
            '9' => 'required',
            '10' => 'required',
            '11' => 'required',
            '12' => 'required',
            '13' => 'required',
            '14' => 'required',
            '15' => 'required',
            // Above is alias for as it always validates in batches
        ];
    }

    public function customValidationMessages()
    {
        return [
            '0.required' => 'First name Field is empty in file',
            '1.required' => 'Last name Field is empty in file',
            // '2.required' => 'Company name Field is empty in file',
            // '3.required' => 'Title Field is empty in file',
            '4.required' => 'Email Field is empty in file',
            '5.required' => 'Office no Field is empty in file',
            '6.required' => 'Mobile no Field is empty in file',

            '8.required' => 'Property Address1 Field is empty in file',
            '9.required' => 'Property Address City Field is empty in file',
            '10.required' => 'Property Address State Field is empty in file',
            '11.required' => 'Property Address Zip Field is empty in file',
            '12.required' => 'Billing Address1 Field is empty in file',
            '13.required' => 'Billing Address City Field is empty in file',
            '14.required' => 'Billing Address State Field is empty in file',
            '15.required' => 'Billing Address Zip Field is empty in file',

            '0.max' => 'First name Field should not exceed 100 characters',
            '1.max' => 'Last name Field should not exceed 100 characters',
            '2.max' => 'Company name should not exceed 150 characters',
            '2.unique' => 'Company name should be unique',

            '4.email' => 'Email Field should be a valid email',
            '4.UniqueEmailInOrganization' => 'Email must be unique email address',
            '4.FilterEmail' => 'Disposible Emails are not allowed',
            // '3.numeric' => 'Cost Field should contain positive numeric values',
            // '3.gt' => 'Cost Field should contain positive numeric values',
            // '4.required' => 'Install Field is empty in file',
            // '4.numeric' => 'Install Field should contain positive numeric values',
            // '4.min' => 'Install Field should contain positive numeric values',
            // '5.required' => 'Gross Margin Field is empty in file',
            // '5.numeric' => 'Gross Margin Field should contain positive numeric values',
            // '5.gt' => 'Gross Margin Field should contain positive numeric values',
        ];
    }
}
