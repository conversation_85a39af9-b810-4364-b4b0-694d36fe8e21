<?php

namespace App\Imports;

use App\Models\Account;
use App\Models\Contact;
use App\Models\PropertyInformation;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class ContactImport implements ToModel, WithStartRow, WithValidation, WithHeadingRow
{
    /**
     * @param  array  $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function startRow(): int
    {
        return 2; // Assuming your data starts from the second row
    }

    public function model(array $row)
    {

        // $propertyName = $this->findPropertyName($row['property_name'] ?? '');
        $accountOwner = $this->findAccountOwner($row['account'] ?? '');

        $existingContact = Contact::where('email', $row['email'] ?? '')
            ->where('organization_id', getOrganizationId())
            ->first();

        if ($existingContact) {
            throw new \Exception("Contact with email '{$row['email']}' already exists.");
        }

        $this->rowCount++;

        return new Contact([
        'organization_id' => getOrganizationId(),
        'prefix' => $row['prefix'] ?? '',
        'first_name' => $row['first_name'] ?? '',
        'last_name' => $row['last_name'] ?? '',
        'email' => $row['email'] ?? '',
        'property_name' => $row['property_name'] ?? '',
        'account_name' => $row['account'] ?? '',
        'phone_number' => $row['phone_number'] ?? null, // Ensure correct column name
        'title' => $row['title'] ?? '',
        'role' => null, // Cast to integer
        'suffix' => null,
        'mailing_address' => $accountOwner ? $accountOwner->address : '',
        'account' => $accountOwner ? $accountOwner->id : null,
        // 'property_id' => $propertyName ? $propertyName->id : null,
        'property_id' => 0

        ]);
    }

    protected function findAccountOwner($identifier)
    {

        if (empty($identifier)) {
            throw new \InvalidArgumentException('Account (email) cannot be empty.');
        }

        $owner = Account::where('company_name', $identifier)
            ->where('parent_id', getOrganizationId())
            ->first();

        if (! $owner) {
            throw new ModelNotFoundException("Account not found with email: $identifier");
        }

        return $owner;
    }
    // protected function findPropertyName($identifier)
    // {

    //     if (empty($identifier)) {
    //         throw new \InvalidArgumentException("Identifier (email) cannot be empty.");
    //     }

    //     $owner = PropertyInformation::where('name', $identifier)
    //         ->where('organization_id', getOrganizationId())
    //         ->first();

    //     if (!$owner) {
    //         throw new ModelNotFoundException("Pr not found with email: $identifier");
    //     }
    //     return $owner;
    // }

    public function rules(): array
    {
        return [
            'prefix' => 'nullable|string',
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'email' => 'required|email',
            'phone_number' => 'required|numeric|digits_between:5,15',
            'title' => 'required|string',
            'account' => 'required',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'first_name.required' => 'First Name field is empty in file',
            'last_name.required' => 'Last Name field is empty in file',
            'email.required' => 'Email field is empty in file',
            'email.email' => 'Email format is invalid in file',
            'phone_number.required' => 'Phone Number field is empty in file',
            'title.required' => 'Title field is empty in file',
            'account.required' => 'Account field is empty in file',
            'role.required' => 'Role field is empty in file',
            'role.integer' => 'Role must be an integer',
        ];
    }

    private $rowCount = 0;

    private $failures = [];

    public function getRowCount(): int
    {

        return $this->rowCount;
    }

    public function getFailures(): array
    {
        return $this->failures;
    }

    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->failures[] = $failure;
        }
    }
}
