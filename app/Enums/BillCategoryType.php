<?php

namespace App\Enums;

enum BillCategoryType: string
{
    case EQUIPMENT = 'equipment';
    case LABORS = 'labors';
    case HARD_MATERIALS = 'hard_materials';
    case OTHER_COSTS = 'other_costs';
    case CONTRACTORS = 'contractors';

    /**
     * Get all category values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label for the category
     */
    public function label(): string
    {
        return match ($this) {
            self::EQUIPMENT => 'Equipment',
            self::LABORS => 'Labor Cost',
            self::HARD_MATERIALS => 'Material',
            self::OTHER_COSTS => 'Other Job Cost',
            self::CONTRACTORS => 'Sub-contractors',
        };
    }

    /**
     * Get icon path for the category
     */
    public function icon(): string
    {
        return match ($this) {
            self::EQUIPMENT => 'operationIcon/Equipment.png',
            self::LABORS => 'operationIcon/labor.png',
            self::HARD_MATERIALS => 'operationIcon/Materials.png',
            self::OTHER_COSTS => 'operationIcon/other.png',
            self::CONTRACTORS => 'operationIcon/contractor.png',
        };
    }

    /**
     * Get color for charts
     */
    public function color(): string
    {
        return match ($this) {
            self::EQUIPMENT => '#28a745',
            self::LABORS => '#ffc107',
            self::HARD_MATERIALS => '#ff5733',
            self::OTHER_COSTS => '#dc3545',
            self::CONTRACTORS => '#007bff',
        };
    }

    /**
     * Get category for radio button values (used in forms)
     */
    public function radioValue(): string
    {
        return match ($this) {
            self::EQUIPMENT => 'Equipment',
            self::LABORS => 'Labor',
            self::HARD_MATERIALS => 'Material',
            self::OTHER_COSTS => 'Other Job Cost',
            self::CONTRACTORS => 'Subcontractor',
        };
    }

    /**
     * Get BillCategoryType from radio button value
     */
    public static function fromRadioValue(string $radioValue): ?self
    {
        return match ($radioValue) {
            'Equipment' => self::EQUIPMENT,
            'Labor' => self::LABORS,
            'Material' => self::HARD_MATERIALS,
            'Other Job Cost' => self::OTHER_COSTS,
            'Subcontractor' => self::CONTRACTORS,
            default => null,
        };
    }

    /**
     * Get all categories with their labels as key-value pairs
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }

    /**
     * Get category name for display in cost ratio
     */
    public function displayName(): string
    {
        return match ($this) {
            self::EQUIPMENT => 'Equipment',
            self::LABORS => 'Labor Cost',
            self::HARD_MATERIALS => 'Hard Materials',
            self::OTHER_COSTS => 'Other Costs',
            self::CONTRACTORS => 'Contractors',
        };
    }
}
