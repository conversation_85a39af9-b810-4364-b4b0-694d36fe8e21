<?php

namespace App\Enums;

enum ClosedLostReason: string
{
    case PRICING = 'pricing';
    case COMPETITION = 'competition';
    case POOR_FIT = 'poor_fit';
    case COMMUNICATION = 'communication';
    case BUDGET = 'budget';
    case TIMING = 'timing';
    case POOR_QUALIFICATION = 'poor_qualification';
    case UNRESPONSIVENESS = 'unresponsiveness';
    case LACK_OF_DECISION_MAKER = 'lack_of_decision_maker';
    case OTHER = 'other';

    /**
     * Get all reason values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label for the reason
     */
    public function label(): string
    {
        return match ($this) {
            self::PRICING => 'Pricing',
            self::COMPETITION => 'Competition',
            self::POOR_FIT => 'Poor Fit',
            self::COMMUNICATION => 'Communication',
            self::BUDGET => 'Budget',
            self::TIMING => 'Timing',
            self::POOR_QUALIFICATION => 'Poor Qualification',
            self::UNRESPONSIVENESS => 'Unresponsiveness',
            self::LACK_OF_DECISION_MAKER => 'Lack of Decision Maker',
            self::OTHER => 'Other',
        };
    }

    /**
     * Get all reasons with their labels as key-value pairs
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }

    /**
     * Get reasons as array for form display
     */
    public static function forForm(): array
    {
        return array_map(function ($case) {
            return [
                'value' => $case->value,
                'label' => $case->label(),
            ];
        }, self::cases());
    }
}
