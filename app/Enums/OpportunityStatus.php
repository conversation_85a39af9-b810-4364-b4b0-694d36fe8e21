<?php

namespace App\Enums;

enum OpportunityStatus: int
{
    case OPEN = 1;
    case ESTIMATING = 2;
    case PROPOSED = 3;
    case REVISION = 4;
    case CLOSED_LOST = 5;
    case COMPLETED = 6;
    case CLOSED_WIND = 7;

    /**
     * Get all status values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human-readable label for the status
     */
    public function label(): string
    {
        return match ($this) {
            self::OPEN => 'Open',
            self::ESTIMATING => 'Estimating',
            self::PROPOSED => 'Proposed',
            self::REVISION => 'Revision',
            self::CLOSED_LOST => 'Closed Lost',
            self::COMPLETED => 'Completed',
            self::CLOSED_WIND => 'Closed Win',
        };
    }

    /**
     * Get CSS class for the status
     */
    public function cssClass(): string
    {
        return match ($this) {
            self::OPEN => 'status-open',
            self::ESTIMATING => 'status-estimating',
            self::PROPOSED => 'status-proposed',
            self::REVISION => 'status-revision',
            self::CLOSED_LOST => 'status-closed',
            self::COMPLETED => 'status-completed',
            self::CLOSED_WIND => 'status-closed',
        };
    }

    /**
     * Get active icon for the status
     */
    public function activeIcon(): string
    {
        return match ($this) {
            self::OPEN => 'opportunityIcon/open.png',
            self::ESTIMATING => 'opportunityIcon/estimate.png',
            self::PROPOSED => 'opportunityIcon/proposed.png',
            self::REVISION => 'opportunityIcon/revision.png',
            self::CLOSED_LOST => 'opportunityIcon/closelost.png',
            self::COMPLETED => 'opportunityIcon/completed.png',
            self::CLOSED_WIND => 'opportunityIcon/closewin.png',
        };
    }

    /**
     * Get inactive icon for the status
     */
    public function inactiveIcon(): ?string
    {
        return match ($this) {
            self::OPEN => 'opportunityIcon/open_inactive.png',
            self::ESTIMATING => 'opportunityIcon/estimate_inactive.png',
            self::PROPOSED => 'opportunityIcon/proposed_inactive.png',
            self::REVISION => null,
            self::CLOSED_LOST => null,
            self::COMPLETED => null,
            self::CLOSED_WIND => null,
        };
    }

    /**
     * Check if status is disabled for mark as complete
     */
    public function isMarkAsCompleteDisabled(): bool
    {
        return in_array($this, [self::CLOSED_LOST, self::CLOSED_WIND]);
    }

    /**
     * Get all statuses with their labels as key-value pairs
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }

    /**
     * Get status mapping for controller
     */
    public static function getStatusMapping(): array
    {
        return [
            self::OPEN->value => ['name' => self::OPEN->label(), 'class' => self::OPEN->cssClass()],
            self::ESTIMATING->value => ['name' => self::ESTIMATING->label(), 'class' => self::ESTIMATING->cssClass()],
            self::PROPOSED->value => ['name' => self::PROPOSED->label(), 'class' => self::PROPOSED->cssClass()],
            self::REVISION->value => ['name' => self::REVISION->label(), 'class' => self::REVISION->cssClass()],
            self::CLOSED_LOST->value => ['name' => self::CLOSED_LOST->label(), 'class' => self::CLOSED_LOST->cssClass()],
            self::COMPLETED->value => ['name' => self::COMPLETED->label(), 'class' => self::COMPLETED->cssClass()],
            self::CLOSED_WIND->value => ['name' => self::CLOSED_WIND->label(), 'class' => self::CLOSED_WIND->cssClass()],
        ];
    }

    /**
     * Check if opportunity can move to next status
     */
    public function canMoveToNext(): bool
    {
        return ! in_array($this, [self::CLOSED_LOST, self::CLOSED_WIND, self::COMPLETED]);
    }

    /**
     * Get next status when marking as complete
     */
    public function getNextStatus(): ?self
    {
        return match ($this) {
            self::OPEN => self::ESTIMATING,
            self::ESTIMATING => self::PROPOSED,
            self::PROPOSED => self::REVISION,
            self::REVISION => self::COMPLETED,
            default => null,
        };
    }
}
