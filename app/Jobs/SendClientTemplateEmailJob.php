<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;

class SendClientTemplateEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $data, public $files, public $path, public $orgId)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $data = $this->data;
        $payload = ['payload' => array_merge($data, $this->getOrganizationDetail($this->orgId))];
        $files = $this->files;

        Mail::send('email_template.client.email-template', $payload, function ($message) use ($data, $files) {
            if (array_key_exists('cc_email', $data) && count($data['cc_email']) > 0) {
                $message->cc($data['cc_email']);
            }

            if (array_key_exists('bcc_email', $data) && count($data['bcc_email']) > 0) {
                $message->bcc($data['bcc_email']);
            }

            $message->to($data['email'])->subject($data['subject']);
            if (count($files) > 0) {
                foreach ($files as $file) {
                    $message->attach($file);
                }
            }
        });

        // Delete the attachments after sending the email
        if (count($files) > 0) {
            foreach ($files as $file) {
                // Use unlink to delete the file from the server
                if (file_exists($file)) {
                    unlink($file);
                }
            }
        }

        if (File::exists($this->path)) {
            File::deleteDirectory($this->path);
        }
    }

    private function getOrganizationDetail($orgId)
    {
        $org = User::with('companyPropertyAddress')->find($orgId);

        return [
            'company_name' => $org->company_name,
            'company_website' => $org->companyPropertyAddress->website_url,
            'company_email' => $org->email,
            'company_phone_no' => $org->companyPropertyAddress->phone_no,
            'company_address' => $org->companyPropertyAddress->address1,
            'org_image' => $org->profile_photo_path ? asset('storage/user_images/'.$org->profile_photo_path) : null,
            'primary_color' => $org->primary_color,
        ];
    }
}
