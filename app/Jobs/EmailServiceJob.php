<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class EmailServiceJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $templateName;

    protected $subject;

    protected $to;

    protected $payload;

    protected $files;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($templateName, $subject, $to, $payload = [], $files = [])
    {
        $this->templateName = $templateName;
        $this->subject = $subject;
        $this->to = $to;
        $this->payload = $payload;
        $this->files = $files;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $subject = $this->subject;
        $to = $this->to;
        $files = $this->files;
        $payload = $this->payload;

        $filesArray = [];

        if ($files instanceof \Illuminate\Support\Collection) {
            // If $files is a collection, convert it to an array
            $filesArray = $files->toArray();
        } elseif (is_object($files)) {
            // If $files is an object, convert it to an array
            $filesArray = (array) $files;
        } elseif (is_null($files)) {
            // If $files is null, keep it as an empty array
            $filesArray = [];
        } else {
            // If $files is already an array, use it directly
            $filesArray = $files;
        }

        if ($to && array_key_exists('dompdf', $filesArray) && array_key_exists('serviceClass', $filesArray) && array_key_exists('classParams', $filesArray) && class_exists($filesArray['serviceClass'])) {
            $serviceInstance = app($filesArray['serviceClass']);

            if (method_exists($serviceInstance, 'preparePdf')) {

                $pdf = $serviceInstance->preparePdf(...$filesArray['classParams']);
                // $pdf = $serviceInstance->preparePdf(...$filesArray['classParams']);

                if (is_array($pdf)) {
                    $pdf = $pdf['pdf'];
                }

                Mail::send($this->templateName, ['payload' => $this->payload], function ($message) use ($subject, $to, $pdf, $payload) {
                    $message->to($to)
                        ->subject($subject)
                        ->attachData(
                            $pdf->output(),
                            'estimate#_'.(($payload['sale_order_number'] != 0) ? $payload['sale_order_number'] : 'invoice').'.pdf'
                        );
                });

                ! Mail::flushMacros();
            } else {
                $this->fail('Method preparePdf not exist in '.$filesArray['serviceClass'].' Class');
            }
        } elseif ($to) {
            Mail::send($this->templateName, ['payload' => $this->payload], function ($message) use ($subject, $to, $filesArray) {
                $message->to($to)->subject($subject);
                $message->from(config('mail.from.address'), config('mail.from.name'));
                if (count($filesArray) > 0) {
                    foreach ($filesArray as $file) {
                        $message->attach($file);
                    }
                }
            });

            ! Mail::flushMacros();
        }
    }
}
