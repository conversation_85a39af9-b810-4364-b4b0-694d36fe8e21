<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Services\EstimateService;
use App\Services\InvoiceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;

class InvoicePdfSendJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Invoice $invoice, public $email_template, public array $payload = [], public $path = null) {}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $data = $this->payload;
            if (array_key_exists('message', $data)) {
                $data['message'] = '<h3 class="invoice_number" style="font-style: normal; font-weight: 600; font-size: 24px; line-height: 33.6px; color: color: rgb(25, 42, 62);">Invoice# '.$this->invoice?->invoice_number.' </h3>'.$data['message'];
            }
            $payload = ['payload' => $data];
            $pdf = null;
            if (array_key_exists('sendAttachment', $data) && $data['sendAttachment'] == true) {
                $pdf = InvoiceService::preparePdf($this->invoice, $this->invoice->organization_id);

            }

            $estimatePDF = null;
            if (array_key_exists('estimateAttachment', $data) && $data['estimateAttachment'] == true) {
                $this->invoice->loadMissing('operation');
                if ($this->invoice->operation) {
                    $this->invoice->operation->loadMissing('request');
                    $estimatePDF = EstimateService::preparePdf($this->invoice->operation?->request->id, $this->invoice->organization_id);
                    if (is_array($estimatePDF)) {
                        $estimatePDF = $estimatePDF['pdf'];
                    } else {
                        $estimatePDF = null;
                    }
                }
            }

            Mail::send($this->email_template, $payload, function ($message) use ($data, $pdf, $estimatePDF) {
                if (array_key_exists('cc_email', $data) && count($data['cc_email']) > 0) {
                    $message->cc($data['cc_email']);
                }

                if (array_key_exists('bcc_email', $data) && count($data['bcc_email']) > 0) {
                    $message->bcc($data['bcc_email']);
                }
                $message->to($data['email'], $data['email'])
                    ->subject($data['subject']);

                // Check if sendAttachment is checked | true
                if ($pdf != null) {
                    $message->attachData($pdf->output(), 'Invoice # '.$this->invoice->invoice_number.'.pdf');
                }

                // Check if estimatePDF is checked | true
                if ($estimatePDF != null) {
                    $message->attachData($estimatePDF->output(), 'Estimate # '.optional($this->invoice->operation)?->request?->sales_order_number.'.pdf');
                }

                // add External files if exists
                if (array_key_exists('files', $data) && count($data['files']) > 0) {
                    foreach ($data['files'] as $file) {
                        if (File::exists($file)) {
                            $message->attach($file);
                        }
                    }
                }
            });

            if (array_key_exists('password', $this->payload)) {
                $this->invoice->client->update([
                    'invite_sent' => 1,
                    'password' => $this->payload['enc_password'],
                ]);
            }

            if (! empty($this->path)) {
                if (File::exists($this->path)) {
                    File::deleteDirectory($this->path);
                }
            }
        } catch (\Exception $e) {
            info('Invoice PDF Send Failed', [$e->getMessage()]);
            $this->job->fail();

        }
    }
}
