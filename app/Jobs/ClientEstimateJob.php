<?php

namespace App\Jobs;

use App\Models\Estimate;
use App\Models\GenerateEstimate;
use App\Services\EstimateService;
use Barryvdh\DomPDF\Facade\Pdf;
use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ClientEstimateJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected GenerateEstimate $generateEstimate;

    protected $payloadData;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(GenerateEstimate $generateEstimate, $payload, public $path = null)
    {
        $this->generateEstimate = $generateEstimate;
        $this->payloadData = $payload;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $generateEstimate = $this->generateEstimate;
        $payloadData = $this->payloadData;
        Log::info('Payload received: ', $this->payloadData);
        $pdf = null;
        if (array_key_exists('estimateAttachment', $payloadData) && $payloadData['estimateAttachment'] == true) {
            $response = EstimateService::preparePdf($this->generateEstimate->opportunity_id, $this->generateEstimate->client->organization_id);
            // dd($this->generateEstimate->client->organization_id);
            $pdf = $response['pdf'];
        }

        if (optional($this->generateEstimate->client)?->invite_sent == 0) {
            $this->generateEstimate->loadMissing('client');

            $url = url('client/'.encodeID($this->generateEstimate->client->organization_id).'/login');
            $payload['url'] = $url;
            $payload['email'] = $this->generateEstimate->client->email;
            $payload['company_name'] = $this->generateEstimate->client->organization->company_name;
            $payload['company_phone_no'] = $this->generateEstimate->client->mobile_no;
            $payload['primary_color'] = $this->generateEstimate->client->organization->primary_color;
            $payload['company_address'] = $this->generateEstimate->client->address;
            $payload['company_website'] = $this->generateEstimate->client->website;
            $payload['company_email'] = $this->generateEstimate->client->email;
            $password = 'Client@'.uniqid();
            $payload['password'] = $password;
            $payload['url'] = $url;
            $payload['message'] = $payloadData['message'];
            $payload['name'] = $this->generateEstimate->client->full_name;
            $payload['org_name'] = $this->generateEstimate->client->organization->company_name;
            $payload['org_image'] = $this->generateEstimate->client->organization->profile_photo_path ? asset('storage/user_images/'.$this->generateEstimate->client->organization->profile_photo_path) : null;
            $this->generateEstimate->client->update([
                // 'invite_sent' => 1,
                'password' => bcrypt($password),
            ]);
            // dd($this->generateEstimate->client);

            $totalPriceSumss = DB::table('estimate_items')
                ->where('opportunity_id', $this->generateEstimate->opportunity_id)
                ->sum('total_price');
            $totalCostSumss = DB::table('estimate_items')
                ->where('opportunity_id', $this->generateEstimate->opportunity_id)
                ->sum('total_cost');

            $this->generateEstimate->update([
                'total_price' => $totalPriceSumss,
                'total_cost' => $totalCostSumss,
            ]);

            // dd($payload["email"]);
            // dd($pdf);

            Mail::send('email_template.client.invite', $payload, function ($message) use ($payload, $payloadData) {
                // if (array_key_exists('cc_email', $payloadData) &&  count($payloadData['cc_email']) > 0) {
                //     $message->cc($payloadData['cc_email']);
                // }

                // if (array_key_exists('bcc_email', $payloadData) &&  count($payloadData['bcc_email']) > 0) {
                //     $message->bcc($payloadData['bcc_email']);
                // }

                $message->to($payload['email'], $payload['email'])
                    ->subject($payloadData['subject'] ?? 'Estimate');
                $message->from(config('mail.from.address'), config('mail.from.name'));

                // // Check if sendAttachment is checked | true
                // if ($pdf != null) {
                //     $message->attachData($pdf->output(), "estimate#" . (($generateEstimate?->pportunityid?->sales_order_number != 0) ? $generateEstimate?->pportunityid?->job_no : 'invoice' ?? 'invoice') . ".pdf");
                // }

                // // add External files if exists
                // if (array_key_exists('files', $payloadData) &&  count($payloadData['files']) > 0) {
                //     foreach ($payloadData['files'] as $file) {
                //         if (File::exists($file)) {
                //             $message->attach($file);
                //         }
                //     }
                // }
            });
            Log::info('Mail sent successfully');
        }

        $data['email'] = $this->generateEstimate->loadMissing('client')->client->email;
        $data['company'] = $this->generateEstimate->client->company_name;
        $data['message'] = $payloadData['message'];
        $data['company_name'] = $this->generateEstimate->client->organization->company_name;
        $data['company_phone_no'] = $this->generateEstimate->client->mobile_no;
        $data['primary_color'] = $this->generateEstimate->client->organization->primary_color;
        $data['company_address'] = $this->generateEstimate->client->address;
        $data['company_website'] = $this->generateEstimate->client->website;
        $data['company_email'] = $this->generateEstimate->client->organization->email;
        $data['grand_total'] = $this->generateEstimate->GrandTotal;
        $data['client_name'] = $this->generateEstimate->client->company_name;
        $data['url'] = url('client/'.encodeID($this->generateEstimate->client->parent_id).'/estimate/view/'.encodeID($generateEstimate->opportunityid->id));
        // dd($data["email"]);

        Mail::send('email_template.estimate-invoice', $data, function ($message) use ($data, $payloadData, $pdf, $generateEstimate) {

            if (array_key_exists('cc_email', $payloadData) && count($payloadData['cc_email']) > 0) {
                $message->cc($payloadData['cc_email']);
            }

            if (array_key_exists('bcc_email', $payloadData) && count($payloadData['bcc_email']) > 0) {
                $message->bcc($payloadData['bcc_email']);
            }

            $message->to($data['email'], $data['email'])
                ->subject($payloadData['subject'] ?? 'Estimate');
            $message->from(config('mail.from.address'), config('mail.from.name'));

            // Check if sendAttachment is checked | true
            if ($pdf != null) {
                $message->attachData($pdf->output(), 'estimate#'.(($generateEstimate?->request?->sales_order_number != 0) ? $generateEstimate?->request?->job_no : 'invoice' ?? 'invoice').'.pdf');
            }

            // add External files if exists
            if (array_key_exists('files', $payloadData) && count($payloadData['files']) > 0) {
                foreach ($payloadData['files'] as $file) {
                    if (File::exists($file)) {
                        $message->attach($file);
                    }
                }
            }
        });
        Log::info('Mail sent successfully');

        if (! empty($this->path)) {
            if (File::exists($this->path)) {
                File::deleteDirectory($this->path);
            }
        }
    }
}
