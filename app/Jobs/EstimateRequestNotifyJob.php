<?php

namespace App\Jobs;

use App\Models\Estimate;
use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EstimateRequestNotifyJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $estimate_id;

    protected $property_name;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($estimate_id, $property_name)
    {
        $this->estimate_id = $estimate_id;
        $this->property_name = $property_name;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $estimate = Estimate::with(['organization', 'client', 'saleMan', 'estimator', 'media'])->find($this->estimate_id);
        $email_template = 'email_template.estimate-creation-notify';
        $saleMan = $estimate->saleMan;
        $estimator = $estimate->estimator;
        $company = $estimate->organization?->company_name;
        $property_name = $this->property_name;
        $saleManData['name'] = $saleMan->first_name.' '.$saleMan->last_name;
        $saleManData['company'] = $company;
        $saleManData['description'] = $estimate->description;
        $saleManData['property_name'] = $property_name;
        $saleManData['client_name'] = $estimate->client->first_name.' '.$estimate->client->last_name;
        $estimatorData['name'] = $estimator->first_name.' '.$estimator->last_name;
        $estimatorData['company'] = $company;
        $estimatorData['description'] = $estimate->description;
        $estimatorData['property_name'] = $property_name;
        $estimatorData['client_name'] = $estimate->client->first_name.' '.$estimate->client->last_name;
        $files = $estimate->media->pluck('original_url');
        $estimatorData['files'] = $files;
        $saleManData['files'] = $files;

        try {
            EmailService::send($email_template, 'Estimate Request Assigned', $saleMan->email, $saleManData, true, $files);
            EmailService::send($email_template, 'Estimate Request Assigned', $estimator->email, $estimatorData, true, $files);
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }
}
