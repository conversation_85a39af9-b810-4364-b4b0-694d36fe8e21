<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Models\Transaction;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProcessStripeOrgWebhook implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $invoice;

    protected $session;

    public function __construct(Invoice $invoice, $session)
    {
        $this->invoice = $invoice;
        $this->session = $session;
    }

    public function handle()
    {
        $invoice = $this->invoice;
        $session = $this->session;

        if ($invoice->status != 'paid') {
            DB::beginTransaction();
            try {
                Transaction::updateOrCreate([
                    'invoice_id' => $invoice->id,
                ], [
                    'transaction_number' => Str::random(8),
                    'session_id' => $session->id,
                    'payment_intent' => $session->payment_intent,
                    'status' => $session->payment_status,
                    'payment_method' => 'stripe',
                    'transaction_date' => Carbon::now(),
                ]);

                $invoice->load(['organization', 'client']);
                $invoice->update(['status' => 'paid']);
                $invoice->transaction_date = customDateFormat(now(), $invoice->organization_id);
                $invoice->payment_method = 'Stripe';
                $organization_email_template = 'email_template.client.invoice-pay-notify';
                $client_email_template = 'email_template.client.invoice-pay-success';
                $payload['invoice'] = $invoice;
                $payload['org_image'] = $invoice->organization->profile_photo_path ? asset('storage/user_images/'.$invoice->organization->profile_photo_path) : null;
                $payload['company_name'] = $invoice->organization->comapny_name;
                $payload['company_phone_no'] = $invoice->organization->companyPropertyAddress->phone_no;
                $payload['primary_color'] = $invoice->organization->primary_color;
                $payload['company_address'] = $invoice->organization->companyPropertyAddress->address1;
                $payload['company_website'] = $invoice->organization->companyPropertyAddress->website_url;
                $payload['company_email'] = $invoice->organization->email;
                // EmailService::send($client_email_template, 'Invoice Paid '.$invoice->invoice_number, $invoice->client->email, $payload, true);
                $payload['email'] = $invoice->client->email;
                $payload['subject'] = 'Invoice Paid '.$invoice->invoice_number;
                $payload['sendAttachment'] = true;
                dispatch(new InvoicePdfSendJob($invoice, $client_email_template, $payload));
                EmailService::send($organization_email_template, 'Payment Received from '.$invoice->client->company_name, $invoice->organization->email, $payload, true);
                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
            }
        }
    }
}
