<?php

namespace App\Services;

use App\Models\Plan;
use App\Traits\StripeConfig;

class StripePlanService
{
    use StripeConfig;

    private Plan $plan;

    private $product;

    public function __construct(Plan $plan)
    {
        $this->setConfig();
        $this->plan = $plan;
    }

    /**
     * Create a new product
     */
    public function createProduct(): array
    {
        try {

            $this->product = $this->stripeContext->products->create(
                [
                    'name' => $this->plan->name,
                    'description' => $this->plan->description,
                ]
            );
            $response['status'] = HTTP_CREATED;
        } catch (\Exception $exception) {
            $response['status'] = $exception->getCode();
        }

        return $response;
    }

    /**
     * Create a new billing plan
     */
    public function createPlanPrice(): array
    {
        try {
            $response['stripePlan'] = $this->stripeContext->prices->create(
                [
                    'currency' => $this->plan->currency,
                    'unit_amount' => $this->plan->price * 100,
                    'product' => $this->product['id'],
                    'billing_scheme' => 'per_unit',
                    'recurring' => [
                        'interval' => $this->plan->interval,
                        'interval_count' => $this->plan->interval_count,
                        'trial_period_days' => $this->plan->trial_days,
                    ],
                ]
            );
            $response['status'] = HTTP_CREATED;
        } catch (\Exception $exception) {
            $response['status'] = $exception->getCode();
        }

        return $response;
    }
}
