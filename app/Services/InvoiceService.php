<?php

namespace App\Services;

use App\Http\Requests\Organization\InvoiceRequest;
use App\Models\Client;
use App\Models\Contact;
use App\Models\CostSummary;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateItem;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Throwable;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class InvoiceService
{
    /**
     * Prepare Pdf For Estimate
     *
     * @param  \App\Models\GenerateEstimate  $id
     * @param  \App\Models\Users  $organizationId
     * @return $pdf
     */
    public static function preparePdf(Invoice $invoice, $organizationId, $isPdfResponse = false)
    {

        $user = User::findorfail($organizationId);
        $logo = $user->profile_photo_path ? asset('storage/user_images/'.$user->profile_photo_path) : $user->company_name;
        $company_name = $user->company_name;
        $statusMappings = [
            'paid' => 'approved',
            'due' => 'unpaid',
            'overdue' => 'overdue',
        ];

        $costSummary = null;
        if ($invoice->operation_id) {
            $costSummary = CostSummary::where('organization_id', $organizationId)->where('operation_id', $invoice->operation_id)->first();
        }

        $invoice->issue_date = $invoice->issue_date ? $invoice->issue_date : now();
        $invoice->due_date = $invoice?->due_date ? $invoice?->due_date : now();

        $data = [
            'title' => 'Invoice',
            'date' => date('m/d/Y'),
            'logo' => $logo,
            'company_name' => $company_name,
            'statusMappings' => $statusMappings,
            'primary_color' => $user->primary_color,
            'secondary_color' => $user->secondary_color,
            'invoice' => $invoice,
            'costSummary' => $costSummary,
        ];
        // dd($invoice);
        // return view('organization.invoices.pdf')->with($data);
        $pdf = Pdf::loadView('organization.invoices.pdf', $data);

        // $pdf->setOptions([
        //     'isPhpEnabled' => true, // Enable PHP processing for header and footer
        //     'isHtml5ParserEnabled' => true, // Enable HTML5 parser
        //     'fontDir' => storage_path('fonts/'), // Adjust the path to your font directory
        //     'fontCache' => storage_path('fonts/cache/'),
        //     'isJavascriptEnabled' => true,
        // ]);

        // dd($pdf);
        if ($invoice->status == 'paid') {
            $pdf->setPaper('L');
            $pdf->output();
            $canvas = $pdf->getDomPDF()->getCanvas();
            $height = $canvas->get_height();
            $width = $canvas->get_width();
            $canvas->set_opacity(.1, 'Normal');
            $canvas->set_opacity(.1);
            $canvas->page_text(
                $width / 5,
                $height / 2,
                'P A I D',
                null,
                90,
                [0, 0, 0],
                2,
                2,
                -30
            );
        }

        if ($isPdfResponse) {
            return response()->make($pdf->output(), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename=Invoice#_'.$invoice->invoice_number.'.pdf',
            ]);
        } else {
            return $pdf;
        }
    }

    public function listing(Request $request)
    {
        $Organization = User::where('id', getOrganizationId())->firstOrFail();
        if ($request->ajax()) {
            $query = Invoice::with('client')
                ->where('organization_id', $Organization->id)
                ->where('is_sent', 1)
                ->when(
                    auth('web')->user()->isEmployee() && ! auth('web')->user()->hasRole('manager'),
                    function ($q) {
                        $q->whereExists(function ($query) {
                            $query->select(DB::raw(1))
                                ->from('contacts')
                                ->whereRaw('invoices.client_id = contacts.id')
                                ->where(function ($subQuery) {
                                    employeeAssociatedDateQuery($subQuery);
                                });
                        });
                    }
                )
                ->latest()
                ->when(request('status'), function ($query, $status) {
                    if (! empty($status)) {
                        return $query->where('status', $status);
                    }

                    return $query;
                })
                ->select('total', 'created_at', 'status', 'id', 'organization_id', 'client_id', 'invoice_number', 'subject', 'issue_date');

            return DataTables::eloquent($query)
                ->filterColumn('client_name', function ($query, $keyword) {
                    $query->whereHas('client', function ($q) use ($keyword) {
                        $sql = 'CONCAT(contacts.first_name) LIKE ?';
                        $q->whereRaw($sql, ["%{$keyword}%"]);
                    });
                })
                ->filterColumn('total', function ($query, $keyword) {
                    $query->where('total', 'LIKE', '%'.$keyword.'%');
                })
                ->addColumn('client_name', function (Invoice $invoice) {
                    // dd($invoice->client->id);
                    return '
                        <div class="table_profile">


                        <h2 class="profile_name">'.$invoice->client?->first_name.' '.$invoice->client?->last_name.'</h2></div>';

                })
                ->addColumn('created_at', function (Invoice $invoice) {
                    // dd($invoice->id);
                    return $invoice->created_at;
                })
                ->addColumn('total', function (Invoice $invoice) {

                    return '<span class="invoiceTotalPayable'.encodeID($invoice->id).'" style="color: inherit;">$'.$invoice->total.'</span>';
                })
                ->addColumn('status', function (Invoice $invoice) {

                    return '<span class="status '.($invoice->status === 'paid' ? 'approved invoice-approved' : 'warning invoice-warning').' status'.encodeID($invoice->id).'">'.ucfirst($invoice?->status).'</span>';
                })
                ->addColumn('action', function (Invoice $invoice) {

                    $user = auth('web')->user();
                    if (! $user->canany(['invoice_listing', 'delete_invoice', 'client_detail', 'collect_invoice'])) {
                        return '';
                    }
                    $action = '
                        <div class="dropdown mx-auto w-fit estimate-dropdown">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                               <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M14 5C14 6.104 13.104 7 12 7C10.896 7 10 6.104 10 5C10 3.896 10.896 3 12 3C13.104 3 14 3.896 14 5Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 10C10.896 10 10 10.896 10 12C10 13.104 10.896 14 12 14C13.104 14 14 13.104 14 12C14 10.896 13.104 10 12 10Z"
                                fill="#828282"
                            />
                            <path
                                d="M12 17C10.896 17 10 17.896 10 19C10 20.104 10.896 21 12 21C13.104 21 14 20.104 14 19C14 17.896 13.104 17 12 17Z"
                                fill="#828282"
                            />
                        </svg>
                            </div>
                            <ul class="dropdown-menu download-estimates" aria-labelledby="dropdown1">';

                    if ($user->can('invoice_detail')) {
                        $action .= '<li><a class="dropdown-item" href="'.route(getRouteAlias().'.invoices.view', encodeID($invoice?->id)).'">View invoice details</a></li>';
                    }

                    if ($user->can('collect_invoice') && $invoice?->status != 'paid') {
                        $clid = $invoice->client?->id;
                        // dd(encodeID($invoice->client->id));
                        $action .= '
                            <li><button data-id="'.encodeID($invoice->id).'" data-clientId="'.encodeID($clid).'" class="dropdown-item collectPaymentBtn paymentBtn'.encodeID($invoice->id).'">Collect Payment</button></li>';
                    }

                    if ($user->can('delete_invoice') && $invoice->status != 'paid') {
                        $action .= '<li><a type="button" class="dropdown-item deleteInvoiceBtn deleteInvoice'.encodeId($invoice->id).'" data-id="'.encodeId($invoice->id).'">Delete</a></li>';
                    }
                    if ($user->can('delete_invoice') && $invoice->status != 'paid') {
                        $action .= '<li><a type="button" class="dropdown-item editInvoiceBtn editInvoice'.encodeId($invoice->id).'" href="'.route(getRouteAlias().'.invoices.edit', encodeID($invoice->id)).'">Edit Invoice</a></li>';
                    }
                    $action .= '</ul>
                        </div>';

                    if (strpos($action, '<li>') === false) {
                        $action = '';
                    }

                    // dd($invoice->id);
                    return $action;
                })
                ->rawColumns(['total', 'status', 'action', 'client_name'])
                ->toJson();
        }
        // dd($Organization);
        $clients = Contact::where('organization_id', $Organization->id)->when(
            auth('web')->user()->isEmployee() && ! auth('web')->user()->hasRole('manager'),
            function ($q) {
                $q->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('opportunities')
                        ->whereRaw('contacts.id = opportunities.contact_id')
                        ->where(function ($subQuery) {
                            employeeAssociatedDateQuery($subQuery);
                        });
                });
            }
        )->latest()->get();

        return view('organization.invoices.index', compact('clients', 'Organization'));
    }

    public function simpleinvoiceUpdateOrCreate(InvoiceRequest $request, $invoiceID, $isUpdate = false)
    {

        $issueDate = $request->issue_date;

        if ($request->due_date == 'net15') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(15);
        } elseif ($request->due_date == 'net30') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(30);
        } elseif ($request->due_date == 'net45') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(45);
        } else {
            // dd($request?->due_date);

            // $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $request->due_date);
            $dueDate = Carbon::createFromFormat('Y-m-d', $request?->due_date);

            // dd($request?->due_date);

        }

        $invoice = Invoice::withCount('invoiceItems')->withSum('invoiceItems', 'total_price')->where('id', decodeID($invoiceID))->firstOrFail();

        if ($invoice->status == 'paid') {
            return redirect()->route(getRouteAlias().'.invoices.index')->with('error', 'You cann\'t update this paid invoice');
        }

        if ($invoice->invoice_items_count == 0) {
            return response()->json(['creation_error' => 'Please add atleast one item to create the invoice'], 400);
        }

        $subTotal = $invoice->invoice_items_sum_total_price;
        $price = $subTotal;

        if ($request->has('tax') && $request->has('discount')) {

            if ($request->discount) {
                if ($request->discount_method == 'amount') {
                    $price = $price - $request->discount;
                } else {
                    $price = $price * (1 - $request->discount / 100);
                }
            }

            if ($request->tax) {
                $price = $price * (1 + $request->tax / 100);
            }
        }

        if ($price > 999999) {
            return response()->json(['creation_error' => 'Invoice total amount should not be greater than 999999'], 400);
        }

        $invoice->update([
            'subject' => $request->subject,
            'status' => 'due',
            'invoice_number' => empty($invoice->invoice_number) ? generateInvoiceNumber(getOrganizationId()) : $invoice->invoice_number,
            'issue_date' => Carbon::createFromFormat('Y-m-d', $issueDate)->format('Y-m-d'),
            'due_date' => $dueDate,
            'client_id' => $invoice->client_id,
            'tax' => $request->tax ?? 0,
            'discount_method' => $request->discount_method,
            'discount_price' => $request->discount,
            'sub_total' => ! empty($request->subTotalPrice) ? str_replace(',', '', $request->subTotalPrice) : 0,
            'total' => $request->totalPrice,
            'payment_mode' => $request->payment_mode,
            'notes' => $request->notes,
            'is_sent' => 1,
        ]);

        if ($isUpdate) {

            // if ($request->exists('document')) {
            // if (sizeOf($invoice->getMedia('document'))) {
            //     foreach ($invoice->getMedia('document') as $media) {
            //         if (!in_array($media->file_name, $request->input('document', []))) {
            //             $media->delete();
            //         }
            //     }
            // }

            // if (sizeOf($invoice->getMedia('document'))) {
            //     $media = $invoice->getMedia('document')->pluck('file_name')->toArray();
            //     foreach ($request->input('document', []) as $file) {
            //         if (count($media) === 0 || !in_array($file, $media)) {
            //             // $invoice->addMedia(public_path('storage/uploads/' . $file))->toMediaCollection('document');
            //         }
            //     }678bdf8b83be0_Fgo2tLjn3M-(9).png
            // } else {
            // dd($isUpdate);
            // if ($request->exists('document')) {
            //     foreach ($request->input('document', []) as $file) {
            //         $existingMedia = $invoice->getMedia('document')->pluck('file_name')->toArray();

            //         if (!in_array($file, $existingMedia)) {
            //             $invoice->addMedia(public_path('storage/uploads/' . $file))
            //                 ->usingName($file) // Save name with extension
            //                 ->usingFileName($file) // Save exact file name in the media table
            //                 ->toMediaCollection('document');
            //         }
            //     }
            // }

            if ($request->exists('document')) {
                // Get the existing media file names in the collection
                $existingMedia = $invoice->getMedia('document')->pluck('file_name')->toArray();

                foreach ($request->input('document', []) as $file) {
                    // Check if the file already exists in the database
                    if (! in_array($file, $existingMedia)) {
                        // Create a new entry in the media table without actually moving or re-uploading the file
                        $invoice->media()->create([
                            'name' => pathinfo($file, PATHINFO_FILENAME), // File name without extension
                            'file_name' => $file, // Complete file name with extension
                            'collection_name' => 'document',
                            'disk' => 'public',
                            'mime_type' => mime_content_type(public_path('storage/uploads/'.$file)),
                            'size' => filesize(public_path('storage/uploads/'.$file)),
                            'manipulations' => json_encode([]),
                            'custom_properties' => json_encode([]),
                            'generated_conversions' => json_encode([]),
                            'responsive_images' => json_encode([]),
                            'order_column' => null,
                        ]);
                    }
                }
            }

            // }
            // }

        } else {
            // if ($request->exists('document')) {
            //     foreach ($request->input('document', []) as $file) {
            //         $filePath = public_path('storage/uploads/' . $file);
            //         if (file_exists($filePath)) {
            //             $invoice->addMedia($filePath)->toMediaCollection('document');
            //         } else {
            //         }
            //     }
            // }
            if ($request->exists('document')) {
                foreach ($request->input('document', []) as $file) {

                    $invoice->media()->create([
                        'name' => pathinfo($file, PATHINFO_FILENAME), // File name without extension
                        'file_name' => $file, // Complete file name with extension
                        'collection_name' => 'document',
                        'disk' => 'public',
                        'mime_type' => mime_content_type(public_path('storage/uploads/'.$file)),
                        'size' => filesize(public_path('storage/uploads/'.$file)),
                        'manipulations' => json_encode([]),
                        'custom_properties' => json_encode([]),
                        'generated_conversions' => json_encode([]),
                        'responsive_images' => json_encode([]),
                        'order_column' => null,
                    ]);
                }
            }

        }

        if ($request->has('sentEmail')) {
            $data = $request->except(['select_file', 'subject', 'issue_date', 'due_date', 'tax', 'discount', 'discount_method']);
            $this->emailInvoiceToClient($request, $invoice, $data);
        }
    }

    public function storeOperationalInvoice(InvoiceRequest $request, $estimateID)
    {
        $issueDate = $request->issue_date;
        // if ($request->due_date == 'net15') {
        //     $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $issueDate)->addDays(15);
        // } elseif ($request->due_date == 'net30') {
        //     $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $issueDate)->addDays(30);
        // } elseif ($request->due_date == 'net45') {
        //     $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $issueDate)->addDays(45);
        // } else {
        //     $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $request->due_date);
        // }

        if ($request->due_date == 'net15') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(15);
        } elseif ($request->due_date == 'net30') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(30);
        } elseif ($request->due_date == 'net45') {
            $dueDate = Carbon::createFromFormat('Y-m-d', $issueDate)->addDays(45);
        } else {
            // dd($request?->due_date);

            // $dueDate =  Carbon::createFromFormat(getOrgDateFormat(null, false), $request->due_date);
            $dueDate = Carbon::createFromFormat('Y-m-d', $request?->due_date);

            // dd($request?->due_date);

        }

        // DB::beginTransaction();
        // try {

        $generate_estimate = GenerateEstimate::with('manager')->where('id', decodeID($estimateID))->with('opportunityid.client')->firstorfail();
        if (is_object($generate_estimate)) {
            // $combinedCollection = collect();
            // dd($generate_estimate);
            $estimateMaterial = EstimateMaterial::with('equipment')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateLabor = EstimateLabor::with('labor')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateHardMaterial = EstimateHardMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimatePlantMaterial = EstimatePlantMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateOtherCost = EstimateOtherCost::with('otherCost')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateItems = EstimateItem::where('generate_estimate_id', $generate_estimate->id)->get();

        } else {
            DB::commit();

            return response()->json(['message' => 'Invoice Not generated Successfully'], 400);
        }
        $Organization = User::where('id', getOrganizationId())->firstorfail();
        if (optional($Organization->stripeAccount)?->acc_connected == 1 &&
        optional($Organization->stripeAccount)?->acc_connected_id != null) {
            $pmode = 'online';
        } else {
            $pmode = 'cash';
        }
        // dd($pmode);

        $invoice = Invoice::create([
            'client_id' => $generate_estimate->opportunityid?->client?->id ?? '',
            'subject' => $request->subject,
            'organization_id' => getOrganizationId(),
            'status' => 'due',
            'invoice_number' => generateInvoiceNumber(getOrganizationId()),
            'issue_date' => Carbon::createFromFormat('Y-m-d', $issueDate)->format('Y-m-d'),
            'due_date' => $dueDate,
            'total' => str_replace(',', '', $generate_estimate->grand_total),
            'notes' => $request->notes,
            'is_sent' => 1,
            'operation_id' => $generate_estimate->id ?? null,
            'payment_mode' => $pmode,
        ]);
        // dd($generate_estimate->grand_total);

        $invoiceItems = collect();
        $estimateItems->each(function ($item) use ($invoiceItems, $invoice) {
            $invoiceItems->push([
                'invoice_id' => $invoice->id,
                'name' => $item->item_name,
                'uom' => $item->uom,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'total_price' => $item->total_price,
                // 'description' => $item->description,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        });

        // $estimateLabor->each(function ($item) use ($invoiceItems, $invoice) {
        //     $invoiceItems->push([
        //         'invoice_id' => $invoice->id,
        //         'name' => $item->labor?->name,
        //         'quantity' => $item->quantity,
        //         'uom' => $item->uom,
        //         'unit_price' => $item->grand_total / $item->quantity,
        //         'total_price' => $item->grand_total,
        //         'description' => $item->description,
        //         'created_at' => now(),
        //         'updated_at' => now()

        //     ]);
        // });

        // $estimatePlantMaterial->each(function ($item) use ($invoiceItems, $invoice) {
        //     $invoiceItems->push([
        //         'invoice_id' => $invoice->id,
        //         'name' => $item->material?->name,
        //         'quantity' => $item->quantity,
        //         'uom' => $item->uom,
        //         'unit_price' => $item->unit_cost + (getSaleTax() / 100) * $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost,
        //         'total_price' => $item->quantity * $item->unit_cost + (getSaleTax() / 100) * ($item->quantity * $item->unit_cost) + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost),
        //         'description' => $item->description,
        //         'created_at' => now(),
        //         'updated_at' => now()
        //     ]);
        // });

        // $estimateHardMaterial->each(function ($item) use ($invoiceItems, $invoice) {
        //     $invoiceItems->push([
        //         'invoice_id' => $invoice->id,
        //         'name' => $item->material?->name,
        //         'quantity' => $item->quantity,
        //         'uom' => $item->uom,
        //         'unit_price' => $item->unit_cost + (getSaleTax() / 100) * $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost,
        //         'total_price' => $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost) + (getSaleTax() / 100) * ($item->quantity * $item->unit_cost),
        //         'description' => $item->description,
        //         'created_at' => now(),
        //         'updated_at' => now()
        //     ]);
        // });

        // $estimateOtherCost->each(function ($item) use ($invoiceItems, $invoice) {
        //     $invoiceItems->push([
        //         'invoice_id' => $invoice->id,
        //         'name' => $item->otherCost?->name,
        //         'quantity' => $item->quantity,
        //         'uom' => $item->uom,
        //         'unit_price' => $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost,
        //         'total_price' => $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity),
        //         'description' => $item->description,
        //         'created_at' => now(),
        //         'updated_at' => now()
        //     ]);
        // });

        // $estimateSubContractor->each(function ($item) use ($invoiceItems, $invoice) {
        //     $invoiceItems->push([
        //         'invoice_id' => $invoice->id,
        //         'name' => $item->name,
        //         'quantity' => $item->quantity,
        //         'uom' => $item->uom,
        //         'unit_price' => $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost,
        //         'total_price' => $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity),
        //         'description' => $item->description,
        //         'created_at' => now(),
        //         'updated_at' => now()
        //     ]);
        // });

        $invoiceItems->chunk(10)->each(function ($chunk) {
            // Insert the prepared data into the database
            InvoiceItem::insert($chunk->toArray());
        });

        if ($request->exists('document')) {
            foreach ($request->input('document', []) as $file) {
                $filePath = public_path('storage/uploads/'.$file);
                if (file_exists($filePath)) {
                    $invoice->addMedia($filePath)->toMediaCollection('document');
                } else {
                }
            }
        }

        if ($request->has('sentEmail')) {
            $data = $request->except(['select_file', 'subject', 'issue_date', 'due_date', 'tax', 'discount', 'discount_method']);
            $this->emailInvoiceToClient($request, $invoice, $data);
        }

        // DB::commit();
        return response()->json(['message' => 'Invoice generated Successfully', 'route' => route(getRouteAlias().'.invoices.index')]);
        // } catch (Throwable $th) {
        //     DB::rollback();
        //     // dd($th->getMessage());
        //     return response()->json(['message' => 'Invoice Not generated Successfully'], 400);
        // }
    }

    private function emailInvoiceToClient(InvoiceRequest $request, $invoice, $data = [])
    {

        $path = public_path('clientEmailUploads').'/'.collect($request->input('send-email'))->first().'Invoice';
        $attachments = $request->select_file ?? [];
        $files = [];

        $path = storage_path('app/public/email_documents');
        // create folder
        if ($attachments != null) {
            if (count($attachments) > 0) {

                if (! File::exists($path)) {
                    File::makeDirectory($path, $mode = 0777, true, true);
                }

                foreach ($attachments as $attachment) {
                    // $name = $path . '/' . $attachment;
                    // $name = time() . '.' . $attachment->getClientOriginalExtension();
                    // $attachment->move($path, $name);
                    $files[] = $path.'/'.$attachment;
                }
            }
        }

        // create folder
        // if (count($attachments) > 0) {

        //     if (!File::exists($path)) {
        //         File::makeDirectory($path, $mode = 0777, true, true);
        //     }

        //     foreach ($attachments  as $attachment) {
        //         $name = time() . '.' . $attachment->getClientOriginalExtension();
        //         $attachment->move($path, $name);
        //         $files[] = $path . '/' . $name;
        //     }
        // }

        if (array_key_exists('copy', $data) && $data['copy'] == 'yes') {
            if (! array_key_exists('cc_email', $data)) {
                $data['cc_email'] = [];
            }
            $data['cc_email'] = array_unique([...$data['cc_email'], auth()->user()->email]);
        }

        $data['files'] = $files;
        // dd($files);

        $invoice->load(['organization.companyPropertyAddress', 'client']);
        $invoice->url = route('client.invoice.view', [encodeID($invoice->organization_id), encodeID($invoice->id)]);
        $email_template = 'email_template.client.invoice-notify';
        $payload['invoice'] = $invoice;
        $payload['invoice_url'] = $invoice->url;
        $payload['org_image'] = $invoice->organization->profile_photo_path ? asset('storage/user_images/'.$invoice->organization->profile_photo_path) : null;
        if ($invoice->client->invite_sent == 0) {
            $url = url('client/'.encodeID($invoice->client->organization_id).'/login');
            $payload['invoice_url'] = $url;
            $payload['email'] = $invoice->client->email;
            $password = 'Client@'.uniqid();
            $payload['password'] = $password;
            $payload['enc_password'] = Hash::make($password);
        }

        $payload['company_website'] = $invoice->client->website;
        $payload['company_email'] = $invoice->client->address;
        $payload['company_phone_no'] = $invoice->client->mobile_no;
        $payload['primary_color'] = $invoice->organization->primary_color;
        $payload['company_address'] = $invoice->client->address;
        $payload['subject'] = $data['send-subject'];
        $payload['message'] = $data['send-message'];
        $payload['email'] = $invoice->client->email;
        $payload['company_name'] = $invoice->organization->company_name;
        $payload['cc_email'] = (array_key_exists('cc_email', $data)) ? $data['cc_email'] : [];
        $payload['bcc_email'] = (array_key_exists('bcc_email', $data)) ? $data['bcc_email'] : [];
        $payload['sendAttachment'] = array_key_exists('invoiceAttachment', $data);
        $payload['estimateAttachment'] = array_key_exists('estimateAttachment', $data);
        $payload['files'] = $data['files'];
        // dispatch(new sendInvoiceEmail($invoice, $email_template, $payload, $path));
        $this->sendInvoiceEmail($invoice, $email_template, $payload, $path);

    }

    public function sendInvoiceEmail($invoice, $email_template, $payload, $path = null)
    {
        try {
            $data = $payload;

            if (array_key_exists('message', $data)) {
                $data['message'] = '<h3 class="invoice_number" style="font-style: normal; font-weight: 600; font-size: 24px; line-height: 33.6px; color: rgb(25, 42, 62);">Invoice# '.$invoice->invoice_number.' </h3>'.$data['message'];
            }

            $payload = ['payload' => $data];
            $pdf = null;

            if (array_key_exists('sendAttachment', $data) && $data['sendAttachment'] == true) {
                $pdf = InvoiceService::preparePdf($invoice, $invoice->organization_id);
            }

            $estimatePDF = null;

            if (array_key_exists('estimateAttachment', $data) && $data['estimateAttachment'] == true) {
                $invoice->loadMissing('operation');

                if ($invoice->operation) {
                    $invoice->operation->loadMissing('request');
                    $estimatePDF = EstimateService::preparePdf($invoice->operation?->request->id, $invoice->organization_id);

                    if (is_array($estimatePDF)) {
                        $estimatePDF = $estimatePDF['pdf'];
                    } else {
                        $estimatePDF = null;
                    }
                }
            }

            // Send Email
            Mail::send($email_template, $payload, function ($message) use ($data, $pdf, $estimatePDF, $invoice) {
                if (array_key_exists('cc_email', $data) && count($data['cc_email']) > 0) {
                    $message->cc($data['cc_email']);
                }

                if (array_key_exists('bcc_email', $data) && count($data['bcc_email']) > 0) {
                    $message->bcc($data['bcc_email']);
                }

                $message->to($data['email'], $data['email'])
                    ->subject($data['subject']);

                // Attach Invoice PDF
                if ($pdf != null) {

                    $message->attachData($pdf->output(), 'Invoice # '.$invoice->invoice_number.'.pdf');
                }

                // Attach Estimate PDF

                if ($estimatePDF != null) {
                    $message->attachData($estimatePDF->output(), 'Estimate # '.optional($invoice->operation)?->request?->sales_order_number.'.pdf');
                }

                // Attach External Files
                if (array_key_exists('files', $data) && count($data['files']) > 0) {
                    foreach ($data['files'] as $file) {
                        if (File::exists($file)) {
                            $message->attach($file);
                        }
                    }
                }
            });

            // Update Client Password if provided
            if (array_key_exists('password', $payload)) {
                $invoice->client->update([
                    'invite_sent' => 1,
                    'password' => $payload['enc_password'],
                ]);
            }

            // Cleanup Temporary Files
            if (! empty($path)) {
                if (File::exists($path)) {
                    File::deleteDirectory($path);
                }
            }
        } catch (\Exception $e) {
            info('Invoice PDF Send Failed', [$e->getMessage()]);
            throw new \Exception('Email sending failed: '.$e->getMessage());
        }
    }
}
