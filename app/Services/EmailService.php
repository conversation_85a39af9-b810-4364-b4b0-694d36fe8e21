<?php

namespace App\Services;

use App\Jobs\EmailServiceJob;
use Illuminate\Support\Facades\Mail;

class EmailService
{
    public static function send($templateName, $subject, $to, $payload = [], $queue = false, $files = []): bool
    {

        // If Mail send using queue

        if ($queue) {
            EmailServiceJob::dispatch($templateName, $subject, $to, $payload, $files);

            // dd($queue);
            return true;
        }

        Mail::send($templateName, ['payload' => $payload], function ($message) use ($subject, $to) {
            $message->to($to)->subject($subject);
            $message->from(config('mail.from.address'), config('mail.from.name'));
        });

        return ! Mail::flushMacros();
    }
}
