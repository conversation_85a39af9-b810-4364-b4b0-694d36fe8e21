<?php

namespace App\Services;

use App\Models\Estimate;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class EstimateRequestService
{
    /**
     * Render Yajra DataTable
     *
     * @return YajraDataTable
     */
    public static function renderTableData(Request $request)
    {

        $estimates = Estimate::with(['propertyAddress:property_name,estimate_id,type', 'client:id,first_name,last_name,mobile_no,image', 'saleMan:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
            ->where('client_show', 0)
            ->where('organization_id', getOrganizationId())
            ->when(request('status'), function ($query) {
                if (request('status') == 'open') {
                    $query->where('is_active', 1);
                } elseif (request('status') == 'close') {
                    $query->where('is_active', 0);
                } elseif (request('status') == 'pending') {
                    $query->where('is_active', 2);
                }
            })->latest()
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })
            ->select('job_no as er_no', 'client_id', 'id', 'project_name', 'created_at', 'is_active', 'estimator_id', 'sale_person_id');

        return DataTables::eloquent($estimates)
            ->filterColumn('sale_man.name', function ($query, $keyword) {
                $query->wherehas('saleMan', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('er_no', function ($query, $keyword) {
                $query->where('job_no', 'LIKE', '%'.$keyword.'%');
            })->filterColumn('estimator.name', function ($query, $keyword) {
                $query->wherehas('estimator', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('client.full_name', function ($query, $keyword) {
                $query->wherehas('client', function ($q) use ($keyword) {
                    $sql = "CONCAT(clients.first_name,' ',clients.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('propertyAddress.property_name', function ($query, $keyword) {
                $query->wherehas('propertyAddress', function ($q) use ($keyword) {
                    $q->where('property_name', 'LIKE', '%'.$keyword.'%');
                });
            })

            ->addColumn('client.full_name', function (Estimate $estimate) {
                if ($estimate->client?->image) {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('storage/client_images/'.$estimate->client?->image).'" alt="profile image">
                        <h2 class="profile_name">'.$estimate->client?->full_name.'</h2>
                    </div>';
                } else {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset('admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name" style="white-space:nowrap;">'.$estimate->client?->full_name.'</h2>
                    </div>';
                }
            })
            ->addColumn('sale_man.name', function (Estimate $estimate) {
                if ($estimate->saleMan) {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset(optional($estimate->saleMan)?->image ? 'storage/user_images/'.$estimate->saleMan?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name">'.$estimate->saleMan?->name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->addColumn('estimator.name', function (Estimate $estimate) {
                if ($estimate->estimator) {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset(optional($estimate->estimator)?->image ? 'storage/user_images/'.$estimate->estimator?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name">'.$estimate->estimator?->name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->addColumn('status', function (Estimate $estimate) {
                if ($estimate->is_active == 1) {
                    return ' <div class="status success"> Open</div>';
                } elseif ($estimate->is_active == 0) {
                    return ' <div class="status warning">Closed</div>';
                } else {
                    return ' <div class="status primary">Pending</div>';
                }
            })
            ->editColumn('created_at', function (Estimate $estimate) {
                return customDateFormat(\Carbon\Carbon::parse($estimate->created_at)).'-'.customTimeFormat(\Carbon\Carbon::parse($estimate->created_at));
            })->addColumn('propertyAddress.property_name', function (Estimate $estimate) {
                return $estimate->propertyAddress->property_name ?? 'name';
            })->addColumn('action', function (Estimate $estimate) {
                $user = auth('web')->user();

                if (! $user->canany(['edit_estimate_request', 'generate_estimate', 'view_estimate_documents', 'delete_estimate_request'])) {
                    return '';
                }

                $html = ' <div class="dropdown mx-auto w-fit">
                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                        <img height="24px" width="24px"
                            src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown1">';

                if ($user->can('generate_estimate')) {
                    $html .= '<li><a class="dropdown-item"   href="'.route(getRouteAlias().'.create.estimate-generate', encodeId($estimate->id)).'">Generate Estimate</a></li>';
                }
                if ($user->can('edit_estimate_request')) {
                    $html .= '<li><a class="dropdown-item"
                        href="'.route(getRouteAlias().'.estimate.edit', encodeId($estimate->id)).'">Edit
                        Request</a></li>';
                }
                if ($user->can('view_estimate_documents')) {
                    $html .= '
                            <li>  <a class="dropdown-item" id="attachedDocuments"
                            data-value='.encodeId($estimate->id).'>
                            View Documents
                        </a></li>';
                }
                if ($estimate->created_by == 'company' && $user->can('delete_estimate_request')) {
                    $html .= '<li>
                                    <a class="dropdown-item" id="deleteRequest" data-toggle="modal" data-target="#DeleteModal"
                                        data-value='.encodeId($estimate->id).'>
                                        Delete Request
                                    </a>
                                </li>';
                }
                $html .= '</ul>
                </div>';

                return $html;
            })
            ->rawColumns(['status', 'action', 'client.full_name', 'sale_man.name', 'estimator.name', 'created_at'])
            ->only(['er_no', 'propertyAddress.property_name', 'project_name', 'client.full_name', 'client.mobile_no', 'sale_man.name', 'estimator.name', 'created_at', 'status', 'action'])
            ->toJson();
    }
}
