<?php

namespace App\Services;

use App\Models\ClientEstimateAction;
use App\Models\Estimate;
use App\Models\EstimateHardMaterial;
use App\Models\EstimateItem;
use App\Models\EstimateLabor;
use App\Models\EstimateMaterial;
use App\Models\EstimateOtherCost;
use App\Models\EstimatePlantMaterial;
use App\Models\EstimateStatus;
use App\Models\EstimateSubContractor;
use App\Models\GenerateEstimate;
use App\Models\Labor;
use App\Models\Opportunity;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class EstimateService
{
    /**
     * Render Yajra DataTable
     *
     * @return YajraDataTable
     */
    public static function renderTableDate(Request $request)
    {

        $estimates = Opportunity::with(['generateEstimate.clientAction:generate_estimate_id,status', 'account:id,company_name,mobile_no', 'salesman:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
            ->where('organization_id', getOrganizationId())
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })
            ->whereHas('generateEstimate', function ($q) {
                $q->where('is_complete', true)->where('is_archive', request()->route()->getName() == (getRouteAlias().'.archive.index') ? 1 : 0)->where('total_price', '>', 0);
            })->when(request('status'), function ($query) {
                $query->whereHas('generateEstimate', function ($q) {
                    if (request('status') == 'proposed') {
                        $q->where('status', 'proposed')->whereNot('client_status', 'request_change');
                    } elseif (request('status') == 'won') {
                        $q->where('status', 'won');
                    } elseif (request('status') == 'revision') {
                        $q->where('status', 'proposed')->where('client_status', 'request_change');
                    } elseif (request('status') == 'lost') {
                        $q->where('status', 'lost');
                    }
                });
            })->latest()->select('id', 'opportunity_name', 'id as er_no', 'account_id', 'sale_person_id', 'estimator_id');

        return DataTables::eloquent($estimates)

            ->filterColumn('er_no', function ($query, $keyword) {
                $query->where('id', 'LIKE', '%'.$keyword.'%');
            })
            ->filterColumn('sale_man.name', function ($query, $keyword) {
                $query->wherehas('salesman', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })->filterColumn('estimator.name', function ($query, $keyword) {
                $query->wherehas('estimator', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('client.full_name', function ($query, $keyword) {
                // dd($query->account_id);
                $query->wherehas('account', function ($q) use ($keyword) {
                    $q->where('accounts.company_name', 'LIKE', ["%$keyword%"]);
                });
                // $query->where('acccompany_name', 'LIKE', '%' . $keyword . '%');

            })
            ->addColumn('generateEstimate.status', function (Opportunity $estimate) {
                if (optional($estimate->generateEstimate)?->clientAction?->status && $estimate->generateEstimate->status == 'proposed') {
                    if ($estimate->generateEstimate->clientAction?->status == 'reject') {
                        return ' <div class="status danger">'.ucfirst($estimate->generateEstimate->clientAction?->status).'</div>';
                    } elseif ($estimate->generateEstimate->clientAction?->status == 'approve') {
                        return ' <div class="status success">'.ucfirst($estimate->generateEstimate->clientAction?->status).'</div>';
                    } else {
                        return ' <div class="status primary">Revision</div>';
                    }
                } else {
                    if ($estimate->generateEstimate->status == 'lost') {
                        return ' <div class="status danger">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    } elseif ($estimate->generateEstimate->status == 'won') {
                        return ' <div class="status success">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    } else {
                        return ' <div class="status warning">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    }
                }
            })
            ->editColumn('client.full_name', function (Opportunity $estimate) {
                // dd($estimate->account);
                if ($estimate->account) {
                    return '
                        <div class="table_profile">
                        <h2 class="profile_name">'.$estimate->account->company_name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">N/A </span>';
                }

            })
            ->editColumn('sale_man.name', function (Opportunity $estimate) {
                // dd($estimate->salesman);
                if ($estimate->salesman) {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset(optional($estimate->salesman)?->image ? 'storage/user_images/'.$estimate->salesman?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name">'.$estimate->salesman?->name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->editColumn('generateEstimate.total_cost', function (Opportunity $estimate) {
                return optional($estimate->generateEstimate)?->total_cost ?? '---';
            })
            ->editColumn('opportunity_name', function (Opportunity $estimate) {
                return $estimate->opportunity_name ?? '---';
            })
            ->editColumn('generateEstimate.total_price', function (Opportunity $estimate) {
                return optional($estimate->generateEstimate)?->total_price ?? '---';
            })
            ->editColumn('estimator.name', function (Opportunity $estimate) {
                if ($estimate->estimator) {
                    return '
                    <div class="table_profile">

                    <img class="image" height="24px" width="24px"
                        src="'.asset(optional($estimate->estimator)?->image ? 'storage/user_images/'.$estimate->estimator?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                    <h2 class="profile_name">'.$estimate->estimator?->name.'</h2>
                </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->editColumn('propertyAddress.property_name', function (Opportunity $estimate) {
                return $estimate->opportunity_name ?? '';
            })
            // ->editColumn('generateEstimate.total_cost', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->total_cost) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->total_cost);
            //     } else {
            //         return '---';
            //     }
            // })->editColumn('generateEstimate.total_price', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->grand_total && $estimate->generateEstimate?->grand_total > 0) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->grand_total);
            //     } else if ($estimate->generateEstimate?->total_price) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->total_price);
            //     } else {
            //         return '---';
            //     }
            // })
            // ->editColumn('generateEstimate.notes', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->notes) {
            //         return '<button class="svg-light viewNoteBtn svg-yellow" style="border: none;background: none;" data-id="' . $estimate->generateEstimate?->id . '">
            //             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            //                 <path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z" fill="#E7E7E7"/>
            //             </svg>
            //         </button>';
            //     } else {
            //         return '<span class="svg-light svg-light">
            //             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            //                 <path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z" fill="#E7E7E7"/>
            //             </svg>
            //         </span>';
            //     }
            // })
            ->addColumn('action', function (Opportunity $estimate) {

                $user = auth('web')->user();

                if (! $user->canany(['change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'can_archive'])) {
                    return '';
                }

                if ($user->can('can_archive') && (request()->route()->getName() == (getRouteAlias().'.archive.index'))) {
                    $html = ' <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li>
                                <a class="dropdown-item"
                                        href="'.route(getRouteAlias().'.archive.unarchive', encodeId(optional($estimate->generateEstimate)?->id)).'">Unarchive
                                    </a>
                                </li>
                                <li>
                                <a class="dropdown-item"
                                        href="'.route(getRouteAlias().'.estimate.invoice-detail', encodeId($estimate->id)).'">View Details
                                    </a>
                                </li>

                            </ul>
                        </div>';

                    return $html;
                }

                $html = ' <div class="dropdown mx-auto w-fit">
                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                        <img height="24px" width="24px"
                            src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown1">';

                if ($user->can('change_estimate_status')) {
                    $html .= '<li>
                        <a class="dropdown-item" id="changeStatus" data-suggestion="'.$estimate->generateEstimate->suggestion.'" data-reason="'.$estimate->generateEstimate->reason.'" data-value="'.$estimate->generateEstimate->status.'" data-id="'.encodeID($estimate->id).'" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>';
                }

                if ($user->can('view_estimate_change_request') && optional($estimate->generateEstimate)?->clientAction?->status == 'request_change') {
                    $html .= '<li><a class="dropdown-item changeRequestBtn" data-id="'.encodeID($estimate->id).'" style="cursor:pointer">View Change Request
                            </a></li>';
                }

                if ($user->can('can_archive') && ! optional($estimate->generateEstimate)?->clientAction && optional($estimate->generateEstimate)?->status == 'proposed') {
                    $html .= '<li><a class="dropdown-item archiveBtn" data-id="'.encodeID($estimate->generateEstimate->id).'" style="cursor:pointer">Archive</a></li>';
                }
                if ($user->can('estimate_detail')) {
                    $html .= '<li><a class="dropdown-item"
                                href="'.route(getRouteAlias().'.organization.opportunityestimation', encodeId($estimate->generateEstimate->opportunity_id)).'">View Details
                                </a></li>';
                }

                $html .= '
                    </ul>
                </div>';

                if (strpos($html, '<li>') === false) {
                    $html = '';
                }

                return $html;

                //     <li>
                //     <a class="dropdown-item" id="deleteRequest" data-toggle="modal" data-target="#DeleteModal"
                //         data-value=' . encodeId($estimate->generateEstimate->id) . '>
                //         Delete Estimate
                //     </a>
                // </li>
            })

            ->rawColumns(['generateEstimate.status', 'action', 'client.full_name', 'sale_man.name', 'estimator.name', 'generateEstimate.notes'])
            ->only([
                'er_no', 'opportunity_name', 'client.full_name', 'sale_man.name', 'estimator.name', 'generateEstimate.total_cost', 'generateEstimate.total_price',
                'generateEstimate.notes', 'generateEstimate.status', 'action',
            ])
            ->toJson();
    }

    public static function renderTableArchive(Request $request)
    {

        $estimates = Opportunity::with(['generateEstimate.clientAction:generate_estimate_id,status', 'account:id,company_name,mobile_no', 'salesman:id,image,first_name,last_name', 'estimator:id,image,first_name,last_name'])
            ->where('organization_id', getOrganizationId())
            ->where(function ($query) {
                employeeAssociatedDateQuery($query);
            })
            ->whereHas('generateEstimate', function ($q) {
                $q->where('is_complete', true)->where('is_archive', 1)->where('total_price', '>', 0);
            })->when(request('status'), function ($query) {
                $query->whereHas('generateEstimate', function ($q) {
                    if (request('status') == 'proposed') {
                        $q->where('status', 'proposed')->whereNot('client_status', 'request_change');
                    } elseif (request('status') == 'won') {
                        $q->where('status', 'won');
                    } elseif (request('status') == 'revision') {
                        $q->where('status', 'proposed')->where('client_status', 'request_change');
                    } elseif (request('status') == 'lost') {
                        $q->where('status', 'lost');
                    }
                });
            })->latest()->select('id', 'opportunity_name', 'id as er_no', 'account_id', 'sale_person_id', 'estimator_id');

        return DataTables::eloquent($estimates)

            ->filterColumn('er_no', function ($query, $keyword) {
                $query->where('id', 'LIKE', '%'.$keyword.'%');
            })
            ->filterColumn('sale_man.name', function ($query, $keyword) {
                $query->wherehas('salesman', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })->filterColumn('estimator.name', function ($query, $keyword) {
                $query->wherehas('estimator', function ($q) use ($keyword) {
                    $sql = "CONCAT(users.first_name,' ',users.last_name)  like ?";
                    $q->whereRaw($sql, ["%{$keyword}%"]);
                });
            })
            ->filterColumn('client.full_name', function ($query, $keyword) {
                // dd($query->account_id);
                $query->wherehas('account', function ($q) use ($keyword) {
                    $q->where('accounts.company_name', 'LIKE', ["%$keyword%"]);
                });
                // $query->where('acccompany_name', 'LIKE', '%' . $keyword . '%');

            })
            ->addColumn('generateEstimate.status', function (Opportunity $estimate) {
                if (optional($estimate->generateEstimate)?->clientAction?->status && $estimate->generateEstimate->status == 'proposed') {
                    if ($estimate->generateEstimate->clientAction?->status == 'reject') {
                        return ' <div class="status danger">'.ucfirst($estimate->generateEstimate->clientAction?->status).'</div>';
                    } elseif ($estimate->generateEstimate->clientAction?->status == 'approve') {
                        return ' <div class="status success">'.ucfirst($estimate->generateEstimate->clientAction?->status).'</div>';
                    } else {
                        return ' <div class="status primary">Revision</div>';
                    }
                } else {
                    if ($estimate->generateEstimate->status == 'lost') {
                        return ' <div class="status danger">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    } elseif ($estimate->generateEstimate->status == 'won') {
                        return ' <div class="status success">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    } else {
                        return ' <div class="status warning">'.ucfirst($estimate->generateEstimate->status).'</div>';
                    }
                }
            })
            ->editColumn('client.full_name', function (Opportunity $estimate) {
                // dd($estimate->account);
                if ($estimate->account) {
                    return '
                        <div class="table_profile">
                        <h2 class="profile_name">'.$estimate->account->company_name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">N/A </span>';
                }

            })
            ->editColumn('sale_man.name', function (Opportunity $estimate) {
                // dd($estimate->salesman);
                if ($estimate->salesman) {
                    return '
                        <div class="table_profile">

                        <img class="image" height="24px" width="24px"
                            src="'.asset(optional($estimate->salesman)?->image ? 'storage/user_images/'.$estimate->salesman?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                        <h2 class="profile_name">'.$estimate->salesman?->name.'</h2>
                    </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->editColumn('generateEstimate.total_cost', function (Opportunity $estimate) {
                return optional($estimate->generateEstimate)?->total_cost ?? '---';
            })
            ->editColumn('opportunity_name', function (Opportunity $estimate) {
                return $estimate->opportunity_name ?? '---';
            })
            ->editColumn('generateEstimate.total_price', function (Opportunity $estimate) {
                return optional($estimate->generateEstimate)?->total_price ?? '---';
            })
            ->editColumn('estimator.name', function (Opportunity $estimate) {
                if ($estimate->estimator) {
                    return '
                    <div class="table_profile">

                    <img class="image" height="24px" width="24px"
                        src="'.asset(optional($estimate->estimator)?->image ? 'storage/user_images/'.$estimate->estimator?->image : 'admin_assets/images/dummy_image.webp').'" alt="profile image">
                    <h2 class="profile_name">'.$estimate->estimator?->name.'</h2>
                </div>';
                } else {
                    return '<span style="color:#90A0B7">Not Assign </span>';
                }
            })
            ->editColumn('propertyAddress.property_name', function (Opportunity $estimate) {
                return $estimate->opportunity_name ?? '';
            })
            // ->editColumn('generateEstimate.total_cost', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->total_cost) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->total_cost);
            //     } else {
            //         return '---';
            //     }
            // })->editColumn('generateEstimate.total_price', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->grand_total && $estimate->generateEstimate?->grand_total > 0) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->grand_total);
            //     } else if ($estimate->generateEstimate?->total_price) {
            //         return  '$' . custom_number_format($estimate->generateEstimate->total_price);
            //     } else {
            //         return '---';
            //     }
            // })
            // ->editColumn('generateEstimate.notes', function (Opportunity $estimate) {
            //     if ($estimate->generateEstimate?->notes) {
            //         return '<button class="svg-light viewNoteBtn svg-yellow" style="border: none;background: none;" data-id="' . $estimate->generateEstimate?->id . '">
            //             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            //                 <path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z" fill="#E7E7E7"/>
            //             </svg>
            //         </button>';
            //     } else {
            //         return '<span class="svg-light svg-light">
            //             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            //                 <path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H14.6906C14.8876 21.0002 15.0826 20.9613 15.2644 20.8857C15.4462 20.8101 15.6113 20.6992 15.75 20.5594L20.5594 15.75C20.6992 15.6113 20.8101 15.4462 20.8857 15.2644C20.9613 15.0826 21.0002 14.8876 21 14.6906V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM9 8.25H15C15.1989 8.25 15.3897 8.32902 15.5303 8.46967C15.671 8.61032 15.75 8.80109 15.75 9C15.75 9.19891 15.671 9.38968 15.5303 9.53033C15.3897 9.67098 15.1989 9.75 15 9.75H9C8.80109 9.75 8.61032 9.67098 8.46967 9.53033C8.32902 9.38968 8.25 9.19891 8.25 9C8.25 8.80109 8.32902 8.61032 8.46967 8.46967C8.61032 8.32902 8.80109 8.25 9 8.25ZM12 15.75H9C8.80109 15.75 8.61032 15.671 8.46967 15.5303C8.32902 15.3897 8.25 15.1989 8.25 15C8.25 14.8011 8.32902 14.6103 8.46967 14.4697C8.61032 14.329 8.80109 14.25 9 14.25H12C12.1989 14.25 12.3897 14.329 12.5303 14.4697C12.671 14.6103 12.75 14.8011 12.75 15C12.75 15.1989 12.671 15.3897 12.5303 15.5303C12.3897 15.671 12.1989 15.75 12 15.75ZM9 12.75C8.80109 12.75 8.61032 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61032 11.329 8.80109 11.25 9 11.25H15C15.1989 11.25 15.3897 11.329 15.5303 11.4697C15.671 11.6103 15.75 11.8011 15.75 12C15.75 12.1989 15.671 12.3897 15.5303 12.5303C15.3897 12.671 15.1989 12.75 15 12.75H9ZM15 19.1906V15H19.1906L15 19.1906Z" fill="#E7E7E7"/>
            //             </svg>
            //         </span>';
            //     }
            // })
            ->addColumn('action', function (Opportunity $estimate) {

                $user = auth('web')->user();

                if (! $user->canany(['change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'can_archive'])) {
                    return '';
                }

                if ($user->can('can_archive')) {
                    $html = ' <div class="dropdown mx-auto w-fit">
                            <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                                <img height="24px" width="24px"
                                    src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                            </div>
                            <ul class="dropdown-menu" aria-labelledby="dropdown1">
                            <li>
                                <a class="dropdown-item"
                                        href="'.route(getRouteAlias().'.archive.unarchive', encodeId(optional($estimate->generateEstimate)?->id)).'">Unarchive
                                    </a>
                                </li>
                                <li>
                                <a class="dropdown-item"
                                        href="'.route(getRouteAlias().'.organization.opportunityestimation', encodeId($estimate->id)).'">View Details
                                    </a>
                                </li>

                            </ul>
                        </div>';

                    return $html;
                }

                $html = ' <div class="dropdown mx-auto w-fit">
                    <div id="dropdown1" data-toggle="dropdown" aria-expanded="false">
                        <img height="24px" width="24px"
                            src="'.asset('admin_assets/images/icons/vertical-dots.svg').'" alt="vertical dots">
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdown1">';

                if ($user->can('change_estimate_status')) {
                    $html .= '<li>
                        <a class="dropdown-item" id="changeStatus" data-suggestion="'.$estimate->generateEstimate->suggestion.'" data-reason="'.$estimate->generateEstimate->reason.'" data-value="'.$estimate->generateEstimate->status.'" data-id="'.encodeID($estimate->id).'" data-toggle="modal"
                                        data-target="#editTableModal">Change Status</a></li>';
                }

                if ($user->can('view_estimate_change_request') && optional($estimate->generateEstimate)?->clientAction?->status == 'request_change') {
                    $html .= '<li><a class="dropdown-item changeRequestBtn" data-id="'.encodeID($estimate->id).'" style="cursor:pointer">View Change Request
                            </a></li>';
                }

                if ($user->can('can_archive') && ! optional($estimate->generateEstimate)?->clientAction && optional($estimate->generateEstimate)?->status == 'proposed') {
                    $html .= '<li><a class="dropdown-item archiveBtn" data-id="'.encodeID($estimate->generateEstimate->id).'" style="cursor:pointer">Archive</a></li>';
                }
                if ($user->can('estimate_detail')) {
                    $html .= '<li><a class="dropdown-item"
                                href="'.route(getRouteAlias().'.organization.opportunityestimation', encodeId($estimate->generateEstimate->opportunity_id)).'">View Details
                                </a></li>';
                }

                $html .= '
                    </ul>
                </div>';

                if (strpos($html, '<li>') === false) {
                    $html = '';
                }

                return $html;

                //     <li>
                //     <a class="dropdown-item" id="deleteRequest" data-toggle="modal" data-target="#DeleteModal"
                //         data-value=' . encodeId($estimate->generateEstimate->id) . '>
                //         Delete Estimate
                //     </a>
                // </li>
            })

            ->rawColumns(['generateEstimate.status', 'action', 'client.full_name', 'sale_man.name', 'estimator.name', 'generateEstimate.notes'])
            ->only([
                'er_no', 'opportunity_name', 'client.full_name', 'sale_man.name', 'estimator.name', 'generateEstimate.total_cost', 'generateEstimate.total_price',
                'generateEstimate.notes', 'generateEstimate.status', 'action',
            ])
            ->toJson();
    }

    /**
     * Prepare Estimate Invoice Detail Data
     *
     * @param  \App\Models\Estimate  $id
     * @return response
     */
    public static function getEstimateInvoiceDetail($id)
    {
        $generate_estimate = GenerateEstimate::where('opportunity_id', decodeID($id))->first();
        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateLabor = EstimateLabor::with('labor')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateHardMaterial = EstimateHardMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimatePlantMaterial = EstimatePlantMaterial::with('material')->where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateSubContractor = $estimateOtherCost = $estimatePlantMaterial = $estimateHardMaterial = $estimateLabor = $estimateMaterial = collect();
            $generate_estimate = GenerateEstimate::create([
                'opportunity_id' => decodeId($id),
                'is_complete' => true,
            ]);
        }

        // info("data test 1",['estimateSubContractor' => $estimateSubContractor , 'estimateOtherCost' => $estimateOtherCost , 'estimatePlantMaterial'=> $estimatePlantMaterial , '$estimateHardMaterial' => $estimateHardMaterial , '$estimateLabor' =>  $estimateLabor , '$estimateMaterial' => $estimateMaterial]);

        $totalPayable = 0;
        $totalCost = 0;
        $laborBurden = (int) DB::table('labors')->where('organization_id', getOrganizationId())->value('labor_burden') ?? 0;

        $estimateMaterial = $estimateMaterial->map(function ($item) use (&$totalPayable, &$totalCost) {
            $item['unit_price'] = ($item->cost ?? $item->equipment->cost) + ($item->gross_margin / 100) * ($item->cost ?? $item->equipment->cost);
            $item['total_price'] = $item->unit_price * $item->quantity;
            $totalPayable += $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
            $totalCost += ($item->cost ?? $item->equipment->cost) * $item->quantity;

            return $item;
        });

        $estimateLabor->map(function ($item) use (&$totalPayable, &$totalCost, $laborBurden) {
            if ($laborBurden) {
                $totalPayable += totalPriceWithLaborBurden($item->quantity, $item->unit_cost, $item->gross_margin, $laborBurden);
            } else {
                $totalPayable += $item->total_cost + ($item->gross_margin / 100) * $item->total_cost;
            }
            $totalCost += $item->total_cost;
            // $totalCost += $item->total_cost * ($laborBurden / 100);
        });

        $estimatePlantMaterial = $estimatePlantMaterial->map(function ($item) use (&$totalPayable, &$totalCost) {
            // $total_price = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
            $total_price = str_replace(',', '', custom_number_format(totalPriceWithGrossMargin($item->total_cost, $item->gross_margin)));
            $item['unit_price'] = $total_price / $item->quantity;
            $item['total_price'] = $total_price;
            $totalPayable += $total_price;
            $totalCost += $item->total_cost;

            // $totalCost += $item->quantity * $item->unit_cost * (getSaleTax() / 100);

            return $item;
        });
        // dd($totalCost);

        $estimateHardMaterial = $estimateHardMaterial->map(function ($item) use (&$totalPayable, &$totalCost) {
            // $total_price = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin);
            $total_price = str_replace(',', '', custom_number_format(totalPriceWithGrossMargin($item->total_cost, $item->gross_margin)));
            $item['unit_price'] = $total_price / $item->quantity;
            $item['total_price'] = $total_price;
            $totalPayable += $total_price;
            $totalCost += $item->total_cost;
            // $totalCost += $item->quantity * $item->unit_cost;
            // $totalCost += $item->quantity * $item->unit_cost * (getSaleTax() / 100);

            return $item;
        });
        $estimateOtherCost = $estimateOtherCost->map(function ($item) use (&$totalPayable, &$totalCost) {
            $item['unit_price'] = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost;
            $item['total_price'] = $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity);
            $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
            $totalCost += $item->unit_cost * $item->quantity;

            return $item;
        });

        $estimateSubContractor = $estimateSubContractor->map(function ($item) use (&$totalPayable, &$totalCost) {
            $item['unit_price'] = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost;
            $item['total_price'] = $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity);
            $totalPayable += $item->quantity * $item->unit_cost + ($item->gross_margin / 100) * ($item->quantity * $item->unit_cost);
            $totalCost += $item->quantity * $item->unit_cost;

            return $item;
        });

        // Calculate the Labor Hours

        $laborHours = (
            $labourQuantityHours = $estimateLabor->sum(function ($item) {
                return $item->quantity ? (float) $item->quantity : 0;
            })
            +
            ($estimatePlantMaterial->count() > 0
                ? $estimatePlantMaterial->map(function ($item) {
                    // Calculate the total_cost by adding quantity and material install
                    // return $item->quantity + ($item->material ? $item->material->install : 0);
                    return $item->material ? $item->material->install * $item->quantity : 0;
                })->sum()
                : 0)
            +
            ($estimateHardMaterial->count() > 0
                ? $estimateHardMaterial->map(function ($item) {
                    // Calculate the total_cost by adding quantity and material labor
                    // return $item->quantity + ($item->material ? $item->material->labor : 0);
                    return $item->material ? $item->material->labor * $item->quantity : 0;
                })->sum()
                : 0)
            -
            $superVisionHours = $estimateLabor->sum(function ($item) {
                return $item->labor->name === 'Supervision' ? (float) $item->quantity : 0;
            })
        );

        // Calculate Supervision Cost

        $superVisionHours = $estimateLabor->sum(function ($item) {
            return $item->labor->name === 'Supervision' ? (float) $item->quantity : 0;
        });

        // $totalCost +=  $superVisionHours;

        // Calculate Material Cost
        $materialCost = $estimatePlantMaterial->map(function ($item) {
            // return $item->total_cost = $item->quantity * $item->unit_cost;
            return $item->total_cost = $item->total_cost;
        })->sum() +
            $estimateHardMaterial->map(function ($item) {
                // return $item->total_cost = $item->quantity * $item->unit_cost;
                return $item->total_cost = $item->total_cost;
            })->sum() ??
            0;

        // Calculate labor Cost (Plant and Hard Material Labor Cost)'
        $PlantHardlaborCost = $estimatePlantMaterial->map(function ($item) {
            // return $item->total_cost = $item->quantity * $item->unit_cost;
            return $item->total_labor_cost = $item->total_labor_cost;
        })->sum() +
            $estimateHardMaterial->map(function ($item) {
                // return $item->total_cost = $item->quantity * $item->unit_cost;
                return $item->total_labor_cost = $item->total_labor_cost;
            })->sum() ??
            0;

        // Calculate labor Cost (Plant and Hard Material Labor Cost)'
        $laborCost = $estimatePlantMaterial->map(function ($item) {
            // return $item->total_cost = $item->quantity * $item->unit_cost;
            return $item->total_labor_cost = $item->total_labor_cost;
        })->sum() +
            $estimateHardMaterial->map(function ($item) {
                // return $item->total_cost = $item->quantity * $item->unit_cost;
                return $item->total_labor_cost = $item->total_labor_cost;
            })->sum() +
            $estimateLabor->map(function ($item) {
                return $item->total_cost ? $item->total_cost : 0;
            })->sum() ??
            0;

        // Calculate Other Job Cost
        $otherJobCost = $estimateOtherCost->map(function ($item) {
            return $item->total_cost = $item->unit_cost * $item->quantity;
        })->sum() ?? 0;

        // Calculate Sub Contractor Cost
        $subContractorCost = $estimateSubContractor->map(function ($item) {
            return $item->total_cost = $item->unit_cost * $item->quantity;
        })->sum() ?? 0;

        // Calculate Desire Margin
        $desireMargin = $generate_estimate->desire_margin ? $generate_estimate->desire_margin : null;

        // Calculate Gross Margin Percentage
        if ($totalPayable != 0) {
            $grossMarginPer = (($totalPayable - $totalCost) / $totalPayable) * 100;
        } else {
            $grossMarginPer = 0; // or any other appropriate value when $totalPayable is zero
        }

        $finalTotalCost = $totalCost + $PlantHardlaborCost + ($materialCost * getSaleTax()) / 100;
        $totalFinalPayablePrice = $finalTotalCost / ((100 - $desireMargin) / 100);
        if (previous_route()->getName() == getRouteAlias().'.create.estimate-generate') {
            Log::info('finalTotalCost', [$finalTotalCost]);
            $generate_estimate->update([
                'total_cost' => $finalTotalCost,
                'total_price' => $totalFinalPayablePrice,
            ]);
        }

        $finalPayableValue = $generate_estimate->grand_total;

        return [
            'finalPayableValue' => $finalPayableValue,
            'laborBurden' => $laborBurden,
            'laborHours' => $laborHours,
            'totalCost' => $totalCost,
            'superVisionHours' => $superVisionHours,
            'materialCost' => $materialCost,
            'laborCost' => $laborCost,
            'otherJobCost' => $otherJobCost,
            'subContractorCost' => $subContractorCost,
            'desireMargin' => $desireMargin,
            'grossMarginPer' => $grossMarginPer,
            'estimateLabor' => $estimateLabor,
            'estimateMaterial' => $estimateMaterial,
            'estimatePlantMaterial' => $estimatePlantMaterial,
            'estimateHardMaterial' => $estimateHardMaterial,
            'estimateOtherCost' => $estimateOtherCost,
            'estimateSubContractor' => $estimateSubContractor,
        ];
    }

    /**
     * Prepare Pdf For Estimate
     *
     * @param  \App\Models\GenerateEstimate  $id
     * @param  \App\Models\Users  $organizationId
     * @return $pdf
     */
    public static function preparePdf($id, $organizationId, $isPdfResponse = false, $mailss = false)
    {

        $estimate = Opportunity::with('client')->withAggregate('propertyInformation', 'contact_id')->where('id', $id)->first();
        $generate_estimate = GenerateEstimate::where('opportunity_id', $id)->first();
        if (is_object($generate_estimate)) {
            $estimateMaterial = EstimateMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateLabor = EstimateLabor::where('generate_estimate_id', $generate_estimate->id)->get();

            $estimateHardMaterial = EstimateHardMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimatePlantMaterial = EstimatePlantMaterial::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateOtherCost = EstimateOtherCost::where('generate_estimate_id', $generate_estimate->id)->get();
            $estimateSubContractor = EstimateSubContractor::where('generate_estimate_id', $generate_estimate->id)->get();
        } else {
            $estimateSubContractor = $estimateOtherCost = $estimatePlantMaterial = $estimateHardMaterial = $estimateLabor = $estimateMaterial = [];
        }
        $laborBurden = (int) DB::table('labors')->where('organization_id', $organizationId)->value('labor_burden') ?? 0;

        $user = User::findorfail($organizationId);
        $logo = $user->profile_photo_path ? asset('storage/user_images/'.$user->profile_photo_path) : asset('admin_assets/images/elmos-logo.png');
        $clientSignature = ClientEstimateAction::where('generate_estimate_id', $generate_estimate->id)->where('status', 'approve')->first(['signature_text', 'signature_date']) ?? null;

        $materialData = collect($estimateMaterial->map(function ($item) {
            $item->cname = $item->equipment->name;
            $item->cuom = $item->uom ?? $item->equipment->uom;
            $item->cquantity = $item->quantity;
            $item->ccost = ($item->cost ?? $item->equipment->cost) + ($item->gross_margin / 100) * ($item->cost ?? $item->equipment->cost) ?? 0;
            $item->ctotal_cost = $item->total_cost + ($item->gross_margin / 100) * $item->total_cost ?? 0;

            return $item;
        }));

        $materialData = $materialData->merge(collect($estimateHardMaterial->map(function ($item) use ($user) {
            $item->cname = $item->material?->name;
            $item->cuom = $item->uom;
            $item->cquantity = $item->quantity;
            $total_price2 = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin, $user->id);
            $item->ccost = $total_price2 / $item->quantity ?? 0;
            $item->ctotal_cost = $total_price2 ?? 0;

            return $item;
        })));

        $materialData = $materialData->merge(collect($estimatePlantMaterial->map(function ($item) use ($user) {
            $item->cname = $item->material?->name;
            $item->cuom = $item->uom;
            $item->cquantity = $item->quantity;
            $total_price2 = totalPriceWithTax($item->quantity, $item->unit_cost, $item->gross_margin, $user->id);
            $item->ccost = $total_price2 / $item->quantity ?? 0;
            $item->ctotal_cost = $total_price2 ?? 0;

            return $item;
        })));

        $materialData = $materialData->merge(collect($estimateLabor->map(function ($item) {
            $item->cname = $item->labor?->name;
            $item->cuom = $item->labor?->uom;
            $item->cquantity = $item->quantity;
            $item->ccost = $item->grand_total / $item->quantity;
            $item->ctotal_cost = $item->grand_total;

            return $item;
        })));

        $materialData = $materialData->merge(collect($estimateOtherCost->map(function ($item) {
            $item->cname = $item->otherCost?->name;
            $item->cuom = $item->uom;
            $item->cquantity = $item->quantity;
            $item->ccost = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0;
            $item->ctotal_cost = $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0;

            return $item;
        })));

        $materialData = $materialData->merge(collect($estimateSubContractor->map(function ($item) {
            $item->cname = $item->name;
            $item->cuom = $item->uom;
            $item->cquantity = $item->quantity;
            $item->ccost = $item->unit_cost + ($item->gross_margin / 100) * $item->unit_cost ?? 0;
            $item->ctotal_cost = $item->unit_cost * $item->quantity + ($item->gross_margin / 100) * ($item->unit_cost * $item->quantity) ?? 0;

            return $item;
        })));

        // $organizationId = getOrganizationId();organizationId
        $organization = User::where('id', $organizationId)->first();
        $burderlabor = Labor::where('organization_id', $organizationId)->where('labor_burden', '!=', null)->first();
        // $opportunity = Opportunity::with('propertyInformation', 'salesman', 'estimator', 'opportunityOwner', 'address.company', 'contactInformation', 'divisionDetails.division.serviceLines','proposal')
        //     ->findOrFail($decodedId);
        $opportunity = EstimateItem::where('opportunity_id', $id)->get();
        $cover = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'cover')
            ->first();
        $about = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'about')
            ->first();
        $intro = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'intro')
            ->first();
        $scope = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'scope')
            ->first();
        $terms = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'terms')
            ->first();
        $gallerys = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'gallery')
            ->get();
        $logo = DB::table('default_settings')
            ->where('opportunity_id', $id)
            ->where('setting_type', 'logo')
            ->first();
        $estimate = Opportunity::with('contactInformation', 'account', 'generateEstimate.serviceLine:id,name', 'generateEstimate.workType:id,name')->where('id', $id)->firstorfail();

        $totalPriceSumss = DB::table('estimate_items')
            ->where('opportunity_id', $id)
            ->sum('total_price');
        $imagePaths = $scope ? json_decode($scope->project_image) : [];
        $payment = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();
        $template = DB::table('default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'template')
            ->first();

        $data = [
            'title' => 'Estimate Invoice',
            'date' => date('m/d/Y'),
            'logo' => $logo,
            'primary_color' => $user->primary_color,
            'secondary_color' => $user->secondary_color,
            'estimate' => $estimate,
            'laborBurden' => $laborBurden,
            'notes' => $generate_estimate->notes,
            'company' => $user,
            'materialData' => $materialData,
            'organization_id' => $user->id,
            'clientSignature' => $clientSignature,
            'generate_estimate' => $generate_estimate,
        ];
        // return view('organization.generate-estimate.pdf')->with($data);
        $coverorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'cover')
            ->first();
        $aboutorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'about')
            ->first();

        $introorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'intro')
            ->first();

        $scopeorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'scope')
            ->first();

        $termsorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'terms')
            ->first();
        $paymentorg = DB::table('organization_default_settings')
            ->where('organization_id', getOrganizationId())
            ->where('setting_type', 'payment')
            ->first();

        $gec = GenerateEstimate::where('opportunity_id', $id)->first();
        $estimateAction = ClientEstimateAction::where('generate_estimate_id', $gec?->id)->first();

        $pdf = Pdf::loadView('organization.opportunity.downloadEstimation', compact('terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity', 'estimate', 'organization', 'gallerys', 'totalPriceSumss', 'payment', 'template', 'coverorg', 'aboutorg', 'introorg', 'scopeorg', 'termsorg', 'paymentorg', 'estimateAction'));

        // 'terms', 'scope', 'intro', 'about', 'cover', 'imagePaths', 'logo', 'opportunity', 'gallerys', 'totalPriceSumss', 'payment', 'estimate', 'organization', 'template'

        if (config('app.env') == 'production') {
            $pdf->setOptions([
                'isPhpEnabled' => true, // Enable PHP processing for header and footer
                'isHtml5ParserEnabled' => true, // Enable HTML5 parser
                'fontDir' => storage_path('fonts/'), // Adjust the path to your font directory
                'fontCache' => storage_path('fonts/cache/'),
                'isJavascriptEnabled' => true,
                'isRemoteEnabled' => true,
            ]);
            // $pdf->setOption('base_path', public_path());

        }

        if ($mailss == true) {
            $uniqueFileName = 'estimate_invoice_'.Str::uuid().'.pdf'; // Unique name using UUID
            $filePath = 'public/pdf/'.$uniqueFileName; // Path where you want to save the file
            Storage::put($filePath, $pdf->output());

            EstimateStatus::create([
                'generate_estimate_id' => $generate_estimate->id,
                'status_name' => 'proposed',
                'created_at' => now(),
                'estimate_total_price' => $totalPriceSumss,
                'pdf_path' => $filePath,
                'opportunity_id' => $id,
            ]);

            // Optional: Generate a public URL for the saved file
            $publicUrl = Storage::url($filePath);
        }
        // return $pdf->stream();
        set_time_limit(300);
        // dd($isPdfResponse);
        if ($isPdfResponse) {

            return response()->make($pdf->output(), 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename=estimate_invoice.pdf',
            ]);
        } else {
            // dd($pdf);
            return ['pdf' => $pdf, 'company' => $user->company_name];
        }
    }
}
