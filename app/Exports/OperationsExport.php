<?php

namespace App\Exports;

use App\Models\GenerateEstimate;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OperationsExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    public function collection()
    {
        return GenerateEstimate::with([
            'opportunityid:id,opportunity_name,contact_id,account_id,sale_person_id,estimator_id,sales_order_number,organization_id,property_id',
            'opportunityid.account:id,company_name,account_owner',
            'opportunityid.account.accountowner:id,first_name,last_name',
            'opportunityid.contactInformation:id,first_name,last_name',
            'manager:id,first_name,last_name',
            'opportunityid.propertyInformation:id,name',
            'latestScheduleDate:id,generate_estimate_id,date',
        ])
            ->whereHas('opportunityid', function ($query) {
                $query->where('organization_id', getOrganizationId());
                employeeAssociatedDateQuery($query);
            })
            ->where(['status' => 'won', 'is_schedule' => true])
            ->get()
            ->map(function ($operation) {
                return [
                    'Job#' => $operation->opportunityid->sales_order_number ?? '---',
                    'Job Name' => $operation->opportunityid->opportunity_name ?? '---',
                    'Property Name' => optional($operation->opportunityid->propertyInformation)->name ?? '---',
                    'Account' => $operation->opportunityid->account->company_name ?? '---',
                    'Account Owner' => optional(optional($operation->opportunityid->account)->accountowner)->first_name.' '.optional(optional($operation->opportunityid->account)->accountowner)->last_name,
                    'Operation Manager' => optional($operation->manager)->first_name.' '.optional($operation->manager)->last_name,
                    'Schedule Date' => optional($operation->latestScheduleDate)->date ?? '---',
                    'Status' => $operation->operation_status ?? '---',
                ];
            });
    }

    public function headings(): array
    {
        return [
            'Job#',
            'Job Name',
            'Property Name',
            'Account',
            'Account Owner',
            'Operation Manager',
            'Schedule Date',
            'Status',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 25,
            'G' => 20,
            'H' => 15,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Operations List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'E5E5E5'],
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->mergeCells('A1:H1');
            },
        ];
    }
}
