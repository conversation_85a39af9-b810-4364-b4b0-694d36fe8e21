<?php

namespace App\Exports;

use App\Models\Account;
use App\Models\PropertyInformation;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomerIssuesExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    protected $issues;

    public function __construct($issues)
    {
        $this->issues = $issues;
    }

    public function collection()
    {
        return $this->issues->map(function ($issue) {
            $account = Account::find($issue->acc_id);
            $property = PropertyInformation::find($issue->prop_id);

            return [
                'Issue ID' => $issue->id,
                'Subject' => $issue->subject ?? 'N/A',
                'Property Name' => $property ? $property->name : 'N/A',
                'Account' => $account ? $account->company_name : 'N/A',
                'Category' => $issue->category ?? 'N/A',
                'Created By' => $issue?->creator_full_name ?? 'N/A',
                'Date & Time' => $issue->created_at->format('n/j/Y - H:i'),
                'Status' => $this->mapStatus($issue->status),
            ];
        });
    }

    public function headings(): array
    {
        return ['Issue ID', 'Subject', 'Property Name', 'Account', 'Category', 'Created By', 'Date & Time', 'Status'];
    }

    private function mapStatus($status)
    {
        $statusMappings = [
            1 => 'Pending',
            2 => 'Open',
            3 => 'Completed',

        ];

        return $statusMappings[$status] ?? 'Unknown';
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10, // Set width for column A
            'B' => 30, // Set width for column B
            'C' => 30, // Set width for column C, and so on...
            'D' => 30,
            'E' => 50,
            'F' => 30,
            'G' => 30,
            'H' => 30,

        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set styles for "Account List" in the first row
        $sheet->setCellValue('A1', 'Property List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'FFD700'], // Dark yellow color
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'ADD8E6'], // Light blue for header row
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Merge the first row for "Account List" title
                $event->sheet->getDelegate()->mergeCells('A1:I1');

                // Apply borders to the header row (second row)
                $headings = $this->headings();
                $event->sheet->getDelegate()->fromArray([$headings], null, 'A2');
                $event->sheet->getDelegate()->getStyle('A2:I2')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                // Set the data starting from the third row
                $dataStartRow = 3; // Data starts from row 3
                $data = $this->collection(); // Fetch the data collection

                // Convert the collection to an array of arrays
                $dataArray = $data->map(function ($item) {
                    return (array) $item; // Convert each stdClass to an array
                })->toArray();

                // Write data to the sheet starting from row 3
                $event->sheet->getDelegate()->fromArray($dataArray, null, 'A'.$dataStartRow);
            },
        ];
    }
}
