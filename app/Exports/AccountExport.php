<?php

namespace App\Exports;

use App\Models\Account;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AccountExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Fetch accounts with user names from the users table
        return DB::table('accounts')
            ->join('users', 'accounts.account_owner', '=', 'users.id')
            ->where('accounts.parent_id', getOrganizationId())
            ->select(
                'accounts.company_name',
                /* 'accounts.email',
                 'accounts.mobile_no',
                 'accounts.website',*/
                'accounts.address',
                'accounts.city',
                'accounts.state',
                'accounts.zip',
                DB::raw('CONCAT(users.first_name, " ", users.last_name) AS account_owner'),
            )
            ->get();
    }

    public function headings(): array
    {
        /*return [
            'Company Name',
            'Email',
            'Phone',
            'Website',
            'Account Owner',
            'Address',
            'City',
            'State',
            'Zip',
        ];*/

        return [
            'Account',
            'Address',
            'City',
            'State',
            'Zip',
            'Account Owner',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30,
            'B' => 30,
            'C' => 30,
            'D' => 40,
            'E' => 30,
            'F' => 50,
            /*'G' => 30,
            'H' => 30,
            'I' => 30,*/
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set styles for "Account List" in the first row
        $sheet->setCellValue('A1', 'Account List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'FFD700'], // Dark yellow color
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'ADD8E6'], // Light blue for header row
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Merge the first row for "Account List" title
                $event->sheet->getDelegate()->mergeCells('A1:F1');

                // Apply borders to the header row (second row)
                $headings = $this->headings();
                $event->sheet->getDelegate()->fromArray([$headings], null, 'A2');
                $event->sheet->getDelegate()->getStyle('A2:F2')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                // Set the data starting from the third row
                $dataStartRow = 3; // Data starts from row 3
                $data = $this->collection(); // Fetch the data collection

                // Convert the collection to an array of arrays
                $dataArray = $data->map(function ($item) {
                    return (array) $item; // Convert each stdClass to an array
                })->toArray();

                // Write data to the sheet starting from row 3
                $event->sheet->getDelegate()->fromArray($dataArray, null, 'A'.$dataStartRow);
            },
        ];
    }
}
