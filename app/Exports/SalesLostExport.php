<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SalesLostExport implements FromCollection, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    private $sales;

    public function __construct($sales)
    {
        $this->sales = $sales;
    }

    public function collection()
    {
        return $this->sales;
    }

    public function map($sale): array
    {
        return [
            $sale->request?->job_no ?? '',
            $sale->request?->client?->full_name ?? '',
            $sale->request?->saleMan?->name ?? '',
            $sale->request?->project_name ?? '',
            $sale->reason ?? '',
            custom_number_format(($sale->grand_total > 0) ? '$'.$sale->grand_total : ($sale?->total_price ? '$'.$sale->total_price : '0')),
        ];
    }

    public function headings(): array
    {
        return [
            'Job #',
            'Client Name',
            'Salesman Name',
            'Project Name',
            'Lost Reason',
            'Total Price',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:H1')->getFont()->setBold(true);
    }
}
