<?php

namespace App\Exports;

use App\Models\PropertyInformation;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PropertyExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // return PropertyInformation::all();
        return DB::table('property_information')->join('accounts', 'property_information.company_id', '=', 'accounts.id')
            ->join('users', 'accounts.account_owner', '=', 'users.id') // Join with users table
            ->select(
                'property_information.name as property_name',
                'property_information.address1',
                'property_information.city',
                'property_information.state',
                'property_information.zip',
                'accounts.company_name as company_name',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as account_owner_name") // Concatenate first name and last name
            )
            ->get();
    }

    public function headings(): array
    {
        return [
            'Property Name',
            'Address',
            'City',
            'State',
            'Zip',
            'Account',
            'Account Owner',

        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30, // Set width for column A
            'B' => 40, // Set width for column B
            'C' => 30, // Set width for column C, and so on...
            'D' => 30,
            'E' => 30,
            'F' => 50,
            'G' => 30,

        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set styles for "Account List" in the first row
        $sheet->setCellValue('A1', 'Property List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'FFD700'], // Dark yellow color
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'ADD8E6'], // Light blue for header row
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Merge the first row for "Account List" title
                $event->sheet->getDelegate()->mergeCells('A1:G1');

                // Apply borders to the header row (second row)
                $headings = $this->headings();
                $event->sheet->getDelegate()->fromArray([$headings], null, 'A2');
                $event->sheet->getDelegate()->getStyle('A2:G2')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                // Set the data starting from the third row
                $dataStartRow = 3; // Data starts from row 3
                $data = $this->collection(); // Fetch the data collection

                // Convert the collection to an array of arrays
                $dataArray = $data->map(function ($item) {
                    return (array) $item; // Convert each stdClass to an array
                })->toArray();

                // Write data to the sheet starting from row 3
                $event->sheet->getDelegate()->fromArray($dataArray, null, 'A'.$dataStartRow);
            },
        ];
    }
}
