<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ContactExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return DB::table('contacts')
            ->join('accounts', 'contacts.account', '=', 'accounts.id')
            ->where('contacts.organization_id', getOrganizationId())
            ->select(
                'contacts.first_name',
                'contacts.last_name',
                DB::raw('accounts.company_name AS account'),
                'contacts.phone_number',
                'contacts.email'
            )->get();
    }

    public function headings(): array
    {
        return [
            'First Name',
            'Last Name',
            'Account',
            'Phone Numer',
            'Email',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 32, // Set width for column A
            'B' => 32, // Set width for column B
            'C' => 32, // Set width for column C, and so on...
            'D' => 32,
            'E' => 32,

        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set styles for "Account List" in the first row
        $sheet->setCellValue('A1', 'Contact List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'FFD700'], // Dark yellow color
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'ADD8E6'], // Light blue for header row
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Merge the first row for "Account List" title
                $event->sheet->getDelegate()->mergeCells('A1:I1');

                // Apply borders to the header row (second row)
                $headings = $this->headings();
                $event->sheet->getDelegate()->fromArray([$headings], null, 'A2');
                $event->sheet->getDelegate()->getStyle('A2:I2')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                // Set the data starting from the third row
                $dataStartRow = 3; // Data starts from row 3
                $data = $this->collection(); // Fetch the data collection

                // Convert the collection to an array of arrays
                $dataArray = $data->map(function ($item) {
                    return (array) $item; // Convert each stdClass to an array
                })->toArray();

                // Write data to the sheet starting from row 3
                $event->sheet->getDelegate()->fromArray($dataArray, null, 'A'.$dataStartRow);
            },
        ];
    }
}
