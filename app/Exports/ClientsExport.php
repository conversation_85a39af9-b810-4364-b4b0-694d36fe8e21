<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwe<PERSON>ite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ClientsExport implements FromCollection, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    private $clients;

    public function __construct($clients)
    {
        $this->clients = $clients;
    }

    public function collection()
    {
        return $this->clients;
    }

    public function map($client): array
    {
        return [
            $client->full_name ?? '',
            $client->title ?? '',
            $client->company_name ?? '',
            $client->mobile_no,
            $client->office_no,
            $client->email,
            $client->propertyAddress?->address1 ?? '',
            $client->billingAddress?->address1 ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Client Name',
            'Title',
            'Company',
            'Phone Number',
            'Office Number',
            'Emai',
            'Address',
            'Billing Address',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:H1')->getFont()->setBold(true);
    }
}
