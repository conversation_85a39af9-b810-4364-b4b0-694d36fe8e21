<?php

namespace App\Exports;

use App\Models\Contact;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OpportunitiesExport implements FromCollection, WithColumnWidths, WithEvents, WithHeadings, WithStyles
{
    protected $opportunities;

    public function __construct($opportunities)
    {
        $this->opportunities = $opportunities;
    }

    /**
     * Return a collection of opportunities.
     *
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {

        return $this->opportunities->map(function ($opportunity) {
            $contact = Contact::find($opportunity->contact_id);
            $accountName = $contact && $contact->account ? $contact->accountget->company_name : 'N/A';

            return [
                'Opportunity ID' => $opportunity->id,
                'Opportunity Name' => $opportunity?->opportunity_name ?? 'N/A',
                'Property Name' => $opportunity->propertyInformation->name ?? 'N/A',
                'Account' => $accountName,
                'Opportunity Owner' => $opportunity->opportunityOwner->name,
                'Opportunity Type' => $opportunity->opportunity_type,
                'Date & Time' => $opportunity->created_at->format('Y-m-d H:i:s'),
                'Status' => $this->mapStatus($opportunity->status),
            ];
        });
    }

    private function mapStatus($status)
    {
        $statusMappings = [
            1 => 'Open',
            2 => 'Estimating',
            3 => 'Proposed',
            4 => 'Closed',
            5 => 'Completed',
        ];

        return $statusMappings[$status] ?? 'Unknown';
    }

    /**
     * Define the headings for the export.
     */
    public function headings(): array
    {
        return ['Opportunity ID', 'Opportunity Name', 'Property Name', 'Account', 'Opportunity Owner', 'Opportunity Type', 'Date & Time', 'Status'];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // Set width for column A
            'B' => 30, // Set width for column B
            'C' => 30, // Set width for column C, and so on...
            'D' => 30,
            'E' => 30,
            'F' => 30,
            'G' => 30,

        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set styles for "Account List" in the first row
        $sheet->setCellValue('A1', 'Opportunity List');

        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'FFD700'], // Dark yellow color
                ],
                'alignment' => [
                    'horizontal' => 'center',
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => 'solid',
                    'color' => ['rgb' => 'ADD8E6'], // Light blue for header row
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Merge the first row for "Account List" title
                $event->sheet->getDelegate()->mergeCells('A1:I1');

                // Apply borders to the header row (second row)
                $headings = $this->headings();
                $event->sheet->getDelegate()->fromArray([$headings], null, 'A2');
                $event->sheet->getDelegate()->getStyle('A2:I2')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                // Set the data starting from the third row
                $dataStartRow = 3; // Data starts from row 3
                $data = $this->collection(); // Fetch the data collection

                // Convert the collection to an array of arrays
                $dataArray = $data->map(function ($item) {
                    return (array) $item; // Convert each stdClass to an array
                })->toArray();

                // Write data to the sheet starting from row 3
                $event->sheet->getDelegate()->fromArray($dataArray, null, 'A'.$dataStartRow);
            },
        ];
    }
}
