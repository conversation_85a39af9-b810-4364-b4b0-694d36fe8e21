<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwe<PERSON>ite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RequestsExport implements FromCollection, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    private $requests;

    public function __construct($requests)
    {
        $this->requests = $requests;
    }

    public function collection()
    {
        return $this->requests;
    }

    public function map($request): array
    {
        return [
            $request->job_no ?? '',
            $request->project_name ?? '',
            $request->client?->full_name ?? '',
            $request->client?->mobile_no ?? '',
            $request->saleMan?->name ?? '',
            $request->estimator?->name ?? '',
            $request->propertyAddress?->property_name ?? '',
            $request->propertyAddress?->address1 ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Job #',
            'Project Name',
            'Client Name',
            'Phone Number',
            'Salesman Name',
            'Estimator Name',
            'Property Name',
            'Address',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:H1')->getFont()->setBold(true);
    }
}
