<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EstimatesExport implements FromCollection, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    private $estimates;

    public function __construct($estimates)
    {
        $this->estimates = $estimates;
    }

    public function collection()
    {
        return $this->estimates;
    }

    public function map($estimate): array
    {
        return [
            $estimate->request?->job_no ?? '',
            $estimate->request?->client?->full_name ?? '',
            $estimate->request?->project_name ?? '',
            $estimate->request?->saleMan?->name ?? '',
            $estimate->request?->estimator?->name ?? '',
            custom_number_format(($estimate->grand_total > 0) ? '$'.$estimate->grand_total : ($estimate?->total_price ? '$'.$estimate->total_price : '0')),

        ];
    }

    public function headings(): array
    {
        return [
            'Job #',
            'Client Name',
            'Project Name',
            'Salesman Name',
            'Estimator Name',
            'Total Price',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:H1')->getFont()->setBold(true);
    }
}
