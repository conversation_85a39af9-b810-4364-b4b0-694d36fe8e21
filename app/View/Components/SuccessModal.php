<?php

namespace App\View\Components;

use Illuminate\View\Component;

class SuccessModal extends Component
{
    public $title;

    public $message;

    public $route;

    public $showButton;

    public $buttonText;

    /**
     * Create a new component instance.
     *
     * @param  string  $title
     * @param  string  $message
     * @param  string|null  $route
     * @param  bool  $showButton
     * @param  string  $buttonText
     */
    public function __construct($title, $message, $route = null, $showButton = true, $buttonText = 'Go to list')
    {
        $this->title = $title;
        $this->message = $message;
        $this->route = $route;
        $this->showButton = $showButton;
        $this->buttonText = $buttonText;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.success-modal');
    }
}
