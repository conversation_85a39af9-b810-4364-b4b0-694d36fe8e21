<?php

namespace App\View\Components;

use Illuminate\View\Component;

class NavbarItem extends Component
{
    public $href;

    public $label;

    public $active;

    public $dropdownItems;

    /**
     * Create a new component instance.
     *
     * @param  string  $href
     * @param  string  $label
     * @param  bool  $active
     * @param  array  $dropdownItems
     * @return void
     */
    public function __construct($href = '', $label = '', $active = false, $dropdownItems = [])
    {
        $this->href = $href;
        $this->label = $label;
        $this->active = $active;
        $this->dropdownItems = $dropdownItems;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.navbar-item');
    }
}
