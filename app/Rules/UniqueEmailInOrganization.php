<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UniqueEmailInOrganization implements Rule
{
    protected $organizationId;

    protected $clientId;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($organizationId, $client_id)
    {
        $this->organizationId = $organizationId;
        $this->clientId = $client_id ?? 0;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $clientCount = DB::table('contacts')
            ->where('email', $value)
            ->whereNot('id', $this->clientId)
            ->where('organization_id', $this->organizationId)
            ->count();

        $userCount = DB::table('users')
            ->where('email', $value)
            ->whereIn('type', [1, 2])
            ->where(function ($query) {
                $query->where('parent_id', $this->organizationId) // Assuming type 1 is for organization and 2 is for employee
                    ->orWhere('id', $this->organizationId);
            })
            ->count();

        return $clientCount == 0 && $userCount == 0;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The email must be unique within your organization.';
    }
}
