<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Cache;

class FilterEmail implements Rule
{
    protected $blacklistedDomains;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        // if (config('app.disposible_emails_allowed' == false)) {
        $this->blacklistedDomains = Cache::remember('TempEmailBlackList', 60 * 10, function () {
            $filePath = storage_path('app/Disposible/emails.txt');

            if (file_exists($filePath)) {
                $data = file_get_contents($filePath);
                $domains = array_filter(array_map('trim', explode("\n", $data)));

                return $domains;
            }

            return [];
        });
        // }
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $emailDomain = substr(strrchr($value, '@'), 1);

        return ! in_array($emailDomain, $this->blacklistedDomains);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Disposible Emails are not allowed';
    }
}
