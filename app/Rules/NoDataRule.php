<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class NoDataRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $strippedValue = strip_tags($value); // Remove HTML tags
        $strippedValue = str_replace('&nbsp;', '', $strippedValue); // Remove non-breaking spaces
        $strippedValue = trim($strippedValue); // Remove leading/trailing whitespace

        return ! empty($strippedValue);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Please enter notes';
    }
}
