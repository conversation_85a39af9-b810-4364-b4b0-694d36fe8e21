<?php

use App\Models\CustomerIssue;
use App\Models\Labor;
use App\Models\Margin;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

if (! function_exists('dateFromTimeStamp')) {

    function dateFromTimeStamp(string $timestamp = ''): ?string
    {
        return $timestamp ? Carbon::createFromTimestamp($timestamp)->toDateTimeString() : null;
    }
}

if (! function_exists('getOrganizationId')) {

    function getOrganizationId(): ?string
    {
        if (auth('web')->check()) {
            $user = auth('web')->user();
            if ($user->isOrganization()) {
                return $user->id;
            } elseif ($user->isEmployee()) {
                return $user->parent_id;
            }
        } elseif (auth('client')->check()) {
            return auth('client')->user()->organization_id;
        }

        return null;
    }
}

function getCustomerIssuesCount()
{
    $organization_id = auth('web')->user()->id;
    $count = CustomerIssue::selectRaw('status, COUNT(*) as count')
        ->where('organization_id', $organization_id)
        ->groupBy('status')
        ->pluck('count', 'status')
        ->toArray();

    return [
        'Open Issue' => $count['open'] ?? 0,
        'Pending' => $count['pending'] ?? 0,
        'Completed' => $count['complete'] ?? 0,
    ];
}

function getStatusStyles($status, $currentStatus)
{
    $originalStyles = [
        1 => ['color' => 'rgba(239, 141, 3, 1)', 'background-color' => 'rgba(255, 245, 233, 1)'],
        2 => ['color' => 'rgba(0, 116, 217, 1)', 'background-color' => 'rgba(230, 241, 251, 1)'],
        3 => ['color' => 'rgba(117, 82, 255, 1)', 'background-color' => 'rgba(234, 229, 251, 1)'],
        4 => ['color' => 'rgba(159, 107, 39, 1)', 'background-color' => 'rgba(239, 238, 235, 1)'],
        5 => ['color' => 'rgba(10, 196, 152, 1)', 'background-color' => 'rgba(210, 245, 237, 1)'],
        6 => ['color' => 'rgba(81, 86, 108, 1)', 'background-color' => 'rgba(226, 228, 234, 1)'],
        9 => ['color' => 'rgba(10, 196, 152, 1)', 'background-color' => 'rgba(210, 245, 237, 1)'],
        7 => ['color' => 'rgba(10, 196, 152, 1)', 'background-color' => 'rgba(210, 245, 237, 1)'],
    ];

    $defaultStyle = ['color' => 'rgba(81, 86, 108, 1)', 'background-color' => 'rgba(226, 228, 234, 1)'];

    if ($currentStatus <= $status) {
        return $originalStyles[$currentStatus];
    }

    return $defaultStyle;
}

if (! function_exists('customDateFormat')) {

    function customDateFormat(Carbon $date, $orgID = null)
    {
        if ($orgID == null) {
            $orgID = getOrganizationId();
        }

        $dateFormat = Cache::remember('customDateFormat-'.$orgID, 7600, function () use ($orgID) {
            return DB::table('company_addresses')->where('company_id', $orgID)->where('type', 0)->first(['date_format', 'timezone']);
        });
        // return $date->setTimezone($dateFormat->timezone)->format($dateFormat->date_format);
    }
}

if (! function_exists('getOrgDateFormat')) {

    function getOrgDateFormat($id = null, $isModify = true)
    {
        $date = Cache::remember('getOrgDateFormat-'.getOrganizationId(), 7600, function () use ($id) {
            return DB::table('company_addresses')->where('company_id', $id ?? getOrganizationId())->where('type', 0)->value('date_format');
        });
        if ($isModify == false) {
            return $date;
        }
        if (strpos($date, 'y') !== false) {
            $date = str_replace('y', 'yy', $date);
        } elseif (strpos($date, 'Y') !== false) {
            $date = str_replace('Y', 'yyyy', $date);
        }

        return $date;
    }
}

if (! function_exists('customTimeFormat')) {

    function customTimeFormat(Carbon $date)
    {
        $time_format = Cache::remember('customTimeFormat-'.getOrganizationId(), 7600, function () {
            return DB::table('company_addresses')->where('company_id', getOrganizationId())->where('type', 0)->first(['time_format', 'timezone']);
        });

        return $date->setTimezone($time_format->timezone)->format($time_format->time_format);
    }
}

if (! function_exists('checkClassHasMethod')) {
    function checkClassHasMethod($className, $methodName)
    {
        return class_exists($className) && is_callable([$className, $methodName]);
    }
}

if (! function_exists('custom_number_format')) {

    function custom_number_format($value, $decimals = 2, $decimalSeparator = '.', $thousandsSeparator = ',')
    {
        // Customize the number formatting logic as needed
        if (is_float($value) || (is_numeric($value) && floor($value) != $value)) {
            return number_format($value, $decimals, $decimalSeparator, $thousandsSeparator);
        }

        return $value;
    }
}

if (! function_exists('calculateGrandTotalRawQuery')) {

    function calculateGrandTotalRawQuery($column = 'grand_total')
    {
        return 'SUM(CASE
        WHEN desire_margin IS NULL OR desire_margin = 0
        THEN total_price
        ELSE total_cost + total_cost * (desire_margin / 100)
        END) as '.$column;
    }
}

if (! function_exists('generateInvoiceNumber')) {

    function generateInvoiceNumber($org_id)
    {
        $latest = DB::table('invoices')
            ->where('organization_id', $org_id)
            ->where('is_sent', 1)
            ->orderByDesc('created_at')
            ->first();

        if (! $latest) {
            // return date('Y') . '-0001';
            return '01';
        }
        $expNum = $latest->invoice_number;

        // if (count($expNum) < 2) {
        //     return date('Y') . '-0001';
        // }

        // Check if it's the first day of the year
        if (date('z') === 0) {

        } else {
            $nextNumber = (int) $expNum + 1;
            $formattedNumber = str_pad($nextNumber, 2, '0', STR_PAD_LEFT);

            return $formattedNumber;
        }
    }
}

if (! function_exists('employeeAssociatedDateQuery')) {
    function employeeAssociatedDateQuery($query = null)
    {
        $query->when(auth('web')->user()->isEmployee() && ! auth('web')->user()->hasRole('manager'), function ($query) {
            $query->where(function ($subQuery) {
                $subQuery->where('opportunities.creator_id', auth('web')->user()->id)
                    ->orWhere('opportunities.estimator_id', auth('web')->user()->id)
                    ->orWhere('opportunities.sale_person_id', auth('web')->user()->id);
            });
        });

        // dd($query);
        return $query;
    }
}

if (! function_exists('totalPriceWithTax')) {

    function totalPriceWithTax(int $quantity = 0, float $unit_cost = 0, int $gross_margin = 0, $org_id = null): ?float
    {
        return ($quantity * $unit_cost) * (1 + ($gross_margin / 100)) * (1 + (getSaleTax($org_id) / 100));
    }
}

if (! function_exists('totalPriceWithGrossMargin')) {

    function totalPriceWithGrossMargin(float $total_cost = 0, int $gross_margin = 0, $org_id = null): ?float
    {
        return $total_cost + ($total_cost * $gross_margin) / 100;
    }
}

if (! function_exists('totalPriceWithLaborBurden')) {

    function totalPriceWithLaborBurden(int $quantity = 0, float $unit_cost = 0, float $gross_margin = 0, float $laborBuden = 0): ?float
    {
        return ($quantity * $unit_cost) * (1 + ($gross_margin / 100)) * (1 + ($laborBuden / 100));
    }
}

if (! function_exists('convertDateToTimestamp')) {

    function convertDateToTimestamp(string $data = ''): ?string
    {
        return $data ? Carbon::parse($data)->timestamp : null;
    }
}

if (! function_exists('convertToShortFormat')) {

    function convertToShortFormat($number)
    {
        $power = floor(log10($number));
        $suffix = '';

        if ($power >= 3 && $power <= 5) {
            $suffix = 'k';
            $number /= 1000;
        }

        $formattedNumber = number_format($number, 3);

        // Remove trailing zeroes after decimal point
        $formattedNumber = rtrim($formattedNumber, '0');

        // Remove decimal point if there are no decimal values
        $formattedNumber = rtrim($formattedNumber, '.');

        return $formattedNumber.$suffix;
    }
}

if (! function_exists('previous_route')) {
    function previous_route()
    {
        $previousRequest = request()->create(url()->previous());

        try {
            $routeName = app('router')->getRoutes()->match($previousRequest);
        } catch (NotFoundHttpException $exception) {
            return null;
        }

        return $routeName;
    }
}

if (! function_exists('custom_title_case')) {
    function custom_title_case($inputString)
    {
        if (strpos($inputString, '_') !== false) {
            // Convert to "Title Case" if the string contains underscores
            return ucwords(str_replace('_', ' ', $inputString));
        } else {
            // Capitalize the first character if there are no underscores
            return ucfirst($inputString);
        }
    }
}

function getRouteAlias(): ?string
{
    $alias = 'admin';

    if (auth()->check()) {
        $user = auth()->user();

        if ($user->isOrganization()) {
            $alias = 'organization';
        }
        if ($user->isEmployee()) {
            $alias = 'employee';
        }
    }

    return $alias;
}

function encodeID($id, $length = 20): string
{
    config(['hashids.connections.main.length' => $length]);

    return $id !== null && $id !== '' && $id !== NO ? \Vinkla\Hashids\Facades\Hashids::encode($id) : '';
}

// function decodeID($id, $length = 20): string
// {
//     config(['hashids.connections.main.length' => $length]);

//     try {
//         $decodedID = \Hashids::decode($id);

//         return $decodedID[0];
//     } catch (\Exception $exception) {
//         return '';
//     }
// }
function decodeID($id, int $length = 20)
{
    // Return empty string immediately if input is invalid
    if (! is_string($id)) {
        return '';
    }

    // Configure hash length
    config(['hashids.connections.main.length' => $length]);

    try {
        $decoded = \Vinkla\Hashids\Facades\Hashids::decode($id);

        // Verify we got a valid decoded array with at least one element
        if (is_array($decoded) && count($decoded) > 0) {
            return $decoded[0];
        }

        return '';
    } catch (\Exception $e) {
        // Log the error if needed
        // logger()->error("Failed to decode ID: {$id}", ['error' => $e->getMessage()]);
        return '';
    }
}

function getRemainingDays($date)
{
    return now()->diffInDays($date);
}

function saveUserOnlineStatus()
{
    if (Auth::user()) {
        Auth::user()->is_online = YES;
        Auth::user()->save();
    }
}
function saveUserLoginTime()
{
    if (Auth::user()) {
        Auth::user()->last_login_at = Carbon::now()->toDateTimeString();
        Auth::user()->save();
    }
}

function insertLabor($userId)
{
    return Labor::insert([
        [
            'organization_id' => $userId,
            'name' => 'Labor',
            'uom' => 'Hour',
            'cost' => '',
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'organization_id' => $userId,
            'name' => 'Supervision',
            'uom' => 'Hour',
            'cost' => '',
            'created_at' => now(),
            'updated_at' => now(),
        ],

    ]);
}
function insertMargin($userId)
{
    return Margin::insert([
        [
            'organization_id' => $userId,
            'name' => "Equipment's",
        ], [
            'organization_id' => $userId,
            'name' => 'Labor',
        ], [
            'organization_id' => $userId,
            'name' => 'Hard Material',
        ], [
            'organization_id' => $userId,
            'name' => 'Plant Material',
        ], [
            'organization_id' => $userId,
            'name' => 'Other Job Costs',
        ],
    ]);
}

if (! function_exists('getSaleTax')) {
    function getSaleTax($id = null)
    {
        if ($id) {
            return User::find($id)->value('sale_tax');
        }

        if (auth()->user() && Auth::guard('client')->check()) { // for Client
            return User::where('id', auth()->user()->organization_id)->value('sale_tax');
        } elseif (auth()->user() && auth()->user()->isEmployee()) {
            return User::where('id', auth()->user()->parent_id)->value('sale_tax');
        } else {
            return auth()->user()->sale_tax;
        }
    }
}

if (! function_exists('calculateDepthSqFt')) {
    function calculateDepthSqFt($depth, $sqft, $isBagUom, $quantity): float
    {
        $bagMultiplier = $isBagUom ? 9 : 1;
        $finalMultiplier = $isBagUom ? 1 : 0.25;

        $depthInFeet = $depth / 12;
        $cubicYards = ($sqft * $depthInFeet) / 27;

        $preRounded = $cubicYards * $bagMultiplier;
        $rounded = round($preRounded); // Match JS Math.round

        $final = ($rounded * $finalMultiplier) + $quantity;

        return number_format($final, 2, '.', '');
    }
}

if (! function_exists('getOpportunityPriceModel')) {
    function getOpportunityPriceModel($type): string
    {
        return match ($type) {
            't_m' => 'T&M',
            'per_even' => 'Per Even',
            'fixed_seasonal' => 'Fixed Seasonal',
            'per_project' => 'Per Project',
            'recurring' => 'Recurring',
            'one_time_job' => 'One Time Job',
            default => '--',
        };
    }
}
