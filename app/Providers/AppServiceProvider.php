<?php

namespace App\Providers;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Schema::defaultStringLength(191);
        Blade::if('admin', function () {
            return auth('web')->check() && auth('web')->user()->isAdmin() ?? false;
        });

        Blade::if('organization', function () {
            return (auth('web')->check() && auth('web')->user()->isOrganization()) ?? false;
        });

        Blade::if('employee', function () {
            return (auth('web')->check() && auth('web')->user()->isEmployee()) ?? false;
        });
        Blade::if('manager', function () {
            return (auth('web')->check() && auth('web')->user()->hasRole('manager')) ?? false;
        });
        Blade::if('estimator', function () {
            return (auth('web')->check() && auth('web')->user()->hasRole('estimator')) ?? false;
        });

        Paginator::useBootstrap();

        Validator::extend('phone_format', function ($attribute, $value, $parameters, $validator) {
            return preg_match('/^\d{3}-\d{3}-\d{4}$/', $value);
        });

        Validator::replacer('phone_format', function ($message, $attribute, $rule, $parameters) {
            return 'Invalid Format';
        });

        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }
}
