<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;

use App\Models\Contact;
use App\Models\Invoice;
use App\Models\Opportunity;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Gate::define('client-view-request', function (Contact $client, Opportunity $estimate) {
            return $client->id == $estimate->contact_id && $estimate->created_by == 'client';
        });

        Gate::define('client-view-estimate', function (Contact $client, Opportunity $estimate) {

            return $client->id == $estimate->contact_id;
        });

        Gate::define('client-view-invoice', function (Contact $client, Invoice $invoice) {
            return $client->id == $invoice->client_id;
        });
    }
}
