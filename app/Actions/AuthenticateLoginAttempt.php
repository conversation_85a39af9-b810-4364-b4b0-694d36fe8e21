<?php

namespace App\Actions;

use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthenticateLoginAttempt
{
    /**
     * @throws ValidationException
     */
    public function __invoke(\Illuminate\Http\Request $request)
    {
        $user = \App\Models\User::where('email', $request->email)->first();
        if ($user && Hash::check($request->password, $user->password)) {
            return $user;
        }
    }
}
