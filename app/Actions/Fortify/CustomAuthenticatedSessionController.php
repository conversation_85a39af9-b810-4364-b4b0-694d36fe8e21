<?php

namespace App\Actions\Fortify;

use Illuminate\Http\Request;
use <PERSON>vel\Fortify\Contracts\LogoutResponse;
use Laravel\Fortify\Http\Controllers\AuthenticatedSessionController;

class CustomAuthenticatedSessionController extends AuthenticatedSessionController
{
    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): LogoutResponse
    {
        $this->guard->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return app(LogoutResponse::class);
    }
}
