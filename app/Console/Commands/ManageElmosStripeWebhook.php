<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ManageElmosStripeWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:manage-elmos-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update Stripe webhook for ELmos';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

        $webhookUrl = config('services.stripe.elmos_webhook.url');
        $webhookId = null;

        // Check if the webhook already exists
        $webhooks = $stripe->webhookEndpoints->all();

        foreach ($webhooks as $webhook) {
            if ($webhook->url === $webhookUrl && $webhook->status != 'disabled') {
                $webhookId = $webhook->id;
                break;
            }
        }

        // Create or update the webhook
        if ($webhookId) {
            $webhook = $stripe->webhookEndpoints->update($webhookId, ['url' => $webhookUrl, 'enabled_events' => config('services.stripe.elmos_webhook.events')]);
            $this->info('Webhook updated: '.$webhook->url);
        } else {
            $webhook = $stripe->webhookEndpoints->create([
                'url' => $webhookUrl,
                'enabled_events' => config('services.stripe.elmos_webhook.events'),
            ]);
            $this->info('Webhook created: '.$webhook->url);
            $this->warn('Please Save the Below Secret it never get again using Api: ');
            $this->info('Webhook Secret: '.$webhook->secret);
        }
    }
}
