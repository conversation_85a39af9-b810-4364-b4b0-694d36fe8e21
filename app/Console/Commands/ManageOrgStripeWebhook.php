<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ManageOrgStripeWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:manage-org-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update Stripe webhook for Organization Connect Account';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

        $webhookUrl = config('services.stripe.client_webhook.url');
        $webhookId = null;

        // Check if the webhook already exists
        $webhooks = $stripe->webhookEndpoints->all();

        foreach ($webhooks as $webhook) {
            if ($webhook->url === $webhookUrl && $webhook->status != 'disabled') {
                $webhookId = $webhook->id;
                break;
            }
        }

        // Create or update the webhook
        if ($webhookId) {
            $webhook = $stripe->webhookEndpoints->update($webhookId, ['url' => $webhookUrl, 'enabled_events' => config('services.stripe.client_webhook.events'), 'connect' => true]);
            $this->info('Webhook updated: '.$webhook->url);
        } else {
            $webhook = $stripe->webhookEndpoints->create([
                'url' => $webhookUrl,
                'enabled_events' => config('services.stripe.client_webhook.events'),
                'connect' => true,
            ]);
            $this->info('Webhook created: '.$webhook->url);
            $this->warn('Please Save the Below Secret IN env(STRIPE_CONNECT_WEBHOOK_SECRET) it never get again using Api: ');
            $this->info('Webhook Secret: '.$webhook->secret);
        }
    }
}
