<?php

namespace App\Webhooks;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use stdClass;
use Throwable;

class StripeWebhookController extends Controller
{
    public function handle(Request $request)
    {
        $event = new stdClass;
        try {
            $event = $this->validateWebhook($request);
        } catch (\UnexpectedValueException|\Stripe\Exception\SignatureVerificationException $e) {
            http_response_code(400);
            exit();
        }

        // Handle the event
        switch ($event->type) {
            // If payment is succeeded
            case 'invoice.payment_succeeded':
                info('payment success', [$event]);
                $account = $this->handleInvoicePaymentSucceeded($event);
                break;
            case 'invoice.payment_failed':
                info('payment failed', [$event]);
                $account = $this->handleInvoicePaymentFailed($event);
                break;
                // case 'customer.subscription.created':
                //     $this->handleCustomerSubscriptionCreated($event);
                //     info("Subscription Created", [$event]);
                //     break;
                // case 'customer.subscription.updated':
                //     $this->handleCustomerSubscriptionUpdated($event);
                //     info("Subscription Updated", [$event]);
                //     break;
            case 'customer.subscription.deleted':
                info('subscription cancelled', [$event]);
                $account = $this->handleCustomerSubscriptionCanceled($event);
                break;
            default:
                echo 'Received unknown event type '.$event->type;
                break;
        }

        http_response_code(200);
    }

    private function validateWebhook(Request $request)
    {
        $endpointSecret = config('services.stripe.webhook_secret');
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');

        return \Stripe\Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
    }

    private function handleInvoicePaymentSucceeded($event)
    {
        $subscription = $event->data->object;
        $subscriptionStart = now();
        $subscriptionEnd = now()->addMonth(1);

        if ($subscription->status == 'paid' && in_array($subscription->billing_reason, ['subscription_cycle', 'subscription_create', 'subscription_update', 'subscription'])) {
            $productId = null;
            foreach ($subscription->lines->data as $lineItem) {
                // Check if the line item is for a subscription (product-based)
                if ($lineItem->type === 'subscription' && $lineItem->plan) {
                    // Access the associated plan and product
                    $planId = $lineItem->plan->id; // Plan ID
                    $productId = $lineItem->plan->product; // Product ID
                    $subscriptionStart = date('Y/m/d H:i:s', $lineItem->period->start); //
                    $subscriptionEnd = date('Y/m/d H:i:s', $lineItem->period->end); //
                }
            }
            $this->updateUserSubscription($subscription->subscription, $subscription->customer, $subscriptionStart, $subscriptionEnd, $productId);
        }
    }

    private function handleInvoicePaymentFailed($event)
    {
        $subscription = $event->data->object;
        if (in_array($subscription->billing_reason, ['subscription_cycle', 'subscription_update', 'subscription'])) {
            $productId = null;
            foreach ($subscription->lines->data as $lineItem) {
                // Check if the line item is for a subscription (product-based)
                if ($lineItem->type === 'subscription' && $lineItem->plan) {
                    // Access the associated plan and product
                    $planId = $lineItem->plan->id; // Plan ID
                    $productId = $lineItem->plan->product; // Product ID

                    // Do something with the product and plan IDs
                    // For example, log them or use them in your logic
                }
            }
            $this->updateUserSubscription($subscription->subscription, $subscription->customer, null, now(), $productId);
        }
    }

    private function handleCustomerSubscriptionCreated($event)
    {
        $subscription = $event->data->object;
        $customer = $subscription->customer;
        $subscriptionEnd = date('Y/m/d H:i:s', $subscription->current_period_end);
        $subscriptionStart = date('Y/m/d H:i:s', $subscription->current_period_start);

        if ($subscription->status == 'paid') {
            $planId = $subscription->items->data[0]->plan->id; // Plan ID
            $productId = $subscription->items->data[0]->plan->product; // Product ID
            $this->updateUserSubscription($subscription->subscription, $customer, $subscriptionStart, $subscriptionEnd, $productId);
        }

        http_response_code(200);
    }

    private function handleCustomerSubscriptionUpdated($event)
    {
        $subscription = $event->data->object;
        $customer = $subscription->customer;
        $subscriptionEnd = date('Y/m/d H:i:s', $subscription->current_period_end);
        $subscriptionStart = date('Y/m/d H:i:s', $subscription->current_period_start);

        if ($subscription->status == 'paid') {
            $planId = $subscription->items->data[0]->plan->id; // Plan ID
            $productId = $subscription->items->data[0]->plan->product; // Product ID
            $this->updateUserSubscription($subscription->subscription, $customer, $subscriptionStart, $subscriptionEnd, $productId);
        }

        http_response_code(200);
    }

    private function handleCustomerSubscriptionCanceled($event)
    {
        $subscription = $event->data->object;
        $subscriptionEnd = $subscription->ended_at ? date('Y/m/d H:i:s', $subscription->ended_at) : date('Y/m/d H:i:s', $subscription->current_period_end);

        if ($subscription->status == 'canceled') {
            $planId = $subscription->items->data[0]->plan->id; // Plan ID
            $productId = $subscription->items->data[0]->plan->product; // Product ID
            $this->updateUserSubscription($subscription->id, $subscription->customer, null, $subscriptionEnd, $productId);
        }
    }

    private function updateUserSubscription($subscriptionId, $customerId, $startAt = null, $expiresAt = null, $productId = null)
    {
        $user = User::where('stripe_customer_id', $customerId)->first();

        DB::beginTransaction();
        try {
            if ($user) {
                $user->update(['expire_untill' => $expiresAt ?: now()]);
                $plan = DB::table('plans')->where('stripe_product', $productId)->first();
                $data = [];
                if ($startAt) {
                    $data['starts_at'] = $startAt;
                }

                $data['updated_at'] = now();
                $data['ends_at'] = $expiresAt ?: now()->addMonth(1);

                Subscription::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'subscription' => $subscriptionId,
                        'plan_id' => $plan->id,
                    ],
                    $data
                );

                $user->update([
                    'is_active' => true,
                    'payment_mode' => 'subscription',
                    'expire_untill' => $expiresAt ?: now()->addMonth(1),
                ]);
            }
            DB::commit();
        } catch (Throwable $th) {
            info($th->getMessage());
            DB::rollBack();
        }
    }
}
