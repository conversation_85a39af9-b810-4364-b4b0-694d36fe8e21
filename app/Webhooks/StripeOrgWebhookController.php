<?php

namespace App\Webhooks;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessStripeOrgWebhook;
use App\Models\Invoice;
use Illuminate\Http\Request;
use stdClass;

class StripeOrgWebhookController extends Controller
{
    public function handle(Request $request)
    {
        $event = new stdClass;
        try {
            $event = $this->validateWebhook($request);
        } catch (\UnexpectedValueException|\Stripe\Exception\SignatureVerificationException $e) {
            http_response_code(400);
            exit();
        }

        // Handle the event
        switch ($event->type) {
            // If payment is succeeded
            case 'checkout.session.completed':
                $account = $this->handleCheckoutSessionCompleted($event);
                break;
            default:
                echo 'Received unknown event type '.$event->type;
                break;
        }

        http_response_code(200);
    }

    private function validateWebhook(Request $request)
    {
        $endpointSecret = config('services.stripe.client_webhook.secret');
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');

        return \Stripe\Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
    }

    private function handleCheckoutSessionCompleted($event)
    {
        $session = $event->data->object;
        if ($session->payment_status == 'paid' && $session->status == 'complete' && $session->mode = 'payment') {
            $invoice = Invoice::where('id', decodeID($session->metadata->invoice_id))->first();
            if ($invoice->status !== 'paid') { // Check if not already paid
                // Queue the job to send the email with a delay
                ProcessStripeOrgWebhook::dispatch($invoice, $session)->delay(now()->addMinutes(1.5)); // Delayed by 1.5 minutes
            }
        }
    }
}
