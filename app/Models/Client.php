<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\File;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Client extends Authenticatable implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use Notifiable;

    protected $fillable = [
        'first_name', 'last_name', 'organization_id', 'company_name', 'title', 'email',
        'alternate_email', 'mobile_no', 'office_no', 'stripe_customer_id', 'alternate_no', 'is_active', 'added_by', 'image', 'first_login', 'password', 'invite_sent',
    ];

    protected $appends = [
        'full_name',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'stripe_customer_id',
    ];

    public static function boot()
    {
        parent::boot();
        // auto-sets values on creation
        static::updating(function ($client) {
            if ($client->isDirty('image') && $client->getOriginal('image')) {
                $oldImagePath = public_path('uploads/'.$client->getOriginal('image'));
                if (File::exists($oldImagePath)) {
                    File::delete($oldImagePath);
                }
            }
        });
    }

    public function getFullNameAttribute()
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function propertyAddress(): HasOne
    {
        return $this->hasOne(Adress::class)->where('type', PROPERTY);
    }

    public function billingAddress(): HasOne
    {
        return $this->hasOne(Adress::class)->where('type', BILLING);
    }

    public function requests(): HasMany
    {
        return $this->hasMany(Estimate::class, 'client_id');
    }

    public function organization()
    {
        return $this->belongsTo(User::class, 'organization_id');
    }

    public function organizationStripeAccount(): HasOneThrough
    {
        return $this->hasOneThrough(
            StripeAccountDetail::class,
            User::class,
            'id', // Foreign key on the Invoice table (organization_id)
            'organization_id', // Foreign key on the StripeAccountDetail table
            'organization_id', // Local key on the Organization table
            'id' // Local key on the Invoice table
        );
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }
}
