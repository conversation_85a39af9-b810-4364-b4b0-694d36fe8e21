<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactInformation extends Model
{
    use HasFactory;

    protected $fillable = [
        'opportunity_id',
        'customer_issue_id',
        'first_name',
        'last_name',
        'title_role',
        'phone_number',
        'email',
        'role',
    ];

    // Optionally define the relationship if needed
    public function opportunity()
    {
        return $this->belongsTo(Opportunity::class);
    }

    public function CustomerIssue()
    {
        return $this->belongsTo(CustomerIssue::class);
    }
}
