<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $dates = [
        'trial_ends_at',
        'starts_at',
        'ends_at',
        'canceled_at',
    ];

    // public function getEndsAtAttribute($value)
    // {
    //     $date = new Carbon($value);
    //     return $date->format('d/m/Y');
    // }

    public function getStartsAtAttribute($value)
    {
        $date = new Carbon($value);

        return $date->format('d/m/Y');
    }

    public function plan(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'id');
    }
}
