<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SnowSetup extends Model
{
    use HasFactory;

    protected $table = 'snow_setups';

    protected $fillable = [
        'category',
        'lineitem',
        'unit_cost',
        'uom',
    ];

    const EQUIPMENT_CATEGORY = 'Equipments';

    const LABOR_CATEGORY = 'Labor';

    const MATERIAL_CATEGORY = 'Material';

    const STAND_BY_CATEGORY = 'Stand By';

    // Frontend display names (commonly used in frontend)
    const EQUIPMENT_DISPLAY = 'Equipment';

    const MATERIALS_DISPLAY = 'Materials';

    public static function getEquipment()
    {
        return self::where('category', self::EQUIPMENT_CATEGORY)->get();
    }

    public static function getLabor()
    {
        return self::where('category', self::LABOR_CATEGORY)->get();
    }

    public static function getMaterial()
    {
        return self::where('category', self::MATERIAL_CATEGORY)->get();
    }

    public static function getStandBy()
    {
        return self::where('category', self::STAND_BY_CATEGORY)->get();
    }

    /**
     * Get all categories for frontend use
     */
    public static function getCategories()
    {
        return [
            self::EQUIPMENT_CATEGORY,
            self::LABOR_CATEGORY,
            self::MATERIAL_CATEGORY,
            self::STAND_BY_CATEGORY,
        ];
    }

    /**
     * Map database categories to frontend display names
     */
    public static function getCategoryDisplayName($category)
    {
        $mapping = [
            self::EQUIPMENT_CATEGORY => self::EQUIPMENT_DISPLAY,
            self::MATERIAL_CATEGORY => self::MATERIALS_DISPLAY,
            self::LABOR_CATEGORY => self::LABOR_CATEGORY,
            self::STAND_BY_CATEGORY => self::STAND_BY_CATEGORY,
        ];

        return $mapping[$category] ?? $category;
    }

    /**
     * Map frontend display names back to database categories
     */
    public static function getDatabaseCategory($displayName)
    {
        $mapping = [
            self::EQUIPMENT_DISPLAY => self::EQUIPMENT_CATEGORY,
            self::MATERIALS_DISPLAY => self::MATERIAL_CATEGORY,
            self::LABOR_CATEGORY => self::LABOR_CATEGORY,
            self::STAND_BY_CATEGORY => self::STAND_BY_CATEGORY,
        ];

        return $mapping[$displayName] ?? $displayName;
    }
}
