<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemBill extends Model
{
    use HasFactory;

    protected $fillable = [
        'item_id',
        'vendor_name',
        'invoice_number',
        'invoice_date',
        'due_date',
        'invoice_amount',
        'invoice_image',
        'actual_qty',
    ];

    public function item()
    {
        return $this->belongsTo(EstimateItem::class, 'item_id');
    }
}
