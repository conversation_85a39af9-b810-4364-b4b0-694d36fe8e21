<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'address1',
        'address2',
        'city',
        'state',
        'zip',
        'opportunity_id',
        'company_id',
        'type',
        'website_url',
        'phone_no',
        'timezone',
        'date_format',
        'time_format',
    ];

    public function opportunity()
    {
        return $this->belongsTo(Opportunity::class);
    }

    public function company()
    {
        return $this->belongsTo(User::class, 'company_id');  // Changed to User model
    }

    public function opportunities()
    {
        return $this->hasMany(Opportunity::class, 'organization_id', 'company_id');
    }
}
