<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class section extends Model
{
    use HasFactory;

    protected $fillable = [
        'opportunity_id',
        'section_name',
        'organization_id',
    ];

    public function estimateItems()
    {
        return $this->hasMany(EstimateItem::class, 'section_name', 'id');
    }
}
