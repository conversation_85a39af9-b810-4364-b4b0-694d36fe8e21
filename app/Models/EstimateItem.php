<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimateItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'generate_estimate_id',
        'item_name',
        'opportunity_id',
        'quantity',
        'total_cost',
        'category_type',
        'section_name',
        'uom',
        'gross_margin',
        'unit_cost',
        'total_cost',
        'unit_price',
        'total_price',
        'item_id',
        'labor_type',
        'depth',
        'sqft',
        'material-qty',
    ];

    public function bills()
    {
        return $this->hasMany(ItemBill::class, 'item_id');
    }

    public function section()
    {
        return $this->belongsTo(section::class, 'section_name', 'id');
    }

    public function opportunity()
    {
        return $this->belongsTo(Opportunity::class, 'opportunity_id', 'id');
    }

    public function material()
    {
        return $this->hasOne(HardMaterial::class, 'id', 'item_id');
    }

    public function hardMaterial()
    {
        return $this->hasOne(HardMaterial::class, 'id', 'item_id');
    }

    public function snowSetup()
    {
        return $this->hasOne(\App\Models\SnowSetup::class, 'id', 'item_id');
    }
}
