<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Adress extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'address1',
        'address2',
        'city',
        'state',
        'zip',
        'opportunity_id',
        'compnany_id',
    ];

    public function opportunity()
    {
        return $this->belongsTo(Opportunity::class);
    }

    protected $guarded = [];
}
