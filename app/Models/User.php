<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use HasRoles;
    use Notifiable;
    use SoftDeletes;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
        'stripe_customer_id',
        'setup_invoice_id',
        'type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
        'name',
    ];

    protected $dates = [
        'expire_untill',
    ];

    public function getNameAttribute()
    {
        return $this->first_name.' '.$this->last_name;
    }

    public function getPackageDaysLeftAttribute()
    {
        return now()->diffInDays($this->expire_untill, false);
    }

    public function isAdmin(): bool
    {
        return (int) $this->type === ADMIN;
    }

    public function isOrganization(): bool
    {
        return (int) $this->type === ORGANIZATION;
    }

    public function opportunitiesAsOwner()
    {
        return $this->hasMany(Opportunity::class, 'opportunity_owner_id');
    }

    public function opportunitiesAsEstimator()
    {
        return $this->hasMany(Opportunity::class, 'estimator_id');
    }

    public function opportunitiesAsSalesman()
    {
        return $this->hasMany(Opportunity::class, 'sale_person_id');
    }

    public function isEmployee(): bool
    {
        return (int) $this->type === EMPLOYEE;
    }

    public function isAccountVerified(): bool
    {
        return ! ($this->email_verified_at === null);
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class)
            ->where('is_main', true)->latestOfMany();
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function companyAddress(): HasOne
    {
        return $this->hasOne(CompanyAddress::class, 'company_id');
    }

    public function companyPropertyAddress(): HasOne
    {
        return $this->hasOne(CompanyAddress::class, 'company_id')->where('type', PROPERTY);
    }

    public function companyBillingAddress(): HasOne
    {
        return $this->hasOne(CompanyAddress::class, 'company_id')->where('type', BILLING);
    }

    public function stripeAccount()
    {
        return $this->hasOne(StripeAccountDetail::class, 'organization_id', 'id');
    }
}
