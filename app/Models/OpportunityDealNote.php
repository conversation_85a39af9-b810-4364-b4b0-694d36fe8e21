<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OpportunityDealNote extends Model
{
    use HasFactory;

    protected $table = 'opportunity_dealnotes';

    // Set the fillable fields to allow mass assignment
    protected $fillable = ['opportunity_id', 'deal_notes'];

    public function opportunityget()
    {
        return $this->belongsTo(Opportunity::class);
    }
}
