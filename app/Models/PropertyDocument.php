<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyDocument extends Model
{
    use HasFactory;

    protected $table = 'property_documents';

    // Specify which attributes are mass assignable
    protected $fillable = ['property_id', 'document_name', 'name', 'path', 'type'];

    // Relationships
    public function property()
    {
        return $this->belongsTo(Property::class);
    }
}
