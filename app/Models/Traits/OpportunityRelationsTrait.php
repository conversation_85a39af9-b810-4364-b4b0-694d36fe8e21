<?php

namespace App\Models\Traits;

use App\Models\CompanyAddress;
use App\Models\Division;
use App\Models\EstimateItem;
use App\Models\ServiceLine;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait OpportunityRelationsTrait
{
    public function address()
    {
        return $this->hasOne(CompanyAddress::class);
    }

    public function estimateItem()
    {
        return $this->hasMany(EstimateItem::class, 'opportunity_id', 'id');
    }

    public function serviceLine()
    {
        return $this->hasOne(ServiceLine::class, 'id', 'service_line_id');
    }

    public function division(): BelongsTo
    {
        return $this->belongsTo(Division::class, 'division_id', 'id');
    }

    public function getAccountOwnerNameAttribute()
    {
        return $this->opportunityOwner?->first_name.' '.$this->opportunityOwner?->last_name;
    }

    public function getEstimatorNameAttribute()
    {
        return $this->estimator?->first_name.' '.$this->estimator?->last_name;
    }
}
