<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GenerateEstimate extends Model
{
    use HasFactory;

    protected $guarded = [];

    public static $WON = 'won';

    public static $LOST = 'lost';

    public static $PROPOSED = 'proposed';

    // Client Status
    public static $CLIENT_PROPOSED_STATUS = 'proposed';

    public static $CLIENT_APPROVE_STATUS = 'approve';

    public static $CLIENT_REJECT_STATUS = 'reject';

    public static $CLIENT_REQUEST_CHANGE_STATUS = 'request_change';

    protected $appends = [
        'grand_total',
    ];

    public function setLaborHoursInfoAttribute($value)
    {
        $this->attributes['labor_hours_info'] = json_encode($value);
    }

    public function getLaborHoursInfoAttribute($value)
    {
        return json_decode($value, true);
    }

    public function getGrandTotalAttribute()
    {
        if (empty($this->attributes['desire_margin'])) {
            return custom_number_format($this->attributes['total_price']);
        }
        // $finalPayableValue = $this->attributes['desire_margin'] ? $this->attributes['total_cost'] + $this->attributes['total_cost'] * ($this->attributes['desire_margin'] / 100) : $this->attributes['total_price'];
        $finalPayableValue = $this->attributes['desire_margin'] ? $this->attributes['final_price'] : $this->attributes['total_price'];

        return custom_number_format($finalPayableValue);
    }

    // public function request()
    // {
    //     return $this->belongsTo(Estimate::class, 'request_id');
    // }

    public function opportunityid()
    {
        return $this->belongsTo(Opportunity::class, 'opportunity_id');
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    // public function client()
    // {
    //     return $this->hasOneThrough(
    //         Client::class,
    //         Estimate::class,
    //         'id',
    //         'id',
    //         'request_id',
    //         'client_id'
    //     );
    // }

    // public function client()
    // {
    //     return $this->hasOneThrough(
    //         Contact::class,      // Target model (Account)
    //         Opportunity::class,  // Intermediate model (Opportunity)
    //         'id',                // Foreign key on Opportunity table (referring to GenerateEstimate)
    //         'id',                // Foreign key on Account table (referring to Opportunity)
    //         'opportunity_id',    // Local key on GenerateEstimate table (referring to Opportunity)
    //         'contact_id'         // Foreign key on Opportunity table (referring to Account)
    //     );
    // }

    public function client()
    {
        return $this->hasOneThrough(
            Contact::class,      // Target model (Contact)
            Opportunity::class,  // Intermediate model (Opportunity)
            'id',                // Foreign key on Opportunity table (referring to GenerateEstimate)
            'id',                // Foreign key on Account table (referring to Opportunity)
            'opportunity_id',    // Local key on GenerateEstimate table (referring to Opportunity)
            'contact_id'         // Foreign key on Opportunity table (referring to Account)
        )->where('contacts.organization_id', getOrganizationId()); // Ensure organization_id matches
    }

    // public function client()
    // {
    //     return $this->hasOneThrough(
    //         Account::class,
    //         Opportunity::class,
    //         'id',
    //         'id',
    //         'opportunity_id',
    //         'client_id'
    //     );
    // }

    public function saleMan()
    {
        return $this->hasOneThrough(
            User::class,
            Opportunity::class,
            'id',
            'id',
            'opportunity_id',
            'sale_person_id'
        );
    }

    public function estimator()
    {
        return $this->hasOneThrough(
            User::class,
            Opportunity::class,
            'id',
            'id',
            'opportunity_id',
            'estimator_id'
        );
    }

    public function schedules()
    {
        return $this->hasMany(GenerateEstimateSchedule::class);
    }

    public function scheduleOneDate()
    {
        return $this->hasOne(GenerateEstimateSchedule::class);
    }

    public function clientAction()
    {
        return $this->hasOne(ClientEstimateAction::class);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class, 'operation_id', 'id');
    }

    // Get Scheudle Latest Date
    public function latestScheduleDate()
    {
        // return $this->hasOne(GenerateEstimateSchedule::class)
        // ->where(function ($query) {
        //     // Include future dates
        //     $query->where('date', '>', now()->toDateString())
        //         // Or include today's date
        //         ->orWhereDate('date', '=', now()->toDateString());
        // })
        // ->oldest('date')
        // ->withDefault(function ($schedule) {
        //     // Use the latest past date if no future dates are found
        //     $schedule->date = GenerateEstimateSchedule::where('date', '<=', now()->toDateString())
        //         ->latest('date')
        //         ->value('date');
        // });

        return $this->hasOne(GenerateEstimateSchedule::class)->oldest('date')->withDefault(function ($schedule) {
            $schedule->date = GenerateEstimateSchedule::latest('date')->value('date');
        });
    }

    public function cost_summary()
    {
        return $this->hasOne(CostSummary::class, 'operation_id', 'id');
    }

    // public function serviceLine()
    // {
    //     return $this->hasOne(ServiceLineMaterial::class, 'id', 'service_line_id');
    // }

    public function serviceLine()
    {
        return $this->hasOne(ServiceLine::class, 'id', 'service_line_id');
    }

    public function workType()
    {
        return $this->hasOne(ServiceLineWorkType::class, 'id', 'work_type_id');
    }

    public function getEstimateMaterial()
    {
        return $this->hasMany(EstimateMaterial::class, 'generate_estimate_id', 'id');
    }

    public function getEstimateHardMaterial()
    {
        return $this->hasMany(EstimateHardMaterial::class, 'generate_estimate_id', 'id');
    }

    public function getEstimatePlantMaterial()
    {
        return $this->hasMany(EstimatePlantMaterial::class, 'generate_estimate_id', 'id');
    }

    public function getEstimateOtherCost()
    {
        return $this->hasMany(EstimateOtherCost::class, 'generate_estimate_id', 'id');
    }

    public function getEstimateSubContractor()
    {
        return $this->hasMany(EstimateSubContractor::class, 'generate_estimate_id', 'id');
    }

    public function getestimateLabor()
    {
        return $this->hasMany(EstimateLabor::class, 'generate_estimate_id', 'id');
    }
}
