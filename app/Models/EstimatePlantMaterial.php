<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimatePlantMaterial extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function material()
    {
        return $this->belongsTo(PlantMaterial::class, 'plant_material_id');
    }

    public function setVendorInfoAttribute($value)
    {
        $this->attributes['vendor_info'] = json_encode($value);
    }

    public function getVendorInfoAttribute($value)
    {
        return json_decode($value);
    }

    public function plantMaterial()
    {
        return $this->hasOne(PlantMaterial::class, 'id', 'plant_material_id');
    }
}
