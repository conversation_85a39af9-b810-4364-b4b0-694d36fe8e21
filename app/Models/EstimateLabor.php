<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimateLabor extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function labor()
    {
        return $this->belongsTo(Labor::class, 'labor_id');
    }

    public function setVendorInfoAttribute($value)
    {
        $this->attributes['vendor_info'] = json_encode($value);
    }

    public function getVendorInfoAttribute($value)
    {
        return json_decode($value);
    }

    public function setLaborHoursInfoAttribute($value)
    {
        $this->attributes['labor_hours_info'] = json_encode($value);
    }

    public function getLaborHoursInfoAttribute($value)
    {
        return json_decode($value);
    }
}
