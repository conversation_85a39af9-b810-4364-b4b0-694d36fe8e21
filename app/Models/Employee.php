<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    protected $fillable = [
        'first_name',
        'last_name',
        'phone_number',
        'email_address',
        'title',
        'division_id', // Updated to division_id
        'status',
        'pay_rate',
        'overtime_rate',
        'employee_number',
        'drivers_license_number',
        'state_issued',
        'date_issued',
        'expiration_date',
        'dot_medical_card_issued_date',
        'dot_medical_card_expiration_date',
        'organization_id',
    ];

    protected $dates = [
        'date_issued',
        'expiration_date',
        'dot_medical_card_issued_date',
        'dot_medical_card_expiration_date',
    ];

    public function division()
    {
        return $this->belongsTo(Division::class);
    }
}
