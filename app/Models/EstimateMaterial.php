<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimateMaterial extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_id');
    }

    public function setVendorInfoAttribute($value)
    {
        $this->attributes['vendor_info'] = json_encode($value);
    }

    public function getVendorInfoAttribute($value)
    {
        return json_decode($value);
    }
}
