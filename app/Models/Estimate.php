<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Estimate extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $highestJobNo = static::where('organization_id', $model->organization_id)->latest()->max('job_no') ?? 0; // Get the highest job_no value
            // $model->organization_id = $organization_id;
            $model->job_no = $highestJobNo + 1; // Increment the highest value by 1
            $model->sales_order_number = 0;
        });

        static::created(function ($model) {});
    }

    public function scopeShow($query)
    {
        return $query->where('client_show', 1); // Replace 'status' with the column name representing the status
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function saleMan()
    {
        return $this->belongsTo(User::class, 'sale_person_id');
    }

    public function estimator()
    {
        return $this->belongsTo(User::class, 'estimator_id');
    }

    public function propertyAddress(): HasOne
    {
        return $this->hasOne(Adress::class, 'estimate_id')->where('type', PROPERTY);
    }

    public function generateEstimate()
    {
        return $this->hasOne(GenerateEstimate::class, 'request_id');
    }

    public function organization()
    {
        return $this->hasOne(User::class, 'id', 'organization_id');
    }
}
