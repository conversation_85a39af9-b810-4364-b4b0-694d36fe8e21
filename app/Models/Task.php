<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'subject',
        'start_date',
        'start_time',
        'end_date',
        'end_time',
        'assign_to',
        'organization_id',
        'assign_to_type',
    ];

    public function client()
    {
        return $this->belongsTo(Contact::class, 'assign_to');
    }

    public function clientUser()
    {
        return $this->belongsTo(User::class, 'assign_to');
    }

    public function isDue()
    {
        // Combine end_date and end_time into a Carbon instance
        $endDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $this->end_date.' '.$this->end_time);

        // Compare with the current date and time
        // return Carbon::now()->greaterThan($endDateTime);
        return Carbon::now()->lessThanOrEqualTo($endDateTime);
    }
}
