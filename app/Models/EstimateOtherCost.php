<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimateOtherCost extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function otherCost()
    {
        return $this->belongsTo(OtherCost::class, 'other_cost_id');
    }

    public function setVendorInfoAttribute($value)
    {
        $this->attributes['vendor_info'] = json_encode($value);
    }

    public function getVendorInfoAttribute($value)
    {
        return json_decode($value);
    }
}
