<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EstimateHardMaterial extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function material()
    {
        return $this->belongsTo(HardMaterial::class, 'hard_material_id');
    }

    public function setVendorInfoAttribute($value)
    {
        $this->attributes['vendor_info'] = json_encode($value);
    }

    public function getVendorInfoAttribute($value)
    {
        return json_decode($value);
    }

    public function hardMaterial()
    {
        return $this->HasOne(HardMaterial::class, 'id', 'hard_material_id');
    }
}
