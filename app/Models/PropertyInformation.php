<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyInformation extends Model
{
    use HasFactory;

    protected $fillable = [
        'opportunity_id',
        'customer_issue_id',
        'address1',
        'address2',
        'city',
        'state',
        'zip',
        'company_id',
        'name',
        'organization_id',
        'property_health',
    ];

    // public function opportunity() // BelongsTo relation with Opportunity
    // {
    //     return $this->belongsTo(Opportunity::class);
    // }
    public function opportunity()
    {
        return $this->hasOne(Opportunity::class, 'property_id');
    }

    public function customerIssues()
    {
        return $this->hasMany(CustomerIssue::class, 'property_id');
    }

    public function contacts()  // Assuming a contacts relation exists elsewhere
    {
        return $this->hasMany(Contact::class);
    }

    public function accountsproperty()
    {
        return $this->belongsTo(Account::class, 'company_id', 'id');
    }
}
