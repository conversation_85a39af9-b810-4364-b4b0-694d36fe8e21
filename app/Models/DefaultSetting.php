<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DefaultSetting extends Model
{
    use HasFactory;

    protected $table = 'default_settings';

    protected $fillable = [
        'cov_title',
        'cov_sub_title',
        'cov_image',
        'intro',
        'project_checked',
        'opportunity_id',
        'setting_type',
        'created_at',
        'updated_at',
        'payment_schedule',
        'expiry',
        'default_project',
    ];

    protected $casts = [
        'project_checked' => 'boolean',
    ];
}
