<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerIssue extends Model
{
    use HasFactory;

    protected $fillable = [
        'property_name',
        'account_id',
        'assigned_job',
        'assign_to',
        'category',
        'contact_id',
        'property_id',
        'subject',
        'description',
        'completed_description',
        'status',
        'organization_id',
        'created_by',
    ];

    /**
     * Get the address associated with the customer issue.
     */
    public function address()
    {
        return $this->hasOne(Address::class, 'issue_id');
    }

    public function opportunitydata()
    {
        return $this->belongsTo(Opportunity::class, 'assigned_job');
    }

    // public function propertyInformation() // New relation for detailed property information
    // {
    //     return $this->hasOne(PropertyInformation::class);
    // }
    public function propertyInformation()
    {
        return $this->belongsTo(PropertyInformation::class, 'property_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Define relationship for the 'assign_to' field
    public function assignee()
    {
        return $this->belongsTo(User::class, 'assign_to');
    }

    public function documents()
    {
        return $this->hasMany(CustomerIssueDocument::class);
    }

    /**
     * Get the organization that owns the customer issue.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Scope a query to only include issues of a given status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to search issues by property name or subject.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('property_name', 'like', "%{$search}%")
            ->orWhere('subject', 'like', "%{$search}%");
    }
}
