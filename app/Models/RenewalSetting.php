<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RenewalSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'division_id',
        'account_id',
        'renewal_month',
        'start_date',
        'auto_activate',
        'expiry_contract',
        'renewal_time',
    ];

    protected $casts = [
        'auto_activate' => 'boolean',
        'start_date' => 'date',
    ];

    public function division()
    {
        return $this->belongsTo(Division::class);
    }

    public function account()
    {
        return $this->belongsTo(User::class, 'account_id');
    }
}
