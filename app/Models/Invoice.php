<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Invoice extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'subject',
        'invoice_number',
        'organization_id',
        'status',
        'issue_date',
        'due_date',
        'client_id',
        'is_sent',
        'sub_total',
        'total',
        'discount_price',
        'discount_method',
        'tax',
        'notes',
        'operation_id',
        'payment_mode',
    ];

    public function client()
    {
        return $this->hasOne(Contact::class, 'id', 'client_id');
    }

    public function organizationStripeAccount(): HasOneThrough
    {
        return $this->hasOneThrough(
            StripeAccountDetail::class,
            User::class,
            'id', // Foreign key on the Invoice table (organization_id)
            'organization_id', // Foreign key on the StripeAccountDetail table
            'organization_id', // Local key on the Organization table
            'id' // Local key on the Invoice table
        );
    }

    public function organization()
    {
        return $this->hasOne(User::class, 'id', 'organization_id');
    }

    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'id', 'invoice_id');
    }

    public function operation()
    {
        return $this->hasOne(GenerateEstimate::class, 'id', 'operation_id');
    }
}
