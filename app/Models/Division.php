<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Division extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    const SNOW_DIVISION = 'Snow';

    const LANDSCAPE_DIVISION = 'Landscape';

    public function serviceLines()
    {
        return $this->hasMany(ServiceLine::class);
    }

    public function workTypes()
    {
        return $this->hasMany(ServiceLineWorkType::class);
    }
}
