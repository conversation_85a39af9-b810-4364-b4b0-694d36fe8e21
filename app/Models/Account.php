<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Account extends Authenticatable
{
    use HasFactory;

    protected $table = 'accounts';

    protected $fillable = [
        'company_name',
        'website',
        'email',
        'mobile_no',
        'city',
        'state',
        'zip',
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_zip',
        'address',
        'account_owner',
        'parent_id',
        'password',
    ];

    public function organization()
    {
        return $this->belongsTo(User::class, 'parent_id');
    }

    public function accountowner()
    {
        return $this->belongsTo(User::class, 'account_owner');
    }

    public function propertyAddress(): HasMany
    {
        return $this->hasMany(PropertyInformation::class, 'company_id');
    }
}
