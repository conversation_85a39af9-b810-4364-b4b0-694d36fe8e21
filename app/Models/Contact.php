<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Contact extends Authenticatable
{
    use HasFactory;

    protected $fillable = [
        'opportunity_id',
        'first_name',
        'email',
        'phone_number',
        'last_name',
        'title',
        'second_email',
        'second_phone',
        'account',
        'role',
        'suffix',
        'prefix',
        'mailing_address',
        'organization_id',
        'property_default',
        'property_id',
        'password',

    ];

    public function accountget()
    {
        return $this->belongsTo(Account::class, 'account');
    }

    public function opportunity()
    {
        return $this->hasOne(Opportunity::class);
    }

    public function organization()
    {
        return $this->belongsTo(User::class, 'organization_id');
    }

    public function propertyAddress()
    {
        return $this->belongsTo(PropertyInformation::class, 'property_id');
    }
}
