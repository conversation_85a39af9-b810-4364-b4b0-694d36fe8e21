<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {

        $uri = request()->getRequestUri();
        if ($uri && str_starts_with($uri, '/client') || str_starts_with($uri, '/organization') || str_starts_with($uri, '/dashboard') || str_starts_with($uri, '/employee')) {

            if (str_starts_with($uri, '/client')) {
                $guardName = 'client';
            } else {
                $guardName = 'organization';
            }

            $this->renderable(function (ModelNotFoundException $e, $request) use ($guardName) {
                if (! $request->wantsJson() && ! $request->is('api/*')) {
                    return response()->view('custom-errors.'.$guardName.'.error', [
                        'code' => 404,
                        'heading' => 'Record Not Found',
                        'message' => 'Sorry the record you are looking for does not exist, if you think something is broken, report a problem',
                    ], 404);
                }
            });

            $this->renderable(function (AuthorizationException $e, $request) use ($guardName) {
                if (! $request->wantsJson() && ! $request->is('api/*')) {
                    return response()->view('custom-errors.'.$guardName.'.error', [
                        'code' => 403,
                        'heading' => 'Permission Denied',
                        'message' => 'Sorry, the page you are looking for is not accessible to you.',
                    ], 404);
                }
            });

            $this->renderable(function (HttpException $e, $request) use ($guardName) {
                if (! $request->wantsJson() && ! $request->is('api/*')) {
                    if ($e->getStatusCode() == 500) {
                        return response()->view('custom-errors.'.$guardName.'.error', [
                            'code' => 500,
                            'heading' => 'Internal Server Error',
                            'message' => 'Oops! Something went wrong on our end. Please try again later.',
                        ], 500);
                    } elseif ($e->getStatusCode() == 404 && str_starts_with(strtolower($e->getMessage()), strtolower('No query results for model'))) {
                        return response()->view('custom-errors.'.$guardName.'.error', [
                            'code' => 404,
                            'heading' => 'Record Not Found',
                            'message' => 'Sorry the record you are looking for does not exist, if you think something is broken, report a problem',
                        ], 404);
                    } elseif ($e->getStatusCode() == 403) {
                        return response()->view('custom-errors.'.$guardName.'.error', [
                            'code' => 403,
                            'heading' => 'Permission Denied',
                            'message' => 'Sorry, the page you are looking for is not accessible to you.',
                        ], 404);
                    }
                }
            });

            $this->renderable(function (NotFoundHttpException $e, $request) use ($guardName) {
                if (! $request->wantsJson() && ! $request->is('api/*')) {
                    return response()->view('custom-errors.'.$guardName.'.error', [
                        'code' => 404,
                        'heading' => 'Page Not Found',
                        'message' => 'Sorry the page you are looking for does not exist, if you think something is broken, report a problem',
                    ], 404);
                }
            });
        }

        $this->reportable(function (Throwable $e) {
            //
        });

    }
}
