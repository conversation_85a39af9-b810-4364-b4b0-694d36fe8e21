<?php

namespace App\Traits;

trait PermissionMiddlewareTrait
{
    protected function applyPermissionMiddleware(array $permissions)
    {
        // its works like this code
        // $this->middleware('permission:listing_announcement', [
        //     'only' =>
        //     ['listing', 'addAnnouncement', 'editAnnouncement', 'remove', 'announcementDetails']
        // ]);

        foreach ($permissions as $action => $permissionNames) {
            $this->middleware('permission:'.implode('|', $permissionNames), ['only' => $action]);
        }
    }
}
