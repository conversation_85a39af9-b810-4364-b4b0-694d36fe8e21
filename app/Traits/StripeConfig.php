<?php

namespace App\Traits;

trait StripeConfig
{
    protected string $stripeKey = '';

    protected string $stripeSecret = '';

    protected $stripeContext;

    public function setConfig(bool $isApi = false)
    {
        $this->stripeKey = config('services.stripe.key');
        $this->stripeSecret = config('services.stripe.secret');
        $this->stripeContext = new \Stripe\StripeClient($this->stripeSecret);
    }
}
