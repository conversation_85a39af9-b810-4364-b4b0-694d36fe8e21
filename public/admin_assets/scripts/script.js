$(document).ready(function () {
    $(".header_items .aside_toggle").on("click", function () {
        $(".sidebar_wrapper aside.side_bar").toggleClass("open"),
            $(this).toggleClass("fa-times");
    });

    $("body").on("click", ".toggle-password", function () {
        $(this).toggleClass("fa-eye fa-eye-slash");
        var input = $(this).parent(".password-wrapper").find("input");
        "password" === input.attr("type")
            ? input.attr("type", "text")
            : input.attr("type", "password");
    });
});

$(document).ready(function () {
    if ($(".basic-single-select").length) {
        $(".basic-single-select")
            .select2({
                width: "100%",
                minimumResultsForSearch: Infinity,
            })
            .on("select2:select", function (e) {
                $(this).next().next(".error").remove();
                $(this).removeClass("error");
            });
    }

    if ($(".basic-single-select-search").length) {
        $(".basic-single-select-search")
            .select2({
                width: "100%",
            })
            .on("select2:select", function (e) {
                $(this).next().next(".error").remove();
                $(this).removeClass("error");
            });
    }

    if ($(".table_filter_select").length) {
        $(".table_filter_select").select2({
            dropdownCssClass: "table_filter_dropdown",
            width: "100%",
            minimumResultsForSearch: Infinity,
            placeholder: "Filter",
        });
    }

    $(document).on("shown.bs.dropdown", ".dropdown", function () {
        // Get the position and dimensions of the button
        var button = $(this).find('[data-toggle="dropdown"]');
        var buttonPos = button.offset();
        var buttonWidth = button.outerWidth();
        var buttonHeight = button.outerHeight();

        // Check if buttonPos exists before accessing its properties
        if (buttonPos) {
            // Set the position of the dropdown menu
            $(this)
                .find(".dropdown-menu")
                .appendTo("body")
                .css({
                    position: "absolute",
                    top: buttonPos.top + buttonHeight,
                    left: buttonPos.left,
                    width: buttonWidth,
                });
        }
    });
});

//Custom Image Dropzone
// $(document).ready(function () {
//     ImgUpload();
//   });

//   function ImgUpload() {
//     var imgWrap = "";
//     var imgArray = [];

//     $('.drop-zone__input').each(function () {
//       $(this).on('change', function (e) {
//         imgWrap = $(this).closest('.drop_zone_wrapper').find('.uploaded__img__wrapper');
//         var maxLength = $(this).attr('data-max_length');

//         var files = e.target.files;
//         var filesArr = Array.prototype.slice.call(files);
//         var iterator = 0;
//         filesArr.forEach(function (f, index) {

//         if (f.type.startsWith("image/") || f.type == "application/pdf") {
//             if (imgArray.length > maxLength) {
//                 return false;
//             } else {
//                 imgArray.push(f);

//                 var reader = new FileReader();
//                 reader.onload = function (e) {
//                     var html;
//                     if (f.type.startsWith("image/")) {
//                         html = "<div title='" + f.name + "' class='uploaded_image'> <img src='"+ e.target.result +"' alt='" + f.name + "' class='main_image' data-number='" + $(".upload__img-close").length + "' data-file='" + f.name + "'/> <div class='file_name'>" + f.name + "</div>  <div class='upload__img-close'></div> </div>";
//                     } else {
//                         html = "<div title='" + f.name + "' class='uploaded_image'> <img src='data:image/svg+xml;base64,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' alt='" + f.name + "' class='main_image' data-number='" + $(".upload__img-close").length + "' data-file='" + f.name + "'/> <div class='file_name'>" + f.name + "</div>  <div class='upload__img-close'></div> </div>";
//                     }
//                     imgWrap.append(html);
//                     iterator++;
//                 };
//                 reader.readAsDataURL(f);
//             }
//         } else {
//             return false;
//         }

//         });
//       });
//     });

//     $('body').on('click', ".upload__img-close", function (e) {
//         var file = $(this).parent().data("file");
//         for (var i = 0; i < imgArray.length; i++) {
//           if (imgArray[i].name === file) {
//             imgArray.splice(i, 1);
//             break;
//           }
//         }
//         $(this).parent().remove();

//         // Reset the imgArray variable
//         imgArray = [];
//       });

//   }
//Custom Image Dropzone

$(document).ready(function () {
    if ($(".addNoteSummer").length) {
        $(".addNoteSummer").summernote({
            placeholder: "Enter description",
            tabsize: 2,
            height: 380,
            toolbar: [
                ["style", ["italic", "bold", "underline", "strikethrough"]],
            ],
            callbacks: {
                onImageUpload: function (files) {
                    // Prevent uploading images
                    alert("Image upload is disabled!");
                    return false;
                },
                onPaste: function (e) {
                    e.preventDefault();
                    var clipboardData =
                        e.originalEvent.clipboardData || window.clipboardData;
                    var plainText = clipboardData.getData("text/plain");

                    // Insert plain text into Summernote editor
                    var currentEditor = $(this);
                    currentEditor.summernote("insertText", plainText);
                    // Check if pasted content doesn't include plain text
                    if (!plainText) {
                        clipboardData.setData("");
                    }
                },
            },
        });
    }
});

$(document).ready(function () {
    $(".dataTables_scrollHead").remove();
});

// File Upload Button
// $(document).ready(function () {
//     var fileInput = $(".file_upload_button_form .input_file");
//     var label = $(".file_upload_button_form label");
//     var selectedFile;

//     fileInput.on("change", function (event) {
//         selectedFile = event.target.files[0];

//         if (
//             selectedFile.type === "application/vnd.ms-excel" ||
//             selectedFile.type ===
//                 "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
//         ) {
//             var fileName = selectedFile.name;
//             label.text(fileName);

//             $(this).parent(".file_upload_button_form").submit();
//         } else {
//             fileInput.val("");
//             label.text("Import Material");

//             var newElement = $(
//                 "<label class='error custom_error'>Only Excel File Is Allowed</label>"
//             );

//             $(this).after(newElement);

//             newElement.fadeOut(3000, function () {
//                 $(this).remove();
//             });
//         }
//     });

//     $(".file_upload_button_form").on("submit", function (e) {
//         e.preventDefault();
//     });
// });
// File Upload Button

// image upload button
$(document).ready(function () {
    var imagefile = $(".upload_image_btn .input_file");
    var label = $(".upload_image_btn label");
    var selectedFile;

    imagefile.on("change", function (event) {
        selectedFile = event.target.files[0];
        console.log(selectedFile.type);
        if (
            selectedFile.type === "image/png" ||
            selectedFile.type === "image/jpg" ||
            selectedFile.type === "image/webp" ||
            selectedFile.type === "image/jpeg"
        ) {
            var fileName = selectedFile.name;
            label.text(fileName);
        } else {
            imagefile.val("");
            label.text("Upload Picture");

            var newElement = $(
                "<label class='error custom_error'>Only Image is allowed</label>"
            );

            $(this).after(newElement);

            newElement.fadeOut(3000, function () {
                $(this).remove();
            });
        }
    });
});
// image upload button

$(document).ready(function () {
    var date = new Date();
    date.setFullYear(2020);

    $(".start-date")
        .datepicker({
            format: orgDateFormat,
            // autoclose: true,
            startDate: date,
        })
        .on("changeDate", function (e) {
            var checkInDate = e.date,
                $checkOut = $(".end-date");
            checkInDate.setDate(checkInDate.getDate() + 1);
            $checkOut.datepicker("setStartDate", checkInDate);

            // $checkOut.datepicker("setDate", checkInDate).focus();
            // Hide the datepicker after selecting a date
        });

    $(".end-date").datepicker({
        format: orgDateFormat,
        // autoclose: true
    });
});

$(document).ready(function () {
    // Get the current date and set it as the startDate
    var date = new Date();
    // Initialize the datepicker on the select box
    $("#dateSelect").datepicker({
        format: "yyyy-mm-dd", // Set the desired date format
        startDate: date,
        autoclose: true,
        todayHighlight: true,
    });

    // Handle the click event on the "Custom Date" option
    $("#dateSelect").change(function () {
        var selectedOption = $(this).val();

        $("#customDateInput").val(selectedOption);
        // console.log($("#customDateInput").val())
        // $(this).datepicker("show");
        // Show the datepicker when "Custom Date" option is selected
        if (selectedOption === "custom" || selectedOption === "custom date") {
            $(this).datepicker("show");
            if (selectedOption === "custom") {
                $(this).val("custom date");
            } else {
                $(this).val("custom");
            }
        }
    });

    $("input[data-provide='datepicker']").change(function () {
        $(this).datepicker("hide");
    });

    // Update the select box value with the selected date
    $("#dateSelect").on("changeDate", function (e) {
        var selectedDate = e.format();
        $("#customDateInput").val(selectedDate);
        $("#select2-dateSelect-container").text(selectedDate);
        $("#select2-dateSelect-container").attr("title", selectedDate);
        $("#dateSelect").next(".error-message").text("");
        $("#dateSelect").val(selectedDate);
        // $("#customDateInput").val(selectedDate);
    });
});

function resizeImageButton() {
    var newInput = $("<input>")
        .attr("type", "hidden")
        .attr("name", "resizeImage")
        .val("on");

    // Append the new input field to the form
    $("#brandingForm").append(newInput);
    $("#branding_image_modal").modal("hide");
    $("#submitButton").attr("disabled", false);
}

function fileValue(value) {
    var path = value.value;
    var extenstion = path.split(".").pop().toLowerCase();
    if (
        extenstion == "jpg" ||
        extenstion == "svg" ||
        extenstion == "jpeg" ||
        extenstion == "png"
    ) {
        var image = new Image();
        image.src = window.URL.createObjectURL(value.files[0]);
        image.onload = function () {
            var width = this.width;
            var height = this.height;

            console.log(`Dimensions: ${width} x ${height}`);
            // Display the image preview
            document.getElementById("image-preview").src = image.src;

            if (width > 84 || height > 44) {
                $("#submitButton").attr("disabled", true);

                $("#branding_image_modal").modal("show");
            } else {
                $("#submitButton").attr("disabled", false);
            }
        };
        // var filename = path
        //     .replace(/^.*[\\\/]/, "")
        //     .split(".")
        //     .slice(0, -1)
        //     .join(".");
        // document.getElementById("filename").innerHTML = filename;
    } else {
        $(".branding_drop_wrapper .branding_error")
            .text("File type should be PNG, JPEG, JPG, SVG")
            .delay(2000)
            .fadeOut(1000); // Fades out over 1 second
    }
}

function maskPhoneNumber(event) {
    var phoneNumberInput = event.target;
    var phoneNumber = phoneNumberInput.value;

    // Remove any non-digit characters from the phone number
    var cleanedNumber = phoneNumber.replace(/\D/g, "");

    // Format the phone number as xxx-xxx-xxxx
    var maskedNumber = cleanedNumber.replace(
        /(\d{3})(\d{3})(\d{4})/,
        "$1-$2-$3"
    );

    // Update the input value with the masked number
    phoneNumberInput.value = maskedNumber;
}

$(document).ready(function () {
    if (document.getElementById("teamManagementTable")) {
        $("#teamManagementTable").DataTable({
            columnDefs: [
                {
                    searchable: false,
                },
            ],
            dom: "lrtip", // Customize the table elements
            language: {
                search: "", // Remove the search label
                info: "Page 1 of 10", // Custom info text
            },
            drawCallback: function () {
                $("#serviceTable th")
                    .removeClass("sorting sorting_asc sorting_desc")
                    .addClass("sorting_disabled");
            },
        });
    }
    // Hide search field
    $(".dataTables_filter").hide();

    // Hide entity selector
    $(".dataTables_length").hide();

    // Hide pagination
    $(".dataTables_paginate").hide();

    // Hide info
    $(".dataTables_info").hide();
});

$(document).ready(function () {
    const dynamicImage = document.querySelector(".dynamic-image");

    const selectImage = document.querySelector("#update");
    if (selectImage) {
        selectImage.addEventListener("change", function (event) {
            const file = event.target.files[0];

            const reader = new FileReader();

            reader.onload = function (e) {
                dynamicImage.src = e.target.result;
            };

            reader.readAsDataURL(file);
        });
    }
});
