/* @import url(https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap); */

@import url("https://fonts.googleapis.com/css2?family=Cabin:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap");

* {
    margin: 0;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    outline: 0;
    list-style: none;
    color: #192a3e;
}
:root {
    --white: #ffffff;
    --green: #13bd38;
    --green2: #16c098;
    --black: #000000;
    --textblack: #192a3e;
    --textblack2: #2b2d42;
    /* --primaryblue: #003366; */
    --primaryblue: #0074d9;
    --textorange: #2ecc40;
    --lightgray: #e7e7e7;
    --lightgray2: #f8f8f8;
    --placeholdertext: #707683;
    --placeholdertext2: #a0a3bd;
    --bordercolor: #90a0b7;
    --bordercolor2: #dbdde0;
    --bordercolor3: #ebeff2;
    --bordercolor4: #f5f6f8;
    --red: #ec3c46;
    --boxShadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
    --sidebar_width: 272px;
}
::-moz-selection {
    background: #192a3e;
    color: #fff;
}
::selection {
    background: #192a3e;
    color: #fff;
}
html {
    font-size: 62.5%;
    scroll-behavior: smooth;
}
h1,
h2,
h3,
h4,
h5,
p {
    font-family: Cabin;
    font-style: normal;
    color: var(--black);
    margin: 0;
    padding: 0;
}
p {
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 2.6rem;
}

.note-editable p {
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    font-family: inherit;
    font-style: inherit;
    color: inherit;
}

.note-editable ul li {
    list-style: disc !important;
}

.note-editable u {
    text-decoration: underline;
}

.note-editable strike {
    text-decoration-line: line-through;
}

.ancher {
    text-decoration: none;
    font-family: Cabin;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: var(--textblack2);
}

.ancher_blue {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    text-decoration-line: underline;
    color: #003366;
}

.confirm_payment_btn {
    margin-top: 211px;
    margin-left: auto;
}

h1 {
    font-weight: 700;
    font-size: 3rem;
    line-height: 4.5rem;
}
h2 {
    font-weight: 600;
    font-size: 2.4rem;
    line-height: 3.6rem;
}
h3 {
    font-weight: 500;
    font-size: 2rem;
    line-height: 3rem;
}
h4 {
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 2.4rem;
}
h5 {
    font-weight: 400;
    font-size: 1.4rem;
    line-height: 2.1rem;
}
.font-14 {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
}
.font-16 {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
}
.font-32 {
    font-weight: 600;
    font-size: 3.2rem;
    line-height: 4.2rem;
}

input[type="radio"] {
    accent-color: var(--primaryblue);
    height: 14px;
    width: 14px;
}
input[type="checkbox"] {
    accent-color: var(--primaryblue);
    height: 16px;
    width: 30px;
}
.label {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    display: block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    user-select: none;
}
.label .steric {
    color: var(--red);
}
.status-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 4px 14px;
    gap: 10px;
    width: 129px;
    height: 40px;
    border-radius: 16px;
    color: #000; /* Adjust text color if needed */
}

.status-open {
    background: #C2EEFF;
    color: #28ACE6;
    border-radius: 60px;
    width: 105px;
    height: 30px;/* You can increase this value as needed */
}


.status-pending {
    /*background: #fffae8;
    color: #f1b602;*/
    background: #FFFACD;
    color: #000000;
}

.status-estimating {
    /*background: #e6f1fb;
    color: #0074d9;*/
    background: #B8DAFF;
    color: #007AFE;
    border-radius: 60px;
    width: 105px;
    height: 30px;
}

.status-production-review {
    /*background: #eae5fb;*/
    background: #FFDAB9;
    color: #000000;
}

.status-pricing-review {
    /*background: #efeeeb;*/
    background: #E6E6FA;
    color: #000000;
}

.status-proposed {
    /*background: #d3f6ed;
    color: #04c295;*/
    background: #D7D3FF;
    color: #833bb0;
    border-radius: 60px;
    width: 105px;
    height: 30px;
}

.status-completed {
    background: #cffaff;
}

.status-closed {
    background: #e2e4ea;
}

.status-default {
    background: #e2e4ea; /* Fallback color */
}
.status-revision {
    background: #FFD6DE;
    color: #EC2A5F;
    border-radius: 60px;
    width: 105px;
    height: 30px;

}
.status-win-closed {
    background: #DFFCE8;
    color: #33C657;
    border-radius: 60px;
    width: 105px;
    height: 30px;

}
.status-lost-closed {
    background: #F4BDBC;
    color: red;
    border-radius: 60px;
    width: 105px;
    height: 30px;
}
.itemsHeading {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: capitalize;
    color: #192a3e;
}
.addDivision {
    font-size: 10px;
    margin-top: 10px;
}
.input {
    background: var(--white);
    border: 1px solid var(--bordercolor);
    border-radius: 6px;
    width: 100%;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    height: 40px;
}
.select-option {
    background: var(--white);
    border: 1px solid var(--bordercolor);
    border-radius: 6px;
    width: 100%;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    height: 70px;
}
.input:focus,
.textarea:focus {
    border-color: var(--primaryblue);
}
.input::-webkit-input-placeholder,
.textarea::-webkit-input-placeholder {
    color: #90a0b7;
}
.input::-moz-placeholder,
.textarea::-moz-placeholder {
    color: #90a0b7;
}
.input:-ms-input-placeholder,
.textarea:-ms-input-placeholder {
    color: #90a0b7;
}
.input::-ms-input-placeholder,
.textarea::-ms-input-placeholder {
    color: #90a0b7;
}
.input::placeholder,
.textarea::placeholder {
    color: #90a0b7;
}

textarea.input {
    min-height: 84px;
    resize: none;
}

.input.datepicker-icon {
    background-image: url("../images/icons/calander.svg");
    background-position: center right 10px;
    background-repeat: no-repeat;
}

.textarea {
    background: var(--white);
    border: 1px solid var(--bordercolor);
    border-radius: 6px;
    width: 100%;
    padding: 8px 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    resize: none;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #f6f6f6;
    border: 1px solid #e7e7e7;
    opacity: 1;
}

.ml-auto {
    margin-left: auto;
}

.mr-auto {
    margin-right: auto;
}

.pl-0 {
    padding-left: 0px !important;
}

.pr-0 {
    padding-right: 0px !important;
}

.pr-4 {
    padding-right: 2.4rem !important;
}
.pr-3 {
    padding-right: 1.8rem !important;
}

.modal {
    z-index: 999999999;
}

/*.modal-backdrop {*/
/*    z-index: 99999999;*/
/*}*/

.dropdown-menu.show {
    z-index: 999999999;
}
body {
    font-family: Cabin;
    font-style: normal;
    /* font-weight: 500; */
    font-size: 12px;
    line-height: 24px;
    color: var(--textblack2);
    min-height: 100vh;
    background-color: #f5f6f8;
    overflow-x: hidden;
}
html::-webkit-scrollbar {
    width: 1rem;
    border-radius: 6px;
}
html::-webkit-scrollbar-track {
    background: 0 0;
    border-radius: 6px;
}
html::-webkit-scrollbar-thumb {
    background: #192a3e;
    border-radius: 6px;
}
.datepicker.dropdown-menu {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: auto;
    padding: 1rem;
    margin: 0;
    font-size: 1.6rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background: #ffffff;
    box-shadow: 0px 0px 15.125px rgba(52, 84, 207, 0.08);
    border-radius: 8px;
}
.datepicker.dropdown-menu .table-condensed .datepicker-switch {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 33px;
    color: #003366;
    text-align: center;
}
.datepicker.dropdown-menu .table-condensed .next,
.datepicker.dropdown-menu .table-condensed .prev {
    cursor: pointer;
    font-size: 3rem;
    height: 2.8rem;
    /* width: 2.8rem; */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2ecc40;
}
/* .datepicker.dropdown-menu .datepicker-days .table-condensed .next {
    float: right;
} */
.datepicker.dropdown-menu .datepicker-days .table-condensed .dow {
    padding: 15px 10px;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 21px;
    color: #192a3e;
}
.datepicker.dropdown-menu .datepicker-days .table-condensed .day {
    text-align: center;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #192a3e;
    padding: 10px 10px;
    border-radius: 3px;
}

.datepicker.dropdown-menu .datepicker-days .table-condensed .day:hover,
.datepicker.dropdown-menu .datepicker-months .table-condensed .month:hover,
.datepicker.dropdown-menu .datepicker-years .table-condensed .year:hover {
    background: #003366;
    color: #fff;
    cursor: pointer;
}

.datepicker.dropdown-menu .datepicker-days .table-condensed .day.disabled,
.datepicker.dropdown-menu
    .datepicker-days
    .table-condensed
    .day.disabled:hover {
    opacity: 0.5 !important;
    background-color: #ffffff !important;
    color: #192a3e !important;
}

.datepicker.dropdown-menu
    .datepicker-months
    .table-condensed
    thead
    tr:nth-child(2),
.datepicker.dropdown-menu
    .datepicker-years
    .table-condensed
    thead
    tr:nth-child(2) {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.datepicker.dropdown-menu .datepicker-months .table-condensed tbody tr td {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 5px;
    text-align: center;
}
.datepicker.dropdown-menu .datepicker-switch {
    cursor: pointer;
}
.datepicker.dropdown-menu .datepicker-years .table-condensed .next {
    float: right;
}
.datepicker.dropdown-menu .datepicker-years .table-condensed tbody tr td {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
}

.auth_section {
    padding: 5rem 0;
    /* background-color: #ffffff; */
}
.auth_section .stepper_header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
}

.auth_section .stepper_header .steps {
    /* margin: 0 auto;
    margin-left: 173px; */

    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.auth_section .stepper_header .steps .steps_list {
    display: grid;
    grid-template-columns: repeat(3, auto);
    justify-content: space-between;
    align-items: center;
    text-align: center;
    width: 626px;
    margin: 0;
    padding: 0;
}

.auth_section .stepper_header .steps .steps_list .step_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    position: relative;
}

.auth_section .stepper_header .steps .steps_list .step_item::after {
    content: "";
    height: 2px;
    width: 274px;
    background-color: #e3e0e0;
    position: absolute;
    top: 16px;
    left: 43px;
    z-index: -5;
}

.auth_section .stepper_header .steps .steps_list .step_item::before {
    content: "";
    height: 2px;
    width: 0px;
    background-color: #003366;
    position: absolute;
    top: 16px;
    left: 43px;
    z-index: -4;
}

/* .auth_section .stepper_header .steps .steps_list .step_item.active::before, */
.auth_section .stepper_header .steps .steps_list .step_item.done::before {
    width: 274px;
}

.auth_section .stepper_header .steps .steps_list .step_item:last-child:after,
.auth_section .stepper_header .steps .steps_list .step_item:last-child:before {
    content: unset;
}

.auth_section .stepper_header .steps .steps_list .step_item .step_count {
    height: 34px;
    width: 34px;
    border-radius: 50%;
    background-color: #ffffff;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 73%;
    color: #989898;

    border: 3px solid #ebebeb;

    display: flex;
    align-items: center;
    justify-content: center;

    position: relative;
}

.auth_section .stepper_header .steps .steps_list .step_item .step_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.2rem;
    color: #989898;
}

.log-out {
    font-family: "Cabin";
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 3.4rem;
    letter-spacing: 0em;
    display: block;
    background-color: #003366;
    border-radius: 0.8rem;
    color: #ffffff;
    padding: 1rem 3rem;
    text-decoration: none;
    transition: 0.3s;
}

.log-out:hover {
    color: white;
    opacity: 0.8;
}

.auth_section .stepper_header .steps .steps_list .step_item.active .step_count {
    background-color: #003366;
    color: #ffffff;
}

.auth_section
    .stepper_header
    .steps
    .steps_list
    .step_item.active
    .step_count::before {
    content: "";
    height: calc(100% + 8px);
    width: calc(100% + 8px);
    border-radius: 50%;
    background-color: #f4fbf3;
    position: absolute;
    top: -4px;
    left: -4px;
    z-index: -2;
    border: 3.5px solid #003366;
}

.auth_section
    .stepper_header
    .steps
    .steps_list
    .step_item.active
    .step_count::after {
    content: "";
    height: calc(100% + 8px);
    width: 10px;
    background-color: #f5f6f8;
    position: absolute;
    top: -8px;
    right: -4px;
    z-index: -1;
    transform: rotate(347deg);
}

.auth_section .stepper_header .steps .steps_list .step_item.active .step_title {
    color: #003366;
}

.auth_section .stepper_header .steps .steps_list .step_item.done .step_count {
    background-color: #003366;
    border-color: #003366;
}
.auth_section
    .stepper_header
    .steps
    .steps_list
    .step_item.done
    .step_count::after {
    height: 100%;
    width: 100%;
    background-color: #003366;
    content: "\2713";
    font-size: 16px;
    color: #ffffff;
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;

    display: flex;
    align-items: center;
    justify-content: center;
}

.packages_plan_container {
    min-height: calc(100vh - 30rem);
    margin-top: 2rem;
    max-width: 829px;
    margin: auto;
}

.packages_plan_container .plan_grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.packages_plan_container .plan_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 2rem;
    line-height: 2.4rem;
    color: #192a3e;
    padding-bottom: 16px;
    border-bottom: 1px solid #e7e7e7;
}

.packages_plan_container .plan_heading {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 4.8rem;
    line-height: 6rem;
    text-align: center;
}

.packages_plan_container .plan_heading span {
    font-size: 24px;
    line-height: 60px;
}

.basic,
.basic span {
    color: #192a3e;
}

.premium,
.premium span {
    color: #2ecc40;
}

.packages_plan_container .plan_type {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 2rem;
    line-height: 3rem;
    text-align: center;
}

.plan_list {
    padding-left: 36px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.plan_list .plan_item {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #667085;

    position: relative;
}

.plan_list .plan_item::before {
    content: "";
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iMTIiIGZpbGw9IiNFQ0Y1RkYiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNy4wOTggNy4zODk2N0w5LjkzNzk3IDE0LjI5OTdMOC4wMzc5NyAxMi4yNjk3QzcuNjg3OTcgMTEuOTM5NyA3LjEzNzk3IDExLjkxOTcgNi43Mzc5NyAxMi4xOTk3QzYuMzQ3OTcgMTIuNDg5NyA2LjIzNzk3IDEyLjk5OTcgNi40Nzc5NyAxMy40MDk3TDguNzI3OTcgMTcuMDY5N0M4Ljk0Nzk3IDE3LjQwOTcgOS4zMjc5NyAxNy42MTk3IDkuNzU3OTcgMTcuNjE5N0MxMC4xNjggMTcuNjE5NyAxMC41NTggMTcuNDA5NyAxMC43NzggMTcuMDY5N0MxMS4xMzggMTYuNTk5NyAxOC4wMDggOC40MDk2NyAxOC4wMDggOC40MDk2N0MxOC45MDggNy40ODk2NyAxNy44MTggNi42Nzk2NyAxNy4wOTggNy4zNzk2N1Y3LjM4OTY3WiIgZmlsbD0iIzAwMzM2NiIvPgo8L3N2Zz4K");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 24px;
    position: absolute;
    top: 0;
    left: -30px;
    height: 24px;
    width: 24px;
}

.packages_plan_container .payments_list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.packages_plan_container .payments_list .payment_item {
    padding: 1.7rem 2.2rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 8px;

    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;

    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.packages_plan_container .payments_list .payment_item .card_info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.packages_plan_container .payments_list .payment_item .payment_info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.packages_plan_container .payments_list .title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    display: flex;
    align-items: center;
    color: #363636;
}

.packages_plan_container .payments_list .expires {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    display: flex;
    align-items: center;

    color: #b6b6b6;
}

.auth_section .auth_container {
    min-height: calc(100vh - 30rem);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: 2rem;
}
.auth_section .auth_wrapper {
    max-width: 503px;
    margin: auto;
    width: 100%;
    border-radius: 8px;
    background: var(--white);
    -webkit-box-shadow: 0 0 15.125px rgba(52, 84, 207, 0.08);
    box-shadow: 0 0 15.125px rgba(52, 84, 207, 0.08);
    padding: 4rem 5.8rem;
}
.auth_section .auth_wrapper .auth_title {
    font-weight: 600;
}
.auth_section .auth_wrapper .auth_note {
    color: var(--placeholdertext);
}
.auth_section .auth_wrapper .remember_container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 1rem;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.auth_section .auth_wrapper .remember_container label {
    color: var(--placeholdertext);
}
.auth_section .auth_wrapper .auth_bottom_note {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    color: #192a3e;
    text-decoration: none;
}

.auth_section .auth_wrapper .auth_bottom_note span {
    /* color: #2ecc40; */
    color: var(--primaryblue);
}

.dashboard_wrapper {
    /* display: grid; */
    grid-template-columns: auto;
}

.dashboard_wrapper .sidebar_wrapper .side_bar {
    background-color: var(--primaryblue);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar_width);
    z-index: 99999999;
    overflow: hidden;
}
.dashboard_wrapper .sidebar_wrapper .side_bar .side_bar_logo {
    /* height: 64px; */
    height: 72px;
    padding: 1rem;
    border-bottom: 1px solid var(--bordercolor3);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.dashboard_wrapper .sidebar_wrapper .side_bar .side_bar_menu {
    height: calc(100vh - 64px);
    overflow-y: auto;
    padding-right: 2.4rem;
    scrollbar-color: var(--textblack);
    scrollbar-width: thin;
}
.dashboard_wrapper
    .sidebar_wrapper
    .side_bar
    .side_bar_menu::-webkit-scrollbar {
    width: 8px;
}
.dashboard_wrapper
    .sidebar_wrapper
    .side_bar
    .side_bar_menu::-webkit-scrollbar-track {
    background: var(--bordercolor);
}
.dashboard_wrapper
    .sidebar_wrapper
    .side_bar
    .side_bar_menu::-webkit-scrollbar-thumb {
    background: var(--textblack);
}
.dashboard_wrapper .side_bar_menu .menu_links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 16px;
    padding: 2.4rem 0;
}
.dashboard_wrapper .side_bar_menu .menu_links .menu_item .menu_link {
    display: grid;
    grid-template-columns: 24px 1fr;
    gap: 8px;
    font-family: Cabin;
    font-style: normal;
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 2.6rem;
    color: var(--lightgray);
    text-decoration: none;
    padding: 8px 5.2rem 8px 3rem;
    border-radius: 0 50px 50px 0;
    width: 234px;
    -webkit-transition: 0.2s ease-in;
    transition: 0.2s ease-in;
}
/* .dashboard_wrapper .side_bar_menu .menu_links .menu_item:hover .menu_link {
    background-color: var(--textblack);
    color: var(--textorange);
    -webkit-transition: 0.2s ease-in;
    transition: 0.2s ease-in;
} */
/* .dashboard_wrapper
    .side_bar_menu
    .menu_links
    .menu_item:hover
    .menu_link
    svg
    path {
    fill: var(--textorange);
    -webkit-transition: 0.2s ease-in;
    transition: 0.2s ease-in;
} */
.dashboard_wrapper .side_bar_menu .menu_links .menu_item.active .menu_link,
.dashboard_wrapper .side_bar_menu .menu_links .menu_item:hover .menu_link {
    background-color: var(--white);
    /* color: var(--textorange); */
    color: var(--primaryblue);
}

.dashboard_wrapper
    .side_bar_menu
    .menu_links
    .menu_item.active
    .menu_link
    svg
    path,
.dashboard_wrapper
    .side_bar_menu
    .menu_links
    .menu_item:hover
    .menu_link
    svg
    path {
    /* fill: var(--textorange); */
    fill: var(--primaryblue);
}
.dashboard_wrapper .content_wrapper {
    background-color: var(--bordercolor4);
}
.dashboard_wrapper .content_wrapper .dashboard_header {
    min-height: 45px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 1rem;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    background-color: rgba(0, 116, 217, 1);
    border-bottom: 1px solid var(--bordercolor3);
    -webkit-box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    padding: 1.1rem 1.5rem;
}

.dashboard_wrapper .content_wrapper .dashboard_header .notifications-link {
    position: relative;
}

.dashboard_wrapper
    .content_wrapper
    .dashboard_header
    .notifications-link
    .notification-status {
    position: absolute;
    display: block;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #534848;
}

.dashboard_wrapper
    .content_wrapper
    .dashboard_header
    .notifications-link
    .notification-status.active {
    background-color: #ed4f9d;
}

.dashboard_wrapper .content_wrapper .dashboard_header .dashboard_header_title {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
}
.dashboard_wrapper .content_wrapper .dashboard_header .header_items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 1.8rem;
}
.dashboard_wrapper .content_wrapper .dashboard_header .aside_toggle {
    display: none;
}
.profile_info_dropdown .profile_info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 8px;
}
.profile_info_dropdown .profile_info .info .compnay_name {
    font-family: "Poppins";
    font-weight: 400;
    font-size: 12px;
    line-height: 19.2px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: rgba(124, 128, 145, 1);
}
.profile_info_dropdown .profile_info .info .profile_name {
    font-family: Cabin;
    font-style: normal;
    font-weight: 500;
    font-size: 1.7rem;
    line-height: 2.6rem;
}
.arrow::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    height: 5px;
    width: 7px;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNyIgaGVpZ2h0PSI0IiB2aWV3Qm94PSIwIDAgNyA0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNNi45MzY4OCAwLjQ5NzAzNEM2LjkzNjg4IDAuNjU2OTUzIDYuODkwNTggMC43OTY5MjEgNi43ODI3MiAwLjg5NjgyOEwzLjY5ODczIDMuODk1MDJDMy41NTk5NiA0LjAzNDk5IDMuMzc0OSA0LjAzNDk5IDMuMjM2MTQgMy44OTUwMkwwLjE1MjE0IDAuODk2ODI4Qy0wLjAxNzUzMSAwLjczNjkxIC0wLjA0ODMxMDMgMC40MTcwODUgMC4wNzUwNjI2IDAuMTk3MzI0QzAuMTk4NDM5IC0wLjAyMjYwMTcgMC40NDUxOCAtMC4wNjI0OTc0IDAuNjE0NzI0IDAuMDk3NDE3TDMuNDY3MjEgMi44NzU1MUw2LjMxOTk2IDAuMDk3MjQ4M0M2LjQ4OTYzIC0wLjA2MjY3MDQgNi43MzYyNSAtMC4wMjI2MDU4IDYuODU5NjMgMC4xOTcxNTVDNi45MDU5MiAwLjI3NzExMiA2LjkzNjgzIDAuMzk2OTcgNi45MzY4MyAwLjQ5NzA0Mkw2LjkzNjg4IDAuNDk3MDM0WiIgZmlsbD0iIzE5MkEzRSIvPgo8L3N2Zz4K);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: none !important;
}
.profile_info_dropdown .dropdown-menu {
    background: #fff;
    -webkit-box-shadow: 0 0 15.125px rgba(52, 84, 207, 0.08);
    box-shadow: 0 0 15.125px rgba(52, 84, 207, 0.08);
    border-radius: 8px;
    border: none;
}
.profile_info_dropdown .dropdown-menu .dropdown-item {
    padding: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #434656;
    min-width: 165px;
}
.dashboard_main {
    padding: 16px;
}
.task_header {
    margin-bottom: 20px;
}

.status_title {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
}

.value {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: rgba(124, 128, 145, 1);
}

.task_list {
    display: flex;
    flex-direction: column;
}
.task_container {
    max-height: 330px; /* Adjust height as needed */
    overflow-y: auto; /* Enables vertical scrolling */
    display: flex;
    flex-direction: column;
}
.task_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.text-end {
    text-align: right;
}

.cancel-button {
    background-color: transparent;
    border: 1px solid rgba(0, 116, 217, 1);
    color: rgba(0, 116, 217, 1);
    height: 40px;
    border-radius: 6px;
    width: 120px;
    margin-right: 10px;
    cursor: pointer;
}
#taskModalLabel {
    color: rgba(0, 116, 217, 1);
}

.create-button {
    background-color: rgba(0, 116, 217, 1);
    border: none;
    color: #fff;
    height: 40px;
    border-radius: 6px;
    width: 120px;
    cursor: pointer;
}

.task_description {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 14px;
    /*line-height: 8px;*/
    color: rgba(81, 86, 108, 1);
    margin: 0;
    width: 78%;
}

.task_date {
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-size: 13px;
    color: rgba(168, 171, 181, 1);
}

.task_details {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.task_image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.task_info {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

.task_name {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 10px;
    color: rgba(168, 171, 181, 1);
}
.task_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.status_title {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 18px;
}

.add_task_button {
    padding: 8px 16px;
    font-family: "Poppins", sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.add_task_button:hover {
    background-color: #0056b3;
}

.dashboard_main .sub_heading {
    font-family: Cabin;
    font-style: normal;
    font-weight: 700;
    font-size: 1.6rem;
    line-height: 2.4rem;
    /* color: var(--primaryblue); */
    color: #036;
}

.dashboard_stat_cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(272px, 1fr));
    gap: 16px;
}

.dashboard_stat_cards .state_card {
    min-height: 96px;
    background: var(--white);
    box-shadow: 0px 0px 12px rgba(52, 84, 207, 0.08);
    border-radius: 7px;
    padding: 12px 16px;
    border: none;

    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    /* flex-wrap: wrap; */
}

.dashboard_stat_cards .state_card .card_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 2.4rem;
}

.dashboard_stat_cards .state_card .card_count {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 2.4rem;
    line-height: 3.2rem;
}

.dashboard_sales_graphs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    min-height: 334px;
    margin-top: 2.4rem;
}

.dashboard_sales_graphs .card_wrapper {
    background: #ffffff;
    border-radius: 8px;
    padding: 2.4rem;
}

.dashboard_sales_graphs .chart_wrapper .chart_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    flex-wrap: wrap;
}

.dashboard_sales_graphs .chart_wrapper .chart_header .info .status_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: var(--placeholdertext);
}

.dashboard_sales_graphs .chart_wrapper .chart_header .info .value {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 1.8rem;
    line-height: 2.7rem;
    letter-spacing: 0.01em;
    color: var(--textblack);

    display: flex;
    align-items: center;
    gap: 1.4rem;
}
.navbar-light {
    background-color: rgba(255, 255, 255, 1);
}
.nav-item.active {
    border-bottom: 4px solid rgba(47, 204, 64, 1);
}

.transparent-btn {
    background-color: transparent;
    border: 1px solid rgba(47, 204, 64, 1);
    color: rgba(47, 204, 64, 1);
    padding: 8px 20px;
    font-size: 13px;
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.3s ease, color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.transparent-btn:hover {
    background-color: rgba(47, 204, 64, 0.1);
    color: rgba(47, 204, 64, 1);
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
    appearance: none;
}

.filter_search {
    height: 40px;
    width: 368px;
    border-radius: 6px;
    padding: 8px 16px 8px 16px;
    padding-right: 48px;
    background: var(--white);
    border: 1px solid var(--lightgray);

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: var(--textblack);

    background-image: url(../images/icons/input-search-icon.svg);
    background-repeat: no-repeat;
    background-position: center right 16px;
    background-size: 24px;
}

.filter_search::placeholder {
    color: var(--placeholdertext2);
}
/* Clients Styles Ends*/

.fa-arrow-trend-up,
.fa-caret-up {
    color: var(--green2);
    font-size: 16px;
}

.indicator {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0.01em;
    color: var(--green2);

    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.dashboard_sales_graphs .chart_wrapper .chart_header .selectBox {
    height: 38px;
    width: fit-content;
    background-color: var(--lightgray2);
    border-radius: 8px;
    padding: 8px 10px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0.01em;
    color: var(--textblack);
}

select.arrow {
    padding-right: 3.6rem !important;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTciIHZpZXdCb3g9IjAgMCAxNiAxNyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNjY2NjcgNi41TDguMDAwMDEgMTEuODMzM0wxMy4zMzMzIDYuNSIgc3Ryb2tlPSIjOTBBMEI3IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K");
    background-repeat: no-repeat;
    background-position: center right 10px;
    background-size: 16px;

    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

select.darkarrow {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNLTIuMTI1NDNlLTA3IDEuMTM3NTlDLTIuMDEwOWUtMDcgMS4zOTk2MSAwLjEwMTk5NyAxLjY2MDYzIDAuMzA0OTkyIDEuODU2NjRMNC4zMDQ4OSA1LjcxOTk0QzQuNjk3ODggNi4wOTc5NyA1LjMyMDg3IDYuMDkyOTcgNS43MDY4NiA1LjcwNjk0TDkuNzA2NzYgMS43MDY2M0MxMC4wOTc3IDEuMzE2NiAxMC4wOTc3IDAuNjgzNTUyIDkuNzA2NzYgMC4yOTI1MjNDOS4zMTU3NyAtMC4wOTc1MDcgOC42ODM3OCAtMC4wOTc1MDY5IDguMjkyNzkgMC4yOTI1MjNMNC45ODc4OCAzLjU5Nzc4TDEuNjk0OTYgMC40MTg1MzNDMS4yOTY5NyAwLjAzNDUwMzQgMC42NjM5ODMgMC4wNDU1MDQ1IDAuMjgwOTkzIDAuNDQyNTM1QzAuMDkyOTk3MyAwLjYzNzU1IC0yLjIzNDcxZS0wNyAwLjg4NzU2OSAtMi4xMjU0M2UtMDcgMS4xMzc1OVoiIGZpbGw9IiM1MTRGNkUiLz4KPC9zdmc+Cg==");
    background-repeat: no-repeat;
    background-position: center right 10px;
    background-size: 16px;

    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 3.4rem !important;
}

.no-border {
    border: none;
}

.modal-small .modal-content {
    background: #ffffff;
    box-shadow: 0px 0px 19px rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    border: none;
    padding: 40px;
}

.modal-small .modal-content .title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 2rem;
    line-height: 3rem;
    color: var(--primaryblue);
}

.modal-small .modal-content .para {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: var(--placeholdertext);
}

.form-control.valid {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8zMDFfMTg4NikiPg0KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0wLjI1IDdDMC4yNSAzLjI3MjA4IDMuMjcyMDggMC4yNSA3IDAuMjVDMTAuNzI2MSAwLjI1NDM0IDEzLjc0NTcgMy4yNzM4OCAxMy43NSA3QzEzLjc1IDEwLjcyNzkgMTAuNzI3OSAxMy43NSA3IDEzLjc1QzMuMjcyMDggMTMuNzUgMC4yNSAxMC43Mjc5IDAuMjUgN1pNNy4wNDYxMiAxMC4wODc2TDEwLjg5NjQgNC44NjI1QzExLjAyNjYgNC43MDEzOSAxMS4wNTc3IDQuNDgxNzkgMTAuOTc3NCA0LjI5MDg3QzEwLjg5NzEgNC4wOTk5NiAxMC43MTgzIDMuOTY4NjMgMTAuNTEyMSAzLjk0OTAyQzEwLjMwNiAzLjkyOTQyIDEwLjEwNTcgNC4wMjQ3MSA5Ljk5MDgxIDQuMTk3MDZMNi40ODgxMiA4Ljk1MDc1TDQuMTk1MzcgNy4xMTY0NEMzLjk1Mjc1IDYuOTIyMjggMy41OTg2NiA2Ljk2MTU2IDMuNDA0NSA3LjIwNDE5QzMuMjEwMzQgNy40NDY4MSAzLjI0OTYyIDcuODAwOSAzLjQ5MjI1IDcuOTk1MDZMNi4yNDE3NSAxMC4xOTMzQzYuMzYxOTUgMTAuMjg3MyA2LjUxNDg1IDEwLjMyOSA2LjY2NjExIDEwLjMwOTFDNi44MTczNyAxMC4yODkyIDYuOTU0MyAxMC4yMDk0IDcuMDQ2MTIgMTAuMDg3NloiIGZpbGw9IiMxM0JEMzgiLz4NCjwvZz4NCjxkZWZzPg0KPGNsaXBQYXRoIGlkPSJjbGlwMF8zMDFfMTg4NiI+DQo8cmVjdCB3aWR0aD0iMTMuNSIgaGVpZ2h0PSIxMy41IiBmaWxsPSJ3aGl0ZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4yNSAwLjI1KSIvPg0KPC9jbGlwUGF0aD4NCjwvZGVmcz4NCjwvc3ZnPg0K);
    background-repeat: no-repeat;
    border: 1px solid var(--green);
    background-position: center right 6px;
    background-size: 14px 15px;
    padding-right: 3rem;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.form-control.error {
    border: 1px solid var(--red);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center right 6px;
    background-size: 16px;
    padding-right: 3rem;
    -webkit-box-shadow: none;
    box-shadow: none;
}
label.error,
.laravel_error {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: -0.02em;
    color: #ff3654;
    margin-top: 6px;
}

/* Settings Page */
.settings_tab_grid {
    display: grid;
    grid-template-columns: 210px calc(100% - 226px);
    gap: 16px;
}

.settings_tabs {
    background: #ffffff;
    box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
    border-radius: 8px;
    padding: 8px 0;

    display: flex;
    flex-direction: column;
    gap: 1.6rem;
    max-width: 210px;
    width: 100%;
}

.settings_tabs .tab_link {
    padding: 8px 8px 8px 3rem;
    position: relative;
}

.settings_tabs .tab_link a {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #90a0b7;
    text-decoration: none;
}

.settings_tabs .tab_link.active a {
    color: #003366;
}

.settings_tabs .tab_link.active::before {
    content: "";
    height: 100%;
    width: 6px;
    background-color: #003366;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    border-radius: 0px 8px 8px 0px;
}

.margin-setup-screen tbody td:not(:first-child) {
    vertical-align: top !important;
}
.custom-navbar .nav-link {
    font-size: 15px;
    margin-right: 13px;
    font-weight: 300;
}

.custom-navbar .dropdown-menu a {
    font-size: 14px;
}

@media (min-width: 1400px) {
    .custom-navbar .nav-link {
        font-size: 15px;
        font-weight: 300;
    }
}
@media (max-width: 1400px) {
    .custom-navbar .nav-link {
        font-size: 15px;
        font-weight: 300;
    }
    .custom-navbar .nav-link {
        margin-right: 3px;
    }
}
@media (max-width: 1400px) {
    .dashboard_wrapper .content_wrapper .dashboard_header .aside_toggle {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        font-size: 18px;
        height: 22px;
        width: 22px;
    }
    @media (max-width: 1050px) {
        .custom-navbar .nav-link {
            font-size: 13px;
            font-weight: 300;
        }
        .custom-navbar .nav-link {
            margin-right: 3px;
        }
    }
    .custom-navbar .dropdown-menu a {
        font-size: 15px;
    }
    .dashboard_wrapper {
        /* display: grid; */
        grid-template-columns: auto;
    }
    .dashboard_wrapper .sidebar_wrapper .side_bar.open {
        -webkit-transform: translateX(0);
        transform: translateX(0);
        width: 272px;
    }
    .auth_section .stepper_header .auth_logo {
        width: 100%;
    }
    .auth_section .stepper_header .steps {
        margin: 15px auto;
        position: unset;
        transform: unset;
    }
}

@media screen and (max-width: 1024px) {
    .dashboard_sales_graphs {
        grid-template-columns: 100%;
    }
}

@media (max-width: 991px) {
    html {
        font-size: 55%;
    }
    .auth_section .stepper_header .steps .steps_list {
        width: 300px;
    }
    .auth_section .stepper_header .steps .steps_list .step_item.active::before,
    .auth_section .stepper_header .steps .steps_list .step_item.done::before,
    .auth_section .stepper_header .steps .steps_list .step_item::after {
        width: 90px;
    }
    .settings_tab_grid {
        grid-template-columns: 100%;
    }
}

@media (max-width: 465px) {
    .packages_plan_container .plan_heading {
        font-size: 3.8rem;
        line-height: 4.8rem;
    }

    .packages_plan_container .plan_heading span {
        font-size: 16px;
        line-height: 28px;
    }

    .filter_search {
        width: 228px;
    }
}

@media (min-width: 576px) {
    .modal-small .modal-dialog {
        max-width: 519px;
        margin: 1.75rem auto;
    }
}
.tox-statusbar .tox-statusbar__path {
    opacity: 0 !important;
}
.tox-statusbar {
    border-top: 0 !important;
}
.table-fixed {
    table-layout: fixed;
    width: 100%;
}
