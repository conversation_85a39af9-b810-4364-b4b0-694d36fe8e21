@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100;200;300;400;500;600;700;800;900&family=<PERSON><PERSON><PERSON>+Beanie&display=swap");

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

/* Datatable Responsive Fixes */
.dataTables_scrollHead {
    display: none;
}

.dataTables_scrollBody {
    overflow-x: auto !important;
    height: fit-content !important;
    border-bottom: none !important;
}

table.dataTable.display tbody tr.odd > .sorting_1,
table.dataTable.order-column.stripe tbody tr.odd > .sorting_1 {
    background: transparent !important;
}

.dataTables_scrollBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 6px;
}

.transactionsList .invoice_number:hover {
    color: #003366;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #192a3e;
    border-radius: 6px;
}

.dataTables_scrollBody thead th .dataTables_sizing {
    height: auto !important;
}

/* Datatable Responsive Fixes Done */

/* Toaster Notifications */
#toast-container .toast {
    border: none !important;
    background-size: 24px;
    opacity: 1;
}

#toast-container .toast-success {
    background-color: #04844b;
}

#toast-container .toast-error {
    background-color: #c23934;
}

#toast-container .toast-title {
    font-family: "Inter";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #ffffff;
    margin-bottom: 5px;
}

#toast-container .toast-message {
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    color: #ffffff;
}

/* Toaster Notifications */

/* Loader Style*/
.loading {
    position: relative;
}

.loading:before {
    content: "";

    position: absolute;
    top: calc(50% - 15px);
    left: calc(50% - 15px);
    transform: translate(-50%, -50%);

    width: 30px;
    height: 30px;
    border: 5px solid;
    border-left-color: transparent;
    border-right-color: transparent;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.5s;
    animation: 0.8s linear infinite rotate;
    z-index: 6;

    transition-delay: 0.5s;
    transition-duration: 1s;
    opacity: 1;
}

.loading:after {
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 4;
    user-select: none;
    pointer-events: none;
}

.loading.small-loader:before {
    top: calc(50% - 20px);
    left: calc(50% - 20px);
    width: 40px;
    height: 40px;
    border: 5px solid var(--main-purple);
    border-left-color: transparent;
    border-right-color: transparent;
}

.loading {
    pointer-events: none;
    cursor: not-allowed;
    gap: 5px;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Loader Style*/

.pencil_icon,
.trash_icon {
    font-size: 20px;
}

.pencil_icon {
    color: #2fa1f8;
}

.trash_icon {
    color: #ff4b55;
}

.border-bottom-left-right-radius-none {
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}

.border-top-left-right-radius-none {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
}

.row-gap-16 {
    row-gap: 16px;
}

/* leadings */
.leading-5 {
    line-height: 22px !important;
}

/* leadings */

/* Fonts */
.font-12 {
    font-size: 12px !important;
}

.font-24 {
    font-weight: 600;
    font-size: 24px;
    line-height: 30px;
}

.font-48 {
    font-weight: 600;
    font-size: 48px;
    line-height: 60px;
}

.text-md {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.8rem;
    line-height: 2.2rem;
}

.text-md2 {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 2rem;
    line-height: 3rem;
}

.capitalize {
    text-transform: capitalize;
}

.text-primary2 {
    color: #003366;
}

.text-primary {
    color: #192a3e !important;
}

.text-sm {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 121.4%;
}

.text-placeholder {
    color: #7e8a9d;
}

.text-right {
    text-align: right;
}

/* Fonts */
.select-small + .select2 {
    width: fit-content !important;
}

.min-w-174 {
    min-width: 174px;
}

.w-fit {
    width: fit-content;
}

.h-fit {
    height: fit-content;
}

.edit_icon_btn {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #003366;

    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.custom_number_field {
    display: grid;
    grid-template-columns: auto 1fr;

    background: #ffffff;
    border: 1px solid #90a0b7;
    border-radius: 6px;
    height: 40px;
}

.custom_number_field + .select2 .select2-selection,
.custom_number_field + .select2 .select2-selection {
    border: none !important;
    height: 40px !important;
}

.custom_number_field .input_number {
    border: none;
    height: 38px;

    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #083a50;
    padding: 8px 1.2rem;
    padding-left: 0px;
    border-radius: 6px;
    width: 100%;
}

.custom_number_field .input_number::-webkit-outer-spin-button,
.custom_number_field .input_number::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
.custom_number_field .input_number[type="number"] {
    -moz-appearance: textfield;
}

.custom_number_field .custom_selectBox + .select2 .select2-selection {
    border: none !important;
    height: 57% !important;
}

.estimate_filters .select-small + .select2 {
    width: 200px !important;
}

.custom_number_field .custom_selectBox + .select2 .select2-selection__rendered {
    /* padding: 5px 3.2rem 5px 1.6rem !important; */
}

.custom_number_field
    .custom_selectBox
    + .select2
    .select2-selection
    .select2-selection__arrow {
    right: 1rem !important;
}

.select2-container--open {
    z-index: 999999999;
}

.assign_om_table {
    max-height: 251px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
}

.assign_om_table .select_om {
    display: flex;
    align-items: center;
    gap: 2.4rem;
}

.assign_om_table .select_om input[type="radio"] {
    height: 16px;
    width: 16px;
}

/* Buttons */
.btn.primaryblue {
    height: 4rem;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    border-radius: 6px;
    padding: 0.8rem 2.8rem;
    font-family: Cabin;
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: var(--white);
    border: 1.5px solid var(--primaryblue);
    background-color: var(--primaryblue);
}

.btn.primaryblue:hover {
    background-color: #012951;
}

.btn.primaryblue.outline {
    color: var(--primaryblue);
    background: var(--white);
}

.btn.anchor-btn {
    border-radius: 6px;
    padding: 8px 16px 8px 16px;
    border: none;
    background-color: var(--white);

    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    display: flex;
    align-items: center;
    justify-content: center;

    color: var(--primaryblue);
    box-shadow: none !important;
}

.btn.transparent {
    border-radius: 6px;
    padding: 8px 16px 8px 16px;
    border: 2px solid #003366;
    background-color: var(--white);

    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    display: flex;
    align-items: center;
    justify-content: center;

    color: var(--primaryblue);
}

.btn.transparent:hover {
    background-color: var(--white);
}

.bg-transparent {
    background-color: transparent !important;
}

.placeholder_btn {
    background: #ffffff;
    box-shadow: 0px 0px 10px rgba(49, 89, 254, 0.07);
    border-radius: 4px;
    padding: 4px 2.4rem;
    height: 4rem;

    display: flex;
    align-items: center;
    justify-content: center;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #90a0b7;
}

.btn-small {
    font-size: 12px !important;
    padding: 4px 8px !important;
    height: auto !important;
}

.branding_color_btn {
    height: 37px;
    width: 91px;
    border-radius: 4px;

    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    background-color: #083a50;
    position: relative;
}

.branding_color_btn input {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);

    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-progress-appearance: none;

    height: 14px;
    width: 14px;
    border-radius: 50%;
    background-color: #e7e7e7;

    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuMDY2NTkgOC4yMDA2NUw0LjYxNjU5IDYuNzUwNjVDNC40OTQzNiA2LjYyODQzIDQuMzQ0MzYgNi41NjczMiA0LjE2NjU4IDYuNTY3MzJDMy45ODg4MSA2LjU2NzMyIDMuODMzMjUgNi42MzM5OCAzLjY5OTkyIDYuNzY3MzJDMy41Nzc3IDYuODg5NTQgMy41MTY1OSA3LjA0NTEgMy41MTY1OSA3LjIzMzk4QzMuNTE2NTkgNy40MjI4NyAzLjU3NzcgNy41Nzg0MyAzLjY5OTkyIDcuNzAwNjVMNS41OTk5MiA5LjYwMDY1QzUuNzIyMTQgOS43MjI4NyA1Ljg3NzcgOS43ODM5OCA2LjA2NjU5IDkuNzgzOThDNi4yNTU0NyA5Ljc4Mzk4IDYuNDExMDMgOS43MjI4NyA2LjUzMzI1IDkuNjAwNjVMMTAuMzE2NiA1LjgxNzMyQzEwLjQzODggNS42OTUxIDEwLjQ5OTkgNS41NDUxIDEwLjQ5OTkgNS4zNjczMkMxMC40OTk5IDUuMTg5NTQgMTAuNDMzMyA1LjAzMzk4IDEwLjI5OTkgNC45MDA2NUMxMC4xNzc3IDQuNzc4NDMgMTAuMDIyMSA0LjcxNzMyIDkuODMzMjUgNC43MTczMkM5LjY0NDM2IDQuNzE3MzIgOS40ODg4MSA0Ljc3ODQzIDkuMzY2NTggNC45MDA2NUw2LjA2NjU5IDguMjAwNjVaTTYuOTk5OTIgMTMuNjY3M0M2LjA3NzcgMTMuNjY3MyA1LjIxMTAzIDEzLjQ5MjIgNC4zOTk5MiAxMy4xNDJDMy41ODg4MSAxMi43OTIyIDIuODgzMjUgMTIuMzE3MyAyLjI4MzI1IDExLjcxNzNDMS42ODMyNSAxMS4xMTczIDEuMjA4MzYgMTAuNDExOCAwLjg1ODU4NSA5LjYwMDY1QzAuNTA4MzYzIDguNzg5NTQgMC4zMzMyNTIgNy45MjI4NyAwLjMzMzI1MiA3LjAwMDY1QzAuMzMzMjUyIDYuMDc4NDMgMC41MDgzNjMgNS4yMTE3NiAwLjg1ODU4NSA0LjQwMDY1QzEuMjA4MzYgMy41ODk1NCAxLjY4MzI1IDIuODgzOTggMi4yODMyNSAyLjI4Mzk4QzIuODgzMjUgMS42ODM5OCAzLjU4ODgxIDEuMjA4ODcgNC4zOTk5MiAwLjg1ODY1MUM1LjIxMTAzIDAuNTA4ODczIDYuMDc3NyAwLjMzMzk4NCA2Ljk5OTkyIDAuMzMzOTg0QzcuOTIyMTQgMC4zMzM5ODQgOC43ODg4MSAwLjUwODg3MyA5LjU5OTkyIDAuODU4NjUxQzEwLjQxMSAxLjIwODg3IDExLjExNjYgMS42ODM5OCAxMS43MTY2IDIuMjgzOThDMTIuMzE2NiAyLjg4Mzk4IDEyLjc5MTUgMy41ODk1NCAxMy4xNDEzIDQuNDAwNjVDMTMuNDkxNSA1LjIxMTc2IDEzLjY2NjYgNi4wNzg0MyAxMy42NjY2IDcuMDAwNjVDMTMuNjY2NiA3LjkyMjg3IDEzLjQ5MTUgOC43ODk1NCAxMy4xNDEzIDkuNjAwNjVDMTIuNzkxNSAxMC40MTE4IDEyLjMxNjYgMTEuMTE3MyAxMS43MTY2IDExLjcxNzNDMTEuMTE2NiAxMi4zMTczIDEwLjQxMSAxMi43OTIyIDkuNTk5OTIgMTMuMTQyQzguNzg4ODEgMTMuNDkyMiA3LjkyMjE0IDEzLjY2NzMgNi45OTk5MiAxMy42NjczWiIgZmlsbD0iI0U3RTdFNyIvPgo8L3N2Zz4K");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.branding_color_btn input:checked {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuMDY2NTkgOC4yMDA2NUw0LjYxNjU5IDYuNzUwNjVDNC40OTQzNiA2LjYyODQzIDQuMzQ0MzYgNi41NjczMiA0LjE2NjU5IDYuNTY3MzJDMy45ODg4MSA2LjU2NzMyIDMuODMzMjUgNi42MzM5OCAzLjY5OTkyIDYuNzY3MzJDMy41Nzc3IDYuODg5NTQgMy41MTY1OSA3LjA0NTEgMy41MTY1OSA3LjIzMzk4QzMuNTE2NTkgNy40MjI4NyAzLjU3NzcgNy41Nzg0MyAzLjY5OTkyIDcuNzAwNjVMNS41OTk5MiA5LjYwMDY1QzUuNzIyMTQgOS43MjI4NyA1Ljg3NzcgOS43ODM5OCA2LjA2NjU5IDkuNzgzOThDNi4yNTU0NyA5Ljc4Mzk4IDYuNDExMDMgOS43MjI4NyA2LjUzMzI1IDkuNjAwNjVMMTAuMzE2NiA1LjgxNzMyQzEwLjQzODggNS42OTUxIDEwLjQ5OTkgNS41NDUxIDEwLjQ5OTkgNS4zNjczMkMxMC40OTk5IDUuMTg5NTQgMTAuNDMzMyA1LjAzMzk4IDEwLjI5OTkgNC45MDA2NUMxMC4xNzc3IDQuNzc4NDMgMTAuMDIyMSA0LjcxNzMyIDkuODMzMjUgNC43MTczMkM5LjY0NDM2IDQuNzE3MzIgOS40ODg4MSA0Ljc3ODQzIDkuMzY2NTkgNC45MDA2NUw2LjA2NjU5IDguMjAwNjVaTTYuOTk5OTIgMTMuNjY3M0M2LjA3NzcgMTMuNjY3MyA1LjIxMTAzIDEzLjQ5MjIgNC4zOTk5MiAxMy4xNDJDMy41ODg4MSAxMi43OTIyIDIuODgzMjUgMTIuMzE3MyAyLjI4MzI1IDExLjcxNzNDMS42ODMyNSAxMS4xMTczIDEuMjA4MzYgMTAuNDExOCAwLjg1ODU4NSA5LjYwMDY1QzAuNTA4MzYzIDguNzg5NTQgMC4zMzMyNTIgNy45MjI4NyAwLjMzMzI1MiA3LjAwMDY1QzAuMzMzMjUyIDYuMDc4NDMgMC41MDgzNjMgNS4yMTE3NiAwLjg1ODU4NSA0LjQwMDY1QzEuMjA4MzYgMy41ODk1NCAxLjY4MzI1IDIuODgzOTggMi4yODMyNSAyLjI4Mzk4QzIuODgzMjUgMS42ODM5OCAzLjU4ODgxIDEuMjA4ODcgNC4zOTk5MiAwLjg1ODY1MUM1LjIxMTAzIDAuNTA4ODczIDYuMDc3NyAwLjMzMzk4NCA2Ljk5OTkyIDAuMzMzOTg0QzcuOTIyMTQgMC4zMzM5ODQgOC43ODg4MSAwLjUwODg3MyA5LjU5OTkyIDAuODU4NjUxQzEwLjQxMSAxLjIwODg3IDExLjExNjYgMS42ODM5OCAxMS43MTY2IDIuMjgzOThDMTIuMzE2NiAyLjg4Mzk4IDEyLjc5MTUgMy41ODk1NCAxMy4xNDEzIDQuNDAwNjVDMTMuNDkxNSA1LjIxMTc2IDEzLjY2NjYgNi4wNzg0MyAxMy42NjY2IDcuMDAwNjVDMTMuNjY2NiA3LjkyMjg3IDEzLjQ5MTUgOC43ODk1NCAxMy4xNDEzIDkuNjAwNjVDMTIuNzkxNSAxMC40MTE4IDEyLjMxNjYgMTEuMTE3MyAxMS43MTY2IDExLjcxNzNDMTEuMTE2NiAxMi4zMTczIDEwLjQxMSAxMi43OTIyIDkuNTk5OTIgMTMuMTQyQzguNzg4ODEgMTMuNDkyMiA3LjkyMjE0IDEzLjY2NzMgNi45OTk5MiAxMy42NjczWiIgZmlsbD0iIzI3QjczNyIvPgo8L3N2Zz4K");
}

.trans-danger-btn {
    max-width: 17.6rem;
    width: 100%;
    height: 4rem;
    background-color: white;
    border: 2px solid #ff4b55;
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 2.4rem;
    letter-spacing: 0em;
    text-align: center;
    border-radius: 6px;
    color: #ff4b55;
}

/* Buttons */
/* .absolute-error {
    position: relative;
} */
/*
.absolute-error label.error {
    position: absolute;
    top: calc(100% + 3px);
    margin: 0;
    left: 0;
} */

/* Select2 Simple Selectbox */
.basic-single-select + .select2 .select2-selection,
.custom_selectBox + .select2 .select2-selection {
    background: #ffffff;
    border: 1px solid var(--bordercolor);
    border-radius: 6px;
    height: 40px;
}

.basic-single-select.error + .select2 .select2-selection,
.custom_selectBox.error + .select2 .select2-selection {
    border: 1px solid var(--red) !important;
}

.table_filter_select + .select2 .select2-selection {
    border: 1px solid var(--lightgray);
}

.basic-single-select
    + .select2
    .select2-selection
    .select2-selection__placeholder,
.custom_selectBox
    + .select2
    .select2-selection
    .select2-selection__placeholder {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #514f6e;
}

.basic-single-select + .select2 .select2-selection .select2-selection__rendered,
.custom_selectBox + .select2 .select2-selection .select2-selection__rendered {
    padding: 8px 32px 8px 16px;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #514f6e;
}

/* Select2 Placeholders */
.basic-single-select
    + .select2
    .select2-selection
    .select2-selection__rendered[title="Select Items"],
.custom_selectBox
    + .select2
    .select2-selection
    .select2-selection__rendered[title="Select Items"] {
    color: var(--bordercolor) !important;
}

/* Select2 Placeholders */

.basic-single-select + .select2 .select2-selection .select2-selection__arrow,
.custom_selectBox + .select2 .select2-selection .select2-selection__arrow {
    border: none;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNLTIuMTI1NDNlLTA3IDEuMTM3NTlDLTIuMDEwOWUtMDcgMS4zOTk2MSAwLjEwMTk5NyAxLjY2MDYzIDAuMzA0OTkyIDEuODU2NjRMNC4zMDQ4OSA1LjcxOTk0QzQuNjk3ODggNi4wOTc5NyA1LjMyMDg3IDYuMDkyOTcgNS43MDY4NiA1LjcwNjk0TDkuNzA2NzYgMS43MDY2M0MxMC4wOTc3IDEuMzE2NiAxMC4wOTc3IDAuNjgzNTUyIDkuNzA2NzYgMC4yOTI1MjNDOS4zMTU3NyAtMC4wOTc1MDcgOC42ODM3OCAtMC4wOTc1MDY5IDguMjkyNzkgMC4yOTI1MjNMNC45ODc4OCAzLjU5Nzc4TDEuNjk0OTYgMC40MTg1MzNDMS4yOTY5NyAwLjAzNDUwMzQgMC42NjM5ODMgMC4wNDU1MDQ1IDAuMjgwOTkzIDAuNDQyNTM1QzAuMDkyOTk3MyAwLjYzNzU1IC0yLjIzNDcxZS0wNyAwLjg4NzU2OSAtMi4xMjU0M2UtMDcgMS4xMzc1OVoiIGZpbGw9IiM1MTRGNkUiLz4KPC9zdmc+Cg==");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 1rem 6px;
    height: 3.8rem;

    width: 10px;
    right: 1.6rem;
}

.basic-single-select + .select2 .select2-selection .select2-selection__arrow b,
.custom_selectBox + .select2 .select2-selection .select2-selection__arrow b {
    display: none;
}

.basic-single-select
    + .select2.select2-container--open
    .select2-selection__arrow,
.custom_selectBox + .select2.select2-container--open .select2-selection__arrow {
    transform: rotate(180deg);
}

.select2-dropdown,
.dropdown-menu {
    background: #ffffff;
    box-shadow: 0px 0px 15.125px rgba(52, 84, 207, 0.08);
    /* border-radius: 8px; */
    border: none;
    min-width: fit-content !important;
}

.select2-results__options li,
.dropdown-menu li {
    padding: 4px 8px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #434656;
}

.download-estimates {
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 15.125px 0px rgba(52, 84, 207, 0.08);
}

.download-estimates li {
    padding-right: 33px;
}

.table_filter_dropdown .select2-results__options li:first-child {
    color: #90a0b7;
}

.table_filter_dropdown .select2-results__options li:hover:first-child {
    color: #ffffff;
}

/* Status Select */
.status_select + .select2 .selection [title="Pending"] {
    background: rgba(255, 150, 27, 0.1);
    color: #efa037;
    border-radius: 8px;
}

.status_select
    + .select2
    .selection
    [title="Pending"]
    + .select2-selection__arrow {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEuNSAxLjgyNzE5QzExLjUgMi4xMzI4OCAxMS4zODc4IDIuNDM3NCAxMS4xNjQ1IDIuNjY2MDhMNi43NjQ2MiA3LjE3MzI2QzYuMzMyMzMgNy42MTQzIDUuNjQ3MDUgNy42MDg0NiA1LjIyMjQ2IDcuMTU4MDlMMC44MjI1NjcgMi40OTEwN0MwLjM5MjQ3NyAyLjAzNjA0IDAuMzkyNDc3IDEuMjk3NDggMC44MjI1NjcgMC44NDEyNzdDMS4yNTI2NiAwLjM4NjI0MiAxLjk0Nzg0IDAuMzg2MjQyIDIuMzc3OTMgMC44NDEyNzdMNi4wMTMzNCA0LjY5NzQxTDkuNjM1NTUgMC45ODgyODhDMTAuMDczMyAwLjU0MDI1NCAxMC43Njk2IDAuNTUzMDg4IDExLjE5MDkgMS4wMTYyOUMxMS4zOTc3IDEuMjQzODEgMTEuNSAxLjUzNTUgMTEuNSAxLjgyNzE5WiIgZmlsbD0iI0ZFOUI2QSIvPgo8L3N2Zz4K);
}

.status_select + .select2 .select2-selection {
    border: none !important;
}

.status_select + .select2 .selection [title="In Progress"] {
    background: rgba(49, 89, 254, 0.1);
    color: #3159fe;
    border-radius: 8px;
}

.status_select
    + .select2
    .selection
    [title="In Progress"]
    + .select2-selection__arrow {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEuNSAxLjgyNzE4QzExLjUgMi4xMzI4OCAxMS4zODc4IDIuNDM3NCAxMS4xNjQ1IDIuNjY2MDhMNi43NjQ2MiA3LjE3MzI2QzYuMzMyMzMgNy42MTQzIDUuNjQ3MDUgNy42MDg0NiA1LjIyMjQ2IDcuMTU4MDlMMC44MjI1NjcgMi40OTEwN0MwLjM5MjQ3NyAyLjAzNjAzIDAuMzkyNDc3IDEuMjk3NDggMC44MjI1NjcgMC44NDEyNzZDMS4yNTI2NiAwLjM4NjI0MiAxLjk0Nzg0IDAuMzg2MjQyIDIuMzc3OTMgMC44NDEyNzZMNi4wMTMzNCA0LjY5NzQxTDkuNjM1NTUgMC45ODgyODdDMTAuMDczMyAwLjU0MDI1MyAxMC43Njk2IDAuNTUzMDg3IDExLjE5MDkgMS4wMTYyOUMxMS4zOTc3IDEuMjQzODEgMTEuNSAxLjUzNTUgMTEuNSAxLjgyNzE4WiIgZmlsbD0iIzMxNTlGRSIvPgo8L3N2Zz4K);
}

.status_select + .select2 .selection [title="Completed"] {
    background: rgba(39, 183, 55, 0.1);
    color: #27b737;
    border-radius: 8px;
}

.status_select
    + .select2
    .selection
    [title="Completed"]
    + .select2-selection__arrow {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEuNSAxLjgyNzE4QzExLjUgMi4xMzI4OCAxMS4zODc4IDIuNDM3NCAxMS4xNjQ1IDIuNjY2MDhMNi43NjQ2MiA3LjE3MzI2QzYuMzMyMzMgNy42MTQzIDUuNjQ3MDUgNy42MDg0NiA1LjIyMjQ2IDcuMTU4MDlMMC44MjI1NjcgMi40OTEwN0MwLjM5MjQ3NyAyLjAzNjAzIDAuMzkyNDc3IDEuMjk3NDggMC44MjI1NjcgMC44NDEyNzZDMS4yNTI2NiAwLjM4NjI0MiAxLjk0Nzg0IDAuMzg2MjQyIDIuMzc3OTMgMC44NDEyNzZMNi4wMTMzNCA0LjY5NzQxTDkuNjM1NTUgMC45ODgyODdDMTAuMDczMyAwLjU0MDI1MyAxMC43Njk2IDAuNTUzMDg3IDExLjE5MDkgMS4wMTYyOUMxMS4zOTc3IDEuMjQzODEgMTEuNSAxLjUzNTUgMTEuNSAxLjgyNzE4WiIgZmlsbD0iIzI3QjczNyIvPgo8L3N2Zz4K);
}

/* Status Select Ends */

/* Select2 Simple Selectbox */

.password-wrapper {
    position: relative;
}

.password-wrapper .toggle-password {
    position: absolute;
    top: 2rem;
    right: 1.2rem;
    transform: translateY(-50%);
    z-index: 1;
    cursor: pointer;
    user-select: none;
    height: 2rem;
    width: 2rem;

    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.password-wrapper input {
    background-image: none !important;
}

/* Datatable Styles */

/* Clients Styles Starts */
.table_filter_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.table_filter_header .table_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    text-transform: capitalize;
    color: #192a3e;
}

.table_filter_header .header-left-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.table_filter_header .header-left-content .view-request-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 12px;
    border-radius: 14px;
    border: 1px solid var(--additional-3, #868686);
    background-color: transparent;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
    color: #868686;
    text-decoration: none;
}

.view-request-btn2 {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 12px;
    border-radius: 14px;
    border: 1px solid var(--additional-3, #868686);
    background-color: transparent;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
    color: #868686;
    text-decoration: none;
}

.table_filter_header .header-buttpns-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 14px;
}

.table_filter_header .header-buttpns-main .header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    height: 48px;
    padding: 12px 16px;
    background-color: transparent;
    text-align: center;
    font-family: Cabin;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}

.table_filter_header .header-buttpns-main .header-btn.request-change {
    border: 1.5px solid #fe9b6a;
    color: #fe9b6a;
}

.table_filter_header .header-buttpns-main .header-btn.reject-btn {
    border: 1.5px solid #ff4b55;
    color: #ff4b55;
}

.table_filter_header .header-buttpns-main .header-btn.approve-btn {
    border: 1.5px solid #27b737;
    background-color: #27b737;
    color: #ffffff;
}

.table_filter_header .header-buttpns-main .header-btn.action-btn {
    border: 1.5px solid #868686;
}

.table_filter_header .header-buttpns-main .header-btn.pay-btn {
    background-color: #27b737;
    color: #ffffff;
    border: 1.5px solid #27b737;
    text-decoration: none;
}

.table_header_footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.table_filter_header .filters,
.table_header_footer .page_per {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.table_header_footer .page_per .info {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #170f49;
}

.table_header_footer .table_paigination {
    border-radius: 8px;
    padding: 9px 12px 9px 12px;
    background-color: #ffffff;
    border: 1px solid #e0e7ed;
    background-size: 10px !important;
}

.clientTable.dataTable.display > tbody > tr.odd > td,
.clientTable.dataTable.order-column.stripe > tbody > tr.odd > td {
    box-shadow: unset !important;
}

table > tbody tr > .sorting_1 {
    box-shadow: none !important;
}

table > tbody tr:hover > .sorting_1 {
    box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.04) !important;
}

/* Clients Styles Starts */

.table_filter_header .basic-single-select + .select2 .select2-selection {
    border: 1px solid var(--lightgray) !important;
}

.select2-search__field {
    background-image: url(../images/icons/input-search-icon.svg);
    background-repeat: no-repeat;
    background-position: center right 10px;
    background-size: 20px;
    padding-right: 32px !important;
}

.company-client_tabs {
    display: flex;
    align-items: center;
    gap: 2.4rem;
    border-bottom: 1px solid #e3e0e0;
    padding: 0;
}

.company-client_tabs_property {
    display: flex;
    align-items: center;
    gap: 2.4rem;
    padding: 0;
}

.company-client_tabs .nav-item .nav-link {
    border: none;
    background-color: transparent;
    padding: 0 0 13px 0;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    color: #949292;
    border-bottom: 1px solid transparent;
}
.open {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 18%;
    border-top-left-radius: 20px; /* Top-left corner */
    border-bottom-left-radius: 20px;
    height: 40px;
}
.label-text {
    color: rgba(124, 128, 145, 1);
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
}
.detail-value {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: rgba(38, 57, 68, 1); /* Apply the specified color */
}
.cancel {
    font-family: "Inter", sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    border-radius: 10px;
    margin-bottom: 16px;
    background-color: rgba(255, 255, 255, 1);
}
.detail-subheading {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: rgba(38, 57, 68, 1);
}
.detail-heading {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 16px;
    /*line-height: 24px;*/
}
.open_detail {
    display: flex; /* or inline-flex if you want it to be inline */
    justify-content: center;
    align-items: center;
    text-align: center;
    color: rgba(239, 141, 3, 1);
    background-color: rgba(255, 245, 233, 1);
    width: 25%;
    font-size: 15px;
    border-radius: 10px;
    height: 40px;
    box-sizing: border-box; /* Ensure padding and border are included in the width and height */
    border: none; /* Remove border if it's not required */
    cursor: pointer; /* Add cursor style for button-like behavior */
}
.estimating {
    display: flex;
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 18%;
    height: 40px;
}
.pricing {
    display: flex;
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 18%;
    height: 40px;
}
.proposed {
    display: flex;
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 18%;
    height: 40px;
}
.production {
    display: flex;
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: rgba(117, 82, 255, 1);
    background-color: rgba(234, 229, 251, 1);
    width: 18%;
    height: 40px;
}

.mark-as-complete {
    background-color: rgba(47, 204, 64, 1);
    height: 42px;
    width: 90%;
    border: rgba(47, 204, 64, 1);
    border-radius: 10px;
    color: white;
}
.closed {
    display: flex;
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 18%;
    height: 40px;
    border-top-right-radius: 20px; /* Top-left corner */
    border-bottom-right-radius: 20px;
}
.upload-container {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: nowrap; /* This ensures everything stays in one line */
    overflow-x: auto;
}
#upload-boxes {
    display: flex;
    gap: 1rem;
}
.upload-box {
    width: 150px;
    height: 150px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f7f7f7;
    overflow: hidden;
    position: relative;
}

.file-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1; /* Image as background */
}

.file-info {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2; /* Overlay info over image */
    color: #0b0202;
    background-color: rgba(
        235,
        225,
        225,
        0.6
    ); /* Semi-transparent background */
    padding: 5px;
    border-radius: 4px;
    font-size: 12px;
}

.upload-placeholder {
    cursor: pointer;
    border: 2px dashed #c7c7c7;
}

.file_manager {
    width: 100%;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    background-color: #fff;
    margin-bottom: 10px;
    margin: 0px 32px;
}

.file {
    display: flex;
    align-items: center;
}
.file_image {
    margin-right: 8px;
}

.file_details {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 7px 10px 0px 10px;
}
.file_info {
    flex-grow: 1;
    margin-right: 10px;
    margin-top: 15px;
}

.file_name {
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-size: 14px;
    margin: 0;
    line-height: 50%;
}

.file_description {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 12px;
    margin: 0;
    color: rgba(124, 128, 145, 0.8);
}

.download_icon {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #007bff; /* Blue color for the icon */
    cursor: pointer;
}

.company-client_tabs .nav-item .nav-link.active {
    color: #036;
    /* border-bottom: 1px solid #FE9B6A; */
    border-bottom: 1px solid rgba(34, 14, 177, 1);
}

.custom_datatable {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
        rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
    background: var(--white);
    border-radius: 8px;
    border: none !important;
    margin-bottom: 16px !important;
}

.custom_datatable thead th {
    padding: 16px 24px !important;
    font-family: "Poppins" !important;
    font-style: normal !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 22px !important;
    color: #192a3e;
    background-color: #dcf2ff;
    border-bottom: none !important;
    white-space: nowrap;
    user-select: none;
}

.custom_datatable.estimate-list thead th {
    background-color: #ffffff;
    color: #868686;
}

.custom_datatable thead th:first-child {
    border-top-left-radius: 8px;
}

.custom_datatable thead th:last-child {
    border-top-right-radius: 8px;
}

.custom_datatable thead th {
    background-image: none !important;
}

.custom_datatable thead th::before,
.custom_datatable thead th::after {
    content: unset !important;
}

.custom_datatable tbody td {
    padding: 16px 24px !important;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #192a3e;
    border: none !important;
    vertical-align: middle;
}

.custom_datatable tbody td .estimate-title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: normal;
    color: #192a3e;
}

.custom_datatable.estimate-list tbody td:last-child {
    white-space: nowrap;
}

.custom_datatable.estimate-list tbody td .view-estimate {
    cursor: pointer;
    display: block;
    padding: 6px 12px;
    border-radius: 31px;
    border: 1px solid #868686;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #192a3e;
    width: fit-content;
    margin: 0 auto;
}

.custom_datatable tbody td .estimate-list-drpdown {
    width: 180px !important;
}

.custom_datatable tbody td .estimate-list-drpdown ul.dropdown-menu {
    width: 180px !important;
    padding: 8px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 15.125px 0px rgba(52, 84, 207, 0.08);
}

.custom_datatable tbody td .estimate-list-drpdown ul.dropdown-menu.show {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.custom_datatable.client-table thead tr th {
    background-color: transparent;
    padding: 16px 24px;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #868686;
    white-space: nowrap;
}

.custom_datatable.client-table tbody tr td,
.custom_datatable.client-table tbody tr td .profile-name {
    padding: 16px 24px;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #192a3e;
    white-space: nowrap;
}

.custom_datatable.client-table tbody tr td .profile-name {
    padding: 0;
}

.custom_datatable.client-table tbody tr td .client-status {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 1px solid #000000;
    padding: 4px 16px;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    color: #000000;
}

.custom_datatable.client-table tbody tr td .client-status.select {
    border: 1px solid #fe9b6a;
    color: #fe9b6a;
}

.width-301 {
    width: 350px;
}

.width-186 {
    width: 250px;
}

.custom_datatable tbody td .estimate-detail {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 19px;
    color: #84818a;
}

.custom_datatable tbody td:nth-child(4) {
    white-space: nowrap;
}

.clientTable.custom_datatable tbody td:nth-child(5) {
    color: #fe9b6a;
}

.clientTable.estimate-list tbody td:nth-child(5) {
    color: #192a3e;
}

.custom_datatable tbody td .status {
    display: flex;
    padding: 4px 17px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: normal;
}

.custom_datatable tbody td .status.open {
    background-color: #fff0e8;
    color: #fe9b6a;
}

.custom_datatable tbody td .status.closed {
    background-color: #f0fff2;
    color: #27b737;
}

.custom_datatable tbody td .status.approved {
    background-color: #f0fff2;
    color: #27b737;
}

.custom_datatable tbody td .status.pending {
    background-color: #e9f4ff;
    color: #036;
}

.custom_datatable tbody td .status.proposed {
    background-color: #e9f4ff;
    color: #036;
}

.estimate-detail-box tbody td .status.reject {
    background-color: #fff0e8;
    color: #fe9b6a;
}

.svg-light svg path {
    fill: #e7e7e7;
}

.svg-yellow svg path {
    fill: #fe9b6a;
}

.estimate-detail-box .status.approved {
    background-color: #f0fff2;
    color: #27b737;
}

.estimate-detail-box .status.proposed {
    background-color: #e9f4ff;
    color: #036;
}

.custom_datatable .status.reject {
    background-color: #fff0e8;
    color: #fe9b6a;
}

.estimate-dropdown .dropdown-menu.show {
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 15.125px 0px rgba(52, 84, 207, 0.08);
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.estimate-dropdown .dropdown-menu .dropdown-item {
    font-family: "Cabin";
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    line-height: 22px;
    color: #434656;
}

/* Delete Modal Style */

.delete-modal .modal-content {
    border-radius: 10px;
    background: #fff;
    box-shadow: 0px 0px 19px 0px rgba(0, 0, 0, 0.07);
    padding: 0;
}

.delete-modal .modal-body {
    padding-top: 4rem;
    padding-bottom: 4rem;
    position: relative;
}

.delete-modal .modal-body .btn-close {
    position: absolute;
    right: 2.4rem;
    top: 2.4rem;
    background-image: url("../../images/icons/fa-times.svg");
}

.delete-modal .modal-body .delete-icon {
    display: block;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.delete-modal .modal-body .delete-request {
    margin-top: 2.5rem;
    margin-bottom: 0.8rem;
    font-family: "Cabin";
    font-weight: 500;
    font-style: normal;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
    color: #036;
}

.delete-modal .modal-body .are-sure {
    margin: 0;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    color: #868686;
}

.delete-modal .modal-body .buttons-wraper {
    margin-top: 3.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.6rem;
}

.delete-modal .modal-body .buttons-wraper .cancel-btn {
    width: 178px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    padding: 12px 16px;
    border: 1.5px solid var(--primary, #036);
    background-color: transparent;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #036;
}

.delete-modal .modal-body .buttons-wraper .conform-btn {
    width: 178px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    padding: 12px 16px;
    border: 1.5px solid var(--primary, #036);
    background-color: #036;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    color: #ffffff;
}

/* Delete Modal Style Ends */

/* Create Request Page Style */

.request-wraper {
    display: grid;
    grid-template-columns: 1.4fr 0.78fr;
    /* grid-template-areas: "item1 item2"
                        "item1 item3"
                        "item1 ."
                        "btns ."; */
    column-gap: 16px;
    row-gap: 0;
}

.request-wraper .create-estimation-btns {
    /* grid-area: btns; */
    display: flex;
    align-items: center;
    gap: 20px;
    width: fit-content;
    margin-left: auto;
    margin-top: 27px;
}

.request-wraper .create-estimation-btns .estimation-btn {
    display: flex;
    padding: 8px 16px;
    height: 48px;
    width: 176px;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    border: 1.5px soli #036;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    color: #036;
}

.request-wraper .create-estimation-btns .estimation-btn.create-estimation {
    width: 244px;
    background-color: #036;
    color: white;
}

.request-wraper .request-details-box,
.request-wraper .request-user-detail {
    border-radius: 8px;
    background: var(--primary-0, #fff);
    padding-top: 20px;
    padding-bottom: 20px;
}

.request-wraper .request-user-detail {
    padding-top: 0;
}

.request-wraper .request-details-box.item-1 {
    /* grid-area: item1; */
}

.request-wraper .request-user-detail.item-2 {
    /* grid-area: item2; */
    height: fit-content;
}

.request-wraper .request-user-detail.item-3 {
    /* grid-area: item3; */
    margin-top: 16px;
}

.px-24 {
    padding-left: 24px;
    padding-right: 24px;
}

.request-detail-header {
    padding-bottom: 21px;
}

.request-detail-header .request-title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 150%;
    color: #192a3e;
}

.request-detail-header .estimation-request {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: normal;
    color: #757575;
    letter-spacing: -0.28px;
}

.border-line-hr {
    height: 1px;
    width: 100%;
    background-color: #e7e7e7;
}

.title-field-wraper {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.title-field-wraper .estimate-status-wraper {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.color-oof-white {
    color: #969696 !important;
    line-height: 24px;
}

.color-black {
    color: #2e2a40 !important;
}

.title-field-wraper .estimate-status-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.title-field-wraper .estimate-status-subwraper .status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 17px;
    border-radius: 4px;
    font-family: Cabin;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.title-field-wraper .estimate-status-wraper .status.pending {
    background: #e9f4ff;
    color: #036;
}

.title-field-wraper .estimate-status-wraper .status.open {
    background: #fff0e8;
    color: #fe9b6a;
}

.title-label {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #192a3e;
}

.title-label.custom-font {
    color: #969696;
}

#update-Client-Profile-Form .update-profile-btn {
    display: flex;
    padding: 8px 16px;
    height: 34px;
    width: 70px;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    border: 1.5px soli #036;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    color: white;
    background: #036;
}

#update-Client-Profile-Form .Cancel-profile-btn {
    display: flex;
    padding: 8px 16px;
    height: 34px;
    width: 70px;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    border: 1.5px soli #036;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    color: #036;
}

.title-field {
    max-width: 478px;
    width: 100%;
    padding: 13px 166px 13px 12px;
    border-radius: 4px;
    border: 1px solid var(--text-filed-stooke, #ebebeb);
    background: #ffffff;
    outline: none;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
    color: #192a3e;
}

.title-field::placeholder {
    color: #c2c2c2;
}

.property-container-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.propert-container {
    border-radius: 6px;
    border: 1px solid var(--text-filed-stooke, #ebebeb);
    padding: 11px 17px 20px 17px;
}

.propert-container .marks-property {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #2e2a40;
    padding-bottom: 10px;
}

.propert-container .country-and-address {
    display: flex;
    align-items: center;
    gap: 15px;
}

.add-new-propert {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    margin-left: auto;
    margin-top: 10px;
    background: transparent;
    border: none;
    text-decoration: underline;
    font-style: normal;
    font-weight: 500;
    font-family: "Cabin";
    font-size: 16px;
    line-height: 24px;
    color: #036;
}

.propert-container .property-address,
.propert-container .propert-country,
.propert-container .propert-country span {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    color: #868686;
}

.propert-container .propert-country span {
    color: #192a3e;
}

.reqest-text-label {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #192a3e;
}

.describe-project {
    border-radius: 6px;
    border: 1px solid var(--text-filed-stooke, #ebebeb);
    min-height: 183px;
    resize: none;
    padding: 11px 17px;
    width: 100%;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 26px;
    color: #192a3e;
}

.describe-project::placeholder {
    color: #c2c2c2;
}

.check-fileds-wraper {
    display: flex;
    align-items: center;
    gap: 71px;
    margin-top: 13px;
}

.check-box-wraper {
    display: flex;
    align-items: center;
    gap: 11px;
}

.check-box-wraper .checkLabel {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24p;
    color: #868686;
}

.check-box-wraper input {
    accent-color: #fe9b6a;
    display: none;
}

.check-box-wraper .radio-label {
    display: block;
    width: 20px;
    height: 20px;
    /* background-image: url(../images/icons/radio-checked-bg2.svg); */
    border-radius: 4px;
    border: 1.5px solid var(--text-grey, #94959b);
}

.check-box-wraper input:checked + .radio-label {
    background-image: url("../images/icons/radio-checked-bg.svg");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    border: none;
}

.request-user-detail .profile-header {
    padding-top: 17px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid #f3f3f3;
}

.request-user-detail .profile-header .edit-btn {
    display: flex;
    align-items: center;
    background-color: transparent;
    border-radius: 28px;
    border: 1px solid #868686;
    padding: 4px 7px;
}

.request-user-detail .profile-header .edit-btn span {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    color: #868686;
}

.upload-image-wraper {
    display: grid;
    grid-template-columns: 104px 1fr;
    gap: 26px;
    padding-top: 16px;
    padding-bottom: 29px;
}

.upload-image-wraper .image-box {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    overflow: hidden;
}

.upload-image-wraper .image-box img {
    object-fit: cover;
}

.upload-image-wraper .user-profile-detail {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.upload-image-wraper .user-profile-detail .edit-photo {
    font-family: "Outfit";
    font-style: normal;
    font-size: 16px;
    font-weight: 500;
    line-height: 20.8px;
    letter-spacing: 0.16px;
    color: #010101;
}

.upload-image-wraper .user-profile-detail .update-photo {
    display: block;
    margin-top: 10px;
    font-style: normal;
    font-family: "Outfit";
    font-size: 14px;
    font-weight: 400;
    line-height: 18.2px;
    letter-spacing: 0.14px;
    color: #e23434;
}

.upload-image-wraper .user-profile-detail .image-extensions {
    font-family: "Outfit";
    font-style: normal;
    font-size: 12px;
    font-weight: 500;
    line-height: 15px;
    letter-spacing: 0.12px;
    color: #93989a;
    margin-top: 14px;
}

.upload-image-wraper .upload-field {
    position: relative;
}

.upload-image-wraper .upload-field input {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.profile-detail-body {
    display: flex;
    flex-direction: column;
    gap: 26px;
    padding-top: 16px;
    padding-bottom: 10px;
}

.profile-detail-body label.error {
    position: absolute;
    bottom: -17px;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    left: 0;
}

.profile-detail-body .detailed-data {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.profile-detail-body .detailed-data .detail-title {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #969696;
}

.profile-detail-body .detailed-data .detailed-description {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #036;
}

.profile-detail-body .detailed-data .detailed-description small {
    color: #969696;
}

/* Property Modal Style */

.propert-modal .modal-content {
    border: none;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 15.125px 0px rgba(52, 84, 207, 0.08);
    overflow: hidden;
}

.propert-modal .modal-content .modal-header {
    background: #e1f4ff;
    padding: 6px 30px;
    border: none;
}

.propert-modal .modal-content .modal-header .modal-title {
    font-family: "Cabin";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 42px;
    color: #192a3e;
}

.propert-modal .modal-content .modal-body {
    padding-top: 17px;
    padding-bottom: 22px;
    padding-left: 30px;
    padding-right: 30px;
}

.propert-modal .modal-content .modal-body .body-wraped {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.propert-modal .modal-content .modal-body .generate-invoice {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 19.42px;
    color: #868686;
}

.propert-modal .modal-content .modal-body .filed-wraper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    position: relative;
}

span.error-message {
    position: absolute;
    bottom: -21px;
}

.propert-modal .modal-content .modal-body .filed-wraper .modal-label {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #192a3e;
}

.propert-modal .modal-content .modal-body .modal-sub-heading {
    font-family: "Cabin";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 21.85px;
    color: #192a3e;
}

.propert-modal .modal-content .modal-body .filed-wraper .reason {
    font-family: "Cabin";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 21.85px;
    color: #192a3e;
}

.propert-modal .modal-content .modal-body .balance {
    padding-top: 10.5px;
    padding-bottom: 10.5px;
}

.propert-modal .modal-content .modal-body .balance-title {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #868686;
}

.propert-modal .modal-content .modal-body .balance-price {
    margin: 0;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #192a3e;
}

.propert-modal .modal-content .modal-body .rejection-detail {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20.38px;
    color: #868686;
}

.propert-modal .modal-content .modal-body .filed-wraper .modal-field,
.monthpicker_input {
    height: 48px;
    border-radius: 6px;
    width: 100%;
    border: 1px solid var(--dark-4-dark, #e8e8e8);
    background: var(--dark-6-white, #fff);
    padding: 13px;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #192a3e;
}

.propert-modal
    .modal-content
    .modal-body
    .filed-wraper
    .modal-field::placeholder,
.propert-modal
    .modal-content
    .modal-body
    .filed-wraper
    .modal-textarea::placeholder,
.monthpicker_input span {
    color: #c2c2c2;
}

.propert-modal .modal-content .modal-body .filed-wraper .modal-textarea {
    border-radius: 6px;
    width: 100%;
    border: 1px solid var(--dark-4-dark, #e8e8e8);
    background: var(--dark-6-white, #fff);
    padding: 13px;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #192a3e;
    min-height: 220px;
    resize: none;
}

.propert-modal .modal-content .modal-body .modal-flex {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.propert-modal .modal-content .modal-body .filed-wraper select {
    border: none;
    outline: none;
    width: 100%;
    height: 100%;
}

.propert-modal
    .modal-content
    .modal-body
    .filed-wraper
    input[type="number"]::-webkit-inner-spin-button,
.propert-modal
    .modal-content
    .modal-body
    .filed-wraper
    input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.propert-modal .modal-content .modal-body .add-btn,
.propert-modal .modal-content .modal-body .add-target {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    border-radius: 6px;
    background: var(--primary, #036);
    border: none;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    color: #ffffff;
    cursor: pointer;
}

.propert-modal .modal-content .modal-body .radio-btns-wraper {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 13px;
}

.propert-modal
    .modal-content
    .modal-body
    .radio-btns-wraper
    .rejection-field-main {
    display: flex;
    align-items: center;
    gap: 10px;
}

.propert-modal
    .modal-content
    .modal-body
    .radio-btns-wraper
    .rejection-field-main
    .rejection-field {
    display: none;
}

.propert-modal
    .modal-content
    .modal-body
    .radio-btns-wraper
    .rejection-field-main
    .rejection-field:checked
    + .radio-btn {
    background-image: url("../images/icons/radio-checked.png");
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    border: none !important;
}

.propert-modal
    .modal-content
    .modal-body
    .radio-btns-wraper
    .rejection-field-main
    .radio-btn {
    width: 21px;
    height: 21px;
    border-radius: 50%;
    border: 1px solid #a2acbd;
}

.propert-modal
    .modal-content
    .modal-body
    .radio-btns-wraper
    .rejection-field-main
    .rejection-label {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 19.42px;
    color: #3e4756;
}

.propert-modal .modal-content .modal-body .signature-box {
    min-height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f6f6f6;
    margin-top: 13px;
}

.propert-modal .modal-content .modal-body .signature-box .signature {
    font-family: "Reenie Beanie";
    font-size: 29px;
    font-style: normal;
    font-weight: 400;
    line-height: 13px;
    color: #192a3e;
}

/* Property Modal Style Ends */

/* Create Request Page Style Ends */

/* Create Invoice Page Style */

.subtotal-main {
    display: flex;
    flex-direction: column;
    gap: 28px;
    max-width: 284px;
    margin-top: 92px;
    margin-left: auto;
}

.subtotal-main .total-title {
    font-family: "Cabin";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #90a0b7;
}

.subtotal-main .total-price {
    font-family: "Cabin";
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    color: #192a3e;
}

/* Create Invoice Page Style Ends */

/* View Individual Estimate Request Detail Page Style */

.estimate-detail-box {
    margin-top: 20px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(36, 185, 236, 0.08);
    padding: 24px;
}

.estimate-detail-box .estimate-top-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.estimate-detail-box .estimate-top-header .header-left {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.estimate-detail-box .estimate-top-header .header-left .status-text {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #868686;
}

.estimate-detail-box .status {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.estimate-detail-box .status.open {
    background: #fff0e8;
    color: #fe9b6a;
}

.estimate-detail-box .status.unpaid {
    background: rgba(255, 150, 27, 0.1);
    color: #efa037;
}

.total-invoces-price > span {
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #192a3e;
}

.total-invoces-price .total-invoce-prices {
    font-family: "Cabin";
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 40px;
    color: #036;
}

.estimate-detail-box .estimate-top-header .header-right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.estimate-detail-box .estimate-id {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #192a3e;
}

.estimate-detail-box table.detailed-table {
    margin-top: 18.5px;
}

.estimate-detail-box table.detailed-table thead tr th {
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #90a0b7;
    padding-bottom: 5px;
    padding-left: 10px;
    padding-right: 10px;
}

.estimate-detail-box table.detailed-table tbody tr td {
    vertical-align: top;
    white-space: nowrap;
    padding-left: 10px;
    padding-right: 10px;
}

.estimate-detail-box table.detailed-table tbody tr td .td-title,
.estimate-detail-box table.detailed-table tbody tr td span {
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    color: #192a3e;
}

.estimate-detail-box table.detailed-table tbody tr:last-child td {
    padding-bottom: 16px;
    border-bottom: 1px solid #eceaea;
}

.estimate-detail-box table.detailed-table tbody tr td .td-text {
    font-family: "Cabin";
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 19px;
    color: #868686;
}

.table-wraper {
    margin-top: 19px;
    padding: 24px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(36, 185, 236, 0.08);
}

table.view-detialed-table thead tr th {
    background-color: transparent;
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #868686;
}

table.view-detialed-table tbody tr td {
    font-family: "Cabin";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #192a3e;
    white-space: nowrap;
}

table.view-detialed-table tbody tr td:last-child {
    color: #192a3e;
}

/* View Individual Estimate Request Detail Page Style Ends */

.custom_datatable + .bottom,
.dataTables_scroll + .bottom {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.dataTables_scroll + .bottom {
    margin-top: 16px !important;
}

.custom_datatable + .bottom .dataTables_length label,
.dataTables_scroll + .bottom .dataTables_length label {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #170f49;

    display: flex;
    align-items: center;
    gap: 8px;
}

.custom_datatable + .bottom .dataTables_length select,
.dataTables_scroll + .bottom .dataTables_length select {
    background: #ffffff;
    border: 1px solid #e0e7ed;
    border-radius: 8px;

    height: 36px;
    width: 66px;
    border-radius: 8px;
    padding: 9px 12px 9px 12px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #170f49;

    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;

    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNLTIuMTI1NDNlLTA3IDEuMTM3NTlDLTIuMDEwOWUtMDcgMS4zOTk2MSAwLjEwMTk5NyAxLjY2MDYzIDAuMzA0OTkyIDEuODU2NjRMNC4zMDQ4OSA1LjcxOTk0QzQuNjk3ODggNi4wOTc5NyA1LjMyMDg3IDYuMDkyOTcgNS43MDY4NiA1LjcwNjk0TDkuNzA2NzYgMS43MDY2M0MxMC4wOTc3IDEuMzE2NiAxMC4wOTc3IDAuNjgzNTUyIDkuNzA2NzYgMC4yOTI1MjNDOS4zMTU3NyAtMC4wOTc1MDcgOC42ODM3OCAtMC4wOTc1MDY5IDguMjkyNzkgMC4yOTI1MjNMNC45ODc4OCAzLjU5Nzc4TDEuNjk0OTYgMC40MTg1MzNDMS4yOTY5NyAwLjAzNDUwMzQgMC42NjM5ODMgMC4wNDU1MDQ1IDAuMjgwOTkzIDAuNDQyNTM1QzAuMDkyOTk3MyAwLjYzNzU1IC0yLjIzNDcxZS0wNyAwLjg4NzU2OSAtMi4xMjU0M2UtMDcgMS4xMzc1OVoiIGZpbGw9IiM1MTRGNkUiLz4KPC9zdmc+Cg==");
    background-repeat: no-repeat;
    background-position: center right 10px;
}

.custom_datatable + .bottom .dataTables_info,
.dataTables_scroll + .bottom .dataTables_info {
    padding: 0px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #170f49;
}

.custom_datatable + .bottom .dataTables_paginate,
.dataTables_scroll + .bottom .dataTables_paginate {
    margin-left: auto;
}

.custom_datatable + .bottom .dataTables_paginate .paginate_button,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button {
    background: #f1f1f1;
    border-radius: 2px;
    padding: 5px 12px 5px 12px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;

    color: #90a0b7 !important;
}

.custom_datatable + .bottom .dataTables_paginate .paginate_button.current,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.current {
    /* background: #003366 !important; */
    background: var(--primaryblue) !important;
    color: #ffffff !important;
}

.custom_datatable + .bottom .dataTables_paginate .paginate_button.current i,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.current i {
    color: #ffffff !important;
}

.custom_datatable + .bottom .dataTables_paginate .paginate_button.previous,
.custom_datatable + .bottom .dataTables_paginate .paginate_button.next,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.previous,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.next {
    background: #003366 !important;
    color: #ffffff !important;
}

.custom_datatable + .bottom .dataTables_paginate .paginate_button.previous i,
.custom_datatable + .bottom .dataTables_paginate .paginate_button.next i,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.previous i,
.dataTables_scroll + .bottom .dataTables_paginate .paginate_button.next i {
    color: #ffffff !important;
}

.custom_datatable
    + .bottom
    .dataTables_paginate
    .paginate_button.previous.disabled,
.custom_datatable + .bottom .dataTables_paginate .paginate_button.next.disabled,
.dataTables_scroll
    + .bottom
    .dataTables_paginate
    .paginate_button.previous.disabled,
.dataTables_scroll
    + .bottom
    .dataTables_paginate
    .paginate_button.next.disabled {
    background: #f1f1f1 !important;
    color: #90a0b7 !important;
}

.custom_datatable
    + .bottom
    .dataTables_paginate
    .paginate_button.previous.disabled
    i,
.custom_datatable
    + .bottom
    .dataTables_paginate
    .paginate_button.next.disabled
    i,
.dataTables_scroll
    + .bottom
    .dataTables_paginate
    .paginate_button.previous.disabled
    i,
.dataTables_scroll
    + .bottom
    .dataTables_paginate
    .paginate_button.next.disabled
    i {
    color: #90a0b7 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    border-color: var(--lightgray);
}

/* Yajra Datatable Pagination */
.custom_datatable + .bottom .dataTables_paginate .pagination {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 16px;
}

.custom_datatable + .bottom .dataTables_paginate .pagination .page-item {
    padding: 0px;
    border: none !important;
}

.custom_datatable
    + .bottom
    .dataTables_paginate
    .pagination
    .page-item
    .page-link {
    height: 32px;
    min-width: 29px;
    padding: 5px 12px;
    border: none;
    background: #f1f1f1;
    border-radius: 2px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;

    display: flex;
    align-items: center;
    justify-content: center;
    color: #90a0b7;
}

.custom_datatable
    + .bottom
    .dataTables_paginate
    .pagination
    .page-item.active
    .page-link {
    background-color: #003366;
    color: white;
}

.custom_datatable
    + .bottom
    .dataTables_paginate
    .pagination
    #DataTables_Table_0_previous.disabled
    .page-link
    i,
.custom_datatable
    + .bottom
    .dataTables_paginate
    .pagination
    #DataTables_Table_0_next.disabled
    .page-link
    i {
    color: #90a0b7 !important;
}

table.custom_datatable.view-invoice thead tr th {
    background: transparent;
    color: #868686;
}

/* Yajra Datatable Pagination */

/* Datatable Styles */

/* Table Tabs Filter */
.table_tabs_filter {
    border-bottom: 3px solid #e3e0e0;
    display: flex;
    gap: 3rem;
}

.table_tabs_filter input:checked + label {
    color: #2ecc40;
    border-bottom: 2px solid #2ecc40;
    font-weight: 500;
}

.table_tabs_filter .btn-tab {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 2rem;
    color: #90a0b7;
    padding: 0px;
    border-radius: 0px;
    box-shadow: none !important;
    padding-bottom: 10px;
    transform: translateY(2px);
}

/* Table Tabs Filter */

.panel {
    background: #ffffff;
    box-shadow: 0px 0px 12px rgba(36, 185, 236, 0.08);
    border-radius: 8px;
    padding: 2.4rem;
}
.add-new {
    color: rgba(0, 116, 217, 1);
}
.vertical-dotted-line {
    border-left: 2px dotted #e2e4ea;
    height: 350px;
    position: absolute;
    left: 2;
    margin-top: 20px;
}
.property-details-wrapper {
    position: relative;
    margin-top: 20px; /* Space between the heading and the bordered section */
}

.detail-heading-property {
    position: absolute;
    top: -15px; /* Adjust this value to move the heading up */
    left: 15px; /* Optional: adjust this value to position the heading horizontally */
    background: #fff; /* Ensures the heading has a background color to cover the border */
    padding: 0 10px; /* Adds space around the text */
    z-index: 1; /* Ensures the heading appears above the border */
}
.line-item {
    width: auto;
    height: 30px;
}
.division-name {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 18px;
    line-height: 27px;
    color: rgba(38, 57, 68, 1);
}
.division-container {
    display: flex;
    flex-wrap: wrap; /* Allows items to wrap to the next line */
}
.horizontal-line {
    border: 0; /* Remove default border */
    border-top: 1px solid rgba(208, 213, 221, 1); /* Style the line */
    width: 100%; /* Ensure it spans the full width of the container */
    margin: 0;
    padding: 10px 0px; /* Remove default margin */
}
.line-item {
    margin-top: 1%; /* Space from the top of each item */
    margin-right: 10px;
    display: inline-flex; /* or flex if you need flexibility */
    width: auto;
    height: 36px;
    border-radius: 163px;
    border: 1px solid rgba(208, 213, 221, 1);
    padding: 6px 13px;
    gap: 10px; /* If you have inner items that need spacing */
    align-items: center; /* For inner alignment */
    justify-content: center; /* For inner alignment */
    color: rgba(38, 57, 68, 1); /* Optional, text color if needed */
    font-family: "Poppins", sans-serif; /* Optional, font-family if needed */
    font-weight: 600; /* Optional, font-weight if needed */
    font-size: 12px; /* Optional, font-size if needed */
    line-height: 27px; /* Optional, line-height if needed */
}

.property-details-container {
    border: 2px dotted #e2e4ea; /* Border for the detailed section */
    padding: 15px; /* Padding inside the border */
    border-radius: 4px; /* Optional: rounds the corners of the border */
}
.section-wrapper {
    position: relative;
    margin-top: 20px; /* Space between the heading and the bordered section */
}

.section-heading {
    position: absolute;
    top: -15px; /* Adjust this value to move the heading up */
    left: 15px; /* Optional: adjust this value to position the heading horizontally */
    background: #fff; /* Ensures the heading has a background color to cover the border */
    padding: 0 10px; /* Adds space around the text */
    z-index: 1; /* Ensures the heading appears above the border */
}

.section-container {
    border: 2px dotted #e2e4ea; /* Border for the detailed section */
    padding: 15px; /* Padding inside the border */
    border-radius: 4px; /* Optional: rounds the corners of the border */
}

.container {
    position: relative;
}
.plus-button {
    font-family: "Inter", sans-serif;
    font-weight: 600;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    width: 8%;
    text-align: center;
    border-radius: 10px;
    color: white;
    background-color: rgba(0, 116, 217, 1);
    border: none;
    cursor: pointer;
}

.panel_top {
    background: #ffffff;
    border-radius: 8px 8px 0 0;
    padding: 2.4rem 2.4rem 0 2.4rem;
}
.circle {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 50%;
}

.circle.top {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}
.circle.center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.circle.bottom {
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.panel_bottom {
    background: #ffffff;
    border-radius: 0 0 8px 8px;
    padding: 0 2.4rem 2.4rem 2.4rem;
    height: 100%;
}
.opportunity_estimates {
    border: 2px solid rgba(137, 137, 137, 0.12);
    padding: 10px;
    border-radius: 10px;
}

.opportunity_estimates_title {
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
}
.container-price {
    display: flex; /* Enable Flexbox */
    align-items: center; /* Vertically center items */
    width: 100%; /* Full width of the parent element */
}

/* Style for label text */

/* Style for division elements */
.division {
    margin-right: 10px;
    color: rgba(239, 160, 55, 1);
    background-color: rgb(251, 213, 164);
    border-radius: 10px;
    width: 60px;
    text-align: center;
}

/* Remove margin-right from the last division */
.division:last-of-type {
    margin-right: 0;
}

/* Style for price */
.price {
    margin-left: auto; /* Push price to the end of the container */
    padding-left: 20px; /* Optional: make price text bold */
    margin-right: 20px;
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 14px;
}
/* Flex container setup */
.container-image {
    display: flex; /* Enable Flexbox */
    align-items: center; /* Vertically center items */
    width: 100%; /* Full width of the parent element */
}

/* Style for image name */
.image {
    margin-right: 10px; /* Space between image name and download icon */
    font-size: 14px; /* Adjust as needed */
}

/* Style for download icon */
.download-icon {
    margin-left: auto;
    padding-left: 20px; /* Optional: make price text bold */
    margin-right: 20px; /* Push download icon to the end of the container */
}

.panel .panel_header,
.panel_top .panel_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.panel_header .view-request-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 6px 19px;
    border: 1px solid #868686;
    border-radius: 28px;
    font-family: "Cabin";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
    color: #192a3e;
}

.panel .panel-header {
    border-radius: 8px 8px 0px 0px;
}

.panel .panel-header .panel-heading {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-transform: capitalize;
    color: #192a3e;
}

.panel .panel-header.warning {
    background-color: #2ecc40;
}

.panel .panel-header.warning + .panel-body {
    background-color: #fffefd;
}

.panel .panel-header.primary {
    background-color: #dcf2ff;
}

.panel_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 2.4rem;
    line-height: 3.6rem;
    text-transform: capitalize;
    color: #003366;
    word-break: break-all;
}
.division_title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 600;
    font-size: 1.8rem;
    line-height: 3.6rem;
    text-transform: capitalize;
    color: #003366;
    word-break: break-all;
}
.addDivision {
    font-size: 10px;
    margin-top: 10px;
}

.panel_summery_header {
    display: flex;
    align-items: center;
    padding: 1.2rem 2.6rem;
    column-gap: 7.8rem;
    row-gap: 1.6rem;
    flex-wrap: wrap;
    background: #eaf5fb;
    border-radius: 8px;
}

.panel .panel_fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    column-gap: 1.6rem;
    row-gap: 2.4rem;
}

.panel .panel_fields.panel_invoice_fields {
    grid-template-columns: 2fr 1fr 1fr 1fr;
}

.panel_bottom .panel_fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    column-gap: 1.6rem;
    row-gap: 2.4rem;
}

.panel .panel_fields-two {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40rem, 1fr));
    column-gap: 1.6rem;
    row-gap: 2.4rem;
}

.panel .panel_fields .field,
.panel .panel_fields-two .field {
    display: flex;
    flex-direction: column;
    /* justify-content: space-between; */
    gap: 4px;
}

.detail_info .label {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 1.3rem;
    line-height: 2.4rem;
    color: #90a0b7;
    margin-bottom: 8px;
    word-break: break-all;
}
.custom-dropdown {
    width: 110px;
    height: 36px;
    border-radius: 8px;
    padding: 4px 16px;
    background-color: rgba(235, 161, 13, 0.376);
    color: rgb(216, 144, 0);
    border: 1px solid #ccc; /* Optional: to match the overall design */
}

.dropdown-wrapper {
    display: inline-block;
}

.centered-div {
    height: 150px;
    width: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px dotted #ccc;
    border-radius: 8px;
}
.upload-text {
    background-color: #007bff;
    padding: 10px 20px;
    color: white;
    border-radius: 8px;
    cursor: pointer;
}

.icon {
    font-size: 50px; /* Adjust the icon size as needed */
}
.contract-input {
    border: none; /* Remove all borders */
    border-bottom: 1px solid #ccc; /* Add a bottom border */
    border-radius: 0; /* Remove any rounded corners */
    box-shadow: none; /* Remove any shadow */
    outline: none; /* Remove outline on focus */
}

.contract-input:focus {
    border-bottom-color: #007bff; /* Optional: Change the color of the bottom border on focus */
}

.detail_info .text {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 1.3rem;
    line-height: 2.4rem;
    color: #192a3e;
    word-break: break-all;
}

.detail_info .text span {
    font-size: 14px;
    line-height: 2.4rem;
    color: #90a0b7;
}

.status {
    padding: 4px 9px;
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    border-radius: 8px;
    text-align: center;
    white-space: nowrap;
    width: fit-content;
    min-width: 96px;
}

.status.warning {
    background: #fff5eb;
    color: #efa037;
}

.status.danger {
    background: #ffecec;
    color: #ff4b55;
}

.status.success {
    background: #ecffec;
    color: #27b737;
}

.status.primary {
    background: rgba(49, 89, 254, 0.1);
    color: #3159fe;
}

.file-icon {
    font-size: 2.2rem;
}

.file-icon.warning {
    color: #e7e7e7;
}

.file-icon.success {
    color: #2ecc40;
}

.placeholder-text {
    color: #90a0b7;
    user-select: none;
}

/* Upload File Button */
.upload_file_wrapper {
    position: relative;
    width: fit-content;
}

.upload_file_wrapper .file_input {
    display: none;
}

.upload_file_wrapper .custom_error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 6px;
    text-align: center;
    margin: 0px;
    background-color: #ffffff;
    border: 2px solid #003366;

    display: flex;
    align-items: center;
    justify-content: center;
}

/* Upload File Button */

/* Upload Document */
.drop_zone_wrapper .uploaded__img__wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(117px, 1fr));
    gap: 3rem;
}

.drop_zone_wrapper .uploaded__img__wrapper .uploaded_image {
    aspect-ratio: 1/1;
    border: 1px solid #90a0b7;
    border-radius: 4px;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px 1.5rem;
    position: relative;
}

.drop_zone_wrapper .uploaded__img__wrapper .uploaded_image .main_image {
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: center;
}

.drop_zone_wrapper .uploaded__img__wrapper .uploaded_image .upload__img-close {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkuMDAwMzMgMC42NjY1MDRDNC4zOTE5OSAwLjY2NjUwNCAwLjY2Njk5MiA0LjM5MTUgMC42NjY5OTIgOC45OTk4NEMwLjY2Njk5MiAxMy42MDgyIDQuMzkxOTkgMTcuMzMzMiA5LjAwMDMzIDE3LjMzMzJDMTMuNjA4NyAxNy4zMzMyIDE3LjMzMzcgMTMuNjA4MiAxNy4zMzM3IDguOTk5ODRDMTcuMzMzNyA0LjM5MTUgMTMuNjA4NyAwLjY2NjUwNCA5LjAwMDMzIDAuNjY2NTA0Wk0xMy4xNjcgMTEuOTkxNUwxMS45OTIgMTMuMTY2NUw5LjAwMDMzIDEwLjE3NDhMNi4wMDg2NiAxMy4xNjY1TDQuODMzNjYgMTEuOTkxNUw3LjgyNTMzIDguOTk5ODRMNC44MzM2NiA2LjAwODE3TDYuMDA4NjYgNC44MzMxN0w5LjAwMDMzIDcuODI0ODRMMTEuOTkyIDQuODMzMTdMMTMuMTY3IDYuMDA4MTdMMTAuMTc1MyA4Ljk5OTg0TDEzLjE2NyAxMS45OTE1WiIgZmlsbD0iIzE5MkEzRSIvPgo8L3N2Zz4K");
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;

    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 1;
}

.drop_zone_wrapper .uploaded__img__wrapper .uploaded_image .file_name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 18px;
    color: #192a3e;
    text-align: center;
}

.upload_documents_wrapper {
    position: relative;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;

    min-height: 134px;
    border-radius: 4px;
    padding: 20px 12px 20px 12px;
    border: 1px dashed #90a0b7;
    border-width: 2px;
}

.upload_documents_wrapper .input_file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 1;
}

.upload_documents_wrapper .drop-zone--over {
    border-style: solid;
}

.upload_documents_wrapper .drop-zone__input {
    display: none;
}

.upload_documents_wrapper .drop-zone__thumb {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    background-color: #cccccc;
    background-size: cover;
    position: relative;
}

.upload_documents_wrapper .drop-zone__thumb::after {
    content: attr(data-label);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 5px 0;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.75);
    font-size: 14px;
    text-align: center;
}

.upload_documents_wrapper .drop-zone__thumb img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: center;
}

.upload_documents_wrapper .drop-zone__thumb .file-icon {
    height: 100px;
    margin-bottom: 30px;
}

/* Upload Document */

/* Dropzone Library Styles */
.dropzone_library_customize {
    min-height: 134px;
    border-radius: 4px;
    padding: 20px 12px 20px 12px;
    border: 1px dashed #90a0b7;
    border-width: 2px;
    margin-top: 4px;
}

.dropzone_library_customize .drop-zone__prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
}

.dropzone_library_customize .dz-preview {
    margin: 8px;
    min-height: 100px;
    border: 1px solid #90a0b7;
    border-radius: 4px;
    padding: 8px;
}

.dropzone_library_customize .dz-preview .dz-image {
    border-radius: 8px;
}

.dropzone_library_customize .dz-preview .dz-remove {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;

    display: flex;
    align-items: center;
    justify-content: center;

    padding: 4px 8px;

    background: #003366;
    border-radius: 6px;

    color: #ffffff;
    text-decoration: none;

    margin-top: 8px;
}

.dropzone .dz-preview .dz-image img {
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: center;
}

.dropzone .dz-preview .dz-details {
    opacity: 0 !important;
    transition: 0.3s ease;
}

.dropzone .dz-preview:hover .dz-details {
    opacity: 1 !important;
}

/* Dropzone Library Styles */

/* Stepper Radio Tabs */
.stepper_radio_tabs {
    display: flex;
    align-items: center;
    gap: 1rem;
    min-height: auto !important;
    border: none !important;
}

.stepper_radio_tabs .radio_tab_btn {
    background-color: #ffffff;
    border: 1px solid #90a0b7;
    border-bottom: none;
    border-radius: 6px 6px 0px 0px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: #90a0b7;
    white-space: nowrap;
    min-width: fit-content !important;
    padding: 4px 12px;
    box-shadow: none !important;
}

.stepper_radio_tabs .btn-check:checked + .radio_tab_btn,
.stepper_radio_tabs .radio_tab_btn.active {
    background-color: #003366 !important;
    border-color: #003366 !important;
    color: #ffffff !important;
}

/* Stepper Radio Tabs */

table {
    width: 100% !important;
}

/* Operation Modal */
@media (min-width: 576px) {
    .m400 {
        max-width: 400px !important;
        margin: 1.75rem auto;
    }

    .modal-md {
        max-width: 738px;
        margin: 1.75rem auto;
    }

    #filePreviewModal .pdf-preview {
        max-width: 80vw !important;
    }

    #filePreviewModal .pdf-preview button {
        max-width: 200px;
    }
}

.operation_modal .modal-content {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}

.operation_modal .modal-header {
    height: 54px;
    background: #e1f4ff;
    padding: 1.6rem 2.4rem;
    border: none;
}

.operation_modal .modal-header .modal-title {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    color: #003366;
}

.operation_modal .modal-header .btn-close {
    font-size: 12px;
}

.operation_modal .modal-body {
    padding: 1.6rem 2.4rem;
}

.operation_modal .modal-footer {
    border: none;
    padding: 1.6rem 3.3rem;
}

.operation_modal .modal-content {
    border: none;
}

.radio_group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.radio_group input[type="radio"] {
    /* accent-color: #FE9B6A; */
    height: 2.1rem;
    width: 2.1rem;
    outline: 1px solid #a2acbd;
    outline-offset: -2px;
    border-radius: 50%;

    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;

    position: relative;
}

.radio_group input[type="radio"]:checked {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTAuNSIgY3k9IjEwLjUiIHI9IjEwIiBzdHJva2U9IiNGRTlCNkEiLz4KPGNpcmNsZSBjeD0iMTAuNSIgY3k9IjEwLjUiIHI9IjcuNSIgZmlsbD0iI0ZFOUI2QSIvPgo8L3N2Zz4K");
    background-size: 100%;
    outline: none;
}

.radio_group label {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    color: #3e4756;
}

.primary_radio_group input[type="radio"] {
    height: 2rem;
    width: 2rem;
}

/* Operation Modal */

/* Setup Wizard */
.setup_wizard_stepper .toolbar-bottom {
    display: none;
}

.setup_wizard_stepper .nav_wrapper {
    overflow-x: auto;
}

.setup_wizard_stepper .nav {
    min-height: 7rem;
    padding: 1.6rem 2.4rem;
    border-bottom: 1px solid #dbdde0;
    min-width: 1136px;
    /* padding-bottom: 20px; */
}

.setup_wizard_stepper .nav .nav-item {
    flex-basis: 0;
    flex-grow: 1;
}

.setup_wizard_stepper .nav .nav-link {
    display: grid;
    grid-template-columns: 38px auto 1fr;
    gap: 11px;
    align-items: center;
}
.nav-item.active .nav-link {
    color: rgb(0, 63, 122);
}

.setup_wizard_stepper .nav .nav-link .separator {
    background: #90a0b7;
    border-radius: 50px;
    height: 3px;
}

.setup_wizard_stepper .nav .nav-link .num {
    height: 30px;
    width: 30px;
    border-radius: 50%;
    outline: 2px solid #90a0b7;
    outline-offset: 3px;

    /* font-family: "Roboto"; */
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 18px;
    color: #90a0b7;

    position: relative;

    display: flex;
    align-items: center;
    justify-content: center;
}

.setup_wizard_stepper .nav .nav-link .title {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-transform: capitalize;
    color: #90a0b7;
}

.setup_wizard_stepper .nav .nav-link.active .num {
    outline: 2px solid #2ecc40;
    background-color: #2ecc40;
    outline-offset: 3px;
    color: #ffffff;
}

.setup_wizard_stepper .nav .nav-link.active .title {
    color: #2ecc40;
}

.setup_wizard_stepper .nav .nav-link.active .separator {
    background-color: #2ecc40;
}

.setup_wizard_stepper .nav .nav-link.done .num::before {
    content: "\2713";
    height: calc(100% + 2px);
    width: calc(100% + 2px);
    position: absolute;
    top: -1px;
    left: -1px;
    border-radius: 50%;
    z-index: 1;
    background-color: #003366;
    z-index: 1;

    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
}

.setup_wizard_stepper .nav .nav-link.done .title {
    color: #003366;
}

.setup_wizard_stepper .nav .nav-link.done .num {
    outline: 5px solid #003366;
    /* background-color: #003366; */
    outline-offset: 0px;
    color: #ffffff;
}

.setup_wizard_stepper .nav .nav-link.done .separator {
    background-color: #003366;
}

/* Setup Wizard */

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 3px;
    /* max-width: calc(100vw - 305px); */
}

.table-striped > tbody > tr:nth-of-type(even) {
    background-color: #f9f9f9 !important;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #ffffff !important;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    --bs-table-accent-bg: #ffffff !important;
    color: var(--bs-table-striped-color);
}

.setup_wizard_stepper .nav_wrapper {
    overflow-x: auto;
    min-width: 100%;
}

.setup_wizard_stepper .tab-content {
    height: fit-content !important;
}

.wizard_buttons .wizard_back.disabled {
    display: none;
}

#wizard_generate_btn {
    display: none;
}

.file_download_wrapper {
    padding: 12px;
    border: 1px solid #d9dbe9;
    border-radius: 4px;

    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.file_download_wrapper .info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.file_download_wrapper .info .text {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #343434;
}

/* Laravel Pagination */
.laravel_pagination .pagination {
    display: flex;
    align-items: center;
    gap: 4px;
}

.laravel_pagination .pagination .page-item .page-link {
    padding: 5px 12px;

    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #90a0b7;

    border: none;
    border-radius: 2px;
    background-color: #f1f1f1;

    height: 32px;
    min-width: 32px;
}

.laravel_pagination .pagination .page-item.active .page-link {
    background-color: #003366;
    color: #ffffff;
}

.laravel_pagination .pagination .page-item:first-child .page-link,
.laravel_pagination .pagination .page-item:last-child .page-link {
    font-size: 23px;
}

/* Laravel Pagination Ends */

/* Subscription Plans */
.plan_cards .plan_card {
    border-radius: 6px;
    padding: 12px 2rem;
    max-width: 350.43px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    margin-left: auto;
}

.plan_cards .plan_card .plan_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.plan_cards .plan_card .plan_header .heading {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
}

.plan_cards .plan_card .para {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
}

.plan_cards .plan_card .pricing {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    opacity: 0.96;
}

.plan_cards .plan_card .pricing span {
    font-weight: 400;
    font-size: 14.4864px;
    line-height: 22px;
    opacity: 0.8;
}

.plan_cards .plan_card.basic {
    background: #181059;
}

.plan_cards .plan_card.basic .heading,
.plan_cards .plan_card.basic .para,
.plan_cards .plan_card.basic .pricing,
.plan_cards .plan_card.basic .pricing span {
    color: #ffffff;
}

.plan_cards .plan_card.pro {
    background: #e7eef5;
}

.plan_cards .plan_card.pro .heading,
.plan_cards .plan_card.pro .pricing,
.plan_cards .plan_card.pro .pricing span {
    color: #003366;
}

.plan_cards .plan_card.pro .para {
    color: #707683;
}

.btn-warning {
    background: #fe9b6a;
    border-radius: 6px;
    padding: 6px 3.9rem;

    font-family: "Poppins";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
    text-align: center;
    color: #ffffff;
}

/* Subscription Plans */

/* Summernote Editor */
.wycwyg_editor + .note-frame {
    background: #ffffff;
    border: 1px solid #cdd2e1;
    border-radius: 8px;
}

.wycwyg_editor + .note-frame .note-toolbar {
    padding: 0px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0px;
    background-color: #fbfbfb;
    /* overflow: hidden; */
}

.wycwyg_editor + .note-frame .note-toolbar .note-btn-group {
    margin: 0px;
    padding: 0px;
}

.wycwyg_editor + .note-frame .note-toolbar .note-btn-group .note-btn {
    border: none;
    border-radius: 4px;
    background-color: #fbfbfb;
}

/* Summernote Editor */

/* Accordian General Styles */
.general_accordian .accordion-item {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
}

.general_accordian .accordion-item .accordion-button {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    text-transform: capitalize;
    color: #192a3e;
    padding: 1.6rem;
    background-color: #ffffff;
    box-shadow: none;
}

.general_accordian .accordion-button:not(.collapsed) {
    box-shadow: none;
    border-bottom: 1px solid #e7e7e7;
}

.shadow-none {
    box-shadow: none !important;
}

.general_accordian .accordion-button::after {
    border: 2px solid #90a0b7;
    border-radius: 6px;
    height: 26px;
    width: 40px;
    padding: 8px 16px 8px 16px;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxOCAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNy4xOTI0IDkuMTY2NDlDMTcuMTkyNCA4LjcyMTA2IDE3LjAxOSA4LjI3NzMyIDE2LjY3MzkgNy45NDQxTDkuODc0MDcgMS4zNzY1QzkuMjA1OTggMC43MzM4NDYgOC4xNDY5MSAwLjc0MjM0NiA3LjQ5MDczIDEuMzk4NkwwLjY5MDg5NSA4LjE5OTEyQzAuMDI2MjExNiA4Ljg2MjE3IDAuMDI2MjExNiA5LjkzODM1IDAuNjkwODk1IDEwLjYwMzFDMS4zNTU1OCAxMS4yNjYyIDIuNDI5OTUgMTEuMjY2MiAzLjA5NDYzIDEwLjYwMzFMOC43MTI5OSA0Ljk4NDE3TDE0LjMxMSAxMC4zODg5QzE0Ljk4NzUgMTEuMDQxNyAxNi4wNjM2IDExLjAyMyAxNi43MTQ3IDEwLjM0ODFDMTcuMDM0MyAxMC4wMTY2IDE3LjE5MjQgOS41OTE1MiAxNy4xOTI0IDkuMTY2NDlaIiBmaWxsPSIjOTBBMEI3Ii8+Cjwvc3ZnPgo=");
    background-position: center;
}

.general_accordian .table {
    border: 1px solid #e7e7e7;
}

.total_payable .text {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #192a3e;
}

.total_payable .value {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 500;
    font-size: 3.2rem;
    line-height: 4rem;
    color: #344054;
}

/* Accordian General Styles */

/* Profile Image and Name */
.table_profile {
    display: flex;
    align-items: center;
    gap: 8px;
}

.table_profile .image {
    border-radius: 50%;
    height: 24px;
    width: 24px;
}

.table_profile .profile_name {
    font-family: "Cabin";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #192a3e;
    word-break: break-word;
    max-width: 150px;
}

.active_users_indication {
    padding: 7px 8px;
    border: 1px solid #90a0b7;
    border-radius: 8px;

    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.active_users_indication .title {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #90a0b7;
}

.active_users_indication .active_count {
    padding: 2px 13px;
    background: rgba(0, 51, 102, 0.22);
    border-radius: 8px;

    font-family: "Poppins";
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #192a3e;
}
.company-client_tabs .nav-item.active .nav-link {
    color: rgb(4, 133, 255);
}
.margin_filter_fields {
    display: grid;
    grid-template-columns: minmax(0, 208px) 1fr auto;
    gap: 16px;
}
.dropdown-menu-lg-scrollable {
    max-height: 200px; /* Adjust the desired maximum height */
    overflow-y: auto;
    scrollbar-width: thin; /* Customize scrollbar width */
    scrollbar-color: #666 #ccc; /* Set scrollbar colors */
}

.company_margins_setup_table .margin_headings,
.company_margins_setup_table .margin_items_grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

.company_margins_setup_table .margin_items_grid .margin_items .margin_item,
.company_margins_setup_table .margin_headings .heading {
    /* height: 56px; */
    padding: 1.7rem 2.4rem;
}

.company_margins_setup_table tbody td,
.company_margins_setup_table thead th {
    padding: 0px !important;
}

.company_margins_setup_table .margin_items_grid {
    max-height: 400px;
    overflow-y: auto;
}

.company_margins_setup_table
    .margin_items_grid
    .margin_items
    .margin_item:nth-child(even) {
    background: #f9f9f9;
}

.company_margins_setup_table .margin_items_grid .margin_items .margin_item {
    display: grid;
    grid-template-columns: 1fr 15px;
    align-items: center;
    gap: 11px;
    min-width: 250px;
}

@media screen and (max-width: 1200px) {
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        /* max-width: 100vw; */
    }
}

@media screen and (max-width: 991px) {
    .table-responsive-lg {
        width: 100vw;
    }

    .request-wraper {
        grid-template-columns: 1fr;
        /* grid-template-areas:
            "item1"
            "item2"
            "item3"
            "btns"; */
    }

    .table_filter_header {
        flex-direction: column;
        align-items: baseline;
        gap: 20px;
    }
}

@media screen and (max-width: 768px) {
    .margin_filter_fields {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 520px) {
    .panel .panel_fields {
        grid-template-columns: repeat(auto-fill, minmax(27rem, 1fr));
    }

    .panel .panel_fields-two {
        grid-template-columns: repeat(auto-fill, minmax(27rem, 1fr));
    }

    .table_filter_header .header-buttpns-main {
        display: grid;
        grid-template-columns: auto auto;
    }
}

@media screen and (max-width: 475px) {
    .estimate-detail-box .estimate-top-header {
        flex-direction: column;
        align-items: baseline;
        gap: 15px;
    }
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 519px !important;
    }

    .propert-modal .modal-dialog {
        max-width: 459px !important;
    }

    .propert-modal.select-client .modal-dialog {
        max-width: 825px !important;
    }
}

input#discount[type="number"]::-webkit-inner-spin-button,
input#discount[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
}

.absolute-error .select2-container,
.absolute-error .select2-container--default .select2-selection--single {
    height: 82%;
}
.propert-modal .modal-content {
    border-radius: 8px;
    padding: 20px;
}

.propert-modal .modal-header {
    border-bottom: none;
}

.propert-modal .modal-title {
    font-weight: bold;
}

.propert-modal .modal-body p.text-muted {
    margin-bottom: 20px;
}

.propert-modal .search-filter input {
    border-radius: 4px;
    padding: 8px 12px;
}

.propert-modal .property-item {
    border-bottom: 1px solid #e9ecef;
}

.propert-modal .property-item:last-child {
    border-bottom: none;
}

.propert-modal .modal-footer {
    border-top: none;
}

.propert-modal .modal-footer .add-btn {
    min-width: 100px;
    padding: 8px 16px;
    text-align: center;
    border-radius: 4px;
}

.propert-modal .modal-footer .btn-secondary {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #ced4da;
}

.propert-modal .modal-footer .btn-primary {
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
}

.property-item {
    margin-bottom: 10px;
}
.select2-container--default .select2-selection--multiple {
    border: 1px solid var(--bordercolor);
}

span.custom-error {
    position: absolute;
    bottom: -25px;
}

.select2-container--default
    .select2-selection--single
    .select2-selection__rendered {
    height: 38px;
    display: flex;
    align-items: center;
}

/* Job Summary Page Style Starts */

.risk-department {
    display: grid;
    grid-template-columns: 1fr 362px;
    gap: 2rem;
}

.bar-chart {
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(36, 185, 236, 0.08);
    padding: 17px 24px;
}

.dughnut-chart {
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(36, 185, 236, 0.08);
    padding: 17px 24px;
}

.dughnut-chart canvas {
    object-fit: contain;
}

.dughnut-chart .dughnu-value {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-family: "Cabin";
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 1px;
}

.bar-chart .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding-bottom: 21px;
}

.bar-chart .chart-header .heat-map-heading {
    border-bottom: none;
    padding: 0 !important;
}

.bar-chart .chart-header .form-select {
    width: fit-content;
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-size: 1.2rem;
    line-height: 1.6rem;
    color: #615e83;
}

.dughnut-chart {
    background: #ffffff;
    box-shadow: 0px 4px 20px rgba(13, 10, 44, 0.08);
    border-radius: 6px;
}

.dughnut-chart canvas {
    object-fit: contain;
}

.heat-map-heading {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 500;
    font-size: 1.6rem;
    line-height: 2.4rem;
    color: #1b2337;
    /* border-bottom: 1px solid #E5E5EF; */
    padding: 1rem;
    margin: 0 !important;
}

.legends {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0;
}

.legends.guages-ul {
    display: flex;
    align-items: center;
    gap: 0.9rem;
    margin-top: -3rem;
    flex-wrap: wrap;
    padding-left: 1.2rem;
    padding-right: 1.2rem;
    padding-bottom: 1.6rem;
}

.dughnut-legends {
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-content: space-between;
    column-gap: 2rem;
    row-gap: 1.2rem;
    padding-left: 1.6rem;
    padding-right: 1.6rem;
    margin-top: 5rem;
}

.dughnut-legends .dughnut-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 3.4rem;
}

.dughnut-legends .dughnut-list .dughnut-value {
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

.dughnut-legends .dughnut-list .dughnut-value .box {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    /* background-color: red; */
}

.risk-department .chart-header .legends .legend-list .legend-span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    /* background-color: red; */
    display: block;
}

.dughnut-legends .dughnut-list .dughnut-value .text,
.dughnut-legends .dughnut-list .percentage,
.risk-department .legends .legend-list .legend-button {
    color: #192a3e;
    font-family: Cabin;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    cursor: pointer;
}

.date-selector {
    padding: 10px 35px 10px 10px;
    border-radius: 8px;
    background: #f8f8f8;
    background-image: url(../images/icons/akar-icons-chevron-down.svg);
    background-position: center right 10px;
    background-repeat: no-repeat;
}

.date-selector select {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    appearance: none;
    color: #192a3e;
    font-family: Cabin;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.12px;
}

.date-selector select option {
    color: #192a3e;
    font-family: Cabin;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.12px;
}

.risk-department .backlog {
    color: #868686;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: capitalize;
}

.risk-department .price {
    color: #036;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
}

.risk-department .basic-single-select + .select2 .select2-selection {
    background: #f8f8f8;
    border: none;
}

table.job-summary thead tr th:first-child,
table.job-summary tbody tr td:first-child {
    text-align: left;
}

table.job-summary thead tr:first-child th:first-child,
table.job-summary thead tr:first-child th:last-child,
table.job-summary tbody tr:last-child td:first-child,
table.job-summary tbody tr:last-child td:last-child {
    border-radius: 0;
}

.backog-container {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 12px 0px rgba(36, 185, 236, 0.08);
    padding-top: 15px;
    padding-bottom: 25px;
    padding-left: 17px;
    padding-right: 17px;
    height: fit-content;
}

.backog-container .backlog-heading {
    color: #036;
    font-family: "Cabin";
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-transform: capitalize;
}

.backog-container .backlog-description {
    color: #868686;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
}

.backog-container .backlog-project {
    color: #192a3e;
    font-family: "Cabin";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}

.custom_datatable.job-summary {
    box-shadow: none;
}

table.field-table {
    box-shadow: none;
}

table.field-table thead tr th,
table.field-table tbody tr td {
    border-top: 1px solid #e7e7e7 !important;
    border-left: 1px solid #e7e7e7 !important;
    border-radius: 0 !important;
}

table.field-table thead tr th:last-child,
table.field-table tbody tr td:last-child {
    border-right: 1px solid #e7e7e7 !important;
}

table.field-table tbody tr:last-child td {
    border-bottom: 1px solid #e7e7e7 !important;
}

table.field-table thead tr th.bg-lightgreen {
    background: #c8f1b5;
}

table.field-table tbody tr td .table-field {
    max-width: 130px;
    outline: none;
    padding: 5px 10px;
    border-radius: 6px;
    border: 1px solid gray;
    background-color: transparent;
    font-family: Cabin;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

table.field-table tbody tr td .table-field::placeholder {
    color: #c2c2c2;
}

.horizontalScroll::-webkit-scrollbar {
    height: 6px;
    /* Width of the scrollbar */
}

/* Styling the scrollbar thumb (the draggable handle) */
.horizontalScroll::-webkit-scrollbar-thumb {
    background-color: rgb(52, 54, 59);
    /* Color of the thumb */
    border-radius: 6px;
    /* Rounded corners */
}

/* Styling the scrollbar track when hovering over it */
.horizontalScroll::-webkit-scrollbar-track {
    background-color: #e5e5e5;
    /* Color of the track on hover */
}

/* Styling the scrollbar thumb when hovering over it */
.horizontalScroll::-webkit-scrollbar-thumb:hover {
    background-color: #243875;
    /* Color of the thumb on hover */
}

/* Job Summary Page Style Ends */

#viewNoteModal .invoice-1-content ul li {
    list-style: initial;
}

/* Error pages Style */

.error-wraper {
    min-height: calc(100vh - 115px);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-container {
    max-width: 995px;
    margin: auto auto;
    width: 100%;
    height: fit-content;
    margin: auto auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.error-heading {
    font-family: "Cabin";
    font-size: 130px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    color: #fe9b6a;
    text-align: center;
    margin: 0;
}

.error-subheading {
    font-family: "Cabin";
    font-size: 35px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    color: #036;
    margin-top: 16px;
    margin-bottom: 0;
    text-align: center;
}

.error-description {
    font-family: "Cabin";
    font-size: 25px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #868686;
    margin-top: 22px;
    margin-bottom: 0;
    text-align: center;
}

.error-btn {
    margin-top: 40px;
    height: 40px;
    width: fit-content;
    border-radius: 6px;
    padding: 3px 20px;
    font-family: Cabin;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    border: 1.5px solid #003366;
    background-color: #003366;
    text-decoration: none;
}

.error-btn:hover {
    background-color: #012951;
}

/* sheraz code */
.buttonss {
    width: Fill (242px) px;
    height: Hug (60px) px;
    padding: 8px 8px;
    gap: 10px;
    border-radius: 37px;
    opacity: 0px;

    background: #f4f7fa;
}
.allbtn {
    border-radius: 20px;
}
.favtbtn {
    border-radius: 20px;
    border: 2px solid #7c8091;
    color: #7c8091;
}
.input-group-append2 {
    background-color: white;
    border-top: 1px solid #ced4da;
    border-right: 1px solid #ced4da;
    border-bottom: 1px solid #ced4da;
}
.cursor-pointer {
    cursor: pointer !important;
}
.section-parent {
    border: 1px solid #B0D4F3;
    border-width: 1px 1px 0 1px;
}
.section-child {
    border: 1px solid #B0D4F3;
    border-width: 0 1px 1px 1px;
}
.lastTypeSection:last-of-type {
    margin-bottom: 10px;
}
.fs-18 {
    font-size: 18px !important;
}
.fs-16 {
    font-size: 16px !important;
}
.fs-14 {
    font-size: 14px !important;
}
.fs-12 {
    font-size: 12px !important;
}
.submenu-active a{
    color: #0074D9 !important;
    font-weight: 600 !important;
}

.custom-model-header {
    background-color: #E1F4FF;
}
.custom-model-header-title{
    color: #003366;
}
.opacity-1 {
    opacity: 1 !important;
}
.changeAccountOwner .model-sm {
    max-width: 400px !important;
}
.modal-custom-search {
    border-radius: 6px !important;
    height: 40px;
    border: 1px solid #AOA3BD;
    color: #AOA3BD;
}
.custom-modal-body .table-item td{
    border: 1px solid #dee2e6 !important;
}
.custom-modal-body .table-item label{
    font-size: 14px !important;
    color: #003366;
    margin-top: 3px;
}
.custom-modal-body .table-item input{
    margin-top: 9px;
    margin-right: 10px;
}
.custom-modal-body .table-item:first-child td:first-child{
    border-radius: 6px 6px 0 0;
}
.custom-modal-body .table-item:last-child td:last-child{
    border-radius: 0 0 6px 6px;
}
.custom-modal-body .custom-footer button {
    height: 40px;
}
.dropdown-item {
    font-size: 14px !important;
}

/* File Upload Modal Styles */
.file-upload-modal .modal-dialog {
    max-width: 500px;
}

.file-upload-modal .modal-content {
    border-radius: 12px;
    border: none;
    overflow: hidden;
}

.file-upload-modal .modal-header {
    /*background-color: #f8f9fa;*/
    border-bottom: none;
    padding: 20px 24px 16px 24px;
}

.file-upload-modal .modal-title {
    font-weight: 600;
    font-size: 18px;
    color: #1f2937;
    margin: 0;
}

.file-upload-modal .close, .file-upload-modal .close:hover {
    background: none;
    border: none;
    font-size: 50px;
    color: #A4A7AE !important;
    padding: 0;
    margin: 0;
    opacity: 1 !important;
    font-weight: 400 !important;
}

.file-upload-modal .modal-body {
    padding: 20px 24px;
}

.file-upload-modal .upload-description {
    color: #535862;
    font-size: 13px;
    margin-bottom: 0;
}

.file-upload-modal .upload-area {
    border: 2px solid #3b82f6;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    background-color: #f9fafb;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-upload-modal .upload-area:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/*.file-upload-modal .upload-icon {*/
/*    margin-bottom: 12px;*/
/*}*/

.file-upload-modal .upload-icon svg {
    margin: 0 auto;
    display: block;
}

.file-upload-modal .upload-trigger {
    color: #3b82f6;
    font-weight: 500;
    cursor: pointer;
}

.file-upload-modal .upload-drag-text {
    color: #535862;
}

.file-upload-modal .upload-hint {
    color: #535862;
    font-size: 12px;
}

/*.file-upload-modal .file-list {*/
/*    max-height: 200px;*/
/*    overflow-y: auto;*/
/*}*/

.file-upload-modal .file-item {
    display: flex;
    align-items: center;
    padding: 12px;
    /*background-color: #f9fafb;*/
    border: 1px solid #E9EABE;
    border-radius: 6px;
    margin-bottom: 8px;
}

.file-upload-modal .file-icon {
    width: 32px;
    height: 32px;
    background-color: #10b981;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.file-upload-modal .file-icon span {
    color: white;
    font-weight: bold;
    font-size: 10px;
    text-transform: uppercase;
}

.file-upload-modal .file-info {
    flex: 1;
    position: relative;
    background-color: transparent;
}

.file-upload-modal .file-name {
    font-weight: 500;
    color: #414651;
    font-size: 14px;
    margin-bottom: 2px;
}

.file-upload-modal .file-size {
    color: #414651;
    font-size: 12px;
}

/*.file-upload-modal .progress-container {*/
/*    margin-top: 4px;*/
/*}*/

.file-upload-modal .progress {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.file-upload-modal .progress-bar {
    width: 100%;
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease;
}

.file-upload-modal .progress-text {
    font-size: 13px;
    color: #414651;
    position: relative;
    bottom: 8px;
}

.file-upload-modal .remove-file {
    background: none;
    border: none;
    /*color: #9ca3af;*/
    cursor: pointer;
    padding: 4px;
    /*margin-top: -49px;*/
    position: relative;
    bottom: 8px;
}

.file-upload-modal .modal-footer {
    /*background-color: #f8f9fa;*/
    border-top: none;
    padding: 16px 24px;
}

.file-upload-modal .btn-cancel {
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 8px;
}

.file-upload-modal .btn-attach {
    background-color: #3b82f6;
    border: 1px solid #3b82f6;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
}

.file-upload-modal .btn-attach:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.file-upload-modal .modal-error {
    margin-top: 12px;
    padding: 8px 12px;
    font-size: 14px;
}
.file-upload-modal .upload-text {
    background-color: transparent;
    padding: 2px;
}
