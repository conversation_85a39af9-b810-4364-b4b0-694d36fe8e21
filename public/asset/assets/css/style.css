/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100&display=swap'); */

@import url('https://fonts.googleapis.com/css2?family=Cabin:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

*{

    margin: 0; padding: 0;
    box-sizing: border-box;
    text-decoration: none;
    outline: none;
    list-style: none;
    /* font-family: 'Inter', 'poppines'; */
    font-family: 'Cabin', 'Arial';
    font-style: normal;
    color: #2A2A2A;
}
:root{
    --timeline-width: 225px;
}
a,h1,h2,h3,h4,h5,p{
    margin: 0;
    padding: 0;
}
a{
    text-decoration: none;
    cursor: pointer;
}
*::selection{
    background: #000000;
    color: #ffffff;
}
html{
    font-size: 62.5%;
    scroll-padding-top:5.5rem;
    scroll-behavior: smooth;
}
html::-webkit-scrollbar{
    width: 1.3rem;
}
html::-webkit-scrollbar-track{
    background: transparent;
}
html::-webkit-scrollbar-thumb{
    background: #8f8787;

}
.container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 1300px !important;

}

.pt-0{
    padding-top: 0rem;
}
.pt-16{
    padding-top: 1.6rem !important;
}
/*Cusom Navbar*/

.main-nav{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    transition: all .3s linear;
    z-index: 10000;
}
/* .main-nav.bgchange{
    background: rgb(198, 210, 212);
    transition: all .3s linear;
} */
.main-nav .navbar{
    height: 10rem;
    flex-wrap: nowrap;
    position: unset;
    transition: all .3s linear;
}
.pt-10{
    padding-top: 10rem;
}
.main-nav .nav-items{
    display: flex;
    align-items: center;
    gap: 10px;
}
.nav-items a{
    font-family: Cabin;
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 3.4rem;
    letter-spacing: 0em;
    text-align: left;
    color: #707683;

}
.main-nav #nav-toggler{
    display: none;
}
.hero-section{
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #F6FBFF 13.55%);
    min-height: calc(100vh - 10rem);
    margin-top: 10.1rem;
    /* padding-bottom: 12.9rem; */
    /* background-image: url(../images/our-services-bg.png); */
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}
.log-btn{
    width: 11.4rem;
    height: 4.4rem;
    /* background-color: #003366; */
    background-color: #0074D9;
    border-radius: 10rem;
    text-align: center !important;
    color: #FFFFFF !important;
}
.sig-btn{
    /* color: #FE9B6A !important; */
    color: #0074D9 !important;
}
.hero-title-main{
    max-width: 85rem;
    margin: 0 auto;
    text-align: center;
}
.hero-title-main h1{
    font-family: Cabin;
    font-size: 5rem;
    font-weight: 600;
    line-height: 7.5rem;
    letter-spacing: 0em;
    text-align: center;
    /* color: #003366; */
    color: #0074D9;
}
.hero-title-main span{
    /* color: #FE9B6A !important; */
    color: #2ECC40 !important;
}
.hero-title-main p{
    font-family: Cabin;
    font-size: 2rem;
    font-weight: 400;
    line-height: 3rem;
    letter-spacing: 0em;
    text-align: center;
    color: #707683;
    padding-top: 2.4rem;
}
.get-st-btn{
    width: 20.3rem;
    height: 5.8rem;
    background-color: #0074D9;
    border-radius: 10rem;
    font-family: Cabin;
    font-size: 2rem;
    font-weight: 600;
    line-height: 3rem;
    letter-spacing: 0em;
    text-align: center;
    color: #FFFFFF;
    margin-top: 5rem;
    margin-bottom: 2rem;
    border: none;

}
/* dashboard-landing-bg1-section */
.dashboard-landing-bg2{
    width: 100%;
    padding-top: 12.9rem;
    background-color: #FFFFFF;
    position: relative;
    z-index: 1000;
}
.dashboard-landing-bg2::after{
    position: absolute;
    content: '';
    background-image: url(../images/pink-slide.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 568px;
    width: 368px;
    top: 0;
    right: 0;
}
.dashboard-landing-bg2-main{
    display: flex;
    gap: 3rem;
    text-align: center;
    align-items: center;
}
.dashboard-landing-bg1-main img{
    width: 100%;
    margin-bottom: -5px;
}
.landing-bg2-txt h2{
    font-family: Cabin;
    font-size: 3rem;
    font-weight: 600;
    line-height: 4.5rem;
    letter-spacing: 0em;
    text-align: left;
    color: #0074D9;
}
.landing-bg2-txt p{
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 400;
    line-height: 2.4rem;
    letter-spacing: 0em;
    text-align: left;
    color: #707683;
    padding-top: 2rem;
}
.landing-bg2-image{
    z-index: 1;
}
.landing-bg2-image img{
    width: 100%;
}
.landing-bg2-txt{
    z-index: 1;
    max-width: 52rem;
}
/* dashboard-landing-bg3-section */
.dashboard-landing-bg3{
    width: 100%;
    padding-top: 23.1rem;
    padding-bottom: 26rem;
    position: relative;
}
.dashboard-landing-bg3::after{
    position: absolute;
    content: '';
    background-image: url(../images/choco-slide.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 568px;
    width: 368px;
    top: 7rem;
    left: 0;
}
.dashboard-landing-bg3-main{
    display: flex;
    gap: 3rem;
    text-align: center;
    align-items: center;
}
.landing-bg3-txt h2{
    font-family: Cabin;
    font-size: 3rem;
    font-weight: 600;
    line-height: 4.5rem;
    letter-spacing: 0em;
    text-align: left;
    color: #0074D9;
}
.landing-bg3-txt p{
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 400;
    line-height: 2.4rem;
    letter-spacing: 0em;
    text-align: left;
    color: #707683;
    padding-top: 2rem;
}
.landing-bg3-image{
    z-index: 1;
}
.landing-bg3-image img{
    width: 100%;
}
.landing-bg3-txt{
    z-index: 1;
    max-width: 52rem;
}
/* dashboard-landing-best-option-section */
.dashboard-landing-best-option{
    padding-bottom: 6rem;
}
.best-option-main-title h2{
    font-family: Cabin;
    font-size: 3rem;
    font-weight: 600;
    line-height: 4.5rem;
    letter-spacing: 0em;
    text-align: center;
    color: #0074D9;
}
.best-option-card1{
    max-width: 41rem;
    min-height: 45.6rem;
    box-shadow: 0px 0px 16px 0px #00000014;
    background: #FFFFFF;
    border-top: 6px solid #0074D9;
    border-radius: 8px;
    padding: 24px 29px 24px 32px;
}
.best-option-card1 p{
    font-family: Cabin;
    font-size: 4.8rem;
    font-weight: 600;
    line-height: 60px;
    letter-spacing: 0em;
    text-align: center;
    color: #192A3E;
}
.best-option-card1 span{
    font-family: Cabin;
    font-size: 2.4rem;
    font-weight: 500;
    line-height: 6rem;
    letter-spacing: -0.02em;
    text-align: center;
    color: #192A3E;
}
.best-option-cards{
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    justify-content: center;
    padding-top: 8rem;
    padding-bottom: 6rem;
}
.best-option-cards h2{
    font-family: Cabin;
    font-size: 2rem;
    font-weight: 600;
    line-height: 3rem;
    letter-spacing: 0em;
    text-align: center;
    color: #192A3E;
    padding-top: 1.6rem;
}
.opt-card-circle-main{
    display: flex;
    /* align-items: center; */
    gap: 1.2rem;
    padding-top: 4.8rem;
}
.opt-card-circle{
    width: 2.4rem;
    height: 2.4rem;
    background: #ECF5FF;
    border-radius: 1.2rem;
    text-align: center;
    display: flex;
    justify-content: center;

}
.opt-card-circle img{
    object-fit: none;
}
.opt-card-circle-txt p{
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 400;
    line-height: 2.4rem;
    letter-spacing: 0em;
    text-align: left;
    color: #667085;
}
.opt-card-demo-btn{
    width: 100%;
    height: 4.8rem;
    background: #0074D9;
    border: none;
    border-radius: 8px;
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 2.4rem;
    letter-spacing: 0em;
    text-align: center;
    color: #FFFFFF;
    margin-top: 3rem;
}
.toop{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100% !important;
}
/* landing-footer-section */
.landing-footer{
    width: 100%;
    min-height: 15.7rem;
    background: #FAFAFA;
    padding-top: 2.4rem;
    padding-bottom: 2.4rem;
}
.footer-main{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 2rem;
    align-items: center;
    border-bottom: 1px solid #D1D1D1;
    height: 9rem;
}
.footer-txt{
    display: flex;
    gap: 5.7rem;
    padding-bottom: 1rem;
}
.footer-txt a{
    font-family: Cabin;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 2.6rem;
    letter-spacing: 0em;
    text-align: left;
    color: #0074D9;
}
.footer-bar{
    padding-top: 1.8rem;
}
.footer-bar p{
    font-family: Cabin;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    letter-spacing: 0em;
    text-align: center;
    color: #707683;
}



@media screen and (max-width: 1100px){
    .dashboard-landing-bg2-main{
        flex-wrap: wrap;
        gap: 3rem;
    }
    .dashboard-landing-bg3-main{
        flex-wrap: wrap;
        gap: 3rem;
    }
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    .dashboard-landing-bg2::after{
        height: 400px;
        width: 250px;
    }
    .dashboard-landing-bg3::after{
        height: 400px;
        width: 250px;
    }
}

@media screen and (max-width: 992px){
.main-nav #nav-toggler{
    display: block;
    font-size: 20px;
    color: black;
}
.main-nav .nav-items{
    position: absolute;
    top: 104%;
    left: 0;
    right: 0;
    height: auto;
    width: 100%;
    background: #ffffff;
    flex-direction: column;
    align-items: center;
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: all .3s linear;
}
.main-nav .nav-items.show{
    clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
    transition: all .5s linear;
    padding-bottom: 3rem;
}
}
@media screen and (max-width: 992px){
    .navbar-collapse.show{
        clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
    }
    .navbar-light .navbar-toggler {
    color: rgba(0,0,0,.55);
    border-color: rgba(0,0,0,.1);
    padding: 0;
    border: none;
    outline: none;
    box-shadow: none;
    }
    .best-option-cards{
        padding-bottom: 7rem;
    }
    .dashboard-landing-best-option{
        padding-bottom: 0rem;
    }
    .dashboard-landing-bg3{
        padding-top: 0rem;
        padding-bottom: 5rem;
    }
    .dashboard-landing-bg2{
        padding-top: 4rem;
    }
}
@media screen and (max-width: 500px){
    .dashboard-landing-bg2::after{
        height: 280px;
        width: 170px;
    }
    .dashboard-landing-bg3::after{
        height: 280px;
        width: 170px;
    }
    .hero-title-main h1{
        font-size: 2.9rem;
        line-height: 4.4rem;
    }
    .landing-bg2-txt h2{
        font-size: 2.5rem;
        line-height: 3.5rem;
    }
    .landing-bg3-txt h2{
        font-size: 2.5rem;
        line-height: 3.5rem;
    }
    .best-option-main-title h2{
        font-size: 2.5rem;
        line-height: 3.5rem;
    }
    .best-option-cards{
        padding-top: 4rem;
    }
    .best-option-card1 .pp{
        font-size: 2.8rem;
    }
    .best-option-card1 span{
        font-size: 1.5rem;
    }
    .best-option-card1{
        padding: 24px 15px 20px 15px;
    }
    .opt-card-circle-main{
        padding-top: 2.8rem;
    }
    .best-option-cards h2{
        font-size: 1.6rem;
        line-height: 1rem;
    }
    .hero-section{
        margin-top: 5rem;
    }
    .get-st-btn{
        width: 15rem;
        height: 5rem;
        font-size: 1.5rem;
    }

}
@media screen and (max-width: 343px){
    .footer-main{
        padding-bottom: 10rem;
    }
}
