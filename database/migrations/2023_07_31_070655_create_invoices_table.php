<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->nullable();
            $table->foreignId('organization_id')->nullable()->constrained('users', 'id')->onDelete('cascade');
            $table->string('subject')->nullable();
            $table->date('issue_date')->nullable();
            $table->date('due_date')->nullable();
            $table->enum('status', ['due', 'paid', 'overdue'])->nullable();
            //            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->boolean('is_sent')->default(0);
            $table->double('sub_total')->nullable();
            $table->double('total')->nullable();
            $table->enum('discount_method', ['per', 'amount'])->nullable();
            $table->float('discount_price')->nullable();
            $table->float('tax')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
};
