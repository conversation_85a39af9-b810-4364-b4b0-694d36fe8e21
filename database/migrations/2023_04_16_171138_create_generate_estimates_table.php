<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('generate_estimates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('request_id');
            //            $table->foreign('request_id')->references('id')->on('estimates')->onDelete('cascade');
            $table->longText('notes')->nullable();
            $table->string('status')->default('proposed');
            $table->tinyInteger('is_complete')->default(NO);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('generate_estimates');
    }
};
