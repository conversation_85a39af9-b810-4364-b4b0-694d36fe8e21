<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cost_summaries', function (Blueprint $table) {
            //            $table->dropForeign(['operation_id']);

            // Add the new foreign key (update as needed)
            $table->foreign('operation_id')
                ->references('id')
                ->on('opportunities') // Update with the correct table name if different
                ->onDelete('cascade') // Define desired behavior (e.g., 'cascade', 'restrict', 'set null')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cost_summaries', function (Blueprint $table) {
            $table->dropForeign(['operation_id']);

            // Re-add the original foreign key (update as needed)
            $table->foreign('operation_id')
                ->references('id')
                ->on('estimates') // Replace with the original table name
                ->onDelete('cascade') // Replace with the original behavior
                ->onUpdate('cascade');
        });
    }
};
