<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_bills', function (Blueprint $table) {
            if (Schema::hasColumn('item_bills', 'vendor_name')) {
                $table->string('vendor_name')->nullable()->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_number')) {
                $table->string('invoice_number')->nullable()->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_date')) {
                $table->date('invoice_date')->nullable()->change();
            }
            if (Schema::hasColumn('item_bills', 'due_date')) {
                $table->date('due_date')->nullable()->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_amount')) {
                $table->decimal('invoice_amount', 10, 2)->nullable()->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_image')) {
                $table->string('invoice_image')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_bills', function (Blueprint $table) {
            if (Schema::hasColumn('item_bills', 'vendor_name')) {
                $table->string('vendor_name')->nullable(false)->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_number')) {
                $table->string('invoice_number')->nullable(false)->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_date')) {
                $table->date('invoice_date')->nullable(false)->change();
            }
            if (Schema::hasColumn('item_bills', 'due_date')) {
                $table->date('due_date')->nullable(false)->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_amount')) {
                $table->decimal('invoice_amount', 10, 2)->nullable(false)->change();
            }
            if (Schema::hasColumn('item_bills', 'invoice_image')) {
                $table->string('invoice_image')->nullable(false)->change();
            }
        });
    }
};
