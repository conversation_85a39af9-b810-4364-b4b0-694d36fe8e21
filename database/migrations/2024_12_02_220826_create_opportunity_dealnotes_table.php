<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('opportunity_dealnotes', function (Blueprint $table) {
            $table->id();  // Adds an auto-incrementing ID field
            $table->bigInteger('opportunity_id')->nullable();  // Nullable opportunity_id (bigInteger)
            $table->text('deal_notes')->nullable();  // Nullable deal_notes (text)
            $table->timestamps();  // Adds created_at and updated_at timestamps
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('opportunity_dealnotes');
    }
};
