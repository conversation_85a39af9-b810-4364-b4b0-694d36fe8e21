<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->boolean('first_login')->default(0); // Add `first_login` field with default value 0
            $table->string('remember_token', 100)->nullable()->after('first_login'); // Add nullable `remember_token` field
            $table->string('password')->nullable()->after('remember_token'); // Add nullable `password` field
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contacts', function (Blueprint $table) {
            $table->dropColumn(['first_login', 'remember_token', 'password']);
        });
    }
};
