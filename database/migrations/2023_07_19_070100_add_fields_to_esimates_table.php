<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('estimates', function (Blueprint $table) {
            $table->foreignId('sale_person_id')->nullable()->change();
            $table->foreignId('estimator_id')->nullable()->change();
            $table->enum('communication_type', ['email', 'text', 'phone'])->nullable();
            $table->enum('created_by', ['client', 'company']);
            $table->integer('is_active')->default(1)->comment('1 = open, 0 = closed, 2 = pending')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('estimates', function (Blueprint $table) {
            $table->dropColumn(['communication_type', 'created_by']);
        });
    }
};
