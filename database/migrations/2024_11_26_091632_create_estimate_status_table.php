<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('estimate_status', function (Blueprint $table) {
            $table->id(); // Primary key (id)
            $table->text('status_name'); // Status name
            $table->unsignedBigInteger('generate_estimate_id'); // Foreign key for generate_estimate
            $table->unsignedBigInteger('opportunity_id'); // Foreign key for opportunity
            $table->timestamps(); // Created at and Updated at

            // Adding foreign key constraints (if the referenced tables exist)
            $table->foreign('generate_estimate_id')
                ->references('id')
                ->on('generate_estimates')
                ->onDelete('cascade');

            $table->foreign('opportunity_id')
                ->references('id')
                ->on('opportunities')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('estimate_status');
    }
};
