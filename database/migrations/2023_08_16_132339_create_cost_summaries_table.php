<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cost_summaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained('users', 'id')->onDelete('cascade');
            //            $table->foreignId('operation_id')->constrained('estimates', 'id')->onDelete('cascade');
            $table->string('hours')->nullable();
            $table->string('equipment_cost2')->nullable();
            $table->string('labor3')->nullable();
            $table->string('hard_material4')->nullable();
            $table->string('plant_material')->nullable();
            $table->string('other_jobCost')->nullable();
            $table->string('sub_contractor')->nullable();
            $table->string('accurals')->nullable();
            $table->string('po_number')->nullable();
            $table->string('work_type')->nullable();
            $table->string('service_line')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cost_summaries');
    }
};
