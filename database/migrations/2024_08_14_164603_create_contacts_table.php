<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('opportunity_id')->nullable();
            $table->string('first_name');
            $table->string('prefix')->nullable();
            $table->string('last_name');
            $table->string('suffix')->nullable();
            $table->string('title');
            $table->string('phone_number');
            $table->string('second_phone')->nullable();
            $table->string('email');
            $table->string('second_email')->nullable();
            $table->string('mailing_address');
            $table->integer('organization_id')->references('id')->on('users')->onDelete('cascade');
            $table->integer('account');
            $table->integer('role');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contacts');
    }
};
