<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('estimate_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('generate_estimate_id');
            //            $table->foreign('generate_estimate_id')->references('id')->on('estimates')->onDelete('cascade');
            // Foreign key for different related IDs (labors, materials, etc.)
            $table->string('item_name')->nullable();
            $table->unsignedBigInteger('opportunity_id');
            $table->foreign('opportunity_id')->references('id')->on('opportunities')->onDelete('cascade');
            // For categorizing the type of item (labor, hard material, plant material, etc.)
            $table->string('category_type'); // e.g., 'labor', 'hard_material', 'plant_material', etc.
            // For defining specific section names
            $table->string('section_name')->nullable(); // e.g., section in the UI
            // Common columns
            $table->string('quantity');
            $table->string('uom')->nullable();
            $table->string('unit_cost')->nullable();
            $table->string('gross_margin')->nullable();
            $table->string('total_cost')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('estimate_items');
    }
};
