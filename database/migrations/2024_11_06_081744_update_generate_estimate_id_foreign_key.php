<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*Schema::table('estimate_items', function (Blueprint $table) {
            $table->dropForeign(['generate_estimate_id']);

            // Add new foreign key for generate_estimate_id to the generate_estimates table
            $table->foreign('generate_estimate_id')->references('id')->on('generate_estimates')
                  ->onDelete('cascade')->onUpdate('cascade');
        });*/
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        /*Schema::table('estimate_items', function (Blueprint $table) {
            $table->dropForeign(['generate_estimate_id']);

            // Optionally, add the original foreign key constraint back to estimates table
            $table->foreign('generate_estimate_id')->references('id')->on('estimates')
                  ->onDelete('cascade')->onUpdate('cascade');
        });*/
    }
};
