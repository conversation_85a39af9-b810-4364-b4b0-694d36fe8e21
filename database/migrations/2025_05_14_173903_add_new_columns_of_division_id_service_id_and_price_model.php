<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->unsignedBigInteger('division_id')->nullable()->after('opportunity_count');
            //            $table->json('service_line_id')->after('division_id')->nullable();
            $table->unsignedBigInteger('service_line_id')->after('division_id')->nullable();
            $table->string('price_model')->after('service_line_id')->nullable();
            DB::statement("ALTER TABLE opportunities MODIFY COLUMN opportunity_type ENUM('Enhancements','Maintenance','New','Renew') NULL");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunities', function (Blueprint $table) {
            $table->dropColumn('division_id');
            $table->dropColumn('service_line_id');
            $table->dropColumn('price_model');
            DB::statement("ALTER TABLE opportunities MODIFY COLUMN opportunity_type ENUM('Enhancements','Maintenance') NULL");
        });
    }
};
