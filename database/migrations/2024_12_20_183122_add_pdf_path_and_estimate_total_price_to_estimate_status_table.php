<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('estimate_status', function (Blueprint $table) {
            $table->string('pdf_path')->nullable(); // Nullable column
            $table->decimal('estimate_total_price', 15, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('estimate_status', function (Blueprint $table) {
            $table->dropColumn('pdf_path');
            $table->dropColumn('estimate_total_price');
        });
    }
};
