<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('hard_materials', function (Blueprint $table) {
            $table->integer('depth')->nullable()->after('division_id');
            $table->integer('sqft')->nullable()->after('depth');
        });
    }

    public function down()
    {
        Schema::table('hard_materials', function (Blueprint $table) {
            $table->dropColumn(['depth', 'sqft']);
        });
    }
};
