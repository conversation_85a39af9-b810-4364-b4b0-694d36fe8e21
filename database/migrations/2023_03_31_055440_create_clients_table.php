<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->unsignedBigInteger('organization_id');
            $table->foreign('organization_id')->references('id')->on('users')->onDelete('cascade');
            $table->string('company_name')->unique();
            $table->string('title');
            $table->string('email')->unique();
            $table->string('alternate_email')->unique();
            $table->string('mobile_no');
            $table->string('office_no');
            $table->string('alternate_no');
            $table->tinyInteger('is_active')
                ->default(YES)
                ->comment('0 = No, 1 = Yes');
            $table->bigInteger('added_by')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clients');
    }
};
