<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_default_settings', function (Blueprint $table) {
            if (! Schema::hasColumn('organization_default_settings', 'project_checked')) {
                $table->boolean('project_checked')->default(false);
            }
        });

        Schema::table('default_settings', function (Blueprint $table) {
            if (! Schema::hasColumn('default_settings', 'project_checked')) {
                $table->boolean('project_checked')->default(false);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_default_settings', function (Blueprint $table) {
            if (Schema::hasColumn('organization_default_settings', 'project_checked')) {
                $table->dropColumn('project_checked');
            }
        });

        Schema::table('default_settings', function (Blueprint $table) {
            if (Schema::hasColumn('default_settings', 'project_checked')) {
                $table->dropColumn('project_checked');
            }
        });
    }
};
