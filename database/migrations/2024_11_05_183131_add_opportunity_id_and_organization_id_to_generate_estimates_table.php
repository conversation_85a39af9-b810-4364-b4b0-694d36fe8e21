<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('generate_estimates', function (Blueprint $table) {
            $table->unsignedBigInteger('opportunity_id')->nullable()->after('request_id');
            $table->unsignedBigInteger('organization_id')->nullable()->after('opportunity_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('generate_estimates', function (Blueprint $table) {
            $table->dropColumn(['opportunity_id', 'organization_id']);
        });
    }
};
