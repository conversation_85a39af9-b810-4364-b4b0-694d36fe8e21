<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Only proceed if the column exists
        if (Schema::hasColumn('generate_estimates', 'request_id')) {
            /*Schema::table('generate_estimates', function (Blueprint $table) {
                // Drop foreign key if it exists
                $table->dropForeign('generate_estimates_request_id_foreign');
                // Now drop the column
                $table->dropColumn('request_id');
            });*/
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        /*Schema::table('generate_estimates', function (Blueprint $table) {
            $table->unsignedBigInteger('request_id')->nullable();
            // Re-add the foreign key constraint if needed
            $table->foreign('request_id')->references('id')->on('estimates')->onDelete('cascade');
        });*/
    }
};
