<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_estimate_actions', function (Blueprint $table) {
            // Purani foreign key ko drop karein agar already exist karti ho
            //            $table->dropForeign(['client_id']);

            // Nai foreign key add karein jo accounts table se link karein
            $table->foreign('client_id')
                ->references('id')
                ->on('accounts')
                ->onDelete('cascade') // Optional: cascade delete rule
                ->onUpdate('cascade'); // Optional: cascade update rule
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_estimate_actions', function (Blueprint $table) {
            // Nai foreign key ko drop karein
            $table->dropForeign(['client_id']);

            // <PERSON><PERSON><PERSON> waali foreign key wapas karein
            //            $table->foreign('client_id')
            //                ->references('id')
            //                ->on('clients')
            //                ->onDelete('cascade')
            //                ->onUpdate('cascade');
        });
    }
};
