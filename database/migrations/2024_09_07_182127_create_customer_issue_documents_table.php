<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_issue_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_issue_id');
            $table->string('name');
            $table->string('path');
            $table->string('type');
            $table->string('document_status')->default('regular');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_issue_documents');
    }
};
