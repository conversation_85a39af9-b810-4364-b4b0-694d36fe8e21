<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_line_work_types', function (Blueprint $table) {

            /* 1️⃣  remove old FK + column */
            /*if (Schema::hasColumn('service_line_work_types', 'service_line_id')) {
                $table->dropForeign(['service_line_id']);
                $table->dropColumn('service_line_id');
            }*/

            /* 2️⃣  add division_id */
            if (! Schema::hasColumn('service_line_work_types', 'division_id')) {
                $table->foreignId('division_id')
                    ->after('id')
                    ->constrained('divisions')
                    ->cascadeOnDelete();
            }

            /* 3️⃣  add account_id (company‐specific) */
            if (! Schema::hasColumn('service_line_work_types', 'account_id')) {
                $table->foreignId('account_id')
                    ->nullable()
                    ->after('division_id')
                    ->constrained('users')
                    ->nullOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_line_work_types', function (Blueprint $table) {

            /*if (Schema::hasColumn('service_line_work_types', 'account_id')) {
                $table->dropForeign(['account_id']);
                $table->dropColumn('account_id');
            }*/

            if (Schema::hasColumn('service_line_work_types', 'division_id')) {
                $table->dropForeign(['division_id']);
                $table->dropColumn('division_id');
            }

            // re-add the old column (optional)
            if (! Schema::hasColumn('service_line_work_types', 'service_line_id')) {
                $table->foreignId('service_line_id')
                    ->after('id')
                    ->constrained('service_lines')
                    ->cascadeOnDelete();
            }
        });
    }
};
