<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('generate_estimates', 'labor_hours_info')) {
            Schema::table('generate_estimates', function (Blueprint $table) {
                $table->dropColumn('labor_hours_info');
            });
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('generate_estimates', function (Blueprint $table) {
            //
        });
    }
};
