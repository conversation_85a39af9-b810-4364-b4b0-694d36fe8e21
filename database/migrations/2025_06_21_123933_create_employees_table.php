<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployeesTable extends Migration
{
    public function up()
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('phone_number');
            $table->string('email_address')->unique();
            $table->string('title')->nullable();
            $table->unsignedBigInteger('division_id'); // Foreign key to divisions table
            $table->foreign('division_id')->references('id')->on('divisions')->onDelete('cascade');
            $table->string('status');
            $table->decimal('pay_rate', 8, 2);
            $table->decimal('overtime_rate', 8, 2);
            $table->string('employee_number')->unique();
            $table->string('drivers_license_number')->nullable();
            $table->string('state_issued')->nullable();
            $table->date('date_issued')->nullable();
            $table->date('expiration_date')->nullable();
            $table->date('dot_medical_card_issued_date')->nullable();
            $table->date('dot_medical_card_expiration_date')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('employees');
    }
}
