<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_estimate_actions', function (Blueprint $table) {
            $table->id();
            //            $table->foreignId('client_id')->constrained('clients','id')->onDelete('cascade');
            $table->foreignId('generate_estimate_id')->constrained('generate_estimates', 'id')->onDelete('cascade');
            $table->string('signature_text')->nullable();
            $table->string('status', 50)->nullable();
            $table->date('signature_date')->nullable();
            $table->double('price')->nullable();
            $table->longText('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_estimate_actions');
    }
};
