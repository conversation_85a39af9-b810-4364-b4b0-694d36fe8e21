<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('invoices', function (Blueprint $table) {
            //            $table->dropForeign(['operation_id']);

            // Add the new foreign key constraint
            $table->foreign('operation_id')
                ->references('id')
                ->on('generate_estimates')
                ->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropForeign(['operation_id']);
            $table->dropColumn(['operation_id']);
        });
    }
};
