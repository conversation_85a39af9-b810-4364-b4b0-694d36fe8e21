<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // if (Schema::hasColumn('cost_summaries', 'operation_id')){

        //     Schema::table('cost_summaries', function (Blueprint $table) {
        //         // Drop the existing foreign key constraint
        //         $table->dropForeign(['operation_id']);
        //         $table->dropColumn(['operation_id']);

        //         // Create a new foreign key constraint
        //         $table->foreignId('operation_id')->constrained('generate_estimates', 'id')->onDelete('cascade');
        //     });

        // }

        // if (Schema::hasColumn('invoices', 'operation_id')){
        //     Schema::table('invoices', function (Blueprint $table) {
        //         $table->dropForeign(['operation_id']);
        //         $table->dropColumn(['operation_id']);
        //         // Create a new foreign key constraint
        //         $table->foreignId('operation_id')->nullable()->constrained('generate_estimates', 'id')->onDelete('cascade');
        //     });
        // }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
};
