<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('organization_default_settings', function (Blueprint $table) {
            $table->bigIncrements('id'); // Primary key, unsigned, auto-increment
            $table->text('cov_title')->nullable(); // Cover title
            $table->text('cov_sub_title')->nullable(); // Cover subtitle
            $table->text('cov_image')->nullable(); // Cover image
            $table->text('intro')->nullable(); // Introduction
            $table->text('project_image')->nullable(); // Project image
            $table->text('expiry')->nullable(); // Expiry
            $table->text('setting_type')->nullable(); // Setting type
            $table->unsignedInteger('organization_id'); // Organization ID
            $table->timestamps(); // created_at and updated_at
            $table->text('payment_schedule')->nullable(); // Payment schedule
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('organization_default_settings');
    }
};
