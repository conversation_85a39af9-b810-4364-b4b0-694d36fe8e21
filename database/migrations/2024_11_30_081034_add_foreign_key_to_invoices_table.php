<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('invoices', function (Blueprint $table) {
            //            $table->dropForeign(['client_id']);
            //            $table->unsignedBigInteger('client_id')->change(); // Ensure it is unsignedBigInteger

            // Add foreign key constraint
            $table->foreign('client_id')->references('id')->on('contacts')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('invoices', function (Blueprint $table) {
            //            $table->dropForeign(['client_id']);
        });
    }
};
