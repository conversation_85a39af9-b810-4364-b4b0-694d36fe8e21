<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('estimate_materials', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });

        Schema::table('estimate_labors', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });

        Schema::table('estimate_plant_materials', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });

        Schema::table('estimate_hard_materials', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });

        Schema::table('estimate_other_costs', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });

        Schema::table('estimate_sub_contractors', function (Blueprint $table) {
            $table->json('vendor_info')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('estimate_materials', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });

        Schema::table('estimate_labors', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });

        Schema::table('estimate_plant_materials', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });

        Schema::table('estimate_hard_materials', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });

        Schema::table('estimate_other_costs', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });

        Schema::table('estimate_sub_contractors', function (Blueprint $table) {
            $table->dropColumn(['vendor_info']);
        });
    }
};
