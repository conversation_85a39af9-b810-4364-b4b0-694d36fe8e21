<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('generate_estimates', function (Blueprint $table) {
            $table->double('total_cost')->change();
            $table->double('total_price')->change();
            $table->double('final_price')->change();
            $table->double('desire_margin')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('generate_estimates', function (Blueprint $table) {});
    }
};
