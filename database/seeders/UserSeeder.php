<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('admin123'),
            'type' => 2,
            'is_active' => true,
            'parent_id' => 2,
        ]);
        $role = Role::updateOrCreate(['name' => 'salesman']);
        $user->assignRole($role->name);
        $user = User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'first_name' => 'Max',
            'last_name' => 'Harvey',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('admin123'),
            'type' => 2,
            'is_active' => true,
            'parent_id' => 2,
        ]);
        $role = Role::updateOrCreate(['name' => 'estimator']);
        $user->assignRole($role->name);
    }
}
