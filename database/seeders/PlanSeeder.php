<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        Plan::updateOrCreate([
            'name' => 'Basic',
        ], [
            'price' => 0.00,
            'currency' => 'USD',
            'interval' => 'week',
            'interval_count' => 1,
            'trial_days' => 14,
            'description' => 'Free Plan',
        ]);
        if (app()->environment(['production'])) {
            Plan::updateOrCreate([
                'name' => 'Premium',
            ], [
                'price' => 200.00,
                'currency' => 'USD',
                'interval' => 'month',
                'interval_count' => 1,
                'trial_days' => 0,
                'stripe_product' => 'prod_OlOvePKJhRYfU3',
                'stripe_price' => 'price_1Nxs7oG1Vp5u33ifa6mp3WQ6',
                'description' => 'Premium plan for month,cancel anytime',
            ]);
        } else {
            Plan::updateOrCreate([
                'name' => 'Premium',
            ], [
                'price' => 200.00,
                'currency' => 'USD',
                'interval' => 'month',
                'interval_count' => 1,
                'trial_days' => 0,
                'stripe_product' => 'prod_ODZccHr6nRUsVm',
                'stripe_price' => 'price_1NR8SpB4FPdmAbqmXHk6X84N',
                'description' => 'Premium plan for month,cancel anytime',
            ]);
        }
    }
}
