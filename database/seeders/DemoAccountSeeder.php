<?php

namespace Database\Seeders;

use App\Models\CompanyAddress;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class DemoAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Creating demo accounts...');

        // Get plans
        $basicPlan = Plan::where('name', config('custom.plans.free.name', 'Basic'))->first();
        $premiumPlan = Plan::where('name', config('custom.plans.premium.name', 'Premium'))->first();

        if (! $basicPlan) {
            $this->command->error('Basic plan not found. Please run PlanSeeder first.');

            return;
        }

        // Get organization role
        $orgRole = Role::where('name', 'organization')->first();
        if (! $orgRole) {
            $this->command->error('Organization role not found. Please run RoleSeeder first.');

            return;
        }

        // Demo accounts data
        $demoAccounts = [
            [
                'company_name' => 'GreenScape Landscaping LLC',
                'first_name' => 'John',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'password' => 'Demo123!',
                'address' => '1234 Landscaping Ave',
                'city' => 'Austin',
                'state' => 'Texas',
                'zip' => '78701',
                'plan_type' => 'free',
                'plan' => $basicPlan,
            ],
            [
                'company_name' => 'Elite Outdoor Services',
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'password' => 'Demo123!',
                'address' => '5678 Garden Street',
                'city' => 'Denver',
                'state' => 'Colorado',
                'zip' => '80202',
                'plan_type' => 'premium',
                'plan' => $premiumPlan,
            ],
            [
                'company_name' => 'Sunshine Lawn Care',
                'first_name' => 'Mike',
                'last_name' => 'Rodriguez',
                'email' => '<EMAIL>',
                'password' => 'Demo123!',
                'address' => '9012 Turf Boulevard',
                'city' => 'Phoenix',
                'state' => 'Arizona',
                'zip' => '85001',
                'plan_type' => 'free',
                'plan' => $basicPlan,
            ],
            [
                'company_name' => 'Premier Landscape Solutions',
                'first_name' => 'Jennifer',
                'last_name' => 'Davis',
                'email' => '<EMAIL>',
                'password' => 'Demo123!',
                'address' => '3456 Professional Way',
                'city' => 'Miami',
                'state' => 'Florida',
                'zip' => '33101',
                'plan_type' => 'premium',
                'plan' => $premiumPlan,
            ],
        ];

        foreach ($demoAccounts as $accountData) {
            $this->createDemoAccount($accountData, $orgRole);
        }

        $this->command->info('Demo accounts created successfully!');
        $this->command->line('');
        $this->command->info('Demo Account Login Credentials:');
        $this->command->line('');

        foreach ($demoAccounts as $account) {
            $planName = $account['plan_type'] === 'free' ? 'FREE TRIAL' : 'PREMIUM';
            $this->command->line("📧 {$account['email']} | 🔑 {$account['password']} | 🏢 {$account['company_name']} | 📋 {$planName}");
        }

        $this->command->line('');
        $this->command->info('All accounts are verified and ready to use!');
    }

    /**
     * Create a demo account following the registration process
     */
    private function createDemoAccount(array $data, Role $orgRole)
    {
        DB::beginTransaction();

        try {
            // Check if user already exists
            $existingUser = User::where('email', $data['email'])->first();
            if ($existingUser) {
                $this->command->warn("User {$data['email']} already exists, skipping...");
                DB::rollBack();

                return;
            }

            // Create user (following AuthController logic)
            $user = User::create([
                'company_name' => $data['company_name'],
                'email' => $data['email'],
                'address' => $data['address'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'password' => Hash::make($data['password']),
                'type' => 1, // ORGANIZATION
                'email_verified_at' => now(), // Pre-verified for demo
                'is_active' => true,
                'status' => 'Active',
            ]);

            // Create company address (following AuthController logic)
            CompanyAddress::create([
                'company_id' => $user->id,
                'address1' => $data['address'],
                'city' => $data['city'],
                'zip' => $data['zip'],
                'state' => $data['state'],
                'time_format' => 'h:i A',
                'date_format' => 'Y/m/d',
                'type' => 0, // Property address
            ]);

            // Assign organization role
            $user->assignRole($orgRole);

            // Set up plan-specific data
            if ($data['plan_type'] === 'free') {
                // Free trial setup (following Index.php freeTrial logic)
                $user->update([
                    'payment_mode' => 'trial',
                    'expire_untill' => now()->addDays(config('custom.plans.free.days', 14)),
                ]);
            } else {
                // Premium plan setup
                $user->update([
                    'payment_mode' => 'subscription',
                    'expire_untill' => now()->addMonth(), // 1 month subscription
                ]);

                // Create subscription record if premium plan exists
                if ($data['plan']) {
                    Subscription::create([
                        'user_id' => $user->id,
                        'plan_id' => $data['plan']->id,
                        'payment_type' => 'demo', // Mark as demo subscription
                        'subscription' => 'demo_subscription_'.$user->id,
                        'trial_ends_at' => null,
                        'starts_at' => now(),
                        'ends_at' => now()->addMonth(),
                        'canceled_immediately' => false,
                        'canceled_at' => null,
                        'is_main' => true,
                    ]);
                }
            }

            // Insert default labor and margin data (following the helper functions)
            insertLabor($user->id);
            insertMargin($user->id);

            DB::commit();

            $planType = $data['plan_type'] === 'free' ? 'Free Trial' : 'Premium';
            $this->command->info("✅ Created demo account: {$data['company_name']} ({$planType}) - {$data['email']}");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error("❌ Failed to create demo account {$data['email']}: ".$e->getMessage());
        }
    }
}
