<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use <PERSON><PERSON>\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();

        Permission::truncate();

        Schema::enableForeignKeyConstraints();

        $permissions = [
            // Dashboard
            'dashboard_clients', 'dashboard_requests', 'dashboard_won', 'dashboard_sales', 'dashboard_estimate_losts', 'dashboard_wip',

            // Clients
            'client_listing', 'add_client', 'edit_client', 'client_detail',

            // Estimate Requests
            'create_estimate_request', 'edit_estimate_request', 'view_estimate_documents', 'estimate_request_listing', 'delete_estimate_request',

            // Generate Estimates
            'estimate_listing', 'generate_estimate', 'change_estimate_status', 'view_estimate_change_request', 'estimate_detail',

            // Operations
            'operation_listing', 'change_operation_status', 'assign_operation_om', 'operation_detail',

            // Opportunity
            'opportunity_listing', 'add_opportunity', 'edit_opportunity', 'opportunity_detail',

            // Customer Issue
            'customer_issue_listing', 'add_customer_issue', 'edit_customer_issue', 'customer_issue_detail',

            // Schedules
            'manage_schedules',

            // Transaction
            'manage_transaction',

            // Invoices
            'create_invoice', 'invoice_listing', 'create_operational_invoice', 'delete_invoice', 'invoice_detail', 'collect_invoice',

            // Archive
            'can_archive',

            // Reports
            'manage_reports',

            // Financial Tools
            'manage_financial_tools',

            // Settings
            'general_setting', 'account_setting', 'estimate_branding', 'margin_setting', 'material_setting', 'users_setting', 'target_setting', 'default_settings',

        ];
        foreach ($permissions as $permission) {
            Permission::updateOrCreate(['name' => $permission]);
        }
    }
}
