<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();

        Role::truncate();

        Schema::enableForeignKeyConstraints();

        $managerRole = Role::updateOrCreate(['name' => 'manager']);
        $estimatorRole = Role::updateOrCreate(['name' => 'estimator']);
        $saleManRole = Role::updateOrCreate(['name' => 'salesman']);
        $orgRole = Role::updateOrCreate(['name' => 'organization']);
        $operationManagerRole = Role::updateOrCreate(['name' => 'operation manager']);

        $managerPermissions = [
            'dashboard_clients', 'dashboard_requests', 'dashboard_won', 'dashboard_sales', 'dashboard_estimate_losts', 'dashboard_wip', // Dashboard
            'client_listing', 'add_client', 'edit_client', 'client_detail',  // Clients
            'opportunity_listing', 'add_opportunity', 'edit_opportunity', 'opportunity_detail',   // Opportunity
            'customer_issue_listing', 'add_customer_issue', 'edit_customer_issue', 'customer_issue_detail',   // Customer Issue
            'create_estimate_request', 'edit_estimate_request', 'view_estimate_documents', 'estimate_request_listing', 'delete_estimate_request', // Estimate Requests
            'generate_estimate', 'change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'estimate_listing', // Generate Estimates
            'can_archive',  // Archive
            'operation_listing', 'change_operation_status', 'operation_detail', // Operations
            'manage_schedules', // Schedules
            'manage_transaction',  // Transaction
            'create_invoice', 'invoice_listing', 'create_operational_invoice', 'delete_invoice', 'invoice_detail', 'collect_invoice', // Invoices
            'manage_reports', // Reports
            'manage_financial_tools', // Financial Tools
            'estimate_branding', 'margin_setting', 'material_setting', 'users_setting', 'default_settings', 'target_setting', // Settings
        ];

        $operationManagerPermission = [
            'dashboard_clients', 'dashboard_won', 'dashboard_sales', 'dashboard_estimate_losts', 'dashboard_wip', // Dashboard
            'operation_listing', 'assign_operation_om', 'change_operation_status', 'operation_detail', // Operations
            'manage_schedules', // Schedules
        ];

        $estimatorPermissions = [
            'dashboard_clients', 'dashboard_requests', 'dashboard_won', 'dashboard_sales', 'dashboard_estimate_losts', 'dashboard_wip', // Dashboard
            'view_estimate_documents', 'estimate_request_listing', // Estimate Requests
            'generate_estimate', 'change_estimate_status', 'view_estimate_change_request', 'estimate_detail', // Generate Estimates
            'manage_schedules', // Schedules
            'operation_listing', 'assign_operation_om', 'change_operation_status', 'operation_detail', 'estimate_listing', // Operations
            'can_archive',  // Archive
            'create_invoice', 'invoice_listing', 'create_operational_invoice', 'delete_invoice', 'invoice_detail', 'collect_invoice', // Invoices
            'estimate_branding',
        ];

        $slaeManPermissions = [
            'dashboard_clients', 'dashboard_requests', 'dashboard_won', 'dashboard_sales', 'dashboard_estimate_losts', 'dashboard_wip', // Dashboard
            'manage_schedules', // Schedules
            'change_estimate_status', 'view_estimate_change_request', 'estimate_detail', 'estimate_listing', // Generate Estimates
            'can_archive',  // Archive
            'create_invoice', 'invoice_listing', 'create_operational_invoice', 'delete_invoice', 'invoice_detail', 'collect_invoice', // Invoices
        ];

        $managerRole->syncPermissions($managerPermissions);

        $operationManagerRole->syncPermissions($operationManagerPermission);

        $estimatorRole->syncPermissions($estimatorPermissions);

        $saleManRole->syncPermissions($slaeManPermissions);

        $mergedPermissions = array_merge($managerPermissions, $estimatorPermissions, $slaeManPermissions, ['general_setting', 'account_setting', 'estimate_branding', 'margin_setting', 'material_setting', 'users_setting', 'target_setting']);
        // dd($mergedPermissions);
        $orgPermissions = array_unique($mergedPermissions);
        $orgRole->syncPermissions([...$orgPermissions]);
    }
}
