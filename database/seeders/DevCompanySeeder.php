<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class DevCompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::beginTransaction();
        try {
            $email = config('app.dev_company_email', env('DEV_COMPANY_EMAIL', '<EMAIL>'));

            $developerOrganization = User::updateOrCreate(
                [
                    'email' => $email,
                ],
                [
                    'first_name' => 'Dev',
                    'last_name' => 'Owner',
                    'company_name' => 'Developer Company',
                    'email_verified_at' => now(),
                    'password' => Hash::make(env('DEV_COMPANY_PASSWORD', 'admin123')),
                    'type' => ORGANIZATION,
                    'is_active' => true,
                    'payment_mode' => 'subscription',
                    'expire_untill' => Carbon::now()->addYears(10),
                ]
            );

            // Ensure the organization role is assigned if available
            $organizationRole = Role::where('name', 'organization')->first();
            if ($organizationRole) {
                $developerOrganization->assignRole($organizationRole->name);
            }

            // Find a plan for the subscription
            $plan = Plan::where('name', 'Premium')->first()
                ?? Plan::where('name', 'Basic')->first()
                ?? Plan::query()->first();

            if ($plan) {
                Subscription::updateOrCreate(
                    [
                        'user_id' => $developerOrganization->id,
                        'is_main' => true,
                    ],
                    [
                        'plan_id' => $plan->id,
                        'payment_type' => 'dev',
                        'subscription' => 'lifetime',
                        'session_id' => null,
                        'trial_ends_at' => null,
                        'starts_at' => now(),
                        'ends_at' => now()->addYears(100),
                        'canceled_immediately' => null,
                        'canceled_at' => null,
                    ]
                );
            }

            // Seed default organization data helpful for testing
            if (function_exists('insertLabor')) {
                insertLabor($developerOrganization->id);
            }
            if (function_exists('insertMargin')) {
                insertMargin($developerOrganization->id);
            }

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw $throwable;
        }
    }
}
