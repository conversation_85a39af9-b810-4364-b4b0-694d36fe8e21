<?php

namespace Database\Seeders;

use App\Models\Division;
use App\Models\ServiceLine;
use Illuminate\Database\Seeder;

class ServiceLineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $serviceLines = [
            // Landscape Division Service Lines
            ['name' => 'Maintenance', 'division_id' => Division::where('name', 'Landscape')->first()->id],
            ['name' => 'Enhancement', 'division_id' => Division::where('name', 'Landscape')->first()->id],

            // Snow Division Service Lines
            ['name' => 'Snow Removal', 'division_id' => Division::where('name', 'Snow')->first()->id],

            // // Division 03 — Concrete
            // ['name' => 'Concrete Formwork', 'division_id' => Division::where('name', 'Concrete')->first()->id],
            // ['name' => 'Reinforcement', 'division_id' => Division::where('name', 'Concrete')->first()->id],
            // ['name' => 'Concrete Mix', 'division_id' => Division::where('name', 'Concrete')->first()->id],
            // ['name' => 'Concrete Finishes', 'division_id' => Division::where('name', 'Concrete')->first()->id],
            // ['name' => 'Specialty Concrete', 'division_id' => Division::where('name', 'Concrete')->first()->id],

            // // Division 08 — Openings
            // ['name' => 'Doors', 'division_id' => Division::where('name', 'Doors and Windows')->first()->id],
            // ['name' => 'Windows', 'division_id' => Division::where('name', 'Doors and Windows')->first()->id],
            // ['name' => 'Automatic Door Operators', 'division_id' => Division::where('name', 'Doors and Windows')->first()->id],
            // ['name' => 'Curtain Walls', 'division_id' => Division::where('name', 'Doors and Windows')->first()->id],
            // ['name' => 'Louvers and Vents', 'division_id' => Division::where('name', 'Doors and Windows')->first()->id],

            // // Division 23 — Heating, Ventilating, and Air Conditioning (HVAC)
            // ['name' => 'Heating Systems', 'division_id' => Division::where('name', 'Heating, Ventilating, and Air Conditioning (HVAC)')->first()->id],
            // ['name' => 'Ventilation Systems', 'division_id' => Division::where('name', 'Heating, Ventilating, and Air Conditioning (HVAC)')->first()->id],
            // ['name' => 'Air Conditioning Systems', 'division_id' => Division::where('name', 'Heating, Ventilating, and Air Conditioning (HVAC)')->first()->id],
            // ['name' => 'HVAC Controls', 'division_id' => Division::where('name', 'Heating, Ventilating, and Air Conditioning (HVAC)')->first()->id],
            // ['name' => 'Maintenance and Service', 'division_id' => Division::where('name', 'Heating, Ventilating, and Air Conditioning (HVAC)')->first()->id],

            // // Division 02 — Existing Conditions
            // ['name' => 'Site Surveying', 'division_id' => Division::where('name', 'Existing Conditions')->first()->id],
            // ['name' => 'Asbestos Abatement', 'division_id' => Division::where('name', 'Existing Conditions')->first()->id],
            // ['name' => 'Demolition and Removal', 'division_id' => Division::where('name', 'Existing Conditions')->first()->id],
            // ['name' => 'Soil Testing', 'division_id' => Division::where('name', 'Existing Conditions')->first()->id],
            // ['name' => 'Building Envelope Assessment', 'division_id' => Division::where('name', 'Existing Conditions')->first()->id],

            // // Division 04 — Masonry
            // ['name' => 'Brickwork', 'division_id' => Division::where('name', 'Masonry')->first()->id],
            // ['name' => 'Blockwork', 'division_id' => Division::where('name', 'Masonry')->first()->id],
            // ['name' => 'Stone Masonry', 'division_id' => Division::where('name', 'Masonry')->first()->id],
            // ['name' => 'Mortar and Grout Application', 'division_id' => Division::where('name', 'Masonry')->first()->id],
            // ['name' => 'Masonry Restoration and Repair', 'division_id' => Division::where('name', 'Masonry')->first()->id],

            // // Division 05 — Metals
            // ['name' => 'Structural Steel Fabrication', 'division_id' => Division::where('name', 'Metals')->first()->id],
            // ['name' => 'Metal Decking', 'division_id' => Division::where('name', 'Metals')->first()->id],
            // ['name' => 'Metal Stairs and Railings', 'division_id' => Division::where('name', 'Metals')->first()->id],
            // ['name' => 'Ornamental Metalwork', 'division_id' => Division::where('name', 'Metals')->first()->id],
            // ['name' => 'Metal Fabrications and Supports', 'division_id' => Division::where('name', 'Metals')->first()->id],

            // // Division 06 — Wood, Plastics, and Composites
            // ['name' => 'Wood Framing', 'division_id' => Division::where('name', 'Wood and Plastics')->first()->id],
            // ['name' => 'Wood Paneling', 'division_id' => Division::where('name', 'Wood and Plastics')->first()->id],
            // ['name' => 'Plastic Laminates', 'division_id' => Division::where('name', 'Wood and Plastics')->first()->id],
            // ['name' => 'Composite Decking', 'division_id' => Division::where('name', 'Wood and Plastics')->first()->id],
            // ['name' => 'Cabinetry and Millwork', 'division_id' => Division::where('name', 'Wood and Plastics')->first()->id],

            // // Division 07 — Thermal and Moisture Protection
            // ['name' => 'Roofing Systems', 'division_id' => Division::where('name', 'Thermal and Moisture Protection')->first()->id],
            // ['name' => 'Insulation Installation', 'division_id' => Division::where('name', 'Thermal and Moisture Protection')->first()->id],
            // ['name' => 'Vapor Barriers', 'division_id' => Division::where('name', 'Thermal and Moisture Protection')->first()->id],
            // ['name' => 'Waterproofing', 'division_id' => Division::where('name', 'Thermal and Moisture Protection')->first()->id],
            // ['name' => 'Flashing and Sheet Metal', 'division_id' => Division::where('name', 'Thermal and Moisture Protection')->first()->id],

            // // Division 09 — Finishes
            // ['name' => 'Gypsum Board Installation', 'division_id' => Division::where('name', 'Finishes')->first()->id],
            // ['name' => 'Acoustic Ceiling Panels', 'division_id' => Division::where('name', 'Finishes')->first()->id],
            // ['name' => 'Floor Coverings (carpet, tile, wood)', 'division_id' => Division::where('name', 'Finishes')->first()->id],
            // ['name' => 'Wall Finishes (paint, wallpaper)', 'division_id' => Division::where('name', 'Finishes')->first()->id],
            // ['name' => 'Specialty Finishes (textured coatings)', 'division_id' => Division::where('name', 'Finishes')->first()->id],

            // // Division 10 — Specialties
            // ['name' => 'Toilet and Bath Accessories', 'division_id' => Division::where('name', 'Specialties')->first()->id],
            // ['name' => 'Signage', 'division_id' => Division::where('name', 'Specialties')->first()->id],
            // ['name' => 'Fire Protection Cabinets', 'division_id' => Division::where('name', 'Specialties')->first()->id],
            // ['name' => 'Partitions (toilet, shower, dressing)', 'division_id' => Division::where('name', 'Specialties')->first()->id],
            // ['name' => 'Lockers and Storage Systems', 'division_id' => Division::where('name', 'Specialties')->first()->id],
        ];

        foreach ($serviceLines as $serviceLine) {
            ServiceLine::updateOrCreate(
                ['name' => $serviceLine['name'], 'division_id' => $serviceLine['division_id']],
                $serviceLine
            );
        }
    }
}
