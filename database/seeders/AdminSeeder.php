<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        User::updateOrCreate([
            'email' => '<EMAIL>',
        ], [
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('admin123'),
            'type' => 0, /* Super admin */
            'is_active' => true,
        ]);
    }
}
