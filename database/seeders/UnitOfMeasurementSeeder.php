<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UnitOfMeasurementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = [
            ['name' => '$', 'type' => 'hard_materials'],
            ['name' => "10'", 'type' => 'hard_materials'],
            ['name' => "16'", 'type' => 'hard_materials'],
            ['name' => 'Bag', 'type' => 'hard_materials'],
            ['name' => 'Bale', 'type' => 'hard_materials'],
            ['name' => 'Box 1000', 'type' => 'hard_materials'],
            ['name' => 'Each', 'type' => 'hard_materials'],
            ['name' => 'Gal.', 'type' => 'hard_materials'],
            ['name' => 'Lb.', 'type' => 'hard_materials'],
            ['name' => 'LF', 'type' => 'hard_materials'],
            ['name' => 'oz', 'type' => 'hard_materials'],
            ['name' => 'pallet', 'type' => 'hard_materials'],
            ['name' => 'per 1,000', 'type' => 'hard_materials'],
            ['name' => 'roll', 'type' => 'hard_materials'],
            ['name' => 'SF', 'type' => 'hard_materials'],
            ['name' => 'Ton', 'type' => 'hard_materials'],
            ['name' => 'Truckload', 'type' => 'hard_materials'],
            ['name' => 'Yd', 'type' => 'hard_materials'],
            ['name' => '1 GAL', 'type' => 'plant_materials'],
            ['name' => 'Quart', 'type' => 'plant_materials'],
            ['name' => '2 GAL', 'type' => 'plant_materials'],
            ['name' => '3 GAL', 'type' => 'plant_materials'],
            ['name' => '5 Gal', 'type' => 'plant_materials'],
            ['name' => '6 GAL', 'type' => 'plant_materials'],
            ['name' => '7 GAL', 'type' => 'plant_materials'],
            ['name' => '10 GAL', 'type' => 'plant_materials'],
            ['name' => '15 GAL', 'type' => 'plant_materials'],
            ['name' => '25 GAL', 'type' => 'plant_materials'],
            ['name' => '2.25" POTS', 'type' => 'plant_materials'],
            ['name' => '2.5" POT', 'type' => 'plant_materials'],
            ['name' => '4" POT', 'type' => 'plant_materials'],
            ['name' => 'Flat', 'type' => 'plant_materials'],
            ['name' => '12-15"', 'type' => 'plant_materials'],
            ['name' => '15-18"', 'type' => 'plant_materials'],
            ['name' => '18-21"', 'type' => 'plant_materials'],
            ['name' => '18-24"', 'type' => 'plant_materials'],
            ['name' => '21-24"', 'type' => 'plant_materials'],
            ['name' => '2-3\'', 'type' => 'plant_materials'],
            ['name' => '24-27"', 'type' => 'plant_materials'],
            ['name' => '24-30"', 'type' => 'plant_materials'],
            ['name' => '30-36"', 'type' => 'plant_materials'],
            ['name' => '3-4\'', 'type' => 'plant_materials'],
            ['name' => '36-42"', 'type' => 'plant_materials'],
            ['name' => '42-48"', 'type' => 'plant_materials'],
            ['name' => '4-5\'', 'type' => 'plant_materials'],
            ['name' => '5-6\'', 'type' => 'plant_materials'],
            ['name' => '1.5"', 'type' => 'plant_materials'],
            ['name' => '1.75"', 'type' => 'plant_materials'],
            ['name' => '2.0"', 'type' => 'plant_materials'],
            ['name' => '2.5"', 'type' => 'plant_materials'],
            ['name' => '3.0"', 'type' => 'plant_materials'],
            ['name' => '3.5"', 'type' => 'plant_materials'],
            ['name' => '10-12\'', 'type' => 'plant_materials'],
            ['name' => '12-14\'', 'type' => 'plant_materials'],
            ['name' => '6-8\'', 'type' => 'plant_materials'],
            ['name' => '7-8\'', 'type' => 'plant_materials'],
            ['name' => '8-10\'', 'type' => 'plant_materials'],
            ['name' => 'General Labor', 'type' => 'labor'],
            ['name' => 'Mowing', 'type' => 'labor'],
            ['name' => 'Policing', 'type' => 'labor'],
            ['name' => 'Pressure Washing', 'type' => 'labor'],
            ['name' => 'Weeding', 'type' => 'labor'],
            ['name' => 'Aerator, Rental per day', 'type' => 'other_costs'],
            ['name' => 'Ambusher rental per day', 'type' => 'other_costs'],
            ['name' => 'Backhoe (not incl. delivery) per day', 'type' => 'other_costs'],
            ['name' => "Manlift 35' Gas day", 'type' => 'other_costs'],
            ['name' => 'Chainsaw, rental per day', 'type' => 'other_costs'],
            ['name' => 'Chipper rental per day', 'type' => 'other_costs'],
            ['name' => 'Cut-off saw w/blade', 'type' => 'other_costs'],
            ['name' => 'Delivery', 'type' => 'other_costs'],
            ['name' => 'Dingo: call for attachments (extra)', 'type' => 'other_costs'],
            ['name' => 'Dump fees / stake body load', 'type' => 'other_costs'],
            ['name' => 'Dumpster 30CY (incl. del.)', 'type' => 'other_costs'],
            ['name' => 'Hydroseeder,  per hr', 'type' => 'other_costs'],
            ['name' => 'Hydroseeder, rental  per day', 'type' => 'other_costs'],
            ['name' => 'Mini-excavator rental per day', 'type' => 'other_costs'],
            ['name' => 'Mower,  22" per hr', 'type' => 'other_costs'],
            ['name' => 'Mower,  48" per hr', 'type' => 'other_costs'],
            ['name' => 'Mower,  61" per hr', 'type' => 'other_costs'],
            ['name' => 'Mower,  Blower per hr', 'type' => 'other_costs'],
            ['name' => 'Mower,  LZR per hr', 'type' => 'other_costs'],
            ['name' => 'Mower,  Weedeater per hr', 'type' => 'other_costs'],
            ['name' => 'Overseeder,  per hr', 'type' => 'other_costs'],
            ['name' => 'Overseeder, Rental per day', 'type' => 'other_costs'],
            ['name' => 'Plate tamper per day', 'type' => 'other_costs'],
            ['name' => 'Plate tamper, per hr.', 'type' => 'other_costs'],
            ['name' => 'Rototiller,  per hr', 'type' => 'other_costs'],
            ['name' => 'Rototiller, Rental per day', 'type' => 'other_costs'],
            ['name' => 'Skid Steer , per hr', 'type' => 'other_costs'],
            ['name' => 'Skid Steer Rental,per day(not incl.del)', 'type' => 'other_costs'],
            ['name' => 'Skid steer T-190,per day(not incl. del)', 'type' => 'other_costs'],
            ['name' => 'Sod Cutter,  per hr.', 'type' => 'other_costs'],
            ['name' => 'Sod Cutter, rental per day', 'type' => 'other_costs'],
            ['name' => 'Stump grinder, per day', 'type' => 'other_costs'],
            ['name' => 'Water trailer,  per hr.', 'type' => 'other_costs'],
        ];

        foreach ($units as $unit) {
            DB::table('unit_of_measurements')->insert([
                'name' => $unit['name'],
                'type' => $unit['type'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
