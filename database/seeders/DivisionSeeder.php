<?php

namespace Database\Seeders;

use App\Models\Division;
use Illuminate\Database\Seeder;

class DivisionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /*$divisions = [
            ['name' => 'General Requirement'],
            ['name' => 'Site Works'],
            ['name' => 'Concrete'],
            ['name' => 'Masonry'],
            ['name' => 'Metals'],
            ['name' => 'Wood and Plastics'],
            ['name' => 'Thermal and Moisture Protection'],
            ['name' => 'Doors and Windows'],
            ['name' => 'Finishes'],
            ['name' => 'Specialties'],
            ['name' => 'Equipment'],
            ['name' => 'Furnishings'],
            ['name' => 'Special Construction'],
            ['name' => 'Conveying Systems'],
            ['name' => 'Mechanical/Plumbing'],
            ['name' => 'Electrical'],
            ['name' => 'Openings'],
            ['name' => 'Heating, Ventilating, and Air Conditioning (HVAC)'],
            ['name' => 'Existing Conditions'],
            ['name' => 'Earthwork'],
            ['name' => 'Exterior Improvements'],
            ['name' => 'Utilities'],
            ['name' => 'Material Processing and Handling Equipment'],
            ['name' => 'Process Heating, Cooling, and Drying Equipment'],
        ];*/
        $divisions = [
            ['name' => 'Landscape'],
            ['name' => 'Snow'],
        ];

        foreach ($divisions as $division) {
            Division::updateOrCreate(['name' => $division], $division);
        }

    }
}
