<?php

use App\Http\Controllers;
use Illuminate\Support\Facades\Route;

/* -------Index Route ------ */

/* -------Clients Route ------ */

Route::group(['middleware' => 'manager'], function () {

    Route::get('client', [Controllers\Organization\ClientController::class, 'index'])
        ->name('client.index');
    // Route::get('client/table-list', [Controllers\Organization\ClientController::class, 'listing'])
    //     ->name('client.table-list');
    Route::get('create-client', [Controllers\Organization\ClientController::class, 'create'])
        ->name('create.client');
    Route::post('store-client', [Controllers\Organization\ClientController::class, 'store'])
        ->name('client.store');
    Route::put('update-client/{id}', [Controllers\Organization\ClientController::class, 'update'])
        ->name('client.update');
    Route::get('client-detail/{id}', [Controllers\Organization\ClientController::class, 'detail'])
        ->name('client.detail');
    Route::get('edit-client/{id}', [Controllers\Organization\ClientController::class, 'edit'])
        ->name('client.edit');
    Route::get('client-estimates-history/{id}', [Controllers\Organization\ClientController::class, 'clientEstimatesHistory'])
        ->name('client.estimates');

    // Reports Module
    Route::get('reports', [Controllers\Organization\ReportController::class, 'index'])->name('reports.index');
    Route::post('generate-reports', [Controllers\Organization\ReportController::class, 'generateReports'])->name('generate.reports');

    // Dashboard Charts
    Route::post('lost-chart', [Controllers\DashboardController::class, 'lostSalesChart'])->name('chartData');
    Route::post('wave-chart', [Controllers\DashboardController::class, 'waveChart'])->name('waveChartData');
    Route::post('double-chart', [Controllers\DashboardController::class, 'doubleChart'])->name('doubleChart');
});
/* -------Generate Estimates Route ------ */

Route::get('estimate-generate', [Controllers\Organization\GenerateEstimateController::class, 'index'])
    ->name('estimate-generate.index');
Route::get('estimate-generate/table-list', [Controllers\Organization\GenerateEstimateController::class, 'listing'])
    ->name('generate-estimate.table-list');

Route::get('get-estimate-invoice-deatil/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateInvoiceDetail'])
    ->name('estimate.invoice-detail');
Route::post('update-estimate-generate-status', [Controllers\Organization\GenerateEstimateController::class, 'updateStatus'])
    ->name('update-estimate-generate-status');
Route::group(['middleware' => 'managerEstimator'], function () {

    /* -------Estimate Requests Route ------ */

    Route::get('estimate', [Controllers\Organization\EstimateController::class, 'index'])
        ->name('estimate.index');
    Route::get('estimate/table-list', [Controllers\Organization\EstimateController::class, 'listing'])
        ->name('estimate.table-list');
    Route::get('create-estimate/{id}', [Controllers\Organization\EstimateController::class, 'create'])
        ->name('create.estimate');
    Route::post('store-estimate', [Controllers\Organization\EstimateController::class, 'store'])
        ->name('estimate.store');
    Route::put('update-estimate/{id}', [Controllers\Organization\EstimateController::class, 'update'])
        ->name('estimate.update');
    Route::get('estimate-detail/{id}', [Controllers\Organization\EstimateController::class, 'detail'])
        ->name('estimate.detail');
    Route::get('edit-estimate/{id}', [Controllers\Organization\EstimateController::class, 'edit'])
        ->name('estimate.edit');
    Route::get('estimate-documents/{id}', [Controllers\Organization\EstimateController::class, 'getDocuments'])
        ->name('estimate.documents');
    Route::delete('delete-estimate/{id}', [Controllers\Organization\EstimateController::class, 'delete'])
        ->name('estimate.delete');

    Route::get('estimate-view-change-request/{id}', [Controllers\Organization\EstimateController::class, 'viewChangeRequest'])
        ->name('estimate.view-change-request');

    /* -------Generate Estimates Route ------ */
    Route::get('create-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'create'])
        ->name('create.estimate-generate');
    Route::get('download-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'downloadEstimate'])
        ->name('estimate-generate.download');
    Route::get('only-download-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'onlyDownloadEstimate'])
        ->name('estimate-generate.only-download');
    Route::get('store-estimate-generate/{id}/', [Controllers\Organization\GenerateEstimateController::class, 'storeEstimate'])
        ->name('estimate-generate.store');
    Route::put('update-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'update'])
        ->name('estimate-generate.update');
    Route::get('estimate-generate-detail/{id}', [Controllers\Organization\GenerateEstimateController::class, 'detail'])
        ->name('estimate-generate.detail');
    Route::get('edit-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'edit'])
        ->name('estimate-generate.edit');
    Route::delete('delete-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'delete'])
        ->name('estimate-generate.delete');

    // routes for generate estimate 1st step
    Route::post('estimate-add-equipment', [Controllers\Organization\GenerateEstimateController::class, 'addEquipment'])
        ->name('estimate.add-equipment');
    Route::get('get-estimate-equipment/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateEquipment'])
        ->name('get-estimate-equipment');
    Route::get('estimate-equipment-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateEquipment'])
        ->name('estimate.equipment.delete');

    // routes for generate estimate 2nd step
    Route::post('estimate-add-labor', [Controllers\Organization\GenerateEstimateController::class, 'addLabor'])
        ->name('estimate.add-labor');
    Route::get('get-estimate-labor/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateLabor'])
        ->name('get-estimate-labor');
    Route::get('estimate-labor-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateLabor'])
        ->name('estimate.labor.delete');

    // routes for generate estimate 3rd step
    Route::post('estimate-add-material', [Controllers\Organization\GenerateEstimateController::class, 'addMaterial'])
        ->name('estimate.add-material');
    Route::get('get-estimate-plant-material/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimatePlantMaterial'])
        ->name('get-estimate-plant-material');
    Route::get('get-estimate-hard-material/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateHardMaterial'])
        ->name('get-estimate-hard-material');
    Route::get('estimate-material-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateMaterial'])
        ->name('estimate.material.delete');

    // routes for generate estimate 4th step
    Route::post('estimate-add-other-cost', [Controllers\Organization\GenerateEstimateController::class, 'addOtherCost'])
        ->name('estimate.add-other-cost');
    Route::get('get-estimate-other-cost/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateOtherCost'])
        ->name('get-estimate-other-cost');
    Route::get('estimate-other-cost-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateOtherCost'])
        ->name('estimate.other.cost.delete');

    // routes for generate estimate 5th step
    Route::post('estimate-add-sub-contractor', [Controllers\Organization\GenerateEstimateController::class, 'addSubContractor'])
        ->name('estimate.add-sub-contractor');
    Route::get('get-estimate-sub-contractor/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateSubContractor'])
        ->name('get-estimate-sub-contractor');
    Route::get('estimate-sub-contractor-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateSubContractor'])
        ->name('estimate.subcontractor.delete');

    // routes for estimate invoice detail

    Route::post('add-note/{id}', [Controllers\Organization\GenerateEstimateController::class, 'addNote'])
        ->name('add-note');
    Route::delete('delete-note/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteNote'])
        ->name('delete-note');

    // routes for estimate desire margin
    Route::post('add-desire-margin/{id}', [Controllers\Organization\GenerateEstimateController::class, 'addDesireMargin'])
        ->name('add-desire-margin');

    // estimate imge or files route
    Route::post('estimate/file', [Controllers\Organization\EstimateController::class, 'imageStore'])
        ->name('estimate.file-store');
    Route::post('estimate/file-delete', [Controllers\Organization\EstimateController::class, 'imageDelete'])
        ->name('estimate.file-delete');

    // route for hard and plant material import
    Route::get('materials', [Controllers\Organization\MaterialController::class, 'index'])
        ->name('materials.index');
    Route::get('material/table-list', [Controllers\Organization\MaterialController::class, 'listing'])
        ->name('material.table-list');
    Route::get('plant_material/table-list', [Controllers\Organization\MaterialController::class, 'plantListing'])
        ->name('plant_material.table-list');
    Route::post('file-import', [Controllers\Organization\MaterialController::class, 'fileImport'])->name('materials.file-import');
    Route::post('plant-file-import', [Controllers\Organization\MaterialController::class, 'plantFileImport'])->name('plant-materials.file-import');

    // route for material equipment export
    Route::get('material-equipment', [Controllers\Organization\MaterialController::class, 'show'])
        ->name('material.equipment.index');
    Route::get('material-equipment/table-list', [Controllers\Organization\MaterialController::class, 'equipmentListing'])
        ->name('material.equipment.table-list');
    Route::post('equipment-file-import', [Controllers\Organization\MaterialController::class, 'equipmentFileImport'])->name('equipment.file-import');

    // Users
    Route::get('users-list', [Controllers\Organization\UserController::class, 'index'])->name('users.list');
    Route::get('invite-users', [Controllers\Organization\UserController::class, 'create'])->name('users.invite');
    Route::post('add-users', [Controllers\Organization\UserController::class, 'store'])->name('users.add');
    Route::get('edit-user/{id}', [Controllers\Organization\UserController::class, 'edit'])
        ->name('users.edit');
    Route::put('update-user/{id}', [Controllers\Organization\UserController::class, 'update'])
        ->name('user.update');
    Route::delete('delete-user/{id}', [Controllers\Organization\UserController::class, 'delete'])
        ->name('user.delete');
    Route::post('update-user-status', [Controllers\Organization\UserController::class, 'updateStatus'])
        ->name('update-user-status');

    /* -------Operation Route ------ */

    Route::get('operation', [Controllers\Organization\OperationController::class, 'index'])
        ->name('operation.index');
    Route::get('operation-list', [Controllers\Organization\OperationController::class, 'listing'])
        ->name('operation-list');
    Route::post('update-operation-status', [Controllers\Organization\OperationController::class, 'updateStatus'])
        ->name('update-operation-status');
    Route::post('assign-manager', [Controllers\Organization\OperationController::class, 'assignManager'])
        ->name('assignManager');
    Route::post('getManagers', [Controllers\Organization\OperationController::class, 'getManagers'])
        ->name('getManagers');
    Route::get('operation-detail/{id}', [Controllers\Organization\OperationController::class, 'detail'])
        ->name('operation.detail');

    // Invoices Routes
    Route::get('invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('invoices/create/{id}', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::get('invoices/retrieve/product/{id}', [InvoiceController::class, 'retriveProduct'])->name('invoices.retrieve.product');
    Route::get('invoices/remove/product/{id}', [InvoiceController::class, 'removeProduct'])->name('invoices.remove.product');
    Route::post('invoices/add-product/{invoiceID}', [InvoiceController::class, 'addProduct'])->name('invoices.add.product');
    Route::post('invoices/generate/{invoiceID}', [InvoiceController::class, 'generate'])->name('invoices.generate');
    Route::post('collect/payment/{id}', [InvoiceController::class, 'collectPayment'])->name('invoices.collect.payment');

    Route::get('invoice/view/{id}', [InvoiceController::class, 'viewInvoice'])->name('invoices.view');
    Route::get('invoice/client/amount/{id}', [InvoiceController::class, 'invoiceCollectAmountDetails'])->name('invoices.client.amount');
    Route::get('invoices/remove/invoice/{id}', [InvoiceController::class, 'removeInvoice'])->name('invoices.remove.invoice');
    Route::get('transactions', [InvoiceController::class, 'transactions'])->name('transactions');
    Route::get('operational/invoice', [InvoiceController::class, 'operationalInvoice'])->name('invoices.operation');
    Route::post('operational/invoice/create/{estimateID}', [InvoiceController::class, 'operationalInvoiceCreate'])->name('invoices.operationInvoices');
    Route::get('invoice/download/{invoiceID}', [InvoiceController::class, 'downloadInvoice'])->name('invoices.download');

    // general Settings

    Route::get('general-settings', [Controllers\SettingsController::class, 'generalSettings'])->name('general.settings');
    Route::post('update-detail/{id}', [Controllers\SettingsController::class, 'updateDetail'])->name('general.settings.update');
    Route::get('margin-setup', [Controllers\SettingsController::class, 'marginSetup'])->name('margin_setup');

    // branding routes
    Route::view('branding', 'organization/settings/branding')->name('branding');
    Route::post('save-branding', [Controllers\SettingsController::class, 'storeBranding'])->name('save-branding');
    // settings route
    Route::get('settings', [Controllers\SettingsController::class, 'index'])->name('settings');
});

/* -------Schedule Route ------ */

Route::get('schedule', [Controllers\Organization\ScheduleController::class, 'index'])
    ->name('schedule.index');
Route::get('unschedule-list', [Controllers\Organization\ScheduleController::class, 'listing'])
    ->name('unschedule-list');
Route::post('get-schedule', [Controllers\Organization\ScheduleController::class, 'getSchedule'])
    ->name('get-schedule');
Route::post('schedule-add', [Controllers\Organization\ScheduleController::class, 'addSchedule'])
    ->name('add.schedule');
Route::get('scheduled-estimates', [Controllers\Organization\ScheduleController::class, 'getCalendarEstimates'])
    ->name('scheduled-estimates');
