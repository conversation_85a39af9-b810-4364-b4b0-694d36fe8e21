<?php

use App\Actions\Fortify\CustomAuthenticatedSessionController;
use App\Http\Controllers;
use App\Http\Controllers\ErrorPagesController;
use App\Http\Controllers\HomeController;
use App\Http\Livewire\Register\Index as RegisterIndex;
use App\Http\Middleware\VerifyCsrfToken;
use App\Services\EstimateService;
use App\Webhooks\StripeOrgWebhookController;
use App\Webhooks\StripeWebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::redirect('/', 'login');

// guest routes

Route::get('privacy-policy', [HomeController::class, 'privacyPolicyView'])->name('privacyPolicy');
Route::get('terms-conditions', [HomeController::class, 'termConsitions'])->name('termsConditions');
Route::view('/', 'landing')->name('landingHome');

Route::middleware('guest:web')->group(function () {

    Route::get('check-mail/{email}', [Controllers\AuthController::class, 'checkMail'])->name('checkMail');
    Route::get('resend-mail/{email}', [Controllers\AuthController::class, 'resendPasswordLink'])
        ->name('resend-mail');
    Route::post('find-account', [Controllers\AuthController::class, 'resetPassword'])->name('passwordEmail');
    Route::post('register', [Controllers\AuthController::class, 'register']);
    Route::get('reset-password/{email}/{token}', [Controllers\AuthController::class, 'resetPasswordScreen']);
    Route::post('update-password', [Controllers\AuthController::class, 'updatePassword'])->name('passwordUpdate');
});

Route::middleware('auth:web', 'prevent-back-history')->group(function () {

    Route::get('verify-account/{email}/{token?}', [Controllers\AuthController::class, 'verifyAccount'])->name('verify.account');
    Route::get('register-step1', [Controllers\AuthController::class, 'stepOne'])->name('register.step1');

    Route::get('email/verify', [Controllers\AuthController::class, 'stepOne'])->name('verification.notice');

    Route::get('resend-verification-mail', [Controllers\AuthController::class, 'resendMailVerifyLink'])
        ->name('resend-verify-mail');

    // plans
    Route::get('/plans', RegisterIndex::class)->name('plans');

    // success screen after payment
    Route::get('/payment-success', [Controllers\SubscriptionController::class, 'retreiveSession'])->name('payment.success');

    Route::view('/success-registered', 'register/success')->name('registered.success');
});

Route::post('stripe/webhooks', [StripeWebhookController::class, 'handle'])->withoutMiddleware([VerifyCsrfToken::class]);
Route::post('org/stripe/webhooks', [StripeOrgWebhookController::class, 'handle'])->withoutMiddleware([VerifyCsrfToken::class]);

// require __DIR__ . '/frontend.php';

require __DIR__.'/client.php';

Route::post('logout', [CustomAuthenticatedSessionController::class, 'destroy'])
    ->name('web.logout')->middleware(['auth:sanctum', config('jetstream.auth_session')]);

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', 'prevent-back-history'])->group(function () {

    Route::get('/dashboard', [Controllers\HomeController::class, 'index'])->name('frontend.dashboard');

    // Route::fallback([ErrorPagesController::class,'__invoke']);

    Route::prefix('organization')->as('organization.')->middleware(['auth', 'check.subscription', 'auth.organization'])->group(function () {
        Route::get('get-client-login-url', function () {
            return route('client.login', [encodeID(auth()->user()->id)]);
        });

        require __DIR__.'/organization.php';
    });
    Route::prefix('admin')->as('admin.')->middleware('admin')->group(function () {

        require __DIR__.'/admin.php';
    });
    Route::prefix('employee')->as('employee.')->middleware(['auth', 'check.subscription', 'inactive'])->group(function () {

        // require __DIR__ . '/employee.php';
        require __DIR__.'/organization.php';
    });
});

Route::get('test', function () {

    $pdf = EstimateService::preparePdf(26, 1);
    if (is_array($pdf)) {
        $pdf = $pdf['pdf'];
    }

    return $pdf->stream();
    // info("info",[$estimatePDF]);
    // auth('web')->logout();
    // // $users = \App\Models\User::where('parent_id',0)->where('type',1)->get();
    // $users=\App\Models\StripeAccountDetail::where('id',7)->update([
    //     'acc_connected_id' => 'acct_1NdRYmB2WraMPyAU'
    // ]);

    // return $users;
    return decodeID(49);
    // return App\Models\Estimate::with('media')->find(11);
    // $stripe = new \Stripe\StripeClient('sk_test_51NE7NEGJIIArHMaXI3HVzNRE1a8CnPxaqWJzk1XFsJo8RHhjlRgFmqKYNC1YH2g5E3Je1ttRqXRlplGvRXjq6p9900VEzE3m70');
    // $id = 'sub_1NGe8pGJIIArHMaXpfAjQvZG';
    // $newCurrentPeriodStart = strtotime('2023-05-08');
    // $subscription = $stripe->subscriptions->retrieve(
    //     $id,
    //     []
    // );
    // //  dd($res);

    // // $subscription = \Stripe\Subscription::retrieve($id);

    // $subscription->billing_cycle_anchor = $newCurrentPeriodStart;
    // $subscription->save();
    // dd($subscription);
    // $stripe->subscriptions->update(
    // 'sub_1NGe8pGJIIArHMaXpfAjQvZG',
    // ['metadata' => ['order_id' => '6735']]
    // );
    // return App\Models\Estimate::where('organization_id',2)->max('job_no');
});

// require __DIR__ . '/mvp2/frontend.php';

Route::get('/new-opportunity', function () {
    return view('organization.opportunity.opportunity-estimation');
});
