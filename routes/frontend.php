<?php

use App\Http\Controllers\PDFController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('frontend')->group(function () {
    Route::view('dashboard', 'template/admin/frontend/dashboard')->name('frontend2.dashboard');
    Route::view('login', 'template/admin/frontend/login')->name('frontend2.login');
    Route::view('findaccount', 'template/admin/frontend/find_account')->name('frontend2.findaccount');
    Route::view('check-mail', 'template/admin/frontend/check_mail')->name('frontend2.check-mail');
    Route::view('new-password', 'template/admin/frontend/create_newpassword')->name('frontend2.new-password');

    // Modals Routes Starts
    Route::view('modals', 'template/admin/modals/modals')->name('modals');
    // Modals Routes Ends

    // Super Admin Routes
    Route::view('companies', 'template/admin/frontend/super_admin/companies')->name('frontend2.companies');
    Route::view('packages', 'template/admin/frontend/super_admin/packages')->name('frontend2.packages');
    Route::view('transactions', 'template/admin/frontend/super_admin/transactions')->name('frontend2.transactions');
    // Super Admin Routes

    // Signup Process
    Route::view('signup', 'template/admin/frontend/register/signup')->name('frontend2.signup');
    Route::view('step1-check-mail', 'template/admin/frontend/register/step1_check_mail')->name('frontend2.signup.step1');
    Route::view('step2-packages', 'template/admin/frontend/register/step2_packages')->name('frontend2.signup.step2');
    Route::view('step3-payment', 'template/admin/frontend/register/step3_payment')->name('frontend2.signup.step3');
    Route::view('payment-success', 'template/admin/frontend/register/success')->name('frontend2.payment.success');
    // Signup Process

    // Clients Routes Starts
    Route::view('clients', 'template/admin/frontend/clients/clients')->name('frontend2.clients');
    Route::view('add-client', 'template/admin/frontend/clients/client_add')->name('frontend2.add_client');
    Route::view('client-detail', 'template/admin/frontend/clients/client_detail')->name('frontend2.client_detail');
    // Clients Routes Ends

    // Requests Routes Starts
    Route::view('requests', 'template/admin/frontend/requests/requests')->name('frontend2.requests');
    Route::view('request-detail', 'template/admin/frontend/requests/request_detail')->name('frontend2.request_detail');
    // Requests Routes Ends

    // Estimate Routes Starts
    Route::view('estimate', 'template/admin/frontend/estimate/estimate')->name('frontend2.estimate');
    Route::view('generate-estimate', 'template/admin/frontend/estimate/generate_estimate')->name('frontend2.generate_estimate');
    Route::view('estimate-invoice-detail', 'template/admin/frontend/estimate/estimate_invoice_detail')->name('frontend2.estimate_invoice_detail');
    // Estimate Routes Ends

    // Schedule Routes Starts
    Route::view('schedule', 'template/admin/frontend/schedule/schedule')->name('frontend2.schedule');
    // Schedule Routes Ends

    // Operations Starts
    Route::view('operations', 'template/admin/frontend/operations/operations')->name('frontend2.operations');
    Route::view('operations-detail', 'template/admin/frontend/operations/operations_detail')->name('frontend2.operations_detail');
    // Operations Ends

    // Reports Starts
    Route::view('reports', 'template/admin/frontend/reports/reports')->name('frontend2.reports');
    // Reports Ends

    // ***Settings Routes Starts***

    // General Settings
    Route::view('general-settings', 'template/admin/frontend/settings/general_settings/general_settings')->name('general_settings');
    // General Settings

    // Material Routes
    Route::view('material-items', 'template/admin/frontend/settings/material/items')->name('material_items');
    Route::view('material-equipment', 'template/admin/frontend/settings/material/equipment')->name('material_equipment');
    // Material Routes

    // Account Settings
    Route::view('account-settings', 'template/admin/frontend/settings/account_settings/account_settings')->name('account.settings');
    // Account Settings

    // Users
    Route::view('add-users', 'template/admin/frontend/settings/users/add-user')->name('users.add');
    Route::view('users-list', 'template/admin/frontend/settings/users/users-detail')->name('users.details');
    // Users

    // Estimate Branding
    Route::view('estimate-branding', 'template/admin/frontend/settings/estimate_branding/estimate_branding')->name('estimate_branding');
    // Estimate Branding

    // Company Margin Setup
    Route::view('company-margin-setup', 'template/admin/frontend/settings/company_margin_setup/company_margin_setup')->name('company_margin_setup');
    // Company Margin Setup

    // ***Settings Routes Ends***

    // PDF Routes Starts
    Route::get('estimate-pdf', [PDFController::class, 'generatePDF'])->name('estimate.pdf');
    Route::get('client-pdf-report', [PDFController::class, 'clientReportPdf'])->name('client_report_pdf');
    Route::get('estimate-pdf-report', [PDFController::class, 'estimatePdfReport'])->name('estimate_pdf_report');
    Route::get('operations-pdf-report', [PDFController::class, 'operationsPdfReport'])->name('operations_pdf_report');
    Route::get('sales-pdf-report', [PDFController::class, 'salesReport'])->name('sales_pdf_report');
    Route::get('estimate-request-pdf-report', [PDFController::class, 'estimateResquestPdf'])->name('estimate_request_report');

    Route::get('generate-estimate-pdf', [PDFController::class, 'generateEstimatePDF'])->name('generate_estimate_report');
    // PDF Routes Ends

    Route::view('global', 'template/admin/frontend/global')->name('global');

});
