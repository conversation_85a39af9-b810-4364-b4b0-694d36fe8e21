<?php

use App\Http\Controllers;
use App\Http\Controllers\ErrorPagesController;
use App\Http\Controllers\Organization\ChangePasswordController;
use Illuminate\Support\Facades\Route;

/* -------Index Route ------ */

/* -------Clients Route ------ */

Route::get('change-password', [ChangePasswordController::class, 'changePasswordScreen'])
    ->name('changePasswordScreen');
Route::post('change-password', [ChangePasswordController::class, 'updatePassword'])
    ->name('passwordUpdate');

Route::get('companies', [Controllers\Admin\CompanyController::class, 'index'])->name('company.index');
Route::post('extend-trial/{id}', [Controllers\Admin\CompanyController::class, 'extendTrial'])->name('company.extendTrial');
Route::get('companies/table-list', [Controllers\Admin\CompanyController::class, 'listing'])->name('company.list');

Route::view('packages', 'admin.packages.index')->name('packages.index');

Route::get('transactions', [Controllers\Admin\TransactionController::class, 'index'])->name('transaction.index');
// Route::get('transactions/table-list', [Controllers\Admin\TransactionController::class, 'listing'])->name('transaction.table-list');
// Dashboard Charts
Route::post('lost-chart', [Controllers\DashboardController::class, 'earningsChart'])->name('chartData');
Route::post('wave-chart', [Controllers\DashboardController::class, 'adminWaveChart'])->name('waveChartData');

Route::fallback([ErrorPagesController::class, '__invoke']);
