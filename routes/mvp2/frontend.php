<?php

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'frontend/mvp2'], function () {
    Route::view('dashboard', 'template/admin/frontend/mvp2/dashboard');
    Route::view('/requests', 'template/admin/frontend/mvp2/requests')->name('requests');
    Route::view('/create-request', 'template/admin/frontend/mvp2/create-request')->name('createRequests');
    Route::view('/view-request', 'template/admin/frontend/mvp2/view-request')->name('viewRequests');
    Route::view('/estimate-list', 'template/admin/frontend/mvp2/estimate-list')->name('estimateList');
    Route::view('/estimate-detail', 'template/admin/frontend/mvp2/estimate-detail')->name('estimateDetail');
    Route::view('/view-invoice', 'template/admin/frontend/mvp2/view-invoice')->name('viewInvoice');
    Route::view('/invoice-detail', 'template/admin/frontend/mvp2/invoice-detail')->name('invoiceDetail');

    Route::view('organization/create-invoice', 'template/admin/frontend/mvp2/organization/invoice/create')->name('createInvoice');
    Route::view('organization/client-profile', 'template/admin/frontend/mvp2/organization/invoice/client-profile')->name('clientProfile');
    Route::view('organization/invoice-modal', 'template/admin/frontend/mvp2/organization/invoice/invoice-modal')->name('invoiceModal');
    Route::view('organization/transactions', 'template/admin/frontend/mvp2/organization/invoice/transactions')->name('transactions');
    Route::view('organization/invoice', 'template/admin/frontend/mvp2/organization/invoice/invoice')->name('invoice');
    Route::view('organization/view-invoice', 'template/admin/frontend/mvp2/organization/invoice/view-invoice')->name('viewInvoice');
    Route::view('organization/estimate-doc', 'template/admin/frontend/mvp2/organization/invoice/estimate-doc')->name('estimateDoc');
    Route::view('organization/shedule-detail', 'template/admin/frontend/mvp2/shedule-detail')->name('sheduleDetail');
    // shedule-detail

    Route::view('organization/error_404', 'template/admin/frontend/mvp2/organization/invoice/error_404')->name('error404');
    Route::view('organization/job-summary', 'template/admin/frontend/mvp2/organization/invoice/job-summary')->name('jobSummary');

});
