<?php

use App\Http\Controllers\Client\AuthController;
use App\Http\Controllers\Client\DashboardController;
use App\Http\Controllers\Client\EstimateRequestController;
use App\Http\Controllers\Client\GenerateEstimateController;
use App\Http\Controllers\Client\InvoiceController;
use App\Http\Controllers\Client\TransactionController;
use App\Http\Controllers\ErrorPagesController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Clients Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('client/{organization_id}')->as('client.')->group(function () {

    Route::middleware('client.guest')->group(function () {
        Route::get('login', [AuthController::class, 'loginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login'])->name('login.submit');
        Route::get('forgot-password', [AuthController::class, 'showForgotPassword'])->name('forgot.password');
        Route::post('find-account', [AuthController::class, 'findaccount'])->name('find.account');
        Route::get('check-mail/{email?}', [AuthController::class, 'checkMail'])->name('checkMail');
        Route::get('resend-mail/{email?}/', [AuthController::class, 'resendPasswordLink'])->name('resend-mail');
        Route::get('reset-password/{email}/{token}', [AuthController::class, 'resetPasswordScreen'])->name('reset-password');
        Route::post('update-password', [AuthController::class, 'updatePassword'])->name('passwordUpdate');
    });

    Route::middleware('auth:client', 'check_org', 'prevent-back-history')->group(function () {
        Route::get('dashboard', [DashboardController::class, 'dashboard'])->name('dashboard');
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::post('update-profile', [AuthController::class, 'updateProfile'])->name('updateProfile');

        // Invoice Routes
        Route::get('invoice/list', [InvoiceController::class, 'index'])->name('invoice.list');
        Route::get('invoice/view/{id}', [InvoiceController::class, 'view'])->name('invoice.view');
        Route::get('invoice/pay/{id}', [InvoiceController::class, 'paymentScreen'])->name('invoice.payment.screen');
        Route::post('invoice/pay/{id}', [InvoiceController::class, 'invoicePay'])->name('invoice.pay');
        Route::get('invoice/payment-success', [InvoiceController::class, 'checkInvoicePaymentSession'])->name('invoice.pay.success');
        Route::get('invoice/download/{invoiceID}', [InvoiceController::class, 'downloadInvoice'])->name('invoices.download');

        // Transaction
        Route::get('transactions', [TransactionController::class, 'transactions'])->name('transactions');

        Route::prefix('estimate')->as('estimates.')->group(function () {
            Route::get('list/', [GenerateEstimateController::class, 'index'])->name('index');
            Route::get('view/{id}', [GenerateEstimateController::class, 'view'])->name('view');
            Route::get('download/{id}', [GenerateEstimateController::class, 'downloadEstimate'])->name('download');
            Route::POST('approval/{id}', [GenerateEstimateController::class, 'approveEstimate'])->name('approval');
            Route::POST('reject/{id}', [GenerateEstimateController::class, 'rejectEstimate'])->name('reject');
            Route::POST('change/request/{id}', [GenerateEstimateController::class, 'changeRequestEstimate'])->name('request_change');

            Route::get('/requests', [EstimateRequestController::class, 'index'])->name('requests.index');
            Route::get('/requests/create', [EstimateRequestController::class, 'create'])->name('requests.create');
            Route::post('/requests/store-estimate', [EstimateRequestController::class, 'store'])->name('requests.store');
            Route::post('/requests/update-estimate/{id}', [EstimateRequestController::class, 'update'])->name('requests.update');
            Route::get('/requests/view/{id}', [EstimateRequestController::class, 'view'])->name('requests.view');
            Route::get('/requests/edit/{id}', [EstimateRequestController::class, 'edit'])->name('requests.edit');
            Route::get('/requests/delete/{id}', [EstimateRequestController::class, 'delete'])->name('requests.delete');
            Route::post('/requests/file', [EstimateRequestController::class, 'imageStore'])->name('requests.file-store');
            Route::post('/requests/file-delete', [EstimateRequestController::class, 'imageDelete'])->name('requests.file-delete');
            Route::get('estimate-documents/{id}', [EstimateRequestController::class, 'getDocuments'])
                ->name('requests.documents');
            Route::get('/client/address/{id}', [EstimateRequestController::class, 'clientAddress'])->name('requests.client_address');
        });
    });

    Route::fallback([ErrorPagesController::class, '__invoke']);
});
