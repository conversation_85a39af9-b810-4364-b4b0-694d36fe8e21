<?php

use App\Http\Controllers;
use App\Http\Controllers\ErrorPagesController;
use App\Http\Controllers\Organization\ChangePasswordController;
use App\Http\Controllers\Organization\ClientController;
use App\Http\Controllers\Organization\EmployeeController;
use App\Http\Controllers\Organization\FinancialToolController;
use App\Http\Controllers\Organization\GenerateEstimateController;
use App\Http\Controllers\Organization\InvoiceController;
use App\Http\Controllers\Organization\OperationController;
use App\Http\Controllers\Organization\OpportunityController;
use App\Http\Controllers\Organization\StripeController;
use App\Http\Controllers\Organization\TaskController;
use App\Http\Controllers\Organization\TransactionController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Middleware\OrganizationMiddleware;
use Illuminate\Support\Facades\Route;

/* -------Index Route ------ */

/* ---- Change password routes --- */

Route::get('change-password', [ChangePasswordController::class, 'changePasswordScreen'])
    ->name('changePasswordScreen');
Route::post('change-password', [ChangePasswordController::class, 'updatePassword'])
    ->name('passwordUpdate');

/* -------Clients Route ------ */

Route::get('client', [Controllers\Organization\ClientController::class, 'index'])
    ->name('client.index');
// Route::get('client/table-list', [Controllers\Organization\ClientController::class, 'listing'])
//     ->name('client.table-list');
Route::get('create-client', [Controllers\Organization\ClientController::class, 'create'])
    ->name('create.client');
Route::post('store-client', [Controllers\Organization\ClientController::class, 'store'])
    ->name('client.store');
Route::put('update-client/{id}', [Controllers\Organization\ClientController::class, 'update'])
    ->name('client.update');
Route::get('client-detail/{id}', [Controllers\Organization\ClientController::class, 'detail'])
    ->name('client.detail');
Route::get('edit-client/{id}', [Controllers\Organization\ClientController::class, 'edit'])
    ->name('client.edit');
Route::get('client-estimates-history/{id}', [Controllers\Organization\ClientController::class, 'clientEstimatesHistory'])
    ->name('client.estimates');

Route::controller(ClientController::class)->group(function () {

    Route::post('client/send-mail', 'sendEmailToClient')->name('send-mail.client');
});

Route::post('clients-import', [Controllers\Organization\ClientController::class, 'clientFileImport'])->name('client.file-import');

/* -------Employees Route ------ */

Route::get('employees', [EmployeeController::class, 'index'])->name('employee.index');
Route::get('employees/listing', [EmployeeController::class, 'listing'])->name('employee.listing');
Route::post('employees', [EmployeeController::class, 'store'])->name('employee.store');
Route::get('employees/{id}/edit', [EmployeeController::class, 'edit'])->name('employee.edit');
Route::put('employees/{id}', [EmployeeController::class, 'update'])->name('employee.update');
Route::post('employee/check-employee-number', [EmployeeController::class, 'checkEmployeeNumber'])->name('employee.checkEmployeeNumber');
// In routes/web.php or routes/api.php
Route::post('/employee/generate-number', [EmployeeController::class, 'generateEmployeeNumber'])->name('employee.generateEmployeeNumber');

Route::delete('employees/{id}', [EmployeeController::class, 'destroy'])->name('employee.destroy');

/* -------Opportunities Route ------ */

Route::controller(OpportunityController::class)->group(function () {
    Route::get('opportunity', 'index')->name('opportunity.index');
    Route::get('create-opportunity', 'create')->name('create.opportunity');
    Route::post('store-opportunity', 'store')->name('opportunity.store');
    Route::get('opportunity-detail/{id}', 'detail')->name('opportunity.detail');
    Route::post('/opportunities/{id}/update-status', 'updateStatus')->name('opportunitiesUpdateStatus');
    Route::post('/opportunities/{id}/simulate-client-request-change', 'simulateClientRequestChange')->name('opportunities.simulateClientRequestChange');
    Route::get('opportunityestimation/{id}', 'OpportunityEstimation')->name('organization.opportunityestimation');

    Route::get('edit-opportunity/{opportunity_id}', 'edit')->name('edit.opportunity');
    Route::post('update-opportunity/{id}', 'update')->name('opportunity.update');

    Route::get('preview/{opportunityId}', 'preview')->name('organization.preview');

    // code for snow estimation
});

Route::get('estimation/search-item', [Controllers\Organization\OpportunityController::class, 'searchItems'])
    ->name('estimation.search-item');

Route::post('add/opportunity/deal/notes', [Controllers\Organization\OpportunityController::class, 'addDealNotes'])
    ->name('add.opportunity.deal.notes');

Route::post('opportunity/file_name/update', [Controllers\Organization\OpportunityController::class, 'updateFileName'])
    ->name('opportunity.update_file_name');

Route::post('opportunity/file', [Controllers\Organization\OpportunityController::class, 'imageStore'])
    ->name('opportunity.file-store');

Route::post('opportunity/send/email/file', [Controllers\Organization\OpportunityController::class, 'imageStoreEmail'])
    ->name('send.emails.file-store');

Route::post('delete/send/email/file-delete', [Controllers\Organization\OpportunityController::class, 'imageDeleteEmail'])
    ->name('delete.send.email.file-delete');

Route::post('opportunity/file-delete', [Controllers\Organization\OpportunityController::class, 'imageDelete'])
    ->name('opportunity.file-delete');

Route::post('opportunity/file-delete-server', [Controllers\Organization\OpportunityController::class, 'imageDeleteServer'])
    ->name('opportunity.file-delete-server');

Route::post('update-labor-item-name', [Controllers\Organization\OpportunityController::class, 'updateLaborItemName'])
    ->name('update.estimate.item.name');

Route::get('/get-property-details/{property_id}', [Controllers\Organization\OpportunityController::class, 'getPropertyDetails'])
    ->name('get-property-details');
Route::get('/get-contact-data-details/{contact_id}', [Controllers\Organization\OpportunityController::class, 'getContactDataDetails'])
    ->name('get-contact-data-details');

Route::post('update/propertyHealth', [Controllers\Organization\PropertyController::class, 'updatePropertyHealth'])
    ->name('update.propertyHealth');

Route::post('update/company/notes', [Controllers\Organization\PropertyController::class, 'saveCompanyNotes'])
    ->name('save.company.notes');

Route::get('/get-contact-details/{contact_id}', [Controllers\Organization\OpportunityController::class, 'getContactDetails'])
    ->name('get-contact-details');

Route::post('opportunity-document-delete/{id}', [Controllers\Organization\OpportunityController::class, 'docDelete'])
    ->name('document.delete.opportunity');

// sheraz code

Route::get('opportunity-estimation/{id}', [Controllers\Organization\OpportunityController::class, 'OpportunityEstimationNew'])->name('organization.opportunity-estimation');
Route::get('getdivisiondata', [Controllers\Organization\OpportunityController::class, 'getDivisionData'])->name('division.getdata');
Route::get('getDataid', [Controllers\Organization\OpportunityController::class, 'getDataId'])->name('division.getDataid');
Route::get('getDataHard', [Controllers\Organization\OpportunityController::class, 'getDataHard'])->name('division.getDataHard');
Route::get('getDataPlant', [Controllers\Organization\OpportunityController::class, 'getDataPlant'])->name('division.getDataPlant');
Route::get('getDataCost', [Controllers\Organization\OpportunityController::class, 'getDataCost'])->name('division.getDataCost');
Route::get('material-labor', [Controllers\Organization\MaterialController::class, 'labors'])
    ->name('material.labor.index');
Route::post('labor-file-import', [Controllers\Organization\MaterialController::class, 'laborFileImport'])->name('labor.file-import');
Route::get('material-labor/table-list', [Controllers\Organization\MaterialController::class, 'laborListing'])
    ->name('material.labor.table-list');
Route::get('getDataLabor', [Controllers\Organization\OpportunityController::class, 'getDataLabor'])->name('division.getDataLabor');

Route::post('deleteDataestimateItem', [Controllers\Organization\OpportunityController::class, 'deleteDataItem'])->name('estimateitems.delete');
Route::get('material-contractor', [Controllers\Organization\MaterialController::class, 'contractors'])->name('material.contractor.index');
Route::post('subcontractor-file-import', [Controllers\Organization\MaterialController::class, 'subcontractorFileImport'])->name('subcontractor.file-import');
Route::get('material-contractor/table-list', [Controllers\Organization\MaterialController::class, 'contractorListing'])->name('material.contractor.table-list');
Route::get('get-material-uom/{type}', [Controllers\Organization\MaterialController::class, 'getMaterialUOM'])->name('material.uom');

Route::get('getDataContractor', [Controllers\Organization\OpportunityController::class, 'getDataContractor'])->name('division.getDataContractor');
Route::post('storeDefaultSettings', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettings'])->name('storeDefaultSettings');
Route::post('storeDefaultSettingsAbout', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsAbout'])->name('storeDefaultSettingsAbout');

Route::post('changeCoverToggle', [Controllers\Organization\OpportunityController::class, 'changeCoverToggle'])->name('changeCoverToggle');

Route::post('changeAboutToggle', [Controllers\Organization\OpportunityController::class, 'changeAboutToggle'])->name('changeAboutToggle');

Route::post('changeIntroToggle', [Controllers\Organization\OpportunityController::class, 'changeIntroToggle'])->name('changeIntroToggle');

Route::post('changeScopeToggle', [Controllers\Organization\OpportunityController::class, 'changeScopeToggle'])->name('changeScopeToggle');

Route::post('changeTermsToggle', [Controllers\Organization\OpportunityController::class, 'changeTermsToggle'])->name('changeTermsToggle');

Route::post('changePaymentsToggle', [Controllers\Organization\OpportunityController::class, 'changePaymentsToggle'])->name('changePaymentsToggle');

Route::post('changeGalleryToggle', [Controllers\Organization\OpportunityController::class, 'changeGalleryToggle'])->name('changeGalleryToggle');

Route::post('storeDefaultSettingsPayment', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsPayment'])->name('storeDefaultSettingsPayment');
Route::get('delete/template/{id}', [Controllers\Organization\OpportunityController::class, 'deleteTemplate'])->name('deleteTemplate');

Route::post('storeDefaultSettingsIntro', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsIntro'])->name('storeDefaultSettingsIntro');

Route::post('storeDefaultSettingsGallery', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsGallery'])->name('storeDefaultSettingsGallery');

Route::post('gallery/delete', [Controllers\Organization\OpportunityController::class, 'deleteImageFromGallery'])->name('gallery.delete');

Route::post('image/upload/template', [Controllers\Organization\OpportunityController::class, 'uploadtemplate'])->name('image.upload.template');

Route::post('storeDefaultSettingsTerms', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsTerms'])->name('storeDefaultSettingsTerms');
Route::post('storeDefaultSettingsScope', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsScope'])->name('storeDefaultSettingsScope');
Route::post('storeDefaultSettingsLogo', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsLogo'])->name('storeDefaultSettingsLogo');

Route::get('storeDefaultSettingsLogoDelete', [Controllers\Organization\OpportunityController::class, 'storeDefaultSettingsLogoDelete'])->name('storeDefaultSettingsLogoDelete');

Route::get('contact-list', [Controllers\Organization\ContactController::class, 'contactList'])->name('contactList');
Route::get('accounts', [Controllers\Organization\AccountController::class, 'index'])->name('accounts');
Route::get('properties', [Controllers\Organization\PropertyController::class, 'index'])->name('properties');
Route::post('add-property', [Controllers\Organization\PropertyController::class, 'create'])->name('property.add');
Route::get('properties/data', [Controllers\Organization\PropertyController::class, 'getData'])->name('properties.data');
Route::get('property-details/{id}', [Controllers\Organization\PropertyController::class, 'propertyDetails'])
    ->name('property.detail');
Route::get('property/edit', [Controllers\Organization\PropertyController::class, 'edit'])->name('property.edit');
Route::post('property/update', [Controllers\Organization\PropertyController::class, 'update'])->name('property.update');
Route::post('property-file-import', [Controllers\Organization\PropertyController::class, 'propertyFileImport'])->name('property.file-import');
Route::post('opportunity-file-upload/{opportunity_id}', [Controllers\Organization\OpportunityController::class, 'storeDocument'])->name('opportunity.file-upload');

Route::post('property-file-upload/{prop_id}', [Controllers\Organization\PropertyController::class, 'storeDocument'])->name('property.file-upload');

Route::post('property-file-store', [Controllers\Organization\PropertyController::class, 'storeFiles'])->name('property.file-store');

Route::post('property-file-delete', [Controllers\Organization\PropertyController::class, 'deleteFiles'])->name('property.file-delete');

Route::post('properties/export', [Controllers\Organization\PropertyController::class, 'exportProperties'])->name('export.properties');
Route::delete('property/delete/{id}', [Controllers\Organization\PropertyController::class, 'delete'])->name('property.delete');
Route::post('add-account', [Controllers\Organization\AccountController::class, 'storeAccount'])->name('account.add');
Route::get('accounts/data', [Controllers\Organization\AccountController::class, 'getData'])->name('accounts.data');
Route::post('add-contact', [Controllers\Organization\ContactController::class, 'storeContact'])->name('contact.add');
Route::get('contacts/data', [Controllers\Organization\ContactController::class, 'getData'])->name('contacts.data');
Route::get('contacts/edit', [Controllers\Organization\ContactController::class, 'edit'])->name('contacts.edit');
Route::post('contacts/update', [Controllers\Organization\ContactController::class, 'updateContact'])->name('contacts.update');
Route::delete('contacts/delete/{id}', [Controllers\Organization\ContactController::class, 'delete'])->name('contacts.delete');
Route::post('contact-file-import', [Controllers\Organization\ContactController::class, 'contactFileImport'])->name('contact.file-import');
Route::post('contacts/export', [Controllers\Organization\ContactController::class, 'exportContacts'])->name('export.contacts');
Route::post('operations/export', [Controllers\Organization\OperationController::class, 'exportOperations'])->name('export.operations');
Route::get('accounts/edit', [Controllers\Organization\AccountController::class, 'edit'])->name('accounts.edit');
Route::post('accounts/update', [Controllers\Organization\AccountController::class, 'updateAccount'])->name('accounts.update');
Route::post('account-file-import', [Controllers\Organization\AccountController::class, 'accountFileImport'])->name('account.file-import');
Route::post('accounts/export', [Controllers\Organization\AccountController::class, 'exportAccounts'])->name('export.accounts');
Route::delete('account/delete/{id}', [Controllers\Organization\AccountController::class, 'delete'])->name('account.delete');
Route::post('accounts/save', [Controllers\Organization\AccountController::class, 'saveAccount'])->name('save-account-details');
Route::get('addNewItem', [Controllers\Organization\OpportunityController::class, 'addNewItem'])->name('division.addNewItem');
Route::get('addFavItem', [Controllers\Organization\OpportunityController::class, 'addFavItem'])->name('division.addFavItem');
Route::get('deleteFavItem', [Controllers\Organization\OpportunityController::class, 'deleteFavItem'])->name('division.deleteFavItem');
Route::get('getDivisionDataFav', [Controllers\Organization\OpportunityController::class, 'getDivisionDataFav'])->name('division.getdataFavItem');
Route::get('send-to-client/{opportunityId}', [Controllers\Organization\OpportunityController::class, 'previewEstimation'])->name('organization.previewEstimation');
Route::post('/estimate-items/{id}/update-dimensions', [Controllers\Organization\OpportunityController::class, 'updateDimensions'])->name('estimate-items.update-dimensions');
Route::post('/update-estimate-item-margin', [Controllers\Organization\OpportunityController::class, 'updateEstimateItemsMargin'])->name('update-estimate-item-margin');

Route::get('/estimation/download-pdf/{opportunityId}', [GenerateEstimateController::class, 'downloadEstimationPDF'])->name('estimation.download-pdf');

Route::get('downloadpdf/{opportunityId}', [Controllers\Organization\OpportunityController::class, 'downloadPdf'])->name('organization.downloadpdf');

Route::get('edit-opportunity/{id}', [Controllers\Organization\OpportunityController::class, 'edit'])
    ->name('opportunity.edit');

Route::get('delete-opportunity/{id}', [Controllers\Organization\OpportunityController::class, 'delete'])
    ->name('opportunity.delete');

Route::get('opportunity-estimates-history/{id}', [Controllers\Organization\OpportunityController::class, 'opportunityEstimatesHistory'])
    ->name('opportunity.estimates');

Route::post('opportunity/send-mail', [Controllers\Organization\OpportunityController::class, 'sendEmailToOpportunity'])
    ->name('send-mail.opportunity');

Route::post('opportunities-import', [Controllers\Organization\OpportunityController::class, 'opportunityFileImport'])
    ->name('opportunity.file-import');

Route::get('/get-service-lines/{division_id}', [OpportunityController::class, 'getServiceLines'])->name('get-service-lines');
Route::post('proposals', [OpportunityController::class, 'storeOrUpdateProposalDetail'])->name('proposals.store');
Route::post('/opportunities/{id}/save-final-status', [OpportunityController::class, 'saveFinalStatus'])->name('save-final-status');
Route::post('opportunities/export', [OpportunityController::class, 'exportOpportunities'])->name('export.opportunities');
Route::post('opportunities/import', [OpportunityController::class, 'importOpportunities'])->name('import.opportunities');

/* -------Customer Issues Route ------ */

Route::get('customer-issues', [Controllers\Organization\CustomerIssueController::class, 'index'])
    ->name('customer-issues.index');

Route::get('create-customer-issue', [Controllers\Organization\CustomerIssueController::class, 'create'])
    ->name('create.customer-issue');

Route::post('store-customer-issue', [Controllers\Organization\CustomerIssueController::class, 'store'])
    ->name('customer-issue.store');

Route::post('update-customer-issue-fields/{id}', [Controllers\Organization\CustomerIssueController::class, 'updateCustomerIssue'])
    ->name('update.customer.issue');

Route::post('issue/file-delete-server', [Controllers\Organization\CustomerIssueController::class, 'imageDeleteServer'])
    ->name('issue.file-delete-server');

Route::put('update-customer-issue/{id}', [Controllers\Organization\CustomerIssueController::class, 'update'])
    ->name('customer-issue.update');

Route::post('issues/{id}/update-status', [Controllers\Organization\CustomerIssueController::class, 'updateStatus'])->name('issuesUpdateStatus');

Route::get('customer-issue/{id}/detail', [Controllers\Organization\CustomerIssueController::class, 'detail'])
    ->name('customer-issue.detail');

Route::get('edit-customer-issue/{id}', [Controllers\Organization\CustomerIssueController::class, 'edit'])
    ->name('customer-issue.edit');

Route::get('delete-customer-issue/{id}', [Controllers\Organization\CustomerIssueController::class, 'delete'])
    ->name('customer-issue.delete');

Route::post('/document/issue/delete/{id}', [Controllers\Organization\CustomerIssueController::class, 'deleteDocument'])->name('document.delete.issue');
Route::post('issue-file-store', [Controllers\Organization\CustomerIssueController::class, 'storeFiles'])->name('issue.file-store');

Route::post('issue-file-delete', [Controllers\Organization\CustomerIssueController::class, 'deleteFiles'])->name('issue.file-delete');

Route::post('customer-issues-import', [Controllers\Organization\CustomerIssueController::class, 'customerIssueFileImport'])
    ->name('customer-issue.file-import');

Route::post('customer-issue/send-mail', [Controllers\Organization\CustomerIssueController::class, 'sendEmailToCustomerIssue'])
    ->name('send-mail.customer-issue');
Route::post('issues/export', [Controllers\Organization\CustomerIssueController::class, 'exportIssues'])->name('export.issues');

/* -------Estimate Requests Route ------ */

Route::get('estimate', [Controllers\Organization\EstimateController::class, 'index'])
    ->name('estimate.index');
Route::get('estimate/table-list', [Controllers\Organization\EstimateController::class, 'listing'])
    ->name('estimate.table-list');
Route::get('create-estimate/{id}', [Controllers\Organization\EstimateController::class, 'create'])
    ->name('create.estimate');
Route::post('store-estimate', [Controllers\Organization\EstimateController::class, 'store'])
    ->name('estimate.store');
Route::put('update-estimate/{id}', [Controllers\Organization\EstimateController::class, 'update'])
    ->name('estimate.update');
Route::get('estimate-detail/{id}', [Controllers\Organization\EstimateController::class, 'detail'])
    ->name('estimate.detail');
Route::get('edit-estimate/{id}', [Controllers\Organization\EstimateController::class, 'edit'])
    ->name('estimate.edit');
Route::get('estimate-documents/{id}', [Controllers\Organization\EstimateController::class, 'getDocuments'])
    ->name('estimate.documents');
Route::delete('delete-estimate/{id}', [Controllers\Organization\EstimateController::class, 'delete'])
    ->name('estimate.delete');

/* -------Generate Estimates Route ------ */

Route::get('estimate-generate', [Controllers\Organization\GenerateEstimateController::class, 'index'])
    ->name('estimate-generate.index');

Route::get('archive-estimate-generate', [Controllers\Organization\GenerateEstimateController::class, 'archiveEstimates'])
    ->name('archive-estimate-generate.archive');

Route::get('estimate-generate/table-list', [Controllers\Organization\GenerateEstimateController::class, 'listing'])
    ->name('generate-estimate.table-list');
Route::get('estimate-generate/archive-list', [Controllers\Organization\GenerateEstimateController::class, 'archiveListing'])
    ->name('generate-estimate.archive-list');
Route::get('create-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'create'])
    ->name('create.estimate-generate');
Route::match(['get', 'post'], 'download-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'downloadEstimate'])
    ->name('estimate-generate.download');
Route::get('only-download-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'onlyDownloadEstimate'])
    ->name('estimate-generate.only-download');
Route::get('store-estimate-generate/{id}/', [Controllers\Organization\GenerateEstimateController::class, 'storeEstimate'])
    ->name('estimate-generate.store');
Route::put('update-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'update'])
    ->name('estimate-generate.update');
Route::get('estimate-generate-detail/{id}', [Controllers\Organization\GenerateEstimateController::class, 'detail'])
    ->name('estimate-generate.detail');
Route::get('edit-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'edit'])
    ->name('estimate-generate.edit');
Route::delete('delete-estimate-generate/{id}', [Controllers\Organization\GenerateEstimateController::class, 'delete'])
    ->name('estimate-generate.delete');
Route::post('update-estimate-generate-status', [Controllers\Organization\GenerateEstimateController::class, 'updateStatus'])
    ->name('update-estimate-generate-status');
Route::get('estimate-view-change-request/{id}', [Controllers\Organization\GenerateEstimateController::class, 'viewChangeRequest'])
    ->name('estimate.view-change-request');

Route::post('estimate-add-item', [Controllers\Organization\GenerateEstimateController::class, 'addEstimateItem'])->name('estimate.add-item');

Route::post('/estimate/add-items-batch', [Controllers\Organization\GenerateEstimateController::class, 'addItemsBatch'])->name('estimate.add-items-batch');

Route::get('/estimate/get-hard-item-details/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateItemById'])->name('estimate.get-estimate-item-by-id');
Route::post('/estimate/hard-item-update', [Controllers\Organization\GenerateEstimateController::class, 'updateEstimateHardItem'])->name('estimate.hard-item-update');

Route::post('/estimate/update-items-batch', [Controllers\Organization\GenerateEstimateController::class, 'updateItemsBatch'])->name('estimate.update-items-batch');

Route::get('/estimate/get-estimate-items', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateItems'])->name('estimate.get-items');

// routes for generate estimate 1st step
Route::post('estimate-add-equipment', [Controllers\Organization\GenerateEstimateController::class, 'addEquipment'])
    ->name('estimate.add-equipment');
Route::get('get-estimate-equipment/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateEquipment'])
    ->name('get-estimate-equipment');
Route::get('estimate-equipment-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateEquipment'])
    ->name('estimate.equipment.delete');

// routes for generate estimate 2nd step
Route::post('estimate-add-labor', [Controllers\Organization\GenerateEstimateController::class, 'addLabor'])
    ->name('estimate.add-labor');
Route::get('get-estimate-labor/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateLabor'])
    ->name('get-estimate-labor');
Route::get('estimate-labor-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateLabor'])
    ->name('estimate.labor.delete');

// routes for generate estimate 3rd step
Route::post('estimate-add-material', [Controllers\Organization\GenerateEstimateController::class, 'addMaterial'])
    ->name('estimate.add-material');
Route::get('get-estimate-plant-material/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimatePlantMaterial'])
    ->name('get-estimate-plant-material');
Route::get('get-estimate-hard-material/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateHardMaterial'])
    ->name('get-estimate-hard-material');
Route::get('estimate-material-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateMaterial'])
    ->name('estimate.material.delete');

// routes for generate estimate 4th step
Route::post('estimate-add-other-cost', [Controllers\Organization\GenerateEstimateController::class, 'addOtherCost'])
    ->name('estimate.add-other-cost');
Route::get('get-estimate-other-cost/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateOtherCost'])
    ->name('get-estimate-other-cost');
Route::get('estimate-other-cost-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateOtherCost'])
    ->name('estimate.other.cost.delete');

// routes for generate estimate 5th step
Route::post('estimate-add-sub-contractor', [Controllers\Organization\GenerateEstimateController::class, 'addSubContractor'])
    ->name('estimate.add-sub-contractor');
Route::get('get-estimate-sub-contractor/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateSubContractor'])
    ->name('get-estimate-sub-contractor');
Route::get('estimate-sub-contractor-delete/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteEstimateSubContractor'])
    ->name('estimate.subcontractor.delete');

// routes for estimate invoice detail

Route::get('get-estimate-invoice-deatil/{id}', [Controllers\Organization\GenerateEstimateController::class, 'getEstimateInvoiceDetail'])
    ->name('estimate.invoice-detail');
Route::post('add-note/{id}', [Controllers\Organization\GenerateEstimateController::class, 'addNote'])
    ->name('add-note');
Route::delete('delete-note/{id}', [Controllers\Organization\GenerateEstimateController::class, 'deleteNote'])
    ->name('delete-note');

// routes for estimate desire margin
Route::post('add-desire-margin/{id}', [Controllers\Organization\GenerateEstimateController::class, 'addDesireMargin'])
    ->name('add-desire-margin');

Route::post('add-work-type', [Controllers\Organization\GenerateEstimateController::class, 'addWorkTypeAndService'])
    ->name('add-work-type');

// estimate imge or files route
Route::post('estimate/file', [Controllers\Organization\EstimateController::class, 'imageStore'])
    ->name('estimate.file-store');
Route::post('estimate/file-delete', [Controllers\Organization\EstimateController::class, 'imageDelete'])
    ->name('estimate.file-delete');

// Route for view notes
Route::get('estimate/notes/{id}', [Controllers\Organization\GenerateEstimateController::class, 'notesView'])
    ->name('estimate.notes-view');

// Archive Estimate
Route::POST('estimate/archive/{id}', [Controllers\Organization\GenerateEstimateController::class, 'archiveEstimate'])
    ->name('estimate.archive');

// route for hard and plant material import
Route::get('materials', [Controllers\Organization\MaterialController::class, 'index'])
    ->name('materials.index');
Route::get('material/table-list', [Controllers\Organization\MaterialController::class, 'hardMaterialListing'])
    ->name('material.table-list');
Route::get('plant_material/table-list', [Controllers\Organization\MaterialController::class, 'plantListing'])
    ->name('plant_material.table-list');
Route::get('material/get-filters/{key}', [Controllers\Organization\MaterialController::class, 'getFilters'])
    ->name('material.get-filters');
Route::post('file-import', [Controllers\Organization\MaterialController::class, 'fileImport'])->name('materials.file-import');
Route::post('plant-file-import', [Controllers\Organization\MaterialController::class, 'plantFileImport'])->name('plant-materials.file-import');
Route::post('/materials/upload-image', [Controllers\Organization\MaterialController::class, 'hardMaterialImageUpload'])->name('hard-material-image-upload');
Route::delete('/materials/delete-image/{id}', [Controllers\Organization\MaterialController::class, 'deleteImage'])->name('hard-material-image-delete');

// route for material equipment export
Route::get('material-equipment', [Controllers\Organization\MaterialController::class, 'show'])
    ->name('material.equipment.index');
Route::get('material-equipment/table-list', [Controllers\Organization\MaterialController::class, 'equipmentListing'])
    ->name('material.equipment.table-list');
Route::post('equipment-file-import', [Controllers\Organization\MaterialController::class, 'equipmentFileImport'])->name('equipment.file-import');
// route for material jobCost export
Route::get('material-jobCost', [Controllers\Organization\MaterialController::class, 'showJobCost'])
    ->name('material.jobCost.index');
Route::get('material-jobCost/table-list', [Controllers\Organization\MaterialController::class, 'jobCostListing'])
    ->name('material.jobCost.table-list');
Route::post('jobCost-file-import', [Controllers\Organization\MaterialController::class, 'jobCostFileImport'])->name('jobCost.file-import');

// settings route
Route::get('material-settings', [Controllers\SettingsController::class, 'materials'])->name('material.settings');

Route::get('company/default/settings', [Controllers\SettingsController::class, 'defaultSettings'])->name('company.default.settings');

Route::post('company/default/settings', [Controllers\SettingsController::class, 'storeDefaultSettingsAbout'])->name('company.default.settings');
Route::post('company/default/intro/settings', [Controllers\SettingsController::class, 'storeDefaultSettingsIntro'])->name('company.default.intro.settings');

Route::post('company/default/terms/settings', [Controllers\SettingsController::class, 'storeDefaultSettingsTerms'])->name('company.default.terms.settings');

Route::post('company/default/payment/settings', [Controllers\SettingsController::class, 'storeDefaultSettingsPayment'])->name('company.default.payment.settings');

Route::post('company/default/cover/settings', [Controllers\SettingsController::class, 'storeDefaultSettings'])->name('company.default.cover.settings');
Route::post('company/default/about/settings', [Controllers\SettingsController::class, 'storeDefaultSettingForAboutUs'])->name('company.default.about.settings');

Route::controller(Controllers\SettingsController::class)->group(function () {
    Route::post('update/company/default/settings/opportunity', 'updateDefaultSettingForOpportunity')->name('update.company.default.settings.opportunity');

});

Route::get('service-line', [Controllers\Organization\MaterialController::class, 'serviceLinePageShow'])
    ->name('material.service-line.index');

Route::get('service-line/listing', [Controllers\Organization\MaterialController::class, 'serviceLineMaterialListing'])
    ->name('material.service-line.table-list');

Route::get('service-line/pluck', [Controllers\Organization\MaterialController::class, 'serviceLineMaterialPluck'])
    ->name('material.service-line.pluck-list');
Route::post('service-line/import', [Controllers\Organization\MaterialController::class, 'serviceLineFileImport'])
    ->name('material.service-line.import');

Route::post('service-line/work-type/import', [Controllers\Organization\MaterialController::class, 'workTypeFileImport'])
    ->name('material.service-line.work-type.import');

Route::get('service-line/work-type', [Controllers\Organization\MaterialController::class, 'workTypeServiceLineListing'])
    ->name('material.work-type.table-list');

/* -------Schedule Route ------ */

Route::get('schedule', [Controllers\Organization\ScheduleController::class, 'index'])
    ->name('schedule.index');
Route::get('unschedule-list', [Controllers\Organization\ScheduleController::class, 'listing'])
    ->name('unschedule-list');
Route::post('get-schedule', [Controllers\Organization\ScheduleController::class, 'getSchedule'])
    ->name('get-schedule');
Route::post('schedule-add', [Controllers\Organization\ScheduleController::class, 'addSchedule'])
    ->name('add.schedule');
Route::get('scheduled-estimates', [Controllers\Organization\ScheduleController::class, 'getCalendarEstimates'])
    ->name('scheduled-estimates');

Route::post('/save-special-notes', [Controllers\Organization\ScheduleController::class, 'saveSpecialNotes'])->name('save.special.notes');

Route::get('get-scheduled-estimate-detail/{id}', [Controllers\Organization\ScheduleController::class, 'getScheduleEstimateDetail'])
    ->name('get-scheduled-estimate-detail');
Route::post('save-schedule-estimate-job-pack/{id}', [Controllers\Organization\ScheduleController::class, 'saveJobPackSchedule'])
    ->name('save-schedule-estimate-job-pack');
Route::get('job-pack/download-pdf/{id}', [Controllers\Organization\ScheduleController::class, 'downloadJobPackPdf'])
    ->name('job-pack.pdf-download');

// Users
Route::get('users-list', [Controllers\Organization\UserController::class, 'index'])->name('users.list');
Route::get('invite-users', [Controllers\Organization\UserController::class, 'create'])->name('users.invite');
Route::post('add-users', [Controllers\Organization\UserController::class, 'store'])->name('users.add');
Route::get('edit-user/{id}', [Controllers\Organization\UserController::class, 'edit'])
    ->name('users.edit');
Route::put('update-user/{id}', [Controllers\Organization\UserController::class, 'update'])
    ->name('user.update');
Route::delete('delete-user/{id}', [Controllers\Organization\UserController::class, 'delete'])
    ->name('user.delete');
Route::post('update-user-status', [Controllers\Organization\UserController::class, 'updateStatus'])
    ->name('update-user-status');

/* -------Operation Route ------ */

Route::controller(OperationController::class)->group(function () {
    Route::get('operation', 'index')->name('operation.index');
    Route::get('operation-list', 'listing')->name('operation-list');
    Route::post('update-operation-status', 'updateStatus')->name('update-operation-status');
    Route::post('assign-manager', 'assignManager')->name('assignManager');
    Route::post('getManagers', 'getManagers')->name('getManagers');
    Route::get('operation-detail/{id}', 'detail')->name('operation.detail');
    Route::get('operation-updated-data/{id}', 'getUpdatedData')->name('operation.updated-data');
});

Route::post('/item-bill', [OperationController::class, 'Billstore'])->name('item-bill.store');
Route::get('/item-bill/{id}', [OperationController::class, 'Billedit'])->name('item-bill.edit');
Route::put('/item-bill/{id}', [OperationController::class, 'Billupdate'])->name('item-bill.update');
Route::get('/estimate-items/{id}/category', [OperationController::class, 'getItemCategory']);
Route::delete('/item-bill/{id}', [OperationController::class, 'Billdestroy']);
Route::post('/labor-bill', [OperationController::class, 'storeLaborBill']);
// routes/web.php
Route::get('/estimate/{encodedId}/download-pdf', [OperationController::class, 'downloadPDF'])
    ->name('estimate.pdf.download'); // Archive Estimates
Route::get('archive', [Controllers\Organization\ArchiveEstimateController::class, 'index'])
    ->name('archive.index');
Route::get('archive-update-status/{id}', [Controllers\Organization\ArchiveEstimateController::class, 'changeArchiveStatus'])
    ->name('archive.unarchive');

// general Settings

Route::get('general-settings', [Controllers\SettingsController::class, 'generalSettings'])->name('general.settings');
Route::post('update-detail/{id}', [Controllers\SettingsController::class, 'updateGeneralDetail'])->name('general.settings.update');
Route::get('margin-setup', [Controllers\SettingsController::class, 'marginSetup'])->name('margin_setup');
Route::post('save-margin', [Controllers\SettingsController::class, 'storeMargin'])->name('save-margin');
Route::get('book-of-business', [Controllers\SettingsController::class, 'bookOfBusiness'])->name('settings.maintenance.book-of-business');
Route::get('book-of-business/data', [Controllers\SettingsController::class, 'bookOfBusinessData'])->name('settings.maintenance.book-of-business.data');
Route::get('book-of-business/export', [Controllers\SettingsController::class, 'exportBookOfBusiness'])->withoutMiddleware('prevent-back-history')->name('settings.maintenance.book-of-business.export');
Route::post('/update-operation-manager',[Controllers\SettingsController::class, 'updateOperationManager'])->name('update.operation.manager');
Route::post('/update-account-owner',[Controllers\SettingsController::class, 'updateAccountOwner'])->name('update.account.owner');

// Target Module
Route::get('target', [Controllers\TargetController::class, 'setTarget'])->name('set.target');
Route::post('target', [Controllers\TargetController::class, 'addTarget'])->name('add.target');
Route::post('edit-target', [Controllers\TargetController::class, 'editTarget'])->name('edit.target');
Route::get('delete-target', [Controllers\TargetController::class, 'deleteTarget'])->name('delete.target');

// branding routes
Route::get('branding', [Controllers\SettingsController::class, 'branding'])->name('branding');
Route::post('save-branding', [Controllers\SettingsController::class, 'storeBranding'])->name('save-branding');

// Reports Module
Route::get('reports', [Controllers\Organization\ReportController::class, 'index'])->name('reports.index');
Route::post('generate-reports', [Controllers\Organization\ReportController::class, 'generateReports'])->name('generate.reports');

// Dashboard Charts
Route::post('lost-chart', [Controllers\DashboardController::class, 'lostSalesChart'])->name('lostChartData');
Route::post('total-sales-chart', [Controllers\DashboardController::class, 'totalSalesChart'])->name('totalSalesChartData');
Route::post('wip-chart-data', [Controllers\DashboardController::class, 'wipChartData'])->name('wipChartData');

// Task Routes
Route::get('/tasks/create', [TaskController::class, 'create'])->name('tasks.create');
Route::get('/tasks/get', [TaskController::class, 'getTaskNow'])->name('getTaskNow');
Route::get('/tasks/completed', [TaskController::class, 'taskCompleted'])->name('tasks.completed');
Route::post('/tasks', [TaskController::class, 'store'])->name('tasks.store');
Route::post('/tasks/update', [TaskController::class, 'updateTasks'])->name('tasks.update');

// Invoices Routes
Route::get('invoices', [InvoiceController::class, 'index'])->name('invoices.index');
Route::get('invoices/create/{id}', [InvoiceController::class, 'create'])->name('invoices.create');
Route::get('invoices/retrieve/product/{id}', [InvoiceController::class, 'retriveInvoiceItem'])->name('invoices.retrieve.product');
Route::get('invoices/remove/product/{id}', [InvoiceController::class, 'removeInvoiceItem'])->name('invoices.remove.product');
Route::post('invoices/add-product/{invoiceID}', [InvoiceController::class, 'addInvoiceItem'])->name('invoices.add.product');
Route::post('invoices/generate/{invoiceID}', [InvoiceController::class, 'store'])->name('invoices.generate');
Route::post('collect/payment/{id}', [InvoiceController::class, 'collectPayment'])->name('invoices.collect.payment');

Route::get('invoice/view/{id}', [InvoiceController::class, 'viewInvoice'])->name('invoices.view');
Route::get('invoice/edit/{id}', [InvoiceController::class, 'editInvoice'])->name('invoices.edit');
Route::get('invoice/view/attachments/{id}', [InvoiceController::class, 'viewAttachments'])->name('invoices.viewAttachments');
Route::post('invoice/update/{id}', [InvoiceController::class, 'updateInvoice'])->name('invoices.update');
Route::get('invoice/client/amount/{id}', [InvoiceController::class, 'invoiceCollectAmountDetails'])->name('invoices.client.amount');
Route::get('invoices/remove/invoice/{id}', [InvoiceController::class, 'removeInvoice'])->name('invoices.remove.invoice');
Route::get('transactions', [TransactionController::class, 'index'])->name('transactions');
Route::get('operational/invoice', [InvoiceController::class, 'createOperationalInvoice'])->name('invoices.operation');
Route::post('operational/invoice/create/{estimateID}', [InvoiceController::class, 'storeOperationalInvoice'])->name('invoices.operationInvoices');
Route::get('invoice/download/{invoiceID}', [InvoiceController::class, 'downloadInvoice'])->name('invoices.download');
Route::get('invoice/items/details/{id}', [InvoiceController::class, 'invoiceItemsDetails'])->name('invoices.ItemsDetails');
Route::post('/requests/file/{id}', [InvoiceController::class, 'imageStore'])->name('invoices.file-store');
Route::post('/requests/file-delete', [InvoiceController::class, 'imageDelete'])->name('invoices.file-delete');
Route::post('/upload/file/invoice', [InvoiceController::class, 'imageStoreFile'])->name('invoices.file.store');
Route::post('invoice/file/delete/server', [InvoiceController::class, 'imageDeleteServer'])
    ->name('invoice.file.delete.server');
// Stripe Express account
Route::get('stripe/express/account', [StripeController::class, 'createStripeStandardAccount'])->name('stripe.account.create');
Route::get('stripe/express/return_url', [StripeController::class, 'return_url'])->name('stripe.account.return_url');

// FinancialTools routes
Route::get('financial-tool', [FinancialToolController::class, 'index'])->name('financialTool');
Route::get('finance-targets', [FinancialToolController::class, 'financeTargets'])->name('financeTargets');
Route::get('revenue-target-percentage', [FinancialToolController::class, 'revenueTargetPercentage'])->name('revenueTargetPercentage');
Route::get('actual-vs-target', [FinancialToolController::class, 'actualVsTarget'])->name('revenue.actualVsTarget');
Route::post('cost-summary', [FinancialToolController::class, 'costSummary'])->name('costSummary');
Route::post('cost-summary-note', [FinancialToolController::class, 'costSummaryNote'])->name('costSummaryNote');
Route::get('cost-summary-view-note/{id}', [FinancialToolController::class, 'costSummaryViewNote'])->name('costSummaryViewNote');
Route::get('cost-summary-filter', [FinancialToolController::class, 'costSummaryFilter'])->name('costSummaryFilter');

Route::post('/sections/save', [OpportunityController::class, 'saveSection'])->name('sections.save');
Route::post('/sections/drag/update', [OpportunityController::class, 'updateSectionDrag'])->name('sections.drag.update');
Route::post('/sections/delete', [OpportunityController::class, 'deleteSection'])->name('sections.delete');
Route::post('/items/delete', [OpportunityController::class, 'deleteItem'])->name('items.delete');

Route::group([], function () {
    Route::get('payment-success', [SubscriptionController::class, 'newSubscriptionSucceeded'])->withOutMiddleware(OrganizationMiddleware::class);
    // Route::get('create-new-subscription', [SubscriptionController::class, 'createNewSubscription'])->name('newSubscription')->withOutMiddleware(OrganizationMiddleware::class);

    // Faizan Developer
    Route::get('account-settings', [Controllers\SettingsController::class, 'accountSettings'])->name('account.settings')->withOutMiddleware(OrganizationMiddleware::class);
    Route::post('/add-service-line', [Controllers\SettingsController::class, 'addServiceLine']);
    Route::post('/add-work-type', [Controllers\SettingsController::class, 'addWorkType']);
    Route::delete('/delete-service-line/{id}', [Controllers\SettingsController::class, 'deleteServiceLine']);
    Route::delete('/delete-work-type/{id}', [Controllers\SettingsController::class, 'deleteWorkType']);
    Route::post('/save-renewal-settings', [Controllers\SettingsController::class, 'saveRenewalSettings']);

    Route::resource('snow', \App\Http\Controllers\Organization\SnowController::class);
    // routes/web.php
    Route::post('/snow/import', [\App\Http\Controllers\Organization\SnowController::class, 'import'])->name('snow.import');

    // Faizan Developer end

    Route::get('division-settings', [Controllers\SettingsController::class, 'divisionSettings'])->name('division.settings')->withOutMiddleware(OrganizationMiddleware::class);

    Route::get('upgrade-subscription', [Controllers\SubscriptionController::class, 'upgradeSubscription'])->name('upgradeSubscription')->withOutMiddleware(OrganizationMiddleware::class);
    Route::post('update-address', [Controllers\SettingsController::class, 'updateAddress'])->name('update.company.address')->withOutMiddleware(OrganizationMiddleware::class);
});

Route::controller(Controllers\CommonController::class)->group(function () {
    Route::get('get-operation-manager', 'getOperationManager')->name('get-operation-manager');
    Route::get('get-account-owner', 'getAccountOwner')->name('get-account-owner');
    Route::post('book-of-business/import', 'importBookOfBusiness')->name('book-of-business.import');
});

Route::fallback([ErrorPagesController::class, '__invoke']);
